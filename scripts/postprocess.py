#!/usr/bin/env python3
"""
后处理脚本 - 整合所有本地分析功能

此脚本提供以下分析功能：
1. 序参量分析 - 从measurements/structure_factor提取数据并生成序参量图表
2. 能量分析 - 扫描saved_models中的训练日志并生成能量分析图表
3. 报告生成 - 生成完整的分析报告

使用示例:
# 运行所有后处理任务
python scripts/postprocess.py --config workflows/configs/postprocess.yaml

# 只运行序参量分析
python scripts/postprocess.py --task order_analysis

# 只运行能量分析
python scripts/postprocess.py --task energy_analysis
"""

import os
import sys
import argparse
import yaml
import numpy as np
import matplotlib.pyplot as plt
import pickle
import re
from pathlib import Path
from collections import defaultdict

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 导入src中的分析模块
from src.analysis import order_calculations as oc

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def run_order_analysis(config):
    """运行序参量分析"""
    print("=" * 80)
    print("运行序参量分析...")
    print("=" * 80)

    try:
        # 导入order_analysis模块
        from src.analysis import order_analysis

        # 创建分析器
        structure_factor_dir = config['data_collection']['structure_factor_dir']
        output_dir = config['order_analysis']['save_dir']

        analyzer = order_analysis.OrderParameterAnalyzer(
            structure_factor_dir=structure_factor_dir,
            output_dir=output_dir
        )

        # 尝试加载已有数据
        if not analyzer.load_data():
            print("未找到已保存的数据，开始重新收集...")
            # 收集数据
            L_values = config['data_collection']['L_values']
            J2_values = config['data_collection']['J2_values']
            analyzer.collect_all_data(L_values=L_values, J2_values=J2_values)
            # 保存数据
            analyzer.save_data()

        # 打印摘要
        analyzer.print_summary()

        # 生成图表
        print("\n开始生成图表...")
        for J2 in config['data_collection']['J2_values']:
            # 检查是否有这个J2值的数据
            has_data = any(key[1] == J2 for key in analyzer.data.keys())
            if has_data:
                print(f"\n生成J2={J2}的图表...")

                # 根据配置生成指定的图表
                for plot_type in config['order_analysis']['plots']:
                    if plot_type == "neel_ratio":
                        analyzer.plot_neel_ratio_vs_J1(J2)
                    elif plot_type == "dimer_ratio":
                        analyzer.plot_dimer_ratio_vs_J1(J2)
                    elif plot_type == "diag_dimer_ratio":
                        analyzer.plot_diag_dimer_ratio_vs_J1(J2)
                    elif plot_type == "af_order_parameter":
                        analyzer.plot_af_order_parameter_vs_J1(J2)
                    elif plot_type == "dimer_order_parameter":
                        analyzer.plot_dimer_order_parameter_vs_J1(J2)
                    elif plot_type == "diag_dimer_order_parameter":
                        analyzer.plot_diag_dimer_order_parameter_vs_J1(J2)

        print("\n✓ 序参量分析完成！")
        return True

    except Exception as e:
        print(f"✗ 序参量分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def run_energy_analysis(config):
    """运行能量分析"""
    print("=" * 80)
    print("运行能量分析...")
    print("=" * 80)

    try:
        # 导入energy_plot模块
        from src.analysis import energy_plot_L_J2

        # 运行能量分析
        energy_config = config['energy_analysis']
        energy_plot_L_J2.scan_and_plot_grouped_energy_logs(
            output_prefix=energy_config['output_prefix'],
            show_plot=energy_config['show_plot'],
            y_min_stable=energy_config['y_min_stable'],
            y_max_stable=energy_config['y_max_stable'],
            auto_adjust_x_stable=energy_config['auto_adjust_x_stable'],
            stable_iter_threshold=energy_config['stable_iter_threshold'],
            models_dir=energy_config.get('models_dir'),
            output_dir=energy_config.get('save_dir')
        )

        print("\n✓ 能量分析完成！")
        return True

    except Exception as e:
        print(f"✗ 能量分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def run_layer_extrapolation(config):
    """运行层外推分析"""
    print("=" * 80)
    print("运行层外推分析...")
    print("=" * 80)
    
    print("注意: 层外推分析通常需要在Jupyter Notebook中交互式运行")
    print("请使用: jupyter lab notebooks/layer_extrapolation_analysis.ipynb")
    
    return True


def generate_report(config):
    """生成分析报告"""
    print("=" * 80)
    print("生成分析报告...")
    print("=" * 80)
    
    if not config['report']['enabled']:
        print("报告生成已禁用")
        return True
    
    print("注意: 报告生成功能待实现")
    print("建议使用Jupyter Notebook生成报告")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='后处理和可视化脚本')
    parser.add_argument('--config', type=str, 
                       default='workflows/configs/postprocess.yaml',
                       help='配置文件路径')
    parser.add_argument('--task', type=str, 
                       choices=['all', 'order_analysis', 'energy_analysis', 
                               'layer_extrapolation', 'report'],
                       default='all',
                       help='要运行的任务类型')
    
    args = parser.parse_args()
    
    # 加载配置
    print(f"加载配置文件: {args.config}")
    config = load_config(args.config)
    
    # 创建输出目录
    output_dir = Path(config['data_collection']['output_dir'])
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 根据任务类型执行
    success = True
    
    if args.task == 'all':
        # 运行所有启用的任务
        if config['order_analysis']['enabled']:
            success &= run_order_analysis(config)
        
        if config['energy_analysis']['enabled']:
            success &= run_energy_analysis(config)
        
        if config['layer_extrapolation']['enabled']:
            success &= run_layer_extrapolation(config)
        
        if config['report']['enabled']:
            success &= generate_report(config)
    
    elif args.task == 'order_analysis':
        success = run_order_analysis(config)
    
    elif args.task == 'energy_analysis':
        success = run_energy_analysis(config)
    
    elif args.task == 'layer_extrapolation':
        success = run_layer_extrapolation(config)
    
    elif args.task == 'report':
        success = generate_report(config)
    
    # 输出最终状态
    print("\n" + "=" * 80)
    if success:
        print("✓ 所有后处理任务完成！")
        print("=" * 80)
        return 0
    else:
        print("✗ 部分后处理任务失败")
        print("=" * 80)
        return 1


if __name__ == "__main__":
    sys.exit(main())
