"""
表征学习模块：VAE和对比学习用于相变检测
"""

import os
import numpy as np
import jax
import jax.numpy as jnp
from jax import random
import flax.linen as nn
import optax
from typing import Tuple, Dict, List
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import pickle


class VAEEncoder(nn.Module):
    """变分自编码器用于学习采样配置的潜在表示"""
    
    input_dim: int
    latent_dim: int = 16
    hidden_dim: int = 64
    
    def setup(self):
        # 编码器
        self.encoder_fc1 = nn.Dense(self.hidden_dim)
        self.encoder_fc2 = nn.Dense(self.hidden_dim)
        self.mu_layer = nn.Dense(self.latent_dim)
        self.logvar_layer = nn.Dense(self.latent_dim)
        
        # 解码器
        self.decoder_fc1 = nn.<PERSON><PERSON>(self.hidden_dim)
        self.decoder_fc2 = nn.<PERSON><PERSON>(self.hidden_dim)
        self.decoder_out = nn.<PERSON><PERSON>(self.input_dim)
    
    def encode(self, x):
        """编码器：配置 → 潜在表示"""
        x = nn.relu(self.encoder_fc1(x))
        x = nn.relu(self.encoder_fc2(x))
        mu = self.mu_layer(x)
        logvar = self.logvar_layer(x)
        return mu, logvar
    
    def reparameterize(self, mu, logvar, key):
        """重参数化技巧"""
        std = jnp.exp(0.5 * logvar)
        eps = random.normal(key, std.shape)
        z = mu + eps * std
        return z
    
    def decode(self, z):
        """解码器：潜在表示 → 配置"""
        x = nn.relu(self.decoder_fc1(z))
        x = nn.relu(self.decoder_fc2(x))
        x = nn.sigmoid(self.decoder_out(x))
        return x
    
    def __call__(self, x, key):
        """前向传播"""
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar, key)
        recon = self.decode(z)
        return recon, mu, logvar, z


class ContrastiveEncoder(nn.Module):
    """对比学习编码器"""
    
    input_dim: int
    repr_dim: int = 32
    hidden_dim: int = 64
    
    def setup(self):
        self.fc1 = nn.Dense(self.hidden_dim)
        self.fc2 = nn.Dense(self.hidden_dim)
        self.fc3 = nn.Dense(self.repr_dim)
    
    def __call__(self, x):
        """学习表示"""
        x = nn.relu(self.fc1(x))
        x = nn.relu(self.fc2(x))
        x = self.fc3(x)
        # L2归一化
        x = x / (jnp.linalg.norm(x, axis=-1, keepdims=True) + 1e-8)
        return x


def vae_loss(params, x, key, model, beta=1.0):
    """VAE损失函数"""
    recon, mu, logvar, z = model.apply({'params': params}, x, key)
    
    # 重构损失
    recon_loss = jnp.mean((x - recon) ** 2)
    
    # KL散度
    kl_loss = -0.5 * jnp.mean(1 + logvar - mu**2 - jnp.exp(logvar))
    
    return recon_loss + beta * kl_loss, {
        'recon_loss': recon_loss,
        'kl_loss': kl_loss,
        'z': z
    }


def contrastive_loss(z_i, z_j, temperature=0.07):
    """NT-Xent对比损失"""
    # 计算相似度矩阵
    batch_size = z_i.shape[0]
    
    # 拼接正对
    z = jnp.concatenate([z_i, z_j], axis=0)
    
    # 计算相似度
    sim_matrix = jnp.dot(z, z.T) / temperature
    
    # 创建标签（对角线是正对）
    labels = jnp.arange(batch_size)
    labels = jnp.concatenate([labels, labels], axis=0)
    
    # 计算损失
    loss = 0.0
    for i in range(2 * batch_size):
        pos_idx = (i + batch_size) % (2 * batch_size)
        neg_mask = jnp.ones(2 * batch_size, dtype=bool)
        neg_mask = neg_mask.at[i].set(False)
        neg_mask = neg_mask.at[pos_idx].set(False)
        
        pos_sim = sim_matrix[i, pos_idx]
        neg_sims = sim_matrix[i, neg_mask]
        
        loss += -jnp.log(jnp.exp(pos_sim) / (jnp.exp(pos_sim) + jnp.sum(jnp.exp(neg_sims))))
    
    return loss / (2 * batch_size)


def train_vae(samples_dict: Dict[float, np.ndarray],
              latent_dim: int = 16,
              epochs: int = 50,
              batch_size: int = 32,
              learning_rate: float = 1e-3) -> Dict:
    """
    训练VAE编码器

    Args:
        samples_dict: {J1_value: samples_array}
        latent_dim: 潜在空间维度
        epochs: 训练轮数
        batch_size: 批大小
        learning_rate: 学习率

    Returns:
        包含训练结果的字典
    """
    print("\n" + "="*80)
    print("Training VAE Encoder for Representation Learning")
    print("="*80)

    # 合并所有样本
    all_samples = np.concatenate(list(samples_dict.values()), axis=0)
    
    # 处理3维数据：重塑为2维 (samples, features)
    if len(all_samples.shape) == 3:
        # 形状从 (n_samples, 1, n_features) 变为 (n_samples, n_features)
        all_samples = all_samples.reshape(all_samples.shape[0], -1)
    
    input_dim = all_samples.shape[1]

    # 标准化
    scaler = StandardScaler()
    all_samples_scaled = scaler.fit_transform(all_samples)

    # 初始化模型
    key = random.PRNGKey(42)
    model = VAEEncoder(input_dim=input_dim, latent_dim=latent_dim)
    params = model.init(key, all_samples_scaled[:1], key)['params']

    # 优化器
    optimizer = optax.adam(learning_rate)
    opt_state = optimizer.init(params)

    # 训练循环
    @jax.jit
    def train_step(params, opt_state, x, key):
        (loss, aux), grads = jax.value_and_grad(vae_loss, argnums=0, has_aux=True)(
            params, x, key, model
        )
        updates, opt_state = optimizer.update(grads, opt_state)
        params = optax.apply_updates(params, updates)
        return params, opt_state, loss, aux

    losses = []
    for epoch in range(epochs):
        epoch_loss = 0.0
        n_batches = len(all_samples_scaled) // batch_size

        for i in range(n_batches):
            key, subkey = random.split(key)
            batch = all_samples_scaled[i*batch_size:(i+1)*batch_size]
            params, opt_state, loss, aux = train_step(params, opt_state, batch, subkey)
            epoch_loss += loss

        epoch_loss /= n_batches
        losses.append(epoch_loss)

        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{epochs}, Loss: {epoch_loss:.6f}")

    # 提取所有J1值的潜在表示
    latent_reps = {}
    for J1, samples in samples_dict.items():
        # 处理3维数据：重塑为2维
        if len(samples.shape) == 3:
            samples = samples.reshape(samples.shape[0], -1)
        
        samples_scaled = scaler.transform(samples)
        key, subkey = random.split(key)
        _, _, _, z = model.apply({'params': params}, samples_scaled, subkey)
        latent_reps[J1] = np.array(z)

    print("✓ VAE training completed")

    return {
        'model': model,
        'params': params,
        'scaler': scaler,
        'latent_reps': latent_reps,
        'samples_dict': samples_dict,  # 保存原始样本用于重构误差计算
        'losses': losses
    }


def analyze_vae_representations(vae_result: Dict, J1_values: List[float]) -> Dict:
    """
    分析VAE学到的表示
    """
    print("\n" + "="*80)
    print("Analyzing VAE Representations")
    print("="*80)

    latent_reps = vae_result['latent_reps']

    # 计算相邻J1值的表示距离
    distances = []
    J1_sorted = sorted(J1_values)

    for i in range(len(J1_sorted) - 1):
        J1_i = J1_sorted[i]
        J1_j = J1_sorted[i + 1]

        z_i = latent_reps[J1_i]
        z_j = latent_reps[J1_j]

        # 计算平均距离
        mean_z_i = np.mean(z_i, axis=0)
        mean_z_j = np.mean(z_j, axis=0)
        dist = np.linalg.norm(mean_z_i - mean_z_j)
        distances.append(dist)

    # 计算重构误差
    model = vae_result['model']
    params = vae_result['params']
    scaler = vae_result['scaler']
    samples_dict = vae_result['samples_dict']

    recon_errors = {}
    key = random.PRNGKey(42)

    for J1, samples in samples_dict.items():
        # 处理3维数据：重塑为2维
        if len(samples.shape) == 3:
            samples = samples.reshape(samples.shape[0], -1)
        
        # 使用原始样本计算重构误差
        samples_scaled = scaler.transform(samples)
        recon, _, _, _ = model.apply({'params': params}, samples_scaled, key)
        error = np.mean((samples_scaled - recon) ** 2)
        recon_errors[J1] = error

    print(f"Representation distances computed: {len(distances)} transitions")
    print(f"Reconstruction errors computed: {len(recon_errors)} J1 values")

    return {
        'distances': distances,
        'recon_errors': recon_errors,
        'J1_sorted': J1_sorted
    }


def plot_vae_analysis(vae_analysis: Dict, output_dir: str):
    """绘制VAE分析结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    J1_sorted = vae_analysis['J1_sorted']
    distances = vae_analysis['distances']
    recon_errors = vae_analysis['recon_errors']
    
    # 绘制表示距离
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))
    
    # 表示距离
    J1_transitions = [(J1_sorted[i] + J1_sorted[i+1])/2 for i in range(len(distances))]
    ax1.plot(J1_transitions, distances, 'o-', linewidth=2, markersize=8, color='blue')
    ax1.set_xlabel('J1 Value', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Latent Representation Distance', fontsize=12, fontweight='bold')
    ax1.set_title('VAE: Representation Distance vs J1', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 重构误差
    J1_vals = sorted(recon_errors.keys())
    errors = [recon_errors[J1] for J1 in J1_vals]
    ax2.plot(J1_vals, errors, 's-', linewidth=2, markersize=8, color='red')
    ax2.set_xlabel('J1 Value', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Reconstruction Error', fontsize=12, fontweight='bold')
    ax2.set_title('VAE: Reconstruction Error vs J1', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'vae_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ VAE analysis plot saved to {output_dir}/vae_analysis.png")

