"""
混合方法：结合参数特征、VAE表征学习和对比学习的相变检测
"""

import os
import numpy as np
import argparse
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import AgglomerativeClustering
from sklearn.metrics import silhouette_score
from scipy.cluster.hierarchy import linkage, fcluster
import pickle

from representation_learning import (
    train_vae, 
    analyze_vae_representations, 
    plot_vae_analysis
)


def normalize_features(features_dict: Dict) -> Tuple[np.ndarray, StandardScaler]:
    """标准化特征"""
    all_features = np.concatenate(list(features_dict.values()), axis=0)
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(all_features)
    return features_scaled, scaler


def fuse_representations(param_features: np.ndarray,
                        vae_latent: np.ndarray,
                        weights: Tuple[float, float] = (0.5, 0.5)) -> np.ndarray:
    """
    融合参数特征和VAE潜在表示
    
    Args:
        param_features: 参数特征 (N, D1)
        vae_latent: VAE潜在表示 (N, D2)
        weights: 融合权重
    
    Returns:
        融合后的特征 (N, D1+D2)
    """
    # 标准化两种特征
    scaler1 = StandardScaler()
    # 确保param_features是2维的
    if param_features.ndim == 1:
        param_features = param_features.reshape(-1, 1)
    param_scaled = scaler1.fit_transform(param_features)
    
    scaler2 = StandardScaler()
    vae_scaled = scaler2.fit_transform(vae_latent)
    
    # 加权融合
    w1, w2 = weights
    fused = np.concatenate([
        param_scaled * w1,
        vae_scaled * w2
    ], axis=1)
    
    return fused


def detect_phase_boundaries(distances: List[float], 
                           J1_values: List[float],
                           threshold_percentile: float = 75.0) -> List[float]:
    """
    从表示距离检测相边界
    
    Args:
        distances: 相邻J1值的表示距离
        J1_values: J1值列表
        threshold_percentile: 阈值百分位数
    
    Returns:
        相边界的J1值列表
    """
    distances = np.array(distances)
    threshold = np.percentile(distances, threshold_percentile)
    
    J1_sorted = sorted(J1_values)
    boundaries = []
    
    for i, dist in enumerate(distances):
        if dist > threshold:
            boundary = (J1_sorted[i] + J1_sorted[i+1]) / 2
            boundaries.append(boundary)
    
    return boundaries


def hybrid_phase_detection(param_features_dict: Dict[float, np.ndarray],
                          samples_dict: Dict[float, np.ndarray],
                          J1_values: List[float],
                          output_dir: str,
                          vae_latent_dim: int = 16,
                          fusion_weights: Tuple[float, float] = (0.5, 0.5)):
    """
    混合方法进行相变检测
    
    Args:
        param_features_dict: {J1: features_array}
        samples_dict: {J1: samples_array}
        J1_values: J1值列表
        output_dir: 输出目录
        vae_latent_dim: VAE潜在维度
        fusion_weights: 融合权重 (参数特征, VAE表示)
    """
    os.makedirs(output_dir, exist_ok=True)
    
    print("\n" + "="*80)
    print("HYBRID PHASE DETECTION: Combining Multiple Representations")
    print("="*80)
    
    # ========== 路径A：参数特征 ==========
    print("\n[Path A] Extracting Parameter Features...")
    param_features_list = []
    J1_sorted = sorted(J1_values)
    
    for J1 in J1_sorted:
        features = param_features_dict[J1]
        param_features_list.append(np.mean(features, axis=0))
    
    param_features_array = np.array(param_features_list)
    print(f"✓ Parameter features shape: {param_features_array.shape}")
    
    # ========== 路径B：VAE表征学习 ==========
    print("\n[Path B] Training VAE for Representation Learning...")
    vae_result = train_vae(samples_dict, latent_dim=vae_latent_dim, epochs=50)
    
    # 提取VAE表示
    vae_latent_list = []
    for J1 in J1_sorted:
        z = vae_result['latent_reps'][J1]
        vae_latent_list.append(np.mean(z, axis=0))
    
    vae_latent_array = np.array(vae_latent_list)
    print(f"✓ VAE latent features shape: {vae_latent_array.shape}")
    
    # ========== 路径C：对比学习分析 ==========
    print("\n[Path C] Analyzing Contrastive Representations...")
    vae_analysis = analyze_vae_representations(vae_result, J1_sorted)
    print(f"✓ Representation distances computed")
    
    # ========== 融合特征 ==========
    print("\n[Fusion] Combining all representations...")
    fused_features = fuse_representations(
        param_features_array,
        vae_latent_array,
        weights=fusion_weights
    )
    print(f"✓ Fused features shape: {fused_features.shape}")
    
    # ========== 聚类分析 ==========
    print("\n[Clustering] Performing hierarchical clustering...")
    linkage_matrix = linkage(fused_features, method='ward')
    
    # 自动确定聚类数
    best_score = -1
    best_n_clusters = 2
    
    for n in range(2, min(len(J1_sorted), 10)):
        cluster_labels = fcluster(linkage_matrix, n, criterion='maxclust')
        score = silhouette_score(fused_features, cluster_labels)
        
        if score > best_score:
            best_score = score
            best_n_clusters = n
    
    cluster_labels = fcluster(linkage_matrix, best_n_clusters, criterion='maxclust') - 1
    print(f"✓ Optimal clusters: {best_n_clusters} (silhouette score: {best_score:.4f})")
    
    # ========== 相边界检测 ==========
    print("\n[Phase Boundaries] Detecting phase transitions...")
    phase_boundaries_vae = detect_phase_boundaries(
        vae_analysis['distances'],
        J1_sorted,
        threshold_percentile=75.0
    )
    print(f"✓ Detected {len(phase_boundaries_vae)} phase boundaries from VAE")
    
    # ========== 生成报告 ==========
    results = {
        'J1_values': J1_sorted,
        'param_features': param_features_array,
        'vae_latent': vae_latent_array,
        'fused_features': fused_features,
        'cluster_labels': cluster_labels,
        'n_clusters': best_n_clusters,
        'silhouette_score': best_score,
        'phase_boundaries_vae': phase_boundaries_vae,
        'vae_analysis': vae_analysis,
        'vae_result': vae_result
    }
    
    # 保存结果
    with open(os.path.join(output_dir, 'hybrid_results.pkl'), 'wb') as f:
        pickle.dump(results, f)
    
    print(f"\n✓ Results saved to {output_dir}/hybrid_results.pkl")
    
    return results


def plot_hybrid_results(results: Dict, output_dir: str):
    """绘制混合方法的结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    J1_values = results['J1_values']
    cluster_labels = results['cluster_labels']
    vae_analysis = results['vae_analysis']
    
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    
    # 1. 聚类结果
    ax = axes[0, 0]
    colors = plt.cm.rainbow(np.linspace(0, 1, len(np.unique(cluster_labels))))
    for i, J1 in enumerate(J1_values):
        ax.scatter(J1, cluster_labels[i], s=200, c=[colors[cluster_labels[i]]], 
                  edgecolors='black', linewidth=2)
    ax.set_xlabel('J1 Value', fontsize=12, fontweight='bold')
    ax.set_ylabel('Cluster ID', fontsize=12, fontweight='bold')
    ax.set_title('Hybrid Clustering Results', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    # 2. VAE表示距离
    ax = axes[0, 1]
    J1_sorted = vae_analysis['J1_sorted']
    distances = vae_analysis['distances']
    J1_transitions = [(J1_sorted[i] + J1_sorted[i+1])/2 for i in range(len(distances))]
    ax.plot(J1_transitions, distances, 'o-', linewidth=2, markersize=8, color='blue')
    ax.set_xlabel('J1 Value', fontsize=12, fontweight='bold')
    ax.set_ylabel('Latent Distance', fontsize=12, fontweight='bold')
    ax.set_title('VAE: Representation Distance', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    # 3. 重构误差
    ax = axes[1, 0]
    recon_errors = vae_analysis['recon_errors']
    J1_vals = sorted(recon_errors.keys())
    errors = [recon_errors[J1] for J1 in J1_vals]
    ax.plot(J1_vals, errors, 's-', linewidth=2, markersize=8, color='red')
    ax.set_xlabel('J1 Value', fontsize=12, fontweight='bold')
    ax.set_ylabel('Reconstruction Error', fontsize=12, fontweight='bold')
    ax.set_title('VAE: Reconstruction Error', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    # 4. 相边界
    ax = axes[1, 1]
    phase_boundaries = results['phase_boundaries_vae']
    ax.hist(J1_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    for boundary in phase_boundaries:
        ax.axvline(boundary, color='red', linestyle='--', linewidth=2, label='Phase Boundary')
    ax.set_xlabel('J1 Value', fontsize=12, fontweight='bold')
    ax.set_ylabel('Count', fontsize=12, fontweight='bold')
    ax.set_title('Detected Phase Boundaries', fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'hybrid_results.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Hybrid results plot saved to {output_dir}/hybrid_results.png")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Hybrid phase detection')
    parser.add_argument('--result_dir', type=str, default='measurements/clustering/L=5/J2=0.00',
                       help='Directory containing clustering results')
    parser.add_argument('--output_dir', type=str, default='measurements/representation_learning/L=5/J2=0.00',
                       help='Output directory')
    parser.add_argument('--vae_latent_dim', type=int, default=16,
                       help='VAE latent dimension')
    
    args = parser.parse_args()
    
    # 加载特征和样本
    print("Loading features and samples...")
    # 这里需要从checkpoint加载数据
    # 具体实现见下一个脚本

