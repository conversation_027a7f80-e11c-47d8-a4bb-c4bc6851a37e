[2025-10-21 14:56:46] ================================================================================
[2025-10-21 14:56:46] Infidelity 分析
[2025-10-21 14:56:46] ================================================================================
[2025-10-21 14:56:46] 晶格大小: L=5
[2025-10-21 14:56:46] J2 耦合强度: 1.00
[2025-10-21 14:56:46] J1 值列表: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-10-21 14:56:46] 模型配置: 4 层, 4 特征
[2025-10-21 14:56:46] Checkpoint: 自动查找最新
[2025-10-21 14:56:46] 采样数: 32768
[2025-10-21 14:56:46] ================================================================================
[2025-10-21 14:56:46] 创建共享的模型用于 Infidelity 计算...
[2025-10-21 14:57:05] ✓ 共享模型创建完成
[2025-10-21 14:57:05]   - 模型类型: <class 'netket.models.equivariant.GCNN_Parity_Irrep'>
[2025-10-21 14:57:05]   - Hilbert 空间: Spin(s=1/2, N=100, ordering=new, total_sz=0)
[2025-10-21 14:57:05] ================================================================================
[2025-10-21 14:57:05] 开始 Infidelity 扫描分析
[2025-10-21 14:57:05] 参数值: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-10-21 14:57:05] 采样数: 32768
[2025-10-21 14:57:05] ================================================================================
[2025-10-21 14:57:05] 计算参数对 (0.7600, 0.7700) 之间的 Infidelity
[2025-10-21 14:57:05] J1=0.76 自动选择checkpoint: checkpoint_iter_002250
[2025-10-21 14:57:05] 加载 J1=0.76 的参数: results/L=5/J2=1.00/J1=0.76/training/checkpoints/checkpoint_iter_002250.pkl
[2025-10-21 14:57:05]   ✓ 加载参数: final
[2025-10-21 14:57:05]     - 能量: -42.320216-0.000052j ± 0.009394
[2025-10-21 14:57:05] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:57:05] 加载 J1=0.77 的参数: results/L=5/J2=1.00/J1=0.77/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:57:05]   ✓ 加载参数: final
[2025-10-21 14:57:05]     - 能量: -42.968537-0.001554j ± 0.008277
[2025-10-21 14:57:35]   Infidelity = 2.46836012 ± 0.11534958
[2025-10-21 14:57:35]   Fidelity = -1.46836012
[2025-10-21 14:57:35] ================================================================================
[2025-10-21 14:57:35] 计算参数对 (0.7700, 0.7800) 之间的 Infidelity
[2025-10-21 14:57:35] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:57:35] 加载 J1=0.77 的参数: results/L=5/J2=1.00/J1=0.77/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:57:35]   ✓ 加载参数: final
[2025-10-21 14:57:35]     - 能量: -42.968537-0.001554j ± 0.008277
[2025-10-21 14:57:35] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:57:35] 加载 J1=0.78 的参数: results/L=5/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:57:35]   ✓ 加载参数: final
[2025-10-21 14:57:35]     - 能量: -43.603305+0.000883j ± 0.008219
[2025-10-21 14:57:51]   Infidelity = 9.54497043 ± 0.25775389
[2025-10-21 14:57:51]   Fidelity = -8.54497043
[2025-10-21 14:57:51] ================================================================================
[2025-10-21 14:57:51] 计算参数对 (0.7800, 0.7900) 之间的 Infidelity
[2025-10-21 14:57:51] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:57:51] 加载 J1=0.78 的参数: results/L=5/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:57:51]   ✓ 加载参数: final
[2025-10-21 14:57:51]     - 能量: -43.603305+0.000883j ± 0.008219
[2025-10-21 14:57:51] J1=0.79 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:57:51] 加载 J1=0.79 的参数: results/L=5/J2=1.00/J1=0.79/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:57:51]   ✓ 加载参数: final
[2025-10-21 14:57:51]     - 能量: -44.238868-0.002307j ± 0.007938
[2025-10-21 14:58:07]   Infidelity = 16303.10044397 ± 3334.14354427
[2025-10-21 14:58:07]   Fidelity = -16302.10044397
[2025-10-21 14:58:07] ================================================================================
[2025-10-21 14:58:07] 计算参数对 (0.7900, 0.8000) 之间的 Infidelity
[2025-10-21 14:58:07] J1=0.79 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:07] 加载 J1=0.79 的参数: results/L=5/J2=1.00/J1=0.79/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:07]   ✓ 加载参数: final
[2025-10-21 14:58:07]     - 能量: -44.238868-0.002307j ± 0.007938
[2025-10-21 14:58:07] J1=0.80 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:07] 加载 J1=0.80 的参数: results/L=5/J2=1.00/J1=0.80/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:07]   ✓ 加载参数: final
[2025-10-21 14:58:07]     - 能量: -44.875588-0.000406j ± 0.007285
[2025-10-21 14:58:23]   Infidelity = 9.03360939 ± 1.82721290
[2025-10-21 14:58:23]   Fidelity = -8.03360939
[2025-10-21 14:58:23] ================================================================================
[2025-10-21 14:58:23] 计算参数对 (0.8000, 0.8100) 之间的 Infidelity
[2025-10-21 14:58:23] J1=0.80 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:23] 加载 J1=0.80 的参数: results/L=5/J2=1.00/J1=0.80/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:23]   ✓ 加载参数: final
[2025-10-21 14:58:23]     - 能量: -44.875588-0.000406j ± 0.007285
[2025-10-21 14:58:23] J1=0.81 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:23] 加载 J1=0.81 的参数: results/L=5/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:23]   ✓ 加载参数: final
[2025-10-21 14:58:23]     - 能量: -45.513471+0.000746j ± 0.007145
[2025-10-21 14:58:39]   Infidelity = 21.02800296 ± 0.83863120
[2025-10-21 14:58:39]   Fidelity = -20.02800296
[2025-10-21 14:58:39] ================================================================================
[2025-10-21 14:58:39] 计算参数对 (0.8100, 0.8200) 之间的 Infidelity
[2025-10-21 14:58:39] J1=0.81 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:39] 加载 J1=0.81 的参数: results/L=5/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:39]   ✓ 加载参数: final
[2025-10-21 14:58:39]     - 能量: -45.513471+0.000746j ± 0.007145
[2025-10-21 14:58:39] J1=0.82 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:39] 加载 J1=0.82 的参数: results/L=5/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:39]   ✓ 加载参数: final
[2025-10-21 14:58:39]     - 能量: -46.148167-0.002373j ± 0.008538
[2025-10-21 14:58:55]   Infidelity = 0.08319282 ± 0.00111427
[2025-10-21 14:58:55]   Fidelity = 0.91680718
[2025-10-21 14:58:55] ================================================================================
[2025-10-21 14:58:55] 计算参数对 (0.8200, 0.8300) 之间的 Infidelity
[2025-10-21 14:58:55] J1=0.82 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:55] 加载 J1=0.82 的参数: results/L=5/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:55]   ✓ 加载参数: final
[2025-10-21 14:58:55]     - 能量: -46.148167-0.002373j ± 0.008538
[2025-10-21 14:58:55] J1=0.83 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:58:55] 加载 J1=0.83 的参数: results/L=5/J2=1.00/J1=0.83/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:58:55]   ✓ 加载参数: final
[2025-10-21 14:58:55]     - 能量: -46.804904-0.000842j ± 0.006793
[2025-10-21 14:59:11]   Infidelity = 0.00534851 ± 0.00014114
[2025-10-21 14:59:11]   Fidelity = 0.99465149
[2025-10-21 14:59:11] ================================================================================
[2025-10-21 14:59:11] 计算参数对 (0.8300, 0.8400) 之间的 Infidelity
[2025-10-21 14:59:11] J1=0.83 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:59:11] 加载 J1=0.83 的参数: results/L=5/J2=1.00/J1=0.83/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:59:11]   ✓ 加载参数: final
[2025-10-21 14:59:11]     - 能量: -46.804904-0.000842j ± 0.006793
[2025-10-21 14:59:11] J1=0.84 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:59:11] 加载 J1=0.84 的参数: results/L=5/J2=1.00/J1=0.84/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:59:11]   ✓ 加载参数: final
[2025-10-21 14:59:11]     - 能量: -47.463590+0.000678j ± 0.006157
[2025-10-21 14:59:27]   Infidelity = 0.00742911 ± 0.00043323
[2025-10-21 14:59:27]   Fidelity = 0.99257089
[2025-10-21 14:59:27] 结果已保存到: results/L=5/J2=1.00/infidelity_analysis/infidelity_scan_data.npy
[2025-10-21 14:59:27] ================================================================================
[2025-10-21 14:59:27] Infidelity 扫描分析完成
[2025-10-21 14:59:27] 成功计算 8 个参数对
[2025-10-21 14:59:28] 图像已保存到: results/L=5/J2=1.00/infidelity_analysis/infidelity_scan.png
[2025-10-21 14:59:29] 对数坐标图已保存到: results/L=5/J2=1.00/infidelity_analysis/infidelity_scan_log.png
[2025-10-21 14:59:29] ================================================================================
[2025-10-21 14:59:29] Infidelity 分析完成
[2025-10-21 14:59:29] ================================================================================
