[2025-10-21 14:55:12] ================================================================================
[2025-10-21 14:55:12] Infidelity 分析
[2025-10-21 14:55:12] ================================================================================
[2025-10-21 14:55:12] 晶格大小: L=4
[2025-10-21 14:55:12] J2 耦合强度: 1.00
[2025-10-21 14:55:12] J1 值列表: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-10-21 14:55:12] 模型配置: 4 层, 4 特征
[2025-10-21 14:55:12] Checkpoint: 自动查找最新
[2025-10-21 14:55:12] 采样数: 32768
[2025-10-21 14:55:12] ================================================================================
[2025-10-21 14:55:12] 创建共享的模型用于 Infidelity 计算...
[2025-10-21 14:55:26] ✓ 共享模型创建完成
[2025-10-21 14:55:26]   - 模型类型: <class 'netket.models.equivariant.GCNN_Parity_Irrep'>
[2025-10-21 14:55:26]   - Hilbert 空间: Spin(s=1/2, N=64, ordering=new, total_sz=0)
[2025-10-21 14:55:26] ================================================================================
[2025-10-21 14:55:26] 开始 Infidelity 扫描分析
[2025-10-21 14:55:26] 参数值: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-10-21 14:55:26] 采样数: 32768
[2025-10-21 14:55:26] ================================================================================
[2025-10-21 14:55:26] 计算参数对 (0.7600, 0.7700) 之间的 Infidelity
[2025-10-21 14:55:27] J1=0.76 自动选择checkpoint: checkpoint_iter_002250
[2025-10-21 14:55:27] 加载 J1=0.76 的参数: results/L=4/J2=1.00/J1=0.76/training/checkpoints/checkpoint_iter_002250.pkl
[2025-10-21 14:55:27]   ✓ 加载参数: final
[2025-10-21 14:55:27]     - 能量: -27.122373+0.000234j ± 0.006194
[2025-10-21 14:55:27] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:55:27] 加载 J1=0.77 的参数: results/L=4/J2=1.00/J1=0.77/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:55:27]   ✓ 加载参数: final
[2025-10-21 14:55:27]     - 能量: -27.531005+0.000478j ± 0.006471
[2025-10-21 14:55:46]   Infidelity = 0.00634979 ± 0.00015203
[2025-10-21 14:55:46]   Fidelity = 0.99365021
[2025-10-21 14:55:46] ================================================================================
[2025-10-21 14:55:46] 计算参数对 (0.7700, 0.7800) 之间的 Infidelity
[2025-10-21 14:55:46] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:55:46] 加载 J1=0.77 的参数: results/L=4/J2=1.00/J1=0.77/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:55:46]   ✓ 加载参数: final
[2025-10-21 14:55:46]     - 能量: -27.531005+0.000478j ± 0.006471
[2025-10-21 14:55:46] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:55:46] 加载 J1=0.78 的参数: results/L=4/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:55:46]   ✓ 加载参数: final
[2025-10-21 14:55:46]     - 能量: -27.950664-0.000441j ± 0.005445
[2025-10-21 14:55:53]   Infidelity = 0.00349788 ± 0.00010783
[2025-10-21 14:55:53]   Fidelity = 0.99650212
[2025-10-21 14:55:53] ================================================================================
[2025-10-21 14:55:53] 计算参数对 (0.7800, 0.7900) 之间的 Infidelity
[2025-10-21 14:55:53] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:55:53] 加载 J1=0.78 的参数: results/L=4/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:55:53]   ✓ 加载参数: final
[2025-10-21 14:55:53]     - 能量: -27.950664-0.000441j ± 0.005445
[2025-10-21 14:55:53] J1=0.79 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:55:53] 加载 J1=0.79 的参数: results/L=4/J2=1.00/J1=0.79/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:55:53]   ✓ 加载参数: final
[2025-10-21 14:55:53]     - 能量: -28.361681-0.000933j ± 0.005115
[2025-10-21 14:56:00]   Infidelity = 0.00441159 ± 0.00014220
[2025-10-21 14:56:00]   Fidelity = 0.99558841
[2025-10-21 14:56:00] ================================================================================
[2025-10-21 14:56:00] 计算参数对 (0.7900, 0.8000) 之间的 Infidelity
[2025-10-21 14:56:00] J1=0.79 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:00] 加载 J1=0.79 的参数: results/L=4/J2=1.00/J1=0.79/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:00]   ✓ 加载参数: final
[2025-10-21 14:56:00]     - 能量: -28.361681-0.000933j ± 0.005115
[2025-10-21 14:56:00] J1=0.80 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:00] 加载 J1=0.80 的参数: results/L=4/J2=1.00/J1=0.80/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:00]   ✓ 加载参数: final
[2025-10-21 14:56:00]     - 能量: -28.768129-0.003554j ± 0.006820
[2025-10-21 14:56:07]   Infidelity = 0.00338288 ± 0.00011613
[2025-10-21 14:56:07]   Fidelity = 0.99661712
[2025-10-21 14:56:07] ================================================================================
[2025-10-21 14:56:07] 计算参数对 (0.8000, 0.8100) 之间的 Infidelity
[2025-10-21 14:56:07] J1=0.80 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:07] 加载 J1=0.80 的参数: results/L=4/J2=1.00/J1=0.80/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:07]   ✓ 加载参数: final
[2025-10-21 14:56:07]     - 能量: -28.768129-0.003554j ± 0.006820
[2025-10-21 14:56:07] J1=0.81 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:07] 加载 J1=0.81 的参数: results/L=4/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:07]   ✓ 加载参数: final
[2025-10-21 14:56:07]     - 能量: -29.186881+0.000153j ± 0.004940
[2025-10-21 14:56:14]   Infidelity = 0.00274858 ± 0.00011857
[2025-10-21 14:56:14]   Fidelity = 0.99725142
[2025-10-21 14:56:14] ================================================================================
[2025-10-21 14:56:14] 计算参数对 (0.8100, 0.8200) 之间的 Infidelity
[2025-10-21 14:56:14] J1=0.81 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:14] 加载 J1=0.81 的参数: results/L=4/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:14]   ✓ 加载参数: final
[2025-10-21 14:56:14]     - 能量: -29.186881+0.000153j ± 0.004940
[2025-10-21 14:56:14] J1=0.82 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:14] 加载 J1=0.82 的参数: results/L=4/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:14]   ✓ 加载参数: final
[2025-10-21 14:56:14]     - 能量: -29.599770-0.001097j ± 0.004723
[2025-10-21 14:56:21]   Infidelity = 0.00204695 ± 0.00006296
[2025-10-21 14:56:21]   Fidelity = 0.99795305
[2025-10-21 14:56:21] ================================================================================
[2025-10-21 14:56:21] 计算参数对 (0.8200, 0.8300) 之间的 Infidelity
[2025-10-21 14:56:21] J1=0.82 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:21] 加载 J1=0.82 的参数: results/L=4/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:21]   ✓ 加载参数: final
[2025-10-21 14:56:21]     - 能量: -29.599770-0.001097j ± 0.004723
[2025-10-21 14:56:21] J1=0.83 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:21] 加载 J1=0.83 的参数: results/L=4/J2=1.00/J1=0.83/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:21]   ✓ 加载参数: final
[2025-10-21 14:56:21]     - 能量: -30.002829-0.000458j ± 0.004636
[2025-10-21 14:56:28]   Infidelity = 0.00344989 ± 0.00079302
[2025-10-21 14:56:28]   Fidelity = 0.99655011
[2025-10-21 14:56:28] ================================================================================
[2025-10-21 14:56:28] 计算参数对 (0.8300, 0.8400) 之间的 Infidelity
[2025-10-21 14:56:28] J1=0.83 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:28] 加载 J1=0.83 的参数: results/L=4/J2=1.00/J1=0.83/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:28]   ✓ 加载参数: final
[2025-10-21 14:56:28]     - 能量: -30.002829-0.000458j ± 0.004636
[2025-10-21 14:56:28] J1=0.84 自动选择checkpoint: checkpoint_iter_001050
[2025-10-21 14:56:28] 加载 J1=0.84 的参数: results/L=4/J2=1.00/J1=0.84/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-21 14:56:28]   ✓ 加载参数: final
[2025-10-21 14:56:28]     - 能量: -30.426395-0.000804j ± 0.004143
[2025-10-21 14:56:34]   Infidelity = 0.00139339 ± 0.00003462
[2025-10-21 14:56:34]   Fidelity = 0.99860661
[2025-10-21 14:56:34] 结果已保存到: results/L=4/J2=1.00/infidelity_analysis/infidelity_scan_data.npy
[2025-10-21 14:56:35] ================================================================================
[2025-10-21 14:56:35] Infidelity 扫描分析完成
[2025-10-21 14:56:35] 成功计算 8 个参数对
[2025-10-21 14:56:35] 图像已保存到: results/L=4/J2=1.00/infidelity_analysis/infidelity_scan.png
[2025-10-21 14:56:36] 对数坐标图已保存到: results/L=4/J2=1.00/infidelity_analysis/infidelity_scan_log.png
[2025-10-21 14:56:36] ================================================================================
[2025-10-21 14:56:36] Infidelity 分析完成
[2025-10-21 14:56:36] ================================================================================
