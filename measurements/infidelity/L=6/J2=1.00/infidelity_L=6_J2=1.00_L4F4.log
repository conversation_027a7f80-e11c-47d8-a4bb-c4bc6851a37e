[2025-11-02 19:56:54] ================================================================================
[2025-11-02 19:56:54] Infidelity 分析
[2025-11-02 19:56:54] ================================================================================
[2025-11-02 19:56:54] 晶格大小: L=6
[2025-11-02 19:56:54] J2 耦合强度: 1.00
[2025-11-02 19:56:54] J1 值列表: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-11-02 19:56:54] 模型配置: 4 层, 4 特征
[2025-11-02 19:56:54] Checkpoint: 自动查找最新
[2025-11-02 19:56:54] 采样数: 65536
[2025-11-02 19:56:54] ================================================================================
[2025-11-02 19:56:54] 创建共享的模型用于 Infidelity 计算...
[2025-11-02 19:57:36] ✓ 共享模型创建完成
[2025-11-02 19:57:36]   - 模型类型: <class 'netket.models.equivariant.GCNN_Parity_Irrep'>
[2025-11-02 19:57:36]   - Hilbert 空间: Spin(s=1/2, N=144, ordering=new, total_sz=0)
[2025-11-02 19:57:36] ================================================================================
[2025-11-02 19:57:36] 开始 Infidelity 扫描分析
[2025-11-02 19:57:36] 参数值: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-11-02 19:57:36] 采样数: 65536
[2025-11-02 19:57:36] ================================================================================
[2025-11-02 19:57:36] 计算参数对 (0.7600, 0.7700) 之间的 Infidelity
[2025-11-02 19:57:36] J1=0.76 自动选择checkpoint: checkpoint_iter_002250
[2025-11-02 19:57:36] 加载 J1=0.76 的参数: saved_models/L=6/J2=1.00/J1=0.76/checkpoints/checkpoint_iter_002250.pkl
[2025-11-02 19:57:36]   ✓ 加载参数: 2250
[2025-11-02 19:57:36]     - 能量: -60.903988+0.003258j ± 0.008471
[2025-11-02 19:57:36] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:57:36] 加载 J1=0.77 的参数: saved_models/L=6/J2=1.00/J1=0.77/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:57:36]   ✓ 加载参数: 1050
[2025-11-02 19:57:36]     - 能量: -61.819004-0.001621j ± 0.012340
[2025-11-02 19:59:16] 警告: 计算参数对 (0.7600, 0.7700) 时出错: INTERNAL: No reference output found!
[2025-11-02 19:59:16] Traceback (most recent call last):
  File "/home/<USER>/Repositories/Shastry-Sutherland_Extra/src/analysis/infidelity.py", line 244, in calculate_infidelity_scan
    driver.run(n_iter=1, out=logger)
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/driver/abstract_variational_driver.py", line 346, in run
    for step in self.iter(n_iter, step_size):
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/driver/abstract_variational_driver.py", line 231, in iter
    self._dp = self._forward_and_backward()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/utils/timing.py", line 292, in timed_function
    result = fun(*args, **kwargs)  # type: ignore[misc]
             ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/experimental/driver/infidelity_sr.py", line 313, in _forward_and_backward
    self._dp, self._old_updates, self.info = compute_sr_update_fun(
                                             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/utils/timing.py", line 292, in timed_function
    result = fun(*args, **kwargs)  # type: ignore[misc]
             ^^^^^^^^^^^^^^^^^^^^
jax.errors.JaxRuntimeError: INTERNAL: No reference output found!
--------------------
For simplicity, JAX has removed its internal frames from the traceback of the following exception. Set JAX_TRACEBACK_FILTERING=off to include these.

[2025-11-02 19:59:16] ================================================================================
[2025-11-02 19:59:16] 计算参数对 (0.7700, 0.7800) 之间的 Infidelity
[2025-11-02 19:59:16] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:59:16] 加载 J1=0.77 的参数: saved_models/L=6/J2=1.00/J1=0.77/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:59:16]   ✓ 加载参数: 1050
[2025-11-02 19:59:16]     - 能量: -61.819004-0.001621j ± 0.012340
[2025-11-02 19:59:16] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:59:16] 加载 J1=0.78 的参数: saved_models/L=6/J2=1.00/J1=0.78/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:59:16]   ✓ 加载参数: 1050
[2025-11-02 19:59:16]     - 能量: -62.735420-0.003897j ± 0.011241
