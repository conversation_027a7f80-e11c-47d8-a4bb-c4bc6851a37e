[2025-10-07 22:35:07] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.77/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 22:35:23] ✓ 从checkpoint加载参数: final
[2025-10-07 22:35:23]   - 能量: -27.531005+0.000478j ± 0.006471
[2025-10-07 22:35:23] ================================================================================
[2025-10-07 22:35:23] 加载量子态: L=4, J2=1.00, J1=0.77, checkpoint=final_GCNN
[2025-10-07 22:35:23] 使用采样数目: 1048576
[2025-10-07 22:35:23] 设置样本数为: 1048576
[2025-10-07 22:35:23] 开始生成共享样本集...
[2025-10-07 22:37:02] 样本生成完成,耗时: 99.196 秒
[2025-10-07 22:37:02] ================================================================================
[2025-10-07 22:37:02] 开始计算自旋结构因子...
[2025-10-07 22:37:02] 初始化操作符缓存...
[2025-10-07 22:37:02] 预构建所有自旋相关操作符...
[2025-10-07 22:37:02] 开始计算自旋相关函数...
[2025-10-07 22:37:10] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.914s
[2025-10-07 22:37:20] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.727s
[2025-10-07 22:37:25] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.873s
[2025-10-07 22:37:29] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.274s
[2025-10-07 22:37:34] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.286s
[2025-10-07 22:37:38] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.272s
[2025-10-07 22:37:42] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.254s
[2025-10-07 22:37:46] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.287s
[2025-10-07 22:37:51] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.254s
[2025-10-07 22:37:55] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.288s
[2025-10-07 22:37:59] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.259s
[2025-10-07 22:38:04] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.289s
[2025-10-07 22:38:08] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.255s
[2025-10-07 22:38:12] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.273s
[2025-10-07 22:38:16] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.309s
[2025-10-07 22:38:22] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.238s
[2025-10-07 22:38:27] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.156s
[2025-10-07 22:38:32] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.271s
[2025-10-07 22:38:37] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.261s
[2025-10-07 22:38:43] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.209s
[2025-10-07 22:38:48] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 5.212s
[2025-10-07 22:38:53] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 5.333s
[2025-10-07 22:38:58] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 5.219s
[2025-10-07 22:39:03] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 5.189s
[2025-10-07 22:39:09] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.313s
[2025-10-07 22:39:13] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.296s
[2025-10-07 22:39:17] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.262s
[2025-10-07 22:39:22] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.279s
[2025-10-07 22:39:26] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.263s
[2025-10-07 22:39:30] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.261s
[2025-10-07 22:39:34] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.261s
[2025-10-07 22:39:39] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.262s
[2025-10-07 22:39:43] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.268s
[2025-10-07 22:39:47] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.263s
[2025-10-07 22:39:52] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.263s
[2025-10-07 22:39:56] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.264s
[2025-10-07 22:40:00] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.262s
[2025-10-07 22:40:04] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.268s
[2025-10-07 22:40:09] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.262s
[2025-10-07 22:40:13] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.279s
[2025-10-07 22:40:17] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.264s
[2025-10-07 22:40:21] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.263s
[2025-10-07 22:40:26] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.328s
[2025-10-07 22:40:31] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 5.100s
[2025-10-07 22:40:36] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 5.178s
[2025-10-07 22:40:41] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.328s
[2025-10-07 22:40:47] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.212s
[2025-10-07 22:40:52] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 5.205s
[2025-10-07 22:40:57] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 5.255s
[2025-10-07 22:41:02] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 5.286s
[2025-10-07 22:41:08] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 5.181s
[2025-10-07 22:41:13] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.179s
[2025-10-07 22:41:17] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.638s
[2025-10-07 22:41:22] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.263s
[2025-10-07 22:41:27] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 5.096s
[2025-10-07 22:41:32] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 5.008s
[2025-10-07 22:41:37] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 5.311s
[2025-10-07 22:41:42] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.166s
[2025-10-07 22:41:47] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.215s
[2025-10-07 22:41:53] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 5.267s
[2025-10-07 22:41:58] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 5.234s
[2025-10-07 22:42:03] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.195s
[2025-10-07 22:42:08] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 5.230s
[2025-10-07 22:42:14] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.292s
[2025-10-07 22:42:14] 自旋相关函数计算完成,总耗时 311.18 秒
[2025-10-07 22:42:14] 计算傅里叶变换...
[2025-10-07 22:42:20] 自旋结构因子计算完成
[2025-10-07 22:42:21] 自旋相关函数平均误差: 0.000556
