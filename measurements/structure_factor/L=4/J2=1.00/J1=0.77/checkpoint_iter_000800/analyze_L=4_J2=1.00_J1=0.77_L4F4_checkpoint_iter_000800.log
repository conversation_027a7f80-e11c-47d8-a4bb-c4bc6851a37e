[2025-10-07 22:13:59] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.77/model_L4F4/training/checkpoints/checkpoint_iter_000800.pkl
[2025-10-07 22:14:15] ✓ 从checkpoint加载参数: 800
[2025-10-07 22:14:15]   - 能量: -27.543767+0.001273j ± 0.006481
[2025-10-07 22:14:15] ================================================================================
[2025-10-07 22:14:15] 加载量子态: L=4, J2=1.00, J1=0.77, checkpoint=checkpoint_iter_000800
[2025-10-07 22:14:15] 使用采样数目: 1048576
[2025-10-07 22:14:15] 设置样本数为: 1048576
[2025-10-07 22:14:15] 开始生成共享样本集...
[2025-10-07 22:15:41] 样本生成完成,耗时: 86.548 秒
[2025-10-07 22:15:41] ================================================================================
[2025-10-07 22:15:41] 开始计算自旋结构因子...
[2025-10-07 22:15:41] 初始化操作符缓存...
[2025-10-07 22:15:41] 预构建所有自旋相关操作符...
[2025-10-07 22:15:41] 开始计算自旋相关函数...
[2025-10-07 22:15:49] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.906s
[2025-10-07 22:15:59] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.758s
[2025-10-07 22:16:04] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 5.215s
[2025-10-07 22:16:10] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.249s
[2025-10-07 22:16:15] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 5.360s
[2025-10-07 22:16:20] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 5.268s
[2025-10-07 22:16:25] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 5.227s
[2025-10-07 22:16:30] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.887s
[2025-10-07 22:16:35] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.191s
[2025-10-07 22:16:40] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.027s
[2025-10-07 22:16:45] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.146s
[2025-10-07 22:16:50] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.238s
[2025-10-07 22:16:55] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.367s
[2025-10-07 22:17:01] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.251s
[2025-10-07 22:17:06] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.232s
[2025-10-07 22:17:11] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.293s
[2025-10-07 22:17:17] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.344s
[2025-10-07 22:17:22] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.221s
[2025-10-07 22:17:27] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.236s
[2025-10-07 22:17:32] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.308s
[2025-10-07 22:17:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.388s
[2025-10-07 22:17:41] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.230s
[2025-10-07 22:17:46] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 5.345s
[2025-10-07 22:17:51] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 5.136s
[2025-10-07 22:17:57] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.223s
[2025-10-07 22:18:02] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.382s
[2025-10-07 22:18:07] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.249s
[2025-10-07 22:18:12] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.220s
[2025-10-07 22:18:18] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.288s
[2025-10-07 22:18:23] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.316s
[2025-10-07 22:18:28] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.218s
[2025-10-07 22:18:34] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.251s
[2025-10-07 22:18:38] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.685s
[2025-10-07 22:18:42] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.222s
[2025-10-07 22:18:47] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.201s
[2025-10-07 22:18:51] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.222s
[2025-10-07 22:18:55] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.191s
[2025-10-07 22:18:59] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.223s
[2025-10-07 22:19:04] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.192s
[2025-10-07 22:19:08] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.230s
[2025-10-07 22:19:12] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.192s
[2025-10-07 22:19:16] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.202s
[2025-10-07 22:19:20] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.192s
[2025-10-07 22:19:25] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.224s
[2025-10-07 22:19:29] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.192s
[2025-10-07 22:19:33] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.203s
[2025-10-07 22:19:37] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.234s
[2025-10-07 22:19:41] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.193s
[2025-10-07 22:19:46] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.231s
[2025-10-07 22:19:50] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.235s
[2025-10-07 22:19:54] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.195s
[2025-10-07 22:19:58] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.237s
[2025-10-07 22:20:03] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.201s
[2025-10-07 22:20:07] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.224s
[2025-10-07 22:20:11] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.192s
[2025-10-07 22:20:15] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.239s
[2025-10-07 22:20:19] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.192s
[2025-10-07 22:20:24] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.192s
[2025-10-07 22:20:28] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.192s
[2025-10-07 22:20:32] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.235s
[2025-10-07 22:20:36] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.211s
[2025-10-07 22:20:41] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.211s
[2025-10-07 22:20:47] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 5.194s
[2025-10-07 22:20:52] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.281s
[2025-10-07 22:20:52] 自旋相关函数计算完成,总耗时 310.49 秒
[2025-10-07 22:20:52] 计算傅里叶变换...
[2025-10-07 22:20:58] 自旋结构因子计算完成
[2025-10-07 22:20:59] 自旋相关函数平均误差: 0.000556
