[2025-10-07 22:27:53] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.77/model_L4F4/training/checkpoints/checkpoint_iter_001000.pkl
[2025-10-07 22:28:08] ✓ 从checkpoint加载参数: 1000
[2025-10-07 22:28:08]   - 能量: -27.540611-0.000837j ± 0.006291
[2025-10-07 22:28:08] ================================================================================
[2025-10-07 22:28:08] 加载量子态: L=4, J2=1.00, J1=0.77, checkpoint=checkpoint_iter_001000
[2025-10-07 22:28:08] 使用采样数目: 1048576
[2025-10-07 22:28:08] 设置样本数为: 1048576
[2025-10-07 22:28:08] 开始生成共享样本集...
[2025-10-07 22:29:35] 样本生成完成,耗时: 86.754 秒
[2025-10-07 22:29:35] ================================================================================
[2025-10-07 22:29:35] 开始计算自旋结构因子...
[2025-10-07 22:29:35] 初始化操作符缓存...
[2025-10-07 22:29:35] 预构建所有自旋相关操作符...
[2025-10-07 22:29:35] 开始计算自旋相关函数...
[2025-10-07 22:29:43] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.946s
[2025-10-07 22:29:53] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.843s
[2025-10-07 22:29:58] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 5.225s
[2025-10-07 22:30:03] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.232s
[2025-10-07 22:30:08] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.966s
[2025-10-07 22:30:13] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.205s
[2025-10-07 22:30:17] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.195s
[2025-10-07 22:30:21] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.524s
[2025-10-07 22:30:26] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 5.188s
[2025-10-07 22:30:32] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.357s
[2025-10-07 22:30:37] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.219s
[2025-10-07 22:30:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.234s
[2025-10-07 22:30:48] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.312s
[2025-10-07 22:30:53] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.322s
[2025-10-07 22:30:58] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.228s
[2025-10-07 22:31:03] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.230s
[2025-10-07 22:31:09] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.380s
[2025-10-07 22:31:14] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.294s
[2025-10-07 22:31:19] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.229s
[2025-10-07 22:31:24] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.595s
[2025-10-07 22:31:28] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.205s
[2025-10-07 22:31:32] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.207s
[2025-10-07 22:31:37] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.202s
[2025-10-07 22:31:41] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.214s
[2025-10-07 22:31:45] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.205s
[2025-10-07 22:31:49] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.207s
[2025-10-07 22:31:53] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.202s
[2025-10-07 22:31:58] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.222s
[2025-10-07 22:32:02] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.206s
[2025-10-07 22:32:06] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.199s
[2025-10-07 22:32:10] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.203s
[2025-10-07 22:32:15] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.130s
[2025-10-07 22:32:21] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.234s
[2025-10-07 22:32:26] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.354s
[2025-10-07 22:32:31] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.239s
[2025-10-07 22:32:36] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.236s
[2025-10-07 22:32:42] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 5.284s
[2025-10-07 22:32:47] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.303s
[2025-10-07 22:32:52] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 5.195s
[2025-10-07 22:32:58] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 5.233s
[2025-10-07 22:33:03] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 5.355s
[2025-10-07 22:33:08] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 5.206s
[2025-10-07 22:33:13] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.855s
[2025-10-07 22:33:17] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.206s
[2025-10-07 22:33:22] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.743s
[2025-10-07 22:33:27] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.094s
[2025-10-07 22:33:32] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.218s
[2025-10-07 22:33:38] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 5.341s
[2025-10-07 22:33:43] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 5.250s
[2025-10-07 22:33:48] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 5.196s
[2025-10-07 22:33:53] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 5.329s
[2025-10-07 22:33:59] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.287s
[2025-10-07 22:34:04] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.214s
[2025-10-07 22:34:09] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 5.235s
[2025-10-07 22:34:14] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.932s
[2025-10-07 22:34:18] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.226s
[2025-10-07 22:34:22] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.199s
[2025-10-07 22:34:27] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.198s
[2025-10-07 22:34:31] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.199s
[2025-10-07 22:34:35] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.212s
[2025-10-07 22:34:39] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.206s
[2025-10-07 22:34:43] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.198s
[2025-10-07 22:34:48] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.209s
[2025-10-07 22:34:52] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.205s
[2025-10-07 22:34:52] 自旋相关函数计算完成,总耗时 316.80 秒
[2025-10-07 22:34:52] 计算傅里叶变换...
[2025-10-07 22:34:58] 自旋结构因子计算完成
[2025-10-07 22:34:59] 自旋相关函数平均误差: 0.000570
