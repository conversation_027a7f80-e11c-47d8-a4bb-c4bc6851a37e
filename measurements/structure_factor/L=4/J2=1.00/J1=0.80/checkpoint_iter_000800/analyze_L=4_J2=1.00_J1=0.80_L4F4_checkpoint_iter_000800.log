[2025-10-07 23:37:25] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.80/model_L4F4/training/checkpoints/checkpoint_iter_000800.pkl
[2025-10-07 23:37:40] ✓ 从checkpoint加载参数: 800
[2025-10-07 23:37:40]   - 能量: -28.768310-0.001494j ± 0.005068
[2025-10-07 23:37:40] ================================================================================
[2025-10-07 23:37:40] 加载量子态: L=4, J2=1.00, J1=0.80, checkpoint=checkpoint_iter_000800
[2025-10-07 23:37:40] 使用采样数目: 1048576
[2025-10-07 23:37:40] 设置样本数为: 1048576
[2025-10-07 23:37:40] 开始生成共享样本集...
[2025-10-07 23:39:14] 样本生成完成,耗时: 93.955 秒
[2025-10-07 23:39:14] ================================================================================
[2025-10-07 23:39:14] 开始计算自旋结构因子...
[2025-10-07 23:39:14] 初始化操作符缓存...
[2025-10-07 23:39:14] 预构建所有自旋相关操作符...
[2025-10-07 23:39:14] 开始计算自旋相关函数...
[2025-10-07 23:39:22] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.734s
[2025-10-07 23:39:31] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.408s
[2025-10-07 23:39:35] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.855s
[2025-10-07 23:39:41] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.296s
[2025-10-07 23:39:46] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 5.184s
[2025-10-07 23:39:51] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 5.209s
[2025-10-07 23:39:57] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 5.362s
[2025-10-07 23:40:02] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 5.234s
[2025-10-07 23:40:07] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 5.202s
[2025-10-07 23:40:12] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.278s
[2025-10-07 23:40:18] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.349s
[2025-10-07 23:40:23] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.217s
[2025-10-07 23:40:28] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.215s
[2025-10-07 23:40:33] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.297s
[2025-10-07 23:40:38] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.225s
[2025-10-07 23:40:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.203s
[2025-10-07 23:40:46] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.220s
[2025-10-07 23:40:50] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.224s
[2025-10-07 23:40:54] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.198s
[2025-10-07 23:40:59] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.225s
[2025-10-07 23:41:03] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.222s
[2025-10-07 23:41:07] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.224s
[2025-10-07 23:41:11] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.196s
[2025-10-07 23:41:16] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.222s
[2025-10-07 23:41:20] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.217s
[2025-10-07 23:41:25] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.224s
[2025-10-07 23:41:30] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.192s
[2025-10-07 23:41:36] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.318s
[2025-10-07 23:41:41] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.265s
[2025-10-07 23:41:46] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.213s
[2025-10-07 23:41:51] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.269s
[2025-10-07 23:41:57] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.317s
[2025-10-07 23:42:02] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.191s
[2025-10-07 23:42:07] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.247s
[2025-10-07 23:42:12] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.324s
[2025-10-07 23:42:18] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.240s
[2025-10-07 23:42:23] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 5.169s
[2025-10-07 23:42:27] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.230s
[2025-10-07 23:42:31] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.199s
[2025-10-07 23:42:35] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.224s
[2025-10-07 23:42:40] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.201s
[2025-10-07 23:42:44] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.748s
[2025-10-07 23:42:50] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.214s
[2025-10-07 23:42:55] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 5.229s
[2025-10-07 23:43:00] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 5.353s
[2025-10-07 23:43:05] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.204s
[2025-10-07 23:43:11] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.243s
[2025-10-07 23:43:16] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 5.289s
[2025-10-07 23:43:21] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 5.350s
[2025-10-07 23:43:27] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 5.201s
[2025-10-07 23:43:32] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 5.213s
[2025-10-07 23:43:37] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.339s
[2025-10-07 23:43:42] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.206s
[2025-10-07 23:43:47] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.545s
[2025-10-07 23:43:51] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.186s
[2025-10-07 23:43:55] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.358s
[2025-10-07 23:44:01] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 5.342s
[2025-10-07 23:44:06] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.218s
[2025-10-07 23:44:11] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.221s
[2025-10-07 23:44:17] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 5.321s
[2025-10-07 23:44:22] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 5.278s
[2025-10-07 23:44:27] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.199s
[2025-10-07 23:44:32] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 5.283s
[2025-10-07 23:44:38] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.273s
[2025-10-07 23:44:38] 自旋相关函数计算完成,总耗时 323.17 秒
[2025-10-07 23:44:38] 计算傅里叶变换...
[2025-10-07 23:44:44] 自旋结构因子计算完成
[2025-10-07 23:44:45] 自旋相关函数平均误差: 0.000552
