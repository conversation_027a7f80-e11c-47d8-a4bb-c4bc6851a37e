[2025-10-08 01:12:09] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.83/model_L4F4/training/checkpoints/checkpoint_iter_001000.pkl
[2025-10-08 01:12:25] ✓ 从checkpoint加载参数: 1000
[2025-10-08 01:12:25]   - 能量: -30.008756-0.000052j ± 0.004764
[2025-10-08 01:12:25] ================================================================================
[2025-10-08 01:12:25] 加载量子态: L=4, J2=1.00, J1=0.83, checkpoint=checkpoint_iter_001000
[2025-10-08 01:12:25] 使用采样数目: 1048576
[2025-10-08 01:12:25] 设置样本数为: 1048576
[2025-10-08 01:12:25] 开始生成共享样本集...
[2025-10-08 01:13:47] 样本生成完成,耗时: 82.137 秒
[2025-10-08 01:13:48] ================================================================================
[2025-10-08 01:13:48] 开始计算自旋结构因子...
[2025-10-08 01:13:48] 初始化操作符缓存...
[2025-10-08 01:13:48] 预构建所有自旋相关操作符...
[2025-10-08 01:13:48] 开始计算自旋相关函数...
[2025-10-08 01:13:55] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.159s
[2025-10-08 01:14:03] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.429s
[2025-10-08 01:14:07] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.251s
[2025-10-08 01:14:12] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.272s
[2025-10-08 01:14:16] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.286s
[2025-10-08 01:14:20] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.272s
[2025-10-08 01:14:25] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.250s
[2025-10-08 01:14:29] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.288s
[2025-10-08 01:14:33] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.252s
[2025-10-08 01:14:37] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.288s
[2025-10-08 01:14:42] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.257s
[2025-10-08 01:14:46] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.287s
[2025-10-08 01:14:50] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.254s
[2025-10-08 01:14:54] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.272s
[2025-10-08 01:14:59] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.291s
[2025-10-08 01:15:03] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.256s
[2025-10-08 01:15:07] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.273s
[2025-10-08 01:15:12] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.298s
[2025-10-08 01:15:16] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.265s
[2025-10-08 01:15:20] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.288s
[2025-10-08 01:15:24] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.275s
[2025-10-08 01:15:29] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.289s
[2025-10-08 01:15:33] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.254s
[2025-10-08 01:15:37] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.273s
[2025-10-08 01:15:42] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.257s
[2025-10-08 01:15:46] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.274s
[2025-10-08 01:15:50] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.256s
[2025-10-08 01:15:54] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.289s
[2025-10-08 01:15:59] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.257s
[2025-10-08 01:16:03] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.254s
[2025-10-08 01:16:07] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.253s
[2025-10-08 01:16:11] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.280s
[2025-10-08 01:16:16] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.267s
[2025-10-08 01:16:20] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.272s
[2025-10-08 01:16:24] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.267s
[2025-10-08 01:16:29] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.273s
[2025-10-08 01:16:33] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.255s
[2025-10-08 01:16:37] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.275s
[2025-10-08 01:16:41] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.256s
[2025-10-08 01:16:46] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.288s
[2025-10-08 01:16:50] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.254s
[2025-10-08 01:16:54] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.257s
[2025-10-08 01:16:58] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.254s
[2025-10-08 01:17:03] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.275s
[2025-10-08 01:17:07] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.253s
[2025-10-08 01:17:11] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.253s
[2025-10-08 01:17:15] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.288s
[2025-10-08 01:17:20] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.261s
[2025-10-08 01:17:24] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.298s
[2025-10-08 01:17:28] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.289s
[2025-10-08 01:17:33] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.254s
[2025-10-08 01:17:37] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.288s
[2025-10-08 01:17:41] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.256s
[2025-10-08 01:17:45] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.274s
[2025-10-08 01:17:50] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.253s
[2025-10-08 01:17:54] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.289s
[2025-10-08 01:17:58] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.264s
[2025-10-08 01:18:03] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.254s
[2025-10-08 01:18:07] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.254s
[2025-10-08 01:18:11] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.289s
[2025-10-08 01:18:15] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.257s
[2025-10-08 01:18:20] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.253s
[2025-10-08 01:18:24] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.294s
[2025-10-08 01:18:28] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.256s
[2025-10-08 01:18:28] 自旋相关函数计算完成,总耗时 280.56 秒
[2025-10-08 01:18:28] 计算傅里叶变换...
[2025-10-08 01:18:34] 自旋结构因子计算完成
[2025-10-08 01:18:35] 自旋相关函数平均误差: 0.000545
