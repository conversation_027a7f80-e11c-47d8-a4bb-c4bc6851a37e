[2025-10-07 23:30:10] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.79/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 23:30:26] ✓ 从checkpoint加载参数: final
[2025-10-07 23:30:26]   - 能量: -28.361681-0.000933j ± 0.005115
[2025-10-07 23:30:26] ================================================================================
[2025-10-07 23:30:26] 加载量子态: L=4, J2=1.00, J1=0.79, checkpoint=final_GCNN
[2025-10-07 23:30:26] 使用采样数目: 1048576
[2025-10-07 23:30:26] 设置样本数为: 1048576
[2025-10-07 23:30:26] 开始生成共享样本集...
[2025-10-07 23:31:48] 样本生成完成,耗时: 82.018 秒
[2025-10-07 23:31:48] ================================================================================
[2025-10-07 23:31:48] 开始计算自旋结构因子...
[2025-10-07 23:31:48] 初始化操作符缓存...
[2025-10-07 23:31:48] 预构建所有自旋相关操作符...
[2025-10-07 23:31:48] 开始计算自旋相关函数...
[2025-10-07 23:31:55] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.035s
[2025-10-07 23:32:04] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.427s
[2025-10-07 23:32:08] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.250s
[2025-10-07 23:32:12] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.272s
[2025-10-07 23:32:16] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.282s
[2025-10-07 23:32:21] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.280s
[2025-10-07 23:32:25] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.250s
[2025-10-07 23:32:29] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.283s
[2025-10-07 23:32:34] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.250s
[2025-10-07 23:32:38] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.284s
[2025-10-07 23:32:42] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.257s
[2025-10-07 23:32:46] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.283s
[2025-10-07 23:32:51] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.256s
[2025-10-07 23:32:55] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.285s
[2025-10-07 23:32:59] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.282s
[2025-10-07 23:33:03] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.252s
[2025-10-07 23:33:08] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.270s
[2025-10-07 23:33:12] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.282s
[2025-10-07 23:33:16] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.252s
[2025-10-07 23:33:21] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.283s
[2025-10-07 23:33:25] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.272s
[2025-10-07 23:33:29] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.283s
[2025-10-07 23:33:33] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.251s
[2025-10-07 23:33:38] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.441s
[2025-10-07 23:33:43] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.267s
[2025-10-07 23:33:48] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.278s
[2025-10-07 23:33:54] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.210s
[2025-10-07 23:33:59] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.227s
[2025-10-07 23:34:04] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.283s
[2025-10-07 23:34:09] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.216s
[2025-10-07 23:34:15] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.236s
[2025-10-07 23:34:20] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.313s
[2025-10-07 23:34:25] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.257s
[2025-10-07 23:34:30] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.138s
[2025-10-07 23:34:35] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.265s
[2025-10-07 23:34:40] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.232s
[2025-10-07 23:34:45] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.991s
[2025-10-07 23:34:50] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.309s
[2025-10-07 23:34:55] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 5.188s
[2025-10-07 23:35:00] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 5.185s
[2025-10-07 23:35:06] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 5.336s
[2025-10-07 23:35:11] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 5.187s
[2025-10-07 23:35:16] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.170s
[2025-10-07 23:35:22] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 5.313s
[2025-10-07 23:35:27] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 5.173s
[2025-10-07 23:35:32] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.191s
[2025-10-07 23:35:37] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.310s
[2025-10-07 23:35:42] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 5.181s
[2025-10-07 23:35:47] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.556s
[2025-10-07 23:35:51] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.281s
[2025-10-07 23:35:55] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.247s
[2025-10-07 23:36:00] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.281s
[2025-10-07 23:36:04] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.249s
[2025-10-07 23:36:08] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.275s
[2025-10-07 23:36:13] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.246s
[2025-10-07 23:36:17] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.281s
[2025-10-07 23:36:22] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.826s
[2025-10-07 23:36:27] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.164s
[2025-10-07 23:36:32] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.200s
[2025-10-07 23:36:37] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 5.290s
[2025-10-07 23:36:43] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 5.214s
[2025-10-07 23:36:48] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.166s
[2025-10-07 23:36:53] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 5.264s
[2025-10-07 23:36:58] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.300s
[2025-10-07 23:36:58] 自旋相关函数计算完成,总耗时 310.12 秒
[2025-10-07 23:36:58] 计算傅里叶变换...
[2025-10-07 23:37:04] 自旋结构因子计算完成
[2025-10-07 23:37:05] 自旋相关函数平均误差: 0.000551
