[2025-10-07 22:42:29] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.78/model_L4F4/training/checkpoints/checkpoint_iter_000800.pkl
[2025-10-07 22:42:45] ✓ 从checkpoint加载参数: 800
[2025-10-07 22:42:45]   - 能量: -27.944783-0.000160j ± 0.005560
[2025-10-07 22:42:45] ================================================================================
[2025-10-07 22:42:45] 加载量子态: L=4, J2=1.00, J1=0.78, checkpoint=checkpoint_iter_000800
[2025-10-07 22:42:45] 使用采样数目: 1048576
[2025-10-07 22:42:45] 设置样本数为: 1048576
[2025-10-07 22:42:45] 开始生成共享样本集...
[2025-10-07 22:44:24] 样本生成完成,耗时: 98.194 秒
[2025-10-07 22:44:24] ================================================================================
[2025-10-07 22:44:24] 开始计算自旋结构因子...
[2025-10-07 22:44:24] 初始化操作符缓存...
[2025-10-07 22:44:24] 预构建所有自旋相关操作符...
[2025-10-07 22:44:24] 开始计算自旋相关函数...
[2025-10-07 22:44:32] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.919s
[2025-10-07 22:44:41] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.550s
[2025-10-07 22:44:46] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 5.326s
[2025-10-07 22:44:52] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.200s
[2025-10-07 22:44:57] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 5.091s
[2025-10-07 22:45:01] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.275s
[2025-10-07 22:45:05] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.248s
[2025-10-07 22:45:10] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.553s
[2025-10-07 22:45:15] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.679s
[2025-10-07 22:45:20] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.172s
[2025-10-07 22:45:25] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.224s
[2025-10-07 22:45:30] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.227s
[2025-10-07 22:45:35] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.184s
[2025-10-07 22:45:41] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.226s
[2025-10-07 22:45:46] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.249s
[2025-10-07 22:45:51] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.179s
[2025-10-07 22:45:56] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.215s
[2025-10-07 22:46:02] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.274s
[2025-10-07 22:46:07] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.179s
[2025-10-07 22:46:12] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.192s
[2025-10-07 22:46:17] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.827s
[2025-10-07 22:46:21] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.274s
[2025-10-07 22:46:25] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.255s
[2025-10-07 22:46:30] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.267s
[2025-10-07 22:46:34] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.256s
[2025-10-07 22:46:38] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.321s
[2025-10-07 22:46:43] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.310s
[2025-10-07 22:46:49] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.199s
[2025-10-07 22:46:54] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.164s
[2025-10-07 22:46:59] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.322s
[2025-10-07 22:47:04] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.157s
[2025-10-07 22:47:10] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.203s
[2025-10-07 22:47:15] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.323s
[2025-10-07 22:47:20] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.228s
[2025-10-07 22:47:25] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.213s
[2025-10-07 22:47:31] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.258s
[2025-10-07 22:47:36] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 5.265s
[2025-10-07 22:47:41] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.137s
[2025-10-07 22:47:45] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.332s
[2025-10-07 22:47:50] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.274s
[2025-10-07 22:47:54] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.257s
[2025-10-07 22:47:58] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.581s
[2025-10-07 22:48:04] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.080s
[2025-10-07 22:48:09] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 5.157s
[2025-10-07 22:48:14] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 5.321s
[2025-10-07 22:48:19] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.181s
[2025-10-07 22:48:24] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.203s
[2025-10-07 22:48:30] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 5.238s
[2025-10-07 22:48:35] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 5.274s
[2025-10-07 22:48:40] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 5.200s
[2025-10-07 22:48:45] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 5.160s
[2025-10-07 22:48:51] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.326s
[2025-10-07 22:48:56] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.187s
[2025-10-07 22:49:01] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 5.051s
[2025-10-07 22:49:05] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.268s
[2025-10-07 22:49:09] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.275s
[2025-10-07 22:49:14] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.345s
[2025-10-07 22:49:19] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.331s
[2025-10-07 22:49:24] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.170s
[2025-10-07 22:49:29] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 5.188s
[2025-10-07 22:49:35] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 5.287s
[2025-10-07 22:49:40] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.231s
[2025-10-07 22:49:45] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 5.207s
[2025-10-07 22:49:50] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.207s
[2025-10-07 22:49:50] 自旋相关函数计算完成,总耗时 326.74 秒
[2025-10-07 22:49:51] 计算傅里叶变换...
[2025-10-07 22:49:53] 自旋结构因子计算完成
[2025-10-07 22:49:54] 自旋相关函数平均误差: 0.000557
