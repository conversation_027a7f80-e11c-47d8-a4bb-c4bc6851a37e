[2025-10-07 22:57:08] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.78/model_L4F4/training/checkpoints/checkpoint_iter_001000.pkl
[2025-10-07 22:57:23] ✓ 从checkpoint加载参数: 1000
[2025-10-07 22:57:23]   - 能量: -27.949075+0.000509j ± 0.006391
[2025-10-07 22:57:23] ================================================================================
[2025-10-07 22:57:23] 加载量子态: L=4, J2=1.00, J1=0.78, checkpoint=checkpoint_iter_001000
[2025-10-07 22:57:23] 使用采样数目: 1048576
[2025-10-07 22:57:23] 设置样本数为: 1048576
[2025-10-07 22:57:23] 开始生成共享样本集...
[2025-10-07 22:58:45] 样本生成完成,耗时: 81.644 秒
[2025-10-07 22:58:45] ================================================================================
[2025-10-07 22:58:45] 开始计算自旋结构因子...
[2025-10-07 22:58:45] 初始化操作符缓存...
[2025-10-07 22:58:45] 预构建所有自旋相关操作符...
[2025-10-07 22:58:45] 开始计算自旋相关函数...
[2025-10-07 22:58:52] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 6.938s
[2025-10-07 22:59:01] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.336s
[2025-10-07 22:59:05] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.195s
[2025-10-07 22:59:09] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.226s
[2025-10-07 22:59:13] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.243s
[2025-10-07 22:59:17] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.226s
[2025-10-07 22:59:22] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.216s
[2025-10-07 22:59:27] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 5.248s
[2025-10-07 22:59:32] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 5.244s
[2025-10-07 22:59:37] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.238s
[2025-10-07 22:59:43] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.363s
[2025-10-07 22:59:48] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.221s
[2025-10-07 22:59:53] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.256s
[2025-10-07 22:59:59] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.264s
[2025-10-07 23:00:04] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.292s
[2025-10-07 23:00:09] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.251s
[2025-10-07 23:00:14] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.216s
[2025-10-07 23:00:20] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.321s
[2025-10-07 23:00:25] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.919s
[2025-10-07 23:00:29] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.243s
[2025-10-07 23:00:33] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.226s
[2025-10-07 23:00:37] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.232s
[2025-10-07 23:00:41] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.190s
[2025-10-07 23:00:46] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.263s
[2025-10-07 23:00:50] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.683s
[2025-10-07 23:00:56] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.162s
[2025-10-07 23:01:01] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.222s
[2025-10-07 23:01:06] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.320s
[2025-10-07 23:01:11] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.239s
[2025-10-07 23:01:17] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.215s
[2025-10-07 23:01:22] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.273s
[2025-10-07 23:01:27] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.347s
[2025-10-07 23:01:32] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.212s
[2025-10-07 23:01:38] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.191s
[2025-10-07 23:01:43] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.296s
[2025-10-07 23:01:48] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.306s
[2025-10-07 23:01:53] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.334s
[2025-10-07 23:01:57] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.217s
[2025-10-07 23:02:01] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.191s
[2025-10-07 23:02:05] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.240s
[2025-10-07 23:02:09] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.189s
[2025-10-07 23:02:14] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.205s
[2025-10-07 23:02:18] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.190s
[2025-10-07 23:02:22] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.233s
[2025-10-07 23:02:26] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.190s
[2025-10-07 23:02:30] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.189s
[2025-10-07 23:02:35] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.233s
[2025-10-07 23:02:39] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.191s
[2025-10-07 23:02:43] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.232s
[2025-10-07 23:02:47] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.233s
[2025-10-07 23:02:52] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.188s
[2025-10-07 23:02:56] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.233s
[2025-10-07 23:03:00] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.204s
[2025-10-07 23:03:04] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.232s
[2025-10-07 23:03:08] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.187s
[2025-10-07 23:03:13] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.233s
[2025-10-07 23:03:17] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.190s
[2025-10-07 23:03:21] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.188s
[2025-10-07 23:03:25] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.188s
[2025-10-07 23:03:30] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.234s
[2025-10-07 23:03:34] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.209s
[2025-10-07 23:03:38] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.187s
[2025-10-07 23:03:42] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.232s
[2025-10-07 23:03:46] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.203s
[2025-10-07 23:03:46] 自旋相关函数计算完成,总耗时 301.10 秒
[2025-10-07 23:03:46] 计算傅里叶变换...
[2025-10-07 23:03:52] 自旋结构因子计算完成
[2025-10-07 23:03:53] 自旋相关函数平均误差: 0.000561
