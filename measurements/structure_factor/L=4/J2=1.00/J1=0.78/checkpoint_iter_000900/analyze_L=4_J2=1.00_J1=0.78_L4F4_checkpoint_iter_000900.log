[2025-10-07 22:50:02] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.78/model_L4F4/training/checkpoints/checkpoint_iter_000900.pkl
[2025-10-07 22:50:18] ✓ 从checkpoint加载参数: 900
[2025-10-07 22:50:18]   - 能量: -27.938066-0.000839j ± 0.005238
[2025-10-07 22:50:18] ================================================================================
[2025-10-07 22:50:18] 加载量子态: L=4, J2=1.00, J1=0.78, checkpoint=checkpoint_iter_000900
[2025-10-07 22:50:18] 使用采样数目: 1048576
[2025-10-07 22:50:18] 设置样本数为: 1048576
[2025-10-07 22:50:18] 开始生成共享样本集...
[2025-10-07 22:51:52] 样本生成完成,耗时: 93.970 秒
[2025-10-07 22:51:52] ================================================================================
[2025-10-07 22:51:52] 开始计算自旋结构因子...
[2025-10-07 22:51:52] 初始化操作符缓存...
[2025-10-07 22:51:52] 预构建所有自旋相关操作符...
[2025-10-07 22:51:52] 开始计算自旋相关函数...
[2025-10-07 22:51:59] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 6.934s
[2025-10-07 22:52:07] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.311s
[2025-10-07 22:52:11] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.199s
[2025-10-07 22:52:15] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.204s
[2025-10-07 22:52:20] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.215s
[2025-10-07 22:52:24] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.202s
[2025-10-07 22:52:28] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.197s
[2025-10-07 22:52:32] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.221s
[2025-10-07 22:52:36] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.198s
[2025-10-07 22:52:41] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.227s
[2025-10-07 22:52:45] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.202s
[2025-10-07 22:52:49] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.216s
[2025-10-07 22:52:53] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.199s
[2025-10-07 22:52:57] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.203s
[2025-10-07 22:53:02] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.214s
[2025-10-07 22:53:06] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.201s
[2025-10-07 22:53:10] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.201s
[2025-10-07 22:53:15] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.004s
[2025-10-07 22:53:20] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.001s
[2025-10-07 22:53:25] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.352s
[2025-10-07 22:53:31] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 5.248s
[2025-10-07 22:53:36] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 5.215s
[2025-10-07 22:53:41] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 5.299s
[2025-10-07 22:53:47] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 5.360s
[2025-10-07 22:53:52] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.231s
[2025-10-07 22:53:57] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.237s
[2025-10-07 22:54:02] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.316s
[2025-10-07 22:54:08] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.173s
[2025-10-07 22:54:13] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.243s
[2025-10-07 22:54:17] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.232s
[2025-10-07 22:54:21] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.202s
[2025-10-07 22:54:26] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.338s
[2025-10-07 22:54:31] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.321s
[2025-10-07 22:54:36] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.242s
[2025-10-07 22:54:41] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.240s
[2025-10-07 22:54:47] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.235s
[2025-10-07 22:54:52] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 5.312s
[2025-10-07 22:54:57] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.222s
[2025-10-07 22:55:02] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 5.238s
[2025-10-07 22:55:08] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 5.353s
[2025-10-07 22:55:13] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 5.235s
[2025-10-07 22:55:18] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 5.194s
[2025-10-07 22:55:23] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.274s
[2025-10-07 22:55:28] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.838s
[2025-10-07 22:55:33] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.202s
[2025-10-07 22:55:37] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.200s
[2025-10-07 22:55:41] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.218s
[2025-10-07 22:55:45] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.201s
[2025-10-07 22:55:49] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.216s
[2025-10-07 22:55:54] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.220s
[2025-10-07 22:55:58] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.210s
[2025-10-07 22:56:02] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.225s
[2025-10-07 22:56:06] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.204s
[2025-10-07 22:56:10] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.206s
[2025-10-07 22:56:15] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.206s
[2025-10-07 22:56:19] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.226s
[2025-10-07 22:56:23] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.199s
[2025-10-07 22:56:27] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.204s
[2025-10-07 22:56:31] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.200s
[2025-10-07 22:56:36] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.220s
[2025-10-07 22:56:40] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.205s
[2025-10-07 22:56:44] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.200s
[2025-10-07 22:56:48] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.219s
[2025-10-07 22:56:53] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.206s
[2025-10-07 22:56:53] 自旋相关函数计算完成,总耗时 300.93 秒
[2025-10-07 22:56:53] 计算傅里叶变换...
[2025-10-07 22:56:59] 自旋结构因子计算完成
[2025-10-07 22:57:00] 自旋相关函数平均误差: 0.000553
