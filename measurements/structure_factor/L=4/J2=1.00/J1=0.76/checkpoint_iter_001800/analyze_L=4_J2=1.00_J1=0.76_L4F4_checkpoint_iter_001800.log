[2025-10-06 23:18:49] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/checkpoint_iter_001800.pkl
[2025-10-06 23:19:05] ✓ 从checkpoint加载参数: 1800
[2025-10-06 23:19:05]   - 能量: -27.116221+0.000165j ± 0.007469
[2025-10-06 23:19:05] ================================================================================
[2025-10-06 23:19:05] 加载量子态: L=4, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_001800
[2025-10-06 23:19:05] 使用采样数目: 1048576
[2025-10-06 23:19:05] 设置样本数为: 1048576
[2025-10-06 23:19:05] 开始生成共享样本集...
[2025-10-06 23:20:27] 样本生成完成,耗时: 82.119 秒
[2025-10-06 23:20:27] ================================================================================
[2025-10-06 23:20:27] 开始计算自旋结构因子...
[2025-10-06 23:20:27] 初始化操作符缓存...
[2025-10-06 23:20:27] 预构建所有自旋相关操作符...
[2025-10-06 23:20:27] 开始计算自旋相关函数...
[2025-10-06 23:20:34] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.046s
[2025-10-06 23:20:43] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.414s
[2025-10-06 23:20:47] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.244s
[2025-10-06 23:20:51] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.268s
[2025-10-06 23:20:56] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.271s
[2025-10-06 23:21:00] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.263s
[2025-10-06 23:21:04] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.241s
[2025-10-06 23:21:08] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.286s
[2025-10-06 23:21:13] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.241s
[2025-10-06 23:21:17] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.288s
[2025-10-06 23:21:21] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.254s
[2025-10-06 23:21:25] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.272s
[2025-10-06 23:21:30] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.244s
[2025-10-06 23:21:34] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.264s
[2025-10-06 23:21:38] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.282s
[2025-10-06 23:21:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.251s
[2025-10-06 23:21:47] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.263s
[2025-10-06 23:21:51] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.287s
[2025-10-06 23:21:55] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.245s
[2025-10-06 23:22:00] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.287s
[2025-10-06 23:22:04] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.264s
[2025-10-06 23:22:08] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.276s
[2025-10-06 23:22:12] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.254s
[2025-10-06 23:22:17] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.265s
[2025-10-06 23:22:21] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.252s
[2025-10-06 23:22:25] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.264s
[2025-10-06 23:22:29] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.246s
[2025-10-06 23:22:34] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.295s
[2025-10-06 23:22:38] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.254s
[2025-10-06 23:22:42] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.251s
[2025-10-06 23:22:46] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.254s
[2025-10-06 23:22:51] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.264s
[2025-10-06 23:22:55] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.262s
[2025-10-06 23:22:59] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.263s
[2025-10-06 23:23:04] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.253s
[2025-10-06 23:23:08] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.264s
[2025-10-06 23:23:12] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.246s
[2025-10-06 23:23:16] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.264s
[2025-10-06 23:23:21] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.246s
[2025-10-06 23:23:25] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.276s
[2025-10-06 23:23:29] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.249s
[2025-10-06 23:23:33] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.253s
[2025-10-06 23:23:38] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.253s
[2025-10-06 23:23:42] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.265s
[2025-10-06 23:23:46] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.244s
[2025-10-06 23:23:50] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.244s
[2025-10-06 23:23:55] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.283s
[2025-10-06 23:23:59] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.245s
[2025-10-06 23:24:03] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.280s
[2025-10-06 23:24:07] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.286s
[2025-10-06 23:24:12] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.243s
[2025-10-06 23:24:16] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.287s
[2025-10-06 23:24:20] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.251s
[2025-10-06 23:24:25] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.264s
[2025-10-06 23:24:29] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.252s
[2025-10-06 23:24:33] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.288s
[2025-10-06 23:24:37] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.243s
[2025-10-06 23:24:42] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.243s
[2025-10-06 23:24:46] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.243s
[2025-10-06 23:24:50] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.286s
[2025-10-06 23:24:54] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.249s
[2025-10-06 23:24:59] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-10-06 23:25:03] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.284s
[2025-10-06 23:25:07] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.250s
[2025-10-06 23:25:07] 自旋相关函数计算完成,总耗时 279.87 秒
[2025-10-06 23:25:07] 计算傅里叶变换...
[2025-10-06 23:25:13] 自旋结构因子计算完成
[2025-10-06 23:25:14] 自旋相关函数平均误差: 0.000562
