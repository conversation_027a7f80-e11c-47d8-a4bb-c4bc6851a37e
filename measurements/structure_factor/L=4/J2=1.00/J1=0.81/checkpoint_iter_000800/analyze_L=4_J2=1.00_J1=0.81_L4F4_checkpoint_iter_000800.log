[2025-10-08 00:05:11] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.81/model_L4F4/training/checkpoints/checkpoint_iter_000800.pkl
[2025-10-08 00:05:27] ✓ 从checkpoint加载参数: 800
[2025-10-08 00:05:27]   - 能量: -29.181670+0.000801j ± 0.005107
[2025-10-08 00:05:27] ================================================================================
[2025-10-08 00:05:27] 加载量子态: L=4, J2=1.00, J1=0.81, checkpoint=checkpoint_iter_000800
[2025-10-08 00:05:27] 使用采样数目: 1048576
[2025-10-08 00:05:27] 设置样本数为: 1048576
[2025-10-08 00:05:27] 开始生成共享样本集...
[2025-10-08 00:06:48] 样本生成完成,耗时: 81.689 秒
[2025-10-08 00:06:48] ================================================================================
[2025-10-08 00:06:48] 开始计算自旋结构因子...
[2025-10-08 00:06:48] 初始化操作符缓存...
[2025-10-08 00:06:48] 预构建所有自旋相关操作符...
[2025-10-08 00:06:48] 开始计算自旋相关函数...
[2025-10-08 00:06:56] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.095s
[2025-10-08 00:07:04] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.350s
[2025-10-08 00:07:08] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.198s
[2025-10-08 00:07:12] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.234s
[2025-10-08 00:07:17] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.288s
[2025-10-08 00:07:22] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 5.288s
[2025-10-08 00:07:27] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 5.304s
[2025-10-08 00:07:32] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 5.192s
[2025-10-08 00:07:37] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.196s
[2025-10-08 00:07:41] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.240s
[2025-10-08 00:07:45] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.216s
[2025-10-08 00:07:50] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.405s
[2025-10-08 00:07:55] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.232s
[2025-10-08 00:08:00] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.356s
[2025-10-08 00:08:05] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.324s
[2025-10-08 00:08:10] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.410s
[2025-10-08 00:08:14] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.225s
[2025-10-08 00:08:18] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.238s
[2025-10-08 00:08:23] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.206s
[2025-10-08 00:08:27] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.239s
[2025-10-08 00:08:31] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.235s
[2025-10-08 00:08:35] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.234s
[2025-10-08 00:08:39] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.205s
[2025-10-08 00:08:44] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.554s
[2025-10-08 00:08:49] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.321s
[2025-10-08 00:08:55] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.352s
[2025-10-08 00:09:00] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.347s
[2025-10-08 00:09:05] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.372s
[2025-10-08 00:09:11] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.340s
[2025-10-08 00:09:16] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.329s
[2025-10-08 00:09:21] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.347s
[2025-10-08 00:09:27] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.343s
[2025-10-08 00:09:32] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.327s
[2025-10-08 00:09:36] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.254s
[2025-10-08 00:09:41] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.206s
[2025-10-08 00:09:45] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.225s
[2025-10-08 00:09:50] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.836s
[2025-10-08 00:09:55] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.340s
[2025-10-08 00:10:00] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 5.306s
[2025-10-08 00:10:06] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 5.303s
[2025-10-08 00:10:11] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 5.331s
[2025-10-08 00:10:16] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 5.331s
[2025-10-08 00:10:22] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.346s
[2025-10-08 00:10:27] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 5.406s
[2025-10-08 00:10:32] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 5.339s
[2025-10-08 00:10:38] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.333s
[2025-10-08 00:10:43] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.294s
[2025-10-08 00:10:48] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.507s
[2025-10-08 00:10:52] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.238s
[2025-10-08 00:10:56] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.685s
[2025-10-08 00:11:02] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 5.314s
[2025-10-08 00:11:07] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.279s
[2025-10-08 00:11:12] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.330s
[2025-10-08 00:11:18] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 5.332s
[2025-10-08 00:11:23] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 5.404s
[2025-10-08 00:11:28] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 5.308s
[2025-10-08 00:11:34] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 5.336s
[2025-10-08 00:11:39] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.332s
[2025-10-08 00:11:44] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.305s
[2025-10-08 00:11:50] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 5.287s
[2025-10-08 00:11:54] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.716s
[2025-10-08 00:11:59] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.198s
[2025-10-08 00:12:03] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.239s
[2025-10-08 00:12:08] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.060s
[2025-10-08 00:12:08] 自旋相关函数计算完成,总耗时 319.50 秒
[2025-10-08 00:12:08] 计算傅里叶变换...
[2025-10-08 00:12:14] 自旋结构因子计算完成
[2025-10-08 00:12:15] 自旋相关函数平均误差: 0.000556
