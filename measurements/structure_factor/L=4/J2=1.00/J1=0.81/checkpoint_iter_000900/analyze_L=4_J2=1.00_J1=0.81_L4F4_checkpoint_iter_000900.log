[2025-10-08 00:12:24] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.81/model_L4F4/training/checkpoints/checkpoint_iter_000900.pkl
[2025-10-08 00:12:40] ✓ 从checkpoint加载参数: 900
[2025-10-08 00:12:40]   - 能量: -29.180950-0.000424j ± 0.006400
[2025-10-08 00:12:40] ================================================================================
[2025-10-08 00:12:40] 加载量子态: L=4, J2=1.00, J1=0.81, checkpoint=checkpoint_iter_000900
[2025-10-08 00:12:40] 使用采样数目: 1048576
[2025-10-08 00:12:40] 设置样本数为: 1048576
[2025-10-08 00:12:40] 开始生成共享样本集...
[2025-10-08 00:14:16] 样本生成完成,耗时: 95.895 秒
[2025-10-08 00:14:16] ================================================================================
[2025-10-08 00:14:16] 开始计算自旋结构因子...
[2025-10-08 00:14:16] 初始化操作符缓存...
[2025-10-08 00:14:16] 预构建所有自旋相关操作符...
[2025-10-08 00:14:16] 开始计算自旋相关函数...
[2025-10-08 00:14:24] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.239s
[2025-10-08 00:14:33] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.481s
[2025-10-08 00:14:38] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 5.392s
[2025-10-08 00:14:44] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.335s
[2025-10-08 00:14:49] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 5.280s
[2025-10-08 00:14:54] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 5.294s
[2025-10-08 00:15:00] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 5.343s
[2025-10-08 00:15:05] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 5.276s
[2025-10-08 00:15:10] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 5.331s
[2025-10-08 00:15:16] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.372s
[2025-10-08 00:15:21] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.326s
[2025-10-08 00:15:25] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.346s
[2025-10-08 00:15:30] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.214s
[2025-10-08 00:15:34] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.247s
[2025-10-08 00:15:38] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.250s
[2025-10-08 00:15:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.226s
[2025-10-08 00:15:47] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.247s
[2025-10-08 00:15:51] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.259s
[2025-10-08 00:15:55] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.214s
[2025-10-08 00:15:59] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.256s
[2025-10-08 00:16:04] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.248s
[2025-10-08 00:16:08] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.250s
[2025-10-08 00:16:12] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.217s
[2025-10-08 00:16:16] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.247s
[2025-10-08 00:16:21] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.257s
[2025-10-08 00:16:26] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.328s
[2025-10-08 00:16:31] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.298s
[2025-10-08 00:16:36] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.282s
[2025-10-08 00:16:42] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.284s
[2025-10-08 00:16:47] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.349s
[2025-10-08 00:16:52] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.029s
[2025-10-08 00:16:56] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.246s
[2025-10-08 00:17:01] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.487s
[2025-10-08 00:17:06] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.279s
[2025-10-08 00:17:12] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.316s
[2025-10-08 00:17:17] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.346s
[2025-10-08 00:17:22] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 5.356s
[2025-10-08 00:17:28] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.289s
[2025-10-08 00:17:33] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 5.331s
[2025-10-08 00:17:38] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 5.289s
[2025-10-08 00:17:43] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 5.315s
[2025-10-08 00:17:49] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 5.285s
[2025-10-08 00:17:54] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.375s
[2025-10-08 00:17:59] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.829s
[2025-10-08 00:18:03] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.216s
[2025-10-08 00:18:07] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.214s
[2025-10-08 00:18:12] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.253s
[2025-10-08 00:18:16] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.214s
[2025-10-08 00:18:20] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.250s
[2025-10-08 00:18:24] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.252s
[2025-10-08 00:18:29] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.390s
[2025-10-08 00:18:34] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.194s
[2025-10-08 00:18:39] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.301s
[2025-10-08 00:18:45] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 5.299s
[2025-10-08 00:18:50] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 5.312s
[2025-10-08 00:18:55] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 5.341s
[2025-10-08 00:19:01] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 5.396s
[2025-10-08 00:19:06] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.322s
[2025-10-08 00:19:11] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.341s
[2025-10-08 00:19:17] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 5.305s
[2025-10-08 00:19:22] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 5.328s
[2025-10-08 00:19:27] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.269s
[2025-10-08 00:19:32] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.253s
[2025-10-08 00:19:36] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.229s
[2025-10-08 00:19:36] 自旋相关函数计算完成,总耗时 319.46 秒
[2025-10-08 00:19:36] 计算傅里叶变换...
[2025-10-08 00:19:42] 自旋结构因子计算完成
[2025-10-08 00:19:43] 自旋相关函数平均误差: 0.000546
