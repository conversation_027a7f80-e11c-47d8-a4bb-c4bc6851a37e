"""
Infidelity 分析模块

基于论文 "Fidelity approach to quantum phase transitions" 的理论，
通过计算相邻参数点之间波函数的 Infidelity 来判断相变点的位置。

Infidelity 定义为: I = 1 - |<ψ1|ψ2>|^2 / (<ψ1|ψ1><ψ2|ψ2>)

参考:
- NetKet Tutorial: https://netket.readthedocs.io/en/latest/tutorials/fidelity.html
- NetKet API: https://netket.readthedocs.io/en/latest/api/_generated/experimental/driver/netket.experimental.driver.Infidelity_SR.html

关键技术点：
使用 Infidelity_SR 时，两个变分态必须共享相同的模型实例和希尔伯特空间，
但可以有不同的参数。正确的做法是创建两个 MCState，它们使用相同的 model 和 hilbert。
"""

import os
import time
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import netket.experimental as nkx
from src.utils.logging import log_message


def calculate_infidelity_between_states(vqs1, vqs2, n_samples=None, log_file=None):
    """
    计算两个变分量子态之间的 Infidelity

    使用直接采样方法计算两个态之间的重叠（overlap）
    参考: NetKet 官方教程 https://netket.readthedocs.io/en/latest/tutorials/fidelity.html

    方法：直接计算 <ψ1|ψ2> 的重叠，然后计算 Infidelity = 1 - |<ψ1|ψ2>|^2

    Infidelity 定义为: I = 1 - |<ψ1|ψ2>|^2 / (<ψ1|ψ1><ψ2|ψ2>)

    参数:
    vqs1: 第一个变分量子态（包含参数 params1）
    vqs2: 第二个变分量子态（包含参数 params2，必须与 vqs1 共享相同的模型结构）
    n_samples: 采样数量（如果为None，使用默认采样数）
    log_file: 日志文件路径

    返回:
    infidelity_stats: 包含 Infidelity 统计信息的字典
        - 'mean': Infidelity 的平均值
        - 'error': Infidelity 的误差
    """
    start_time = time.time()

    if log_file:
        log_message(log_file, "开始计算 Infidelity...")

    # 如果指定了采样数，更新采样器
    if n_samples is not None:
        original_n_samples_1 = vqs1.n_samples
        original_n_samples_2 = vqs2.n_samples
        vqs1.n_samples = n_samples
        vqs2.n_samples = n_samples

    # 保存原始参数
    params1 = vqs1.parameters
    params2 = vqs2.parameters

    # 方法1: 使用 log_overlap 直接计算重叠
    # 从 vqs1 的样本上计算 log(<ψ1|σ>) 和 log(<ψ2|σ>)
    try:
        # 重新采样
        vqs1.reset()
        
        # 获取 vqs1 的样本
        samples = vqs1.samples
        
        # 计算 log(<ψ1|samples>)
        vqs1.parameters = params1
        log_psi1 = vqs1.log_value(samples)
        
        # 计算 log(<ψ2|samples>)
        vqs2.parameters = params2
        log_psi2 = vqs2.log_value(samples)
        
        # 计算 log(overlap) = log(<ψ1*|ψ2>)
        # <ψ1|ψ2> ≈ (1/N) Σ_σ psi1*(σ) * psi2(σ) / psi1(σ)
        #          = (1/N) Σ_σ exp(log_psi2(σ) - log_psi1(σ)) * conj(exp(log_psi1(σ)))
        #          = (1/N) Σ_σ exp(log_psi2(σ)) * conj(exp(log_psi1(σ)))
        
        # 计算相位差
        log_ratio = log_psi2 - log_psi1
        
        # 使用 JAX 计算统计量
        overlap_samples = jnp.exp(log_ratio)
        
        # 计算均值和标准差
        overlap_mean = jnp.mean(overlap_samples)
        overlap_std = jnp.std(overlap_samples) / jnp.sqrt(len(overlap_samples))
        
        # 计算 Fidelity = |<ψ1|ψ2>|^2
        fidelity_mean = jnp.abs(overlap_mean)**2
        
        # Infidelity = 1 - Fidelity
        infidelity_mean = 1.0 - fidelity_mean
        
        # 误差传播: dI = -d|overlap|^2 = -2|overlap|*d|overlap|
        infidelity_error = 2.0 * jnp.abs(overlap_mean) * overlap_std
        
        # 转换为标量
        infidelity_mean = float(infidelity_mean)
        infidelity_error = float(infidelity_error)
        
    except Exception as e:
        if log_file:
            log_message(log_file, f"计算 Infidelity 时出错: {str(e)}")
        raise

    # 恢复原始参数和采样数
    vqs1.parameters = params1
    vqs2.parameters = params2
    if n_samples is not None:
        vqs1.n_samples = original_n_samples_1
        vqs2.n_samples = original_n_samples_2

    end_time = time.time()

    if log_file:
        log_message(log_file, f"Infidelity 计算完成，耗时 {end_time - start_time:.2f} 秒")
        log_message(log_file, f"  Infidelity = {infidelity_mean:.8f} ± {infidelity_error:.8f}")
        log_message(log_file, f"  Fidelity = {1.0 - infidelity_mean:.8f}")

    return {
        'mean': infidelity_mean,
        'error': infidelity_error,
        'fidelity': 1.0 - infidelity_mean,
    }


def calculate_infidelity_scan(parameter_values, load_params_func, model, hilbert,
                               n_samples=None, save_dir=None, log_file=None):
    """
    扫描一系列参数值，计算相邻参数点之间的 Infidelity
    
    关键：使用 Infidelity_SR 时，两个 MCState 必须共享相同的 model 和 hilbert 实例！
    
    参数:
    parameter_values: 参数值列表（例如 J1 值）
    load_params_func: 加载模型参数的函数，接受参数值作为输入，返回参数字典
                      函数签名: load_params_func(param_value) -> parameters
    model: 共享的模型实例
    hilbert: 共享的希尔伯特空间
    n_samples: 采样数量
    save_dir: 保存结果的目录
    log_file: 日志文件路径
    
    返回:
    results: 包含所有 Infidelity 计算结果的字典
        - 'parameter_pairs': 参数对列表 [(p1, p2), ...]
        - 'infidelities': Infidelity 值列表
        - 'errors': Infidelity 误差列表
        - 'fidelities': Fidelity 值列表
    """
    if log_file:
        log_message(log_file, "="*80)
        log_message(log_file, "开始 Infidelity 扫描分析")
        log_message(log_file, f"参数值: {parameter_values}")
        log_message(log_file, f"采样数: {n_samples}")
    
    parameter_pairs = []
    infidelities = []
    errors = []
    fidelities = []
    
    # 遍历相邻参数对
    for i in range(len(parameter_values) - 1):
        param1 = parameter_values[i]
        param2 = parameter_values[i + 1]
        
        if log_file:
            log_message(log_file, "="*80)
            log_message(log_file, f"计算参数对 ({param1:.4f}, {param2:.4f}) 之间的 Infidelity")
        
        # 加载参数并计算 infidelity
        try:
            # 加载两组参数
            params1 = load_params_func(param1)
            params2 = load_params_func(param2)
            
            # 创建两个MCState，共享相同的 model 和 hilbert
            # 这是使用 Infidelity_SR 的关键！
            # 注意：sampler 需要为每个状态创建独立的实例（包含独立的随机数生成器）
            from src.physics.shastry_sutherland import shastry_sutherland_lattice
            import numpy as np
            
            # 重新创建晶格以获取距离信息（用于采样器）
            # 注意：这里需要知道 L 值，我们从 hilbert 推断
            N = hilbert.size
            L = int(np.sqrt(N / 4))  # Shastry-Sutherland 晶格：N = 4*L^2
            lattice = shastry_sutherland_lattice(L, L)
            distance = lattice.distances()
            max_distance = np.max(distance)
            
            # 为每个状态创建独立的采样器
            sampler1 = nk.sampler.MetropolisExchange(
                hilbert=hilbert,
                graph=lattice,
                n_chains=n_samples if n_samples else 2**12,
                d_max=max_distance
            )
            
            sampler2 = nk.sampler.MetropolisExchange(
                hilbert=hilbert,
                graph=lattice,
                n_chains=n_samples if n_samples else 2**12,
                d_max=max_distance
            )
            
            vqs1 = nk.vqs.MCState(
                sampler=sampler1,
                model=model,  # 共享同一个模型实例
                n_samples=n_samples if n_samples else 2**12,
                n_discard_per_chain=0,
            )
            vqs1.parameters = params1
            
            vqs2 = nk.vqs.MCState(
                sampler=sampler2,
                model=model,  # 共享同一个模型实例
                n_samples=n_samples if n_samples else 2**12,
                n_discard_per_chain=0,
            )
            vqs2.parameters = params2
            
            # 使用 Infidelity_SR 计算
            optimizer = nk.optimizer.Sgd(learning_rate=0.0)
            driver = nkx.driver.Infidelity_SR(
                target_state=vqs1,
                optimizer=optimizer,
                diag_shift=1e-4,
                variational_state=vqs2,
                operator=None,
            )
            
            # 运行一步获取 Infidelity
            logger = nk.logging.RuntimeLog()
            driver.run(n_iter=1, out=logger)
            
            # 提取结果
            infidelity_mean = float(logger.data["Infidelity"]["Mean"][0])
            infidelity_error = float(logger.data["Infidelity"]["Sigma"][0])
            fidelity_val = 1.0 - infidelity_mean
            
            # 记录结果
            if log_file:
                log_message(log_file, f"  Infidelity = {infidelity_mean:.8f} ± {infidelity_error:.8f}")
                log_message(log_file, f"  Fidelity = {fidelity_val:.8f}")
            
            parameter_pairs.append((param1, param2))
            infidelities.append(infidelity_mean)
            errors.append(infidelity_error)
            fidelities.append(fidelity_val)
            
        except Exception as e:
            if log_file:
                import traceback
                log_message(log_file, f"警告: 计算参数对 ({param1:.4f}, {param2:.4f}) 时出错: {str(e)}")
                log_message(log_file, traceback.format_exc())
            continue
    
    # 构建结果字典
    results = {
        'parameter_pairs': parameter_pairs,
        'parameter_values': parameter_values,
        'infidelities': np.array(infidelities),
        'errors': np.array(errors),
        'fidelities': np.array(fidelities),
        'n_samples': n_samples
    }
    
    # 保存结果
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        save_path = os.path.join(save_dir, "infidelity_scan_data.npy")
        np.save(save_path, results)
        if log_file:
            log_message(log_file, f"结果已保存到: {save_path}")
    
    if log_file:
        log_message(log_file, "="*80)
        log_message(log_file, "Infidelity 扫描分析完成")
        log_message(log_file, f"成功计算 {len(infidelities)} 个参数对")
    
    return results


def plot_infidelity_scan(results, save_dir=None, log_file=None):
    """
    绘制 Infidelity 扫描结果
    
    绘制两种图：
    1. 线性坐标图（原始数据）
    2. 对数坐标图（更清楚地显示所有数据点）

    参数:
    results: calculate_infidelity_scan 返回的结果字典
    save_dir: 保存图像的目录
    log_file: 日志文件路径
    """
    try:
        import matplotlib.pyplot as plt
    except ImportError:
        if log_file:
            log_message(log_file, "警告: 无法导入 matplotlib，跳过绘图")
        return

    parameter_pairs = results['parameter_pairs']
    infidelities = results['infidelities']
    errors = results['errors']

    # 使用参数对的第二个值作为 x 轴位置
    # 即 (0.76, 0.77) 的 infidelity 画在 x=0.77 处
    x_values = [p2 for p1, p2 in parameter_pairs]

    # 创建两个子图：线性和对数
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 子图1：线性坐标
    ax1.errorbar(x_values, infidelities, yerr=errors,
                fmt='o-', capsize=5, capthick=2,
                label='Infidelity', color='blue', markersize=8)
    ax1.set_xlabel('J1 Parameter', fontsize=14)
    ax1.set_ylabel('Infidelity', fontsize=14)
    ax1.set_title('Infidelity vs J1 (Linear Scale)', fontsize=16)
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=12)

    # 子图2：对数坐标
    ax2.errorbar(x_values, infidelities, yerr=errors,
                fmt='o-', capsize=5, capthick=2,
                label='Infidelity', color='red', markersize=8)
    ax2.set_xlabel('J1 Parameter', fontsize=14)
    ax2.set_ylabel('Infidelity (log scale)', fontsize=14)
    ax2.set_title('Infidelity vs J1 (Log Scale)', fontsize=16)
    ax2.set_yscale('log')
    ax2.grid(True, alpha=0.3, which='both')
    ax2.legend(fontsize=12)

    plt.tight_layout()

    # 保存组合图
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        save_path = os.path.join(save_dir, "infidelity_scan.png")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        if log_file:
            log_message(log_file, f"图像已保存到: {save_path}")

    plt.close()
    
    # 额外绘制一个单独的对数坐标图（更大更清晰）
    fig2, ax = plt.subplots(figsize=(10, 6))
    ax.errorbar(x_values, infidelities, yerr=errors,
                fmt='o-', capsize=5, capthick=2,
                label='Infidelity', color='darkgreen', markersize=10, linewidth=2)
    ax.set_xlabel('J1 Parameter', fontsize=14)
    ax.set_ylabel('Infidelity (log scale)', fontsize=14)
    ax.set_title('Infidelity vs J1 Parameter (Log Scale)', fontsize=16)
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3, which='both')
    ax.legend(fontsize=12)
    
    plt.tight_layout()
    
    if save_dir:
        save_path_log = os.path.join(save_dir, "infidelity_scan_log.png")
        plt.savefig(save_path_log, dpi=300, bbox_inches='tight')
        if log_file:
            log_message(log_file, f"对数坐标图已保存到: {save_path_log}")
    
    plt.close()

