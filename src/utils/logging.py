import datetime
import pytz

def log_message(log_file, message):
    """记录带时间戳的消息（新加坡时间 UTC+8）"""
    # 获取新加坡时区
    singapore_tz = pytz.timezone('Asia/Singapore')
    # 获取当前新加坡时间
    singapore_time = datetime.datetime.now(singapore_tz)
    timestamp = singapore_time.strftime("[%Y-%m-%d %H:%M:%S]")
    log_line = f"{timestamp} {message}"
    with open(log_file, "a") as f:
        f.write(log_line + "\n")
    print(log_line)
