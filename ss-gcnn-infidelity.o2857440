==================== NQS Infidelity 分析任务 ====================
Job start at: Sun Nov  2 19:51:03 +08 2025
Running on node: hpc-pinaki-gpu2
Working directory: /home/<USER>/Repositories/Shastry-Sutherland_Extra
GPU Information:
Sun Nov  2 19:51:03 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 580.65.06              Driver Version: 580.65.06      CUDA Version: 13.0     |
+-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA H200 NVL                On  |   00000000:B0:00.0 Off |                    0 |
| N/A   35C    P0             70W /  600W |       0MiB / 143771MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+

+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI              PID   Type   Process name                        GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+
读取配置文件: workflows/configs/infidelity_measurement.yaml
Loading modules...
Anaconda 2025 python3 module loaded.

Please run the following commands (include the quote): eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"

Loading anaconda2025/2025
  Loading requirement: cuda/12.2
Using GPU device: 0
Python path: /home/<USER>/.conda/envs/netket/bin/python
Python version: Python 3.12.11
Current conda environment: netket
==================== 开始 Infidelity 分析任务 ====================
扫描配置数量: 1
模型配置: 4,4
Checkpoint: 
采样数: 8192
==================== 扫描配置 ====================
L=6, J2=1.00
J1 值: 0.76 0.77 0.78 0.79 0.8 0.81 0.82 0.83 0.84
==================== 模型: Layers=4, Features=4 ====================
运行 Infidelity 分析脚本...
将自动为每个J1值查找最新的checkpoint
[2025-11-02 19:51:20] ================================================================================
[2025-11-02 19:51:20] Infidelity 分析
[2025-11-02 19:51:20] ================================================================================
[2025-11-02 19:51:20] 晶格大小: L=6
[2025-11-02 19:51:20] J2 耦合强度: 1.00
[2025-11-02 19:51:20] J1 值列表: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-11-02 19:51:20] 模型配置: 4 层, 4 特征
[2025-11-02 19:51:20] Checkpoint: 自动查找最新
[2025-11-02 19:51:20] 采样数: 8192
[2025-11-02 19:51:20] ================================================================================
[2025-11-02 19:51:20] 创建共享的模型用于 Infidelity 计算...
[2025-11-02 19:52:01] ✓ 共享模型创建完成
[2025-11-02 19:52:01]   - 模型类型: <class 'netket.models.equivariant.GCNN_Parity_Irrep'>
[2025-11-02 19:52:01]   - Hilbert 空间: Spin(s=1/2, N=144, ordering=new, total_sz=0)
[2025-11-02 19:52:01] ================================================================================
[2025-11-02 19:52:01] 开始 Infidelity 扫描分析
[2025-11-02 19:52:01] 参数值: [0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.82, 0.83, 0.84]
[2025-11-02 19:52:01] 采样数: 8192
[2025-11-02 19:52:01] ================================================================================
[2025-11-02 19:52:01] 计算参数对 (0.7600, 0.7700) 之间的 Infidelity
[2025-11-02 19:52:01] J1=0.76 自动选择checkpoint: checkpoint_iter_002250
[2025-11-02 19:52:01] 加载 J1=0.76 的参数: saved_models/L=6/J2=1.00/J1=0.76/checkpoints/checkpoint_iter_002250.pkl
[2025-11-02 19:52:01]   ✓ 加载参数: 2250
[2025-11-02 19:52:01]     - 能量: -60.903988+0.003258j ± 0.008471
[2025-11-02 19:52:01] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:52:01] 加载 J1=0.77 的参数: saved_models/L=6/J2=1.00/J1=0.77/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:52:01]   ✓ 加载参数: 1050
[2025-11-02 19:52:01]     - 能量: -61.819004-0.001621j ± 0.012340
Automatic SR implementation choice:  NTK

  0%|          | 0/1 [00:00<?, ?it/s]
  0%|          | 0/1 [00:29<?, ?it/s, Infidelity=0.0285 ± 0.0011 [σ²=1.2e-02]]
100%|██████████| 1/1 [00:00<00:00, 13.54it/s, Infidelity=0.0285 ± 0.0011 [σ²=1.2e-02]]
[2025-11-02 19:53:06]   Infidelity = 0.02850438 ± 0.00111347
[2025-11-02 19:53:06]   Fidelity = 0.97149562
[2025-11-02 19:53:06] ================================================================================
[2025-11-02 19:53:06] 计算参数对 (0.7700, 0.7800) 之间的 Infidelity
[2025-11-02 19:53:06] J1=0.77 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:53:06] 加载 J1=0.77 的参数: saved_models/L=6/J2=1.00/J1=0.77/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:53:06]   ✓ 加载参数: 1050
[2025-11-02 19:53:06]     - 能量: -61.819004-0.001621j ± 0.012340
[2025-11-02 19:53:06] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:53:06] 加载 J1=0.78 的参数: saved_models/L=6/J2=1.00/J1=0.78/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:53:06]   ✓ 加载参数: 1050
[2025-11-02 19:53:06]     - 能量: -62.735420-0.003897j ± 0.011241
Automatic SR implementation choice:  NTK

  0%|          | 0/1 [00:00<?, ?it/s]
  0%|          | 0/1 [00:09<?, ?it/s, Infidelity=0.0302 ± 0.0011 [σ²=1.1e-02]]
100%|██████████| 1/1 [00:00<00:00,  8.52it/s, Infidelity=0.0302 ± 0.0011 [σ²=1.1e-02]]
100%|██████████| 1/1 [00:00<00:00,  8.50it/s, Infidelity=0.0302 ± 0.0011 [σ²=1.1e-02]]
[2025-11-02 19:53:47]   Infidelity = 0.03018113 ± 0.00106237
[2025-11-02 19:53:47]   Fidelity = 0.96981887
[2025-11-02 19:53:47] ================================================================================
[2025-11-02 19:53:47] 计算参数对 (0.7800, 0.7900) 之间的 Infidelity
[2025-11-02 19:53:47] J1=0.78 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:53:47] 加载 J1=0.78 的参数: saved_models/L=6/J2=1.00/J1=0.78/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:53:47]   ✓ 加载参数: 1050
[2025-11-02 19:53:47]     - 能量: -62.735420-0.003897j ± 0.011241
[2025-11-02 19:53:47] J1=0.79 自动选择checkpoint: checkpoint_iter_001050
[2025-11-02 19:53:47] 加载 J1=0.79 的参数: saved_models/L=6/J2=1.00/J1=0.79/checkpoints/checkpoint_iter_001050.pkl
[2025-11-02 19:53:47]   ✓ 加载参数: 1050
[2025-11-02 19:53:47]     - 能量: -63.660900+0.000285j ± 0.010408
Automatic SR implementation choice:  NTK

  0%|          | 0/1 [00:00<?, ?it/s]~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-11-02 19:54:33.832140:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 2857440.hpc-pbs-sched1
	Project: gs_spms_psengupta
	Exit Status: 271
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(1), Used(1)
	CPU Time Used: 00:01:59
	Memory: Requested(75gb), Used(1250048kb)
	Vmem Used: 1250048kb
	Walltime: Requested(1440:00:00), Used(00:03:36)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (hpc-pinaki-gpu2:ngpus=1:mem=78643200kb:ncpus=1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	No GPU-related information available for this job.
