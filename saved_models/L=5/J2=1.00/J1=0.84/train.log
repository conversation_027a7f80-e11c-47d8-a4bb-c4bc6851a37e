[2025-10-06 16:45:34] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.83/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 16:45:34]   - 迭代次数: final
[2025-10-06 16:45:34]   - 能量: -46.804904-0.000842j ± 0.006793, Var: 0.189029
[2025-10-06 16:45:34]   - 时间戳: 2025-10-06T16:45:19.553051+08:00
[2025-10-06 16:45:54] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 16:45:54] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 16:45:54] ======================================================================================================
[2025-10-06 16:45:54] GCNN for Shastry-Sutherland Model
[2025-10-06 16:45:54] ======================================================================================================
[2025-10-06 16:45:54] System parameters:
[2025-10-06 16:45:54]   - System size: L=5, N=100
[2025-10-06 16:45:54]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-10-06 16:45:54] ------------------------------------------------------------------------------------------------------
[2025-10-06 16:45:54] Model parameters:
[2025-10-06 16:45:54]   - Number of layers = 4
[2025-10-06 16:45:54]   - Number of features = 4
[2025-10-06 16:45:54]   - Total parameters = 19628
[2025-10-06 16:45:54] ------------------------------------------------------------------------------------------------------
[2025-10-06 16:45:54] Training parameters:
[2025-10-06 16:45:54]   - Total iterations: 1050
[2025-10-06 16:45:54]   - Annealing cycles: 3
[2025-10-06 16:45:54]   - Initial period: 150
[2025-10-06 16:45:54]   - Period multiplier: 2.0
[2025-10-06 16:45:54]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 16:45:54]   - Samples: 4096
[2025-10-06 16:45:54]   - Discarded samples: 0
[2025-10-06 16:45:54]   - Chunk size: 4096
[2025-10-06 16:45:54]   - Diagonal shift: 0.15
[2025-10-06 16:45:54]   - Gradient clipping: 1.0
[2025-10-06 16:45:54]   - Checkpoint enabled: interval=100
[2025-10-06 16:45:54]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.84/model_L4F4/training/checkpoints
[2025-10-06 16:45:54] ------------------------------------------------------------------------------------------------------
[2025-10-06 16:45:54] Device status:
[2025-10-06 16:45:54]   - Devices model: NVIDIA H200 NVL
[2025-10-06 16:45:54]   - Number of devices: 1
[2025-10-06 16:45:54]   - Sharding: True
[2025-10-06 16:45:54] ======================================================================================================
[2025-10-06 16:46:25] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -47.433654 | E_var:     0.5756 | E_err:   0.011854
[2025-10-06 16:46:45] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -47.464502 | E_var:     0.3255 | E_err:   0.008914
[2025-10-06 16:46:50] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -47.453201 | E_var:     0.2733 | E_err:   0.008168
[2025-10-06 16:46:55] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -47.472002 | E_var:     0.2889 | E_err:   0.008398
[2025-10-06 16:47:00] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -47.448907 | E_var:     0.2722 | E_err:   0.008152
[2025-10-06 16:47:05] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -47.444549 | E_var:     0.2558 | E_err:   0.007902
[2025-10-06 16:47:11] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -47.475444 | E_var:     0.2068 | E_err:   0.007105
[2025-10-06 16:47:16] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -47.452569 | E_var:     0.2346 | E_err:   0.007568
[2025-10-06 16:47:21] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -47.469829 | E_var:     0.2040 | E_err:   0.007057
[2025-10-06 16:47:26] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -47.460956 | E_var:     0.2528 | E_err:   0.007856
[2025-10-06 16:47:31] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -47.452777 | E_var:     0.1984 | E_err:   0.006960
[2025-10-06 16:47:36] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -47.450845 | E_var:     0.1624 | E_err:   0.006297
[2025-10-06 16:47:41] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -47.454248 | E_var:     0.1555 | E_err:   0.006161
[2025-10-06 16:47:46] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -47.457407 | E_var:     0.1658 | E_err:   0.006362
[2025-10-06 16:47:52] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -47.453389 | E_var:     0.2031 | E_err:   0.007042
[2025-10-06 16:47:57] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -47.460902 | E_var:     0.2697 | E_err:   0.008114
[2025-10-06 16:48:02] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -47.461007 | E_var:     0.1765 | E_err:   0.006565
[2025-10-06 16:48:07] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -47.449808 | E_var:     0.1630 | E_err:   0.006309
[2025-10-06 16:48:12] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -47.462522 | E_var:     0.1593 | E_err:   0.006236
[2025-10-06 16:48:17] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -47.454959 | E_var:     0.1762 | E_err:   0.006559
[2025-10-06 16:48:22] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -47.464654 | E_var:     0.1296 | E_err:   0.005624
[2025-10-06 16:48:27] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -47.465202 | E_var:     0.1441 | E_err:   0.005932
[2025-10-06 16:48:33] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -47.459622 | E_var:     0.1644 | E_err:   0.006335
[2025-10-06 16:48:38] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -47.469711 | E_var:     0.1681 | E_err:   0.006407
[2025-10-06 16:48:43] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -47.454220 | E_var:     0.2551 | E_err:   0.007891
[2025-10-06 16:48:48] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -47.453136 | E_var:     0.1428 | E_err:   0.005905
[2025-10-06 16:48:53] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -47.454398 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 16:48:58] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -47.449806 | E_var:     0.1863 | E_err:   0.006743
[2025-10-06 16:49:03] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -47.452089 | E_var:     0.1763 | E_err:   0.006561
[2025-10-06 16:49:08] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -47.462734 | E_var:     0.1486 | E_err:   0.006024
[2025-10-06 16:49:14] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -47.453518 | E_var:     0.1760 | E_err:   0.006556
[2025-10-06 16:49:19] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -47.447281 | E_var:     0.2016 | E_err:   0.007015
[2025-10-06 16:49:24] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -47.457664 | E_var:     0.1885 | E_err:   0.006783
[2025-10-06 16:49:29] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -47.455169 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 16:49:34] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -47.450343 | E_var:     0.2118 | E_err:   0.007190
[2025-10-06 16:49:39] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -47.460499 | E_var:     0.1651 | E_err:   0.006349
[2025-10-06 16:49:44] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -47.460408 | E_var:     0.1733 | E_err:   0.006504
[2025-10-06 16:49:49] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -47.460719 | E_var:     0.2690 | E_err:   0.008104
[2025-10-06 16:49:55] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -47.467906 | E_var:     0.2985 | E_err:   0.008537
[2025-10-06 16:50:00] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -47.461802 | E_var:     0.1937 | E_err:   0.006877
[2025-10-06 16:50:05] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -47.454961 | E_var:     0.1648 | E_err:   0.006343
[2025-10-06 16:50:10] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -47.457154 | E_var:     0.2737 | E_err:   0.008174
[2025-10-06 16:50:15] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -47.451769 | E_var:     0.1570 | E_err:   0.006191
[2025-10-06 16:50:20] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -47.468682 | E_var:     0.1290 | E_err:   0.005612
[2025-10-06 16:50:25] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -47.449149 | E_var:     0.1636 | E_err:   0.006320
[2025-10-06 16:50:30] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -47.459569 | E_var:     0.1705 | E_err:   0.006451
[2025-10-06 16:50:36] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -47.454280 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 16:50:41] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -47.454059 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 16:50:46] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -47.451426 | E_var:     0.1818 | E_err:   0.006661
[2025-10-06 16:50:51] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -47.459096 | E_var:     0.1724 | E_err:   0.006488
[2025-10-06 16:50:56] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -47.448791 | E_var:     0.2092 | E_err:   0.007146
[2025-10-06 16:51:01] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -47.461038 | E_var:     0.2160 | E_err:   0.007262
[2025-10-06 16:51:06] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -47.459246 | E_var:     0.2088 | E_err:   0.007139
[2025-10-06 16:51:11] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -47.460522 | E_var:     0.2085 | E_err:   0.007135
[2025-10-06 16:51:17] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -47.457126 | E_var:     0.1877 | E_err:   0.006769
[2025-10-06 16:51:22] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -47.454470 | E_var:     0.1534 | E_err:   0.006119
[2025-10-06 16:51:27] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -47.454274 | E_var:     0.1633 | E_err:   0.006315
[2025-10-06 16:51:32] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -47.454404 | E_var:     0.1951 | E_err:   0.006902
[2025-10-06 16:51:37] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -47.457341 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 16:51:42] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -47.445031 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 16:51:47] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -47.458143 | E_var:     0.1885 | E_err:   0.006784
[2025-10-06 16:51:52] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -47.453492 | E_var:     0.2030 | E_err:   0.007040
[2025-10-06 16:51:57] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -47.459963 | E_var:     0.1727 | E_err:   0.006492
[2025-10-06 16:52:03] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -47.448033 | E_var:     0.1872 | E_err:   0.006760
[2025-10-06 16:52:08] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -47.449654 | E_var:     0.1480 | E_err:   0.006011
[2025-10-06 16:52:13] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -47.446921 | E_var:     0.4541 | E_err:   0.010529
[2025-10-06 16:52:18] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -47.469879 | E_var:     0.4624 | E_err:   0.010625
[2025-10-06 16:52:23] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -47.465886 | E_var:     0.1329 | E_err:   0.005695
[2025-10-06 16:52:28] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -47.458301 | E_var:     0.1859 | E_err:   0.006737
[2025-10-06 16:52:33] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -47.455486 | E_var:     0.1449 | E_err:   0.005948
[2025-10-06 16:52:38] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -47.458886 | E_var:     0.1948 | E_err:   0.006897
[2025-10-06 16:52:44] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -47.446463 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 16:52:49] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -47.464033 | E_var:     0.1625 | E_err:   0.006298
[2025-10-06 16:52:54] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -47.456227 | E_var:     0.1738 | E_err:   0.006514
[2025-10-06 16:52:59] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -47.465011 | E_var:     0.1728 | E_err:   0.006496
[2025-10-06 16:53:04] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -47.460674 | E_var:     0.1638 | E_err:   0.006323
[2025-10-06 16:53:09] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -47.455631 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 16:53:14] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -47.449590 | E_var:     0.1927 | E_err:   0.006859
[2025-10-06 16:53:19] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -47.436168 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 16:53:25] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -47.450296 | E_var:     0.1421 | E_err:   0.005890
[2025-10-06 16:53:30] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -47.457226 | E_var:     0.2209 | E_err:   0.007344
[2025-10-06 16:53:35] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -47.453714 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 16:53:40] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -47.464735 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 16:53:45] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -47.461157 | E_var:     0.1772 | E_err:   0.006578
[2025-10-06 16:53:50] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -47.458189 | E_var:     0.1335 | E_err:   0.005710
[2025-10-06 16:53:55] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -47.462071 | E_var:     0.1802 | E_err:   0.006632
[2025-10-06 16:54:00] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -47.443754 | E_var:     0.1424 | E_err:   0.005896
[2025-10-06 16:54:06] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -47.447113 | E_var:     0.2023 | E_err:   0.007029
[2025-10-06 16:54:11] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -47.464519 | E_var:     0.2699 | E_err:   0.008117
[2025-10-06 16:54:16] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -47.455396 | E_var:     0.1906 | E_err:   0.006821
[2025-10-06 16:54:21] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -47.441882 | E_var:     0.2017 | E_err:   0.007018
[2025-10-06 16:54:26] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -47.452219 | E_var:     0.1854 | E_err:   0.006727
[2025-10-06 16:54:31] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -47.454725 | E_var:     0.1840 | E_err:   0.006702
[2025-10-06 16:54:36] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -47.453812 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 16:54:41] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -47.446997 | E_var:     0.1485 | E_err:   0.006021
[2025-10-06 16:54:47] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -47.459813 | E_var:     0.1725 | E_err:   0.006489
[2025-10-06 16:54:52] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -47.463816 | E_var:     0.1610 | E_err:   0.006269
[2025-10-06 16:54:57] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -47.448317 | E_var:     0.1586 | E_err:   0.006223
[2025-10-06 16:55:02] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -47.456081 | E_var:     0.1511 | E_err:   0.006074
[2025-10-06 16:55:07] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -47.466278 | E_var:     0.4176 | E_err:   0.010097
[2025-10-06 16:55:07] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 16:55:12] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -47.446801 | E_var:     0.1689 | E_err:   0.006421
[2025-10-06 16:55:17] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -47.457748 | E_var:     0.2550 | E_err:   0.007890
[2025-10-06 16:55:22] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -47.456257 | E_var:     0.1500 | E_err:   0.006052
[2025-10-06 16:55:28] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -47.450574 | E_var:     0.1809 | E_err:   0.006646
[2025-10-06 16:55:33] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -47.461613 | E_var:     0.2118 | E_err:   0.007191
[2025-10-06 16:55:38] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -47.453046 | E_var:     0.1706 | E_err:   0.006454
[2025-10-06 16:55:43] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -47.445997 | E_var:     0.1702 | E_err:   0.006446
[2025-10-06 16:55:48] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -47.466468 | E_var:     0.1387 | E_err:   0.005820
[2025-10-06 16:55:53] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -47.446865 | E_var:     0.1862 | E_err:   0.006742
[2025-10-06 16:55:58] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -47.463115 | E_var:     0.2130 | E_err:   0.007210
[2025-10-06 16:56:03] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -47.457709 | E_var:     0.1786 | E_err:   0.006603
[2025-10-06 16:56:08] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -47.457416 | E_var:     0.2131 | E_err:   0.007213
[2025-10-06 16:56:14] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -47.460943 | E_var:     0.1639 | E_err:   0.006325
[2025-10-06 16:56:19] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -47.446371 | E_var:     0.1662 | E_err:   0.006371
[2025-10-06 16:56:24] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -47.465765 | E_var:     0.1603 | E_err:   0.006255
[2025-10-06 16:56:29] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -47.458878 | E_var:     0.1747 | E_err:   0.006530
[2025-10-06 16:56:34] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -47.443648 | E_var:     0.2134 | E_err:   0.007218
[2025-10-06 16:56:39] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -47.458504 | E_var:     0.2465 | E_err:   0.007757
[2025-10-06 16:56:44] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -47.447103 | E_var:     0.1691 | E_err:   0.006426
[2025-10-06 16:56:49] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -47.444161 | E_var:     0.3331 | E_err:   0.009018
[2025-10-06 16:56:55] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -47.446770 | E_var:     0.2459 | E_err:   0.007749
[2025-10-06 16:57:00] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -47.455771 | E_var:     0.1362 | E_err:   0.005767
[2025-10-06 16:57:05] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -47.462227 | E_var:     0.2068 | E_err:   0.007106
[2025-10-06 16:57:10] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -47.458128 | E_var:     0.2047 | E_err:   0.007069
[2025-10-06 16:57:15] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -47.461859 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 16:57:20] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -47.470526 | E_var:     0.1708 | E_err:   0.006457
[2025-10-06 16:57:25] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -47.454558 | E_var:     0.1697 | E_err:   0.006436
[2025-10-06 16:57:30] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -47.449894 | E_var:     0.1713 | E_err:   0.006467
[2025-10-06 16:57:36] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -47.448739 | E_var:     0.2342 | E_err:   0.007561
[2025-10-06 16:57:41] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -47.461234 | E_var:     0.1858 | E_err:   0.006735
[2025-10-06 16:57:46] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -47.448805 | E_var:     0.2179 | E_err:   0.007294
[2025-10-06 16:57:51] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -47.463472 | E_var:     0.1588 | E_err:   0.006227
[2025-10-06 16:57:56] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -47.446587 | E_var:     0.1868 | E_err:   0.006752
[2025-10-06 16:58:01] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -47.453451 | E_var:     0.1525 | E_err:   0.006102
[2025-10-06 16:58:06] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -47.453519 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 16:58:11] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -47.464859 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 16:58:17] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -47.455724 | E_var:     0.1950 | E_err:   0.006899
[2025-10-06 16:58:22] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -47.458726 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 16:58:27] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -47.458703 | E_var:     0.2064 | E_err:   0.007098
[2025-10-06 16:58:32] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -47.452096 | E_var:     0.1594 | E_err:   0.006238
[2025-10-06 16:58:37] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -47.459447 | E_var:     0.1916 | E_err:   0.006839
[2025-10-06 16:58:42] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -47.449553 | E_var:     0.1868 | E_err:   0.006754
[2025-10-06 16:58:47] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -47.457751 | E_var:     0.1554 | E_err:   0.006160
[2025-10-06 16:58:52] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -47.461211 | E_var:     0.1769 | E_err:   0.006572
[2025-10-06 16:58:57] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -47.460455 | E_var:     0.1663 | E_err:   0.006372
[2025-10-06 16:59:03] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -47.460890 | E_var:     0.1627 | E_err:   0.006303
[2025-10-06 16:59:08] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -47.456025 | E_var:     0.2133 | E_err:   0.007216
[2025-10-06 16:59:13] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -47.449627 | E_var:     0.1577 | E_err:   0.006204
[2025-10-06 16:59:18] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -47.445196 | E_var:     0.1540 | E_err:   0.006132
[2025-10-06 16:59:23] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -47.468256 | E_var:     0.1442 | E_err:   0.005934
[2025-10-06 16:59:23] 🔄 RESTART #1 | Period: 300
[2025-10-06 16:59:28] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -47.449067 | E_var:     0.1574 | E_err:   0.006199
[2025-10-06 16:59:33] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -47.456716 | E_var:     0.2145 | E_err:   0.007236
[2025-10-06 16:59:38] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -47.449614 | E_var:     0.1744 | E_err:   0.006524
[2025-10-06 16:59:44] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -47.452465 | E_var:     0.2829 | E_err:   0.008311
[2025-10-06 16:59:49] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -47.456019 | E_var:     0.2184 | E_err:   0.007302
[2025-10-06 16:59:54] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -47.483178 | E_var:     1.9760 | E_err:   0.021964
[2025-10-06 16:59:59] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -47.443573 | E_var:     0.2261 | E_err:   0.007429
[2025-10-06 17:00:04] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -47.471451 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 17:00:09] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -47.455644 | E_var:     0.1862 | E_err:   0.006743
[2025-10-06 17:00:14] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -47.442617 | E_var:     0.2037 | E_err:   0.007051
[2025-10-06 17:00:19] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -47.464842 | E_var:     0.1650 | E_err:   0.006347
[2025-10-06 17:00:24] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -47.448884 | E_var:     1.3006 | E_err:   0.017819
[2025-10-06 17:00:30] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -47.453156 | E_var:     0.1564 | E_err:   0.006180
[2025-10-06 17:00:35] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -47.453004 | E_var:     0.1759 | E_err:   0.006554
[2025-10-06 17:00:40] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -47.467924 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 17:00:45] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -47.458250 | E_var:     0.1386 | E_err:   0.005817
[2025-10-06 17:00:50] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -47.466924 | E_var:     0.1756 | E_err:   0.006547
[2025-10-06 17:00:55] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -47.462701 | E_var:     0.1715 | E_err:   0.006470
[2025-10-06 17:01:00] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -47.449033 | E_var:     0.2011 | E_err:   0.007008
[2025-10-06 17:01:05] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -47.456958 | E_var:     0.2730 | E_err:   0.008164
[2025-10-06 17:01:11] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -47.450284 | E_var:     0.1692 | E_err:   0.006426
[2025-10-06 17:01:16] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -47.462436 | E_var:     0.2356 | E_err:   0.007584
[2025-10-06 17:01:21] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -47.453345 | E_var:     0.1934 | E_err:   0.006871
[2025-10-06 17:01:26] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -47.451093 | E_var:     0.1797 | E_err:   0.006623
[2025-10-06 17:01:31] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -47.450458 | E_var:     0.1456 | E_err:   0.005962
[2025-10-06 17:01:36] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -47.467491 | E_var:     0.2310 | E_err:   0.007509
[2025-10-06 17:01:41] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -47.449302 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 17:01:46] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -47.458285 | E_var:     0.1898 | E_err:   0.006807
[2025-10-06 17:01:51] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -47.457137 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 17:01:57] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -47.447481 | E_var:     0.1897 | E_err:   0.006805
[2025-10-06 17:02:02] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -47.459906 | E_var:     0.1347 | E_err:   0.005735
[2025-10-06 17:02:07] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -47.456056 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 17:02:12] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -47.447373 | E_var:     0.1501 | E_err:   0.006053
[2025-10-06 17:02:17] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -47.467744 | E_var:     0.1833 | E_err:   0.006690
[2025-10-06 17:02:22] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -47.452201 | E_var:     0.1762 | E_err:   0.006558
[2025-10-06 17:02:27] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -47.459562 | E_var:     0.1436 | E_err:   0.005920
[2025-10-06 17:02:33] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -47.454428 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 17:02:38] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -47.453791 | E_var:     0.1814 | E_err:   0.006654
[2025-10-06 17:02:43] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -47.456087 | E_var:     0.1523 | E_err:   0.006098
[2025-10-06 17:02:48] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -47.459383 | E_var:     0.1747 | E_err:   0.006530
[2025-10-06 17:02:53] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -47.465345 | E_var:     0.1870 | E_err:   0.006756
[2025-10-06 17:02:58] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -47.452028 | E_var:     0.1465 | E_err:   0.005980
[2025-10-06 17:03:03] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -47.457488 | E_var:     0.1643 | E_err:   0.006333
[2025-10-06 17:03:09] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -47.465077 | E_var:     0.1824 | E_err:   0.006673
[2025-10-06 17:03:14] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -47.466583 | E_var:     0.2593 | E_err:   0.007956
[2025-10-06 17:03:19] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -47.455174 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 17:03:24] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -47.454808 | E_var:     0.2029 | E_err:   0.007038
[2025-10-06 17:03:29] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -47.445710 | E_var:     0.1662 | E_err:   0.006369
[2025-10-06 17:03:34] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -47.464385 | E_var:     0.1720 | E_err:   0.006480
[2025-10-06 17:03:40] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -47.462108 | E_var:     0.1584 | E_err:   0.006219
[2025-10-06 17:03:40] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 17:03:45] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -47.457457 | E_var:     0.1604 | E_err:   0.006257
[2025-10-06 17:03:50] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -47.456729 | E_var:     0.1848 | E_err:   0.006717
[2025-10-06 17:03:55] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -47.465290 | E_var:     0.1663 | E_err:   0.006372
[2025-10-06 17:04:00] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -47.453922 | E_var:     0.4409 | E_err:   0.010375
[2025-10-06 17:04:05] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -47.451836 | E_var:     0.1417 | E_err:   0.005881
[2025-10-06 17:04:10] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -47.450024 | E_var:     0.1502 | E_err:   0.006056
[2025-10-06 17:04:16] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -47.461727 | E_var:     0.1568 | E_err:   0.006187
[2025-10-06 17:04:21] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -47.456286 | E_var:     0.1656 | E_err:   0.006359
[2025-10-06 17:04:26] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -47.466785 | E_var:     0.1467 | E_err:   0.005986
[2025-10-06 17:04:31] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -47.456341 | E_var:     0.2822 | E_err:   0.008300
[2025-10-06 17:04:36] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -47.455678 | E_var:     0.1677 | E_err:   0.006398
[2025-10-06 17:04:41] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -47.456840 | E_var:     0.1661 | E_err:   0.006368
[2025-10-06 17:04:46] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -47.459228 | E_var:     0.1408 | E_err:   0.005862
[2025-10-06 17:04:51] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -47.455944 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 17:04:57] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -47.440857 | E_var:     0.1693 | E_err:   0.006428
[2025-10-06 17:05:02] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -47.453007 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 17:05:08] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -47.461126 | E_var:     0.1563 | E_err:   0.006176
[2025-10-06 17:05:13] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -47.458685 | E_var:     0.2014 | E_err:   0.007013
[2025-10-06 17:05:18] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -47.458146 | E_var:     0.1516 | E_err:   0.006084
[2025-10-06 17:05:23] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -47.460924 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 17:05:28] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -47.457594 | E_var:     0.1421 | E_err:   0.005890
[2025-10-06 17:05:33] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -47.455001 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 17:05:39] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -47.451804 | E_var:     0.1658 | E_err:   0.006361
[2025-10-06 17:05:44] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -47.445711 | E_var:     0.1890 | E_err:   0.006793
[2025-10-06 17:05:49] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -47.454569 | E_var:     0.1651 | E_err:   0.006349
[2025-10-06 17:05:54] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -47.444824 | E_var:     0.2366 | E_err:   0.007600
[2025-10-06 17:05:59] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -47.458785 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 17:06:04] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -47.458064 | E_var:     0.1558 | E_err:   0.006167
[2025-10-06 17:06:09] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -47.462913 | E_var:     0.2064 | E_err:   0.007098
[2025-10-06 17:06:14] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -47.465185 | E_var:     0.1916 | E_err:   0.006840
[2025-10-06 17:06:20] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -47.458834 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 17:06:25] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -47.455781 | E_var:     0.1489 | E_err:   0.006030
[2025-10-06 17:06:30] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -47.449971 | E_var:     0.1410 | E_err:   0.005868
[2025-10-06 17:06:35] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -47.446005 | E_var:     0.1757 | E_err:   0.006549
[2025-10-06 17:06:40] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -47.450441 | E_var:     0.2177 | E_err:   0.007291
[2025-10-06 17:06:45] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -47.437859 | E_var:     0.5257 | E_err:   0.011329
[2025-10-06 17:06:50] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -47.455185 | E_var:     0.2955 | E_err:   0.008493
[2025-10-06 17:06:55] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -47.471698 | E_var:     0.1512 | E_err:   0.006076
[2025-10-06 17:07:01] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -47.455278 | E_var:     0.1984 | E_err:   0.006960
[2025-10-06 17:07:06] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -47.459787 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 17:07:11] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -47.450185 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 17:07:16] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -47.453572 | E_var:     0.1671 | E_err:   0.006387
[2025-10-06 17:07:21] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -47.458811 | E_var:     0.1551 | E_err:   0.006154
[2025-10-06 17:07:26] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -47.454421 | E_var:     0.1687 | E_err:   0.006417
[2025-10-06 17:07:31] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -47.464392 | E_var:     0.1895 | E_err:   0.006801
[2025-10-06 17:07:36] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -47.456882 | E_var:     0.1736 | E_err:   0.006509
[2025-10-06 17:07:41] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -47.444610 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 17:07:47] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -47.456631 | E_var:     0.1934 | E_err:   0.006872
[2025-10-06 17:07:52] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -47.457031 | E_var:     0.1478 | E_err:   0.006007
[2025-10-06 17:07:57] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -47.453984 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 17:08:02] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -47.457248 | E_var:     0.1692 | E_err:   0.006427
[2025-10-06 17:08:07] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -47.457952 | E_var:     0.1464 | E_err:   0.005978
[2025-10-06 17:08:12] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -47.449752 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 17:08:17] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -47.465782 | E_var:     0.1873 | E_err:   0.006763
[2025-10-06 17:08:22] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -47.457510 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 17:08:28] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -47.447854 | E_var:     0.1743 | E_err:   0.006523
[2025-10-06 17:08:33] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -47.450815 | E_var:     0.1495 | E_err:   0.006042
[2025-10-06 17:08:38] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -47.444936 | E_var:     0.1644 | E_err:   0.006336
[2025-10-06 17:08:43] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -47.460786 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 17:08:49] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -47.456156 | E_var:     0.1573 | E_err:   0.006198
[2025-10-06 17:08:54] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -47.463615 | E_var:     0.1725 | E_err:   0.006490
[2025-10-06 17:08:59] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -47.456332 | E_var:     0.1838 | E_err:   0.006699
[2025-10-06 17:09:04] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -47.465768 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 17:09:09] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -47.453764 | E_var:     0.1510 | E_err:   0.006071
[2025-10-06 17:09:14] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -47.454893 | E_var:     0.2185 | E_err:   0.007304
[2025-10-06 17:09:19] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -47.460166 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 17:09:24] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -47.464905 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 17:09:30] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -47.458038 | E_var:     0.1619 | E_err:   0.006287
[2025-10-06 17:09:35] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -47.465846 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 17:09:40] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -47.454560 | E_var:     0.1722 | E_err:   0.006483
[2025-10-06 17:09:45] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -47.448524 | E_var:     0.1902 | E_err:   0.006815
[2025-10-06 17:09:50] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -47.452208 | E_var:     0.1886 | E_err:   0.006786
[2025-10-06 17:09:55] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -47.446339 | E_var:     0.2007 | E_err:   0.007000
[2025-10-06 17:10:00] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -47.460046 | E_var:     0.1739 | E_err:   0.006516
[2025-10-06 17:10:06] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -47.452109 | E_var:     0.1444 | E_err:   0.005938
[2025-10-06 17:10:11] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -47.457727 | E_var:     0.1386 | E_err:   0.005817
[2025-10-06 17:10:16] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -47.454425 | E_var:     0.1827 | E_err:   0.006679
[2025-10-06 17:10:21] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -47.442929 | E_var:     0.1814 | E_err:   0.006654
[2025-10-06 17:10:26] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -47.455926 | E_var:     0.1604 | E_err:   0.006257
[2025-10-06 17:10:31] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -47.451153 | E_var:     0.2099 | E_err:   0.007159
[2025-10-06 17:10:36] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -47.459838 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 17:10:41] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -47.444076 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 17:10:47] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -47.445589 | E_var:     0.2107 | E_err:   0.007173
[2025-10-06 17:10:52] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -47.464206 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 17:10:57] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -47.452096 | E_var:     0.1930 | E_err:   0.006865
[2025-10-06 17:11:02] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -47.453608 | E_var:     0.1490 | E_err:   0.006032
[2025-10-06 17:11:07] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -47.449269 | E_var:     0.1631 | E_err:   0.006311
[2025-10-06 17:11:12] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -47.457774 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 17:11:18] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -47.459820 | E_var:     0.1514 | E_err:   0.006079
[2025-10-06 17:11:23] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -47.453777 | E_var:     0.1584 | E_err:   0.006218
[2025-10-06 17:11:28] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -47.463261 | E_var:     0.1565 | E_err:   0.006181
[2025-10-06 17:11:33] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -47.463016 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 17:11:38] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -47.451889 | E_var:     0.2128 | E_err:   0.007207
[2025-10-06 17:11:43] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -47.453746 | E_var:     0.1518 | E_err:   0.006088
[2025-10-06 17:11:48] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -47.446461 | E_var:     0.1982 | E_err:   0.006956
[2025-10-06 17:11:53] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -47.456397 | E_var:     0.3466 | E_err:   0.009199
[2025-10-06 17:11:59] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -47.457128 | E_var:     0.1556 | E_err:   0.006164
[2025-10-06 17:12:04] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -47.446883 | E_var:     0.1656 | E_err:   0.006359
[2025-10-06 17:12:09] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -47.456005 | E_var:     0.1556 | E_err:   0.006163
[2025-10-06 17:12:14] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -47.463122 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 17:12:14] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 17:12:19] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -47.450972 | E_var:     0.3045 | E_err:   0.008622
[2025-10-06 17:12:24] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -47.452954 | E_var:     0.1614 | E_err:   0.006276
[2025-10-06 17:12:29] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -47.463465 | E_var:     0.1660 | E_err:   0.006366
[2025-10-06 17:12:35] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -47.449935 | E_var:     0.1762 | E_err:   0.006558
[2025-10-06 17:12:40] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -47.447012 | E_var:     0.1892 | E_err:   0.006796
[2025-10-06 17:12:45] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -47.448250 | E_var:     0.1929 | E_err:   0.006863
[2025-10-06 17:12:50] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -47.458902 | E_var:     0.2802 | E_err:   0.008271
[2025-10-06 17:12:55] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -47.454451 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 17:13:00] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -47.463144 | E_var:     0.1526 | E_err:   0.006103
[2025-10-06 17:13:05] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -47.453781 | E_var:     0.2174 | E_err:   0.007286
[2025-10-06 17:13:11] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -47.462992 | E_var:     0.2014 | E_err:   0.007013
[2025-10-06 17:13:16] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -47.446151 | E_var:     0.1782 | E_err:   0.006597
[2025-10-06 17:13:21] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -47.443028 | E_var:     0.1612 | E_err:   0.006274
[2025-10-06 17:13:26] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -47.450310 | E_var:     0.1822 | E_err:   0.006670
[2025-10-06 17:13:31] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -47.457419 | E_var:     0.1483 | E_err:   0.006017
[2025-10-06 17:13:36] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -47.456276 | E_var:     0.1862 | E_err:   0.006742
[2025-10-06 17:13:41] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -47.459549 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 17:13:47] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -47.461607 | E_var:     0.1465 | E_err:   0.005981
[2025-10-06 17:13:52] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -47.457590 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 17:13:57] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -47.457651 | E_var:     0.1893 | E_err:   0.006798
[2025-10-06 17:14:02] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -47.449196 | E_var:     0.2112 | E_err:   0.007180
[2025-10-06 17:14:07] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -47.457065 | E_var:     0.1391 | E_err:   0.005828
[2025-10-06 17:14:12] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -47.446744 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 17:14:17] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -47.466866 | E_var:     0.1487 | E_err:   0.006025
[2025-10-06 17:14:22] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -47.455703 | E_var:     0.1698 | E_err:   0.006439
[2025-10-06 17:14:28] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -47.458674 | E_var:     0.1392 | E_err:   0.005829
[2025-10-06 17:14:33] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -47.454491 | E_var:     0.1808 | E_err:   0.006643
[2025-10-06 17:14:38] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -47.456057 | E_var:     0.1818 | E_err:   0.006663
[2025-10-06 17:14:43] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -47.453354 | E_var:     0.1526 | E_err:   0.006103
[2025-10-06 17:14:48] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -47.458202 | E_var:     0.1780 | E_err:   0.006593
[2025-10-06 17:14:53] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -47.460262 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 17:14:58] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -47.461817 | E_var:     0.1604 | E_err:   0.006258
[2025-10-06 17:15:03] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -47.451549 | E_var:     0.1634 | E_err:   0.006315
[2025-10-06 17:15:08] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -47.455653 | E_var:     0.1541 | E_err:   0.006134
[2025-10-06 17:15:14] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -47.447763 | E_var:     0.1667 | E_err:   0.006379
[2025-10-06 17:15:19] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -47.452375 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 17:15:24] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -47.447886 | E_var:     0.1494 | E_err:   0.006040
[2025-10-06 17:15:29] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -47.457951 | E_var:     0.2112 | E_err:   0.007180
[2025-10-06 17:15:34] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -47.457038 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 17:15:39] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -47.458438 | E_var:     0.2843 | E_err:   0.008331
[2025-10-06 17:15:44] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -47.475884 | E_var:     0.1491 | E_err:   0.006034
[2025-10-06 17:15:49] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -47.467829 | E_var:     0.2025 | E_err:   0.007031
[2025-10-06 17:15:55] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -47.452409 | E_var:     0.2201 | E_err:   0.007331
[2025-10-06 17:16:00] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -47.459138 | E_var:     0.1540 | E_err:   0.006132
[2025-10-06 17:16:05] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -47.456562 | E_var:     0.2382 | E_err:   0.007625
[2025-10-06 17:16:10] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -47.463729 | E_var:     0.1481 | E_err:   0.006012
[2025-10-06 17:16:15] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -47.456672 | E_var:     0.1833 | E_err:   0.006689
[2025-10-06 17:16:20] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -47.458109 | E_var:     0.1776 | E_err:   0.006585
[2025-10-06 17:16:25] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -47.466121 | E_var:     0.1651 | E_err:   0.006348
[2025-10-06 17:16:30] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -47.461934 | E_var:     0.2377 | E_err:   0.007618
[2025-10-06 17:16:36] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -47.463513 | E_var:     0.1450 | E_err:   0.005950
[2025-10-06 17:16:41] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -47.447579 | E_var:     0.1756 | E_err:   0.006547
[2025-10-06 17:16:46] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -47.461895 | E_var:     0.1899 | E_err:   0.006808
[2025-10-06 17:16:51] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -47.456200 | E_var:     0.1726 | E_err:   0.006491
[2025-10-06 17:16:56] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -47.455571 | E_var:     0.2492 | E_err:   0.007799
[2025-10-06 17:17:01] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -47.463638 | E_var:     0.1226 | E_err:   0.005470
[2025-10-06 17:17:06] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -47.452524 | E_var:     0.1500 | E_err:   0.006051
[2025-10-06 17:17:11] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -47.451916 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 17:17:16] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -47.456423 | E_var:     0.1450 | E_err:   0.005950
[2025-10-06 17:17:22] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -47.450226 | E_var:     0.1668 | E_err:   0.006382
[2025-10-06 17:17:27] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -47.452296 | E_var:     0.2176 | E_err:   0.007289
[2025-10-06 17:17:32] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -47.454983 | E_var:     0.1339 | E_err:   0.005718
[2025-10-06 17:17:37] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -47.465784 | E_var:     0.1828 | E_err:   0.006681
[2025-10-06 17:17:42] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -47.463056 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 17:17:47] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -47.456644 | E_var:     0.1710 | E_err:   0.006461
[2025-10-06 17:17:52] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -47.462435 | E_var:     0.1643 | E_err:   0.006334
[2025-10-06 17:17:57] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -47.456308 | E_var:     0.2212 | E_err:   0.007348
[2025-10-06 17:18:03] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -47.463449 | E_var:     0.1699 | E_err:   0.006440
[2025-10-06 17:18:14] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -47.449209 | E_var:     0.1509 | E_err:   0.006069
[2025-10-06 17:18:19] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -47.457702 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 17:18:25] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -47.462869 | E_var:     0.1889 | E_err:   0.006791
[2025-10-06 17:18:30] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -47.456941 | E_var:     0.1866 | E_err:   0.006749
[2025-10-06 17:18:35] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -47.453405 | E_var:     0.1583 | E_err:   0.006217
[2025-10-06 17:18:40] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -47.449212 | E_var:     0.2533 | E_err:   0.007863
[2025-10-06 17:18:45] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -47.447834 | E_var:     0.1809 | E_err:   0.006645
[2025-10-06 17:18:50] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -47.452673 | E_var:     0.1791 | E_err:   0.006612
[2025-10-06 17:18:55] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -47.446112 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 17:19:00] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -47.450700 | E_var:     0.1780 | E_err:   0.006592
[2025-10-06 17:19:05] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -47.454485 | E_var:     0.1392 | E_err:   0.005830
[2025-10-06 17:19:11] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -47.461131 | E_var:     0.1659 | E_err:   0.006365
[2025-10-06 17:19:16] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -47.460348 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 17:19:21] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -47.449607 | E_var:     0.1412 | E_err:   0.005870
[2025-10-06 17:19:26] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -47.456874 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 17:19:31] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -47.461528 | E_var:     0.1892 | E_err:   0.006797
[2025-10-06 17:19:36] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -47.452470 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 17:19:41] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -47.461390 | E_var:     0.1554 | E_err:   0.006160
[2025-10-06 17:19:46] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -47.445952 | E_var:     0.2468 | E_err:   0.007763
[2025-10-06 17:19:52] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -47.455921 | E_var:     0.1609 | E_err:   0.006268
[2025-10-06 17:19:57] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -47.464840 | E_var:     0.1542 | E_err:   0.006135
[2025-10-06 17:20:02] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -47.454020 | E_var:     0.2083 | E_err:   0.007132
[2025-10-06 17:20:07] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -47.455984 | E_var:     0.1874 | E_err:   0.006765
[2025-10-06 17:20:12] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -47.454095 | E_var:     0.1627 | E_err:   0.006302
[2025-10-06 17:20:17] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -47.439835 | E_var:     0.3118 | E_err:   0.008725
[2025-10-06 17:20:22] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -47.455047 | E_var:     0.2421 | E_err:   0.007688
[2025-10-06 17:20:27] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -47.455457 | E_var:     0.1834 | E_err:   0.006691
[2025-10-06 17:20:33] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -47.450773 | E_var:     0.1586 | E_err:   0.006223
[2025-10-06 17:20:38] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -47.444978 | E_var:     0.2275 | E_err:   0.007453
[2025-10-06 17:20:43] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -47.463316 | E_var:     0.2230 | E_err:   0.007378
[2025-10-06 17:20:48] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -47.466900 | E_var:     0.1832 | E_err:   0.006688
[2025-10-06 17:20:53] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -47.463719 | E_var:     0.1633 | E_err:   0.006315
[2025-10-06 17:20:53] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 17:20:58] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -47.452443 | E_var:     0.1591 | E_err:   0.006232
[2025-10-06 17:21:03] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -47.458169 | E_var:     0.1716 | E_err:   0.006472
[2025-10-06 17:21:08] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -47.460170 | E_var:     0.1534 | E_err:   0.006119
[2025-10-06 17:21:14] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -47.438496 | E_var:     0.2091 | E_err:   0.007145
[2025-10-06 17:21:19] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -47.451742 | E_var:     0.1482 | E_err:   0.006014
[2025-10-06 17:21:24] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -47.446525 | E_var:     0.1889 | E_err:   0.006792
[2025-10-06 17:21:29] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -47.464898 | E_var:     0.1555 | E_err:   0.006162
[2025-10-06 17:21:34] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -47.453645 | E_var:     0.1812 | E_err:   0.006651
[2025-10-06 17:21:39] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -47.441325 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 17:21:44] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -47.463108 | E_var:     0.1760 | E_err:   0.006555
[2025-10-06 17:21:49] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -47.459573 | E_var:     0.2094 | E_err:   0.007151
[2025-10-06 17:21:55] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -47.452114 | E_var:     0.1581 | E_err:   0.006213
[2025-10-06 17:22:00] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -47.457664 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 17:22:05] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -47.451799 | E_var:     0.3069 | E_err:   0.008655
[2025-10-06 17:22:10] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -47.459520 | E_var:     0.1775 | E_err:   0.006582
[2025-10-06 17:22:15] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -47.446637 | E_var:     0.1792 | E_err:   0.006614
[2025-10-06 17:22:20] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -47.456261 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 17:22:25] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -47.448633 | E_var:     0.1845 | E_err:   0.006711
[2025-10-06 17:22:30] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -47.459287 | E_var:     0.1644 | E_err:   0.006336
[2025-10-06 17:22:36] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -47.440760 | E_var:     0.1583 | E_err:   0.006216
[2025-10-06 17:22:41] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -47.445593 | E_var:     0.1927 | E_err:   0.006859
[2025-10-06 17:22:46] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -47.463833 | E_var:     0.1700 | E_err:   0.006443
[2025-10-06 17:22:51] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -47.455995 | E_var:     0.1791 | E_err:   0.006612
[2025-10-06 17:22:56] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -47.461011 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 17:23:01] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -47.454201 | E_var:     0.1224 | E_err:   0.005466
[2025-10-06 17:23:06] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -47.452507 | E_var:     0.1356 | E_err:   0.005754
[2025-10-06 17:23:11] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -47.454146 | E_var:     0.2198 | E_err:   0.007326
[2025-10-06 17:23:16] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -47.447510 | E_var:     0.1697 | E_err:   0.006437
[2025-10-06 17:23:22] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -47.455065 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 17:23:27] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -47.459514 | E_var:     0.1916 | E_err:   0.006840
[2025-10-06 17:23:32] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -47.453298 | E_var:     0.1868 | E_err:   0.006753
[2025-10-06 17:23:37] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -47.457968 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 17:23:42] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -47.461783 | E_var:     0.1496 | E_err:   0.006044
[2025-10-06 17:23:47] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -47.453938 | E_var:     0.1623 | E_err:   0.006295
[2025-10-06 17:23:52] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -47.458046 | E_var:     0.2100 | E_err:   0.007160
[2025-10-06 17:23:57] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -47.454991 | E_var:     0.1505 | E_err:   0.006062
[2025-10-06 17:24:03] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -47.452001 | E_var:     0.2241 | E_err:   0.007397
[2025-10-06 17:24:08] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -47.456817 | E_var:     0.2143 | E_err:   0.007234
[2025-10-06 17:24:13] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -47.451548 | E_var:     0.1664 | E_err:   0.006374
[2025-10-06 17:24:18] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -47.448675 | E_var:     0.1482 | E_err:   0.006016
[2025-10-06 17:24:23] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -47.460012 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 17:24:28] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -47.455462 | E_var:     0.2834 | E_err:   0.008318
[2025-10-06 17:24:33] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -47.454683 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 17:24:38] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -47.470227 | E_var:     0.3934 | E_err:   0.009800
[2025-10-06 17:24:43] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -47.448524 | E_var:     0.2582 | E_err:   0.007940
[2025-10-06 17:24:49] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -47.448914 | E_var:     0.1328 | E_err:   0.005695
[2025-10-06 17:24:54] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -47.453972 | E_var:     0.1895 | E_err:   0.006802
[2025-10-06 17:24:59] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -47.466006 | E_var:     0.1783 | E_err:   0.006597
[2025-10-06 17:25:04] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -47.449163 | E_var:     0.1936 | E_err:   0.006874
[2025-10-06 17:25:09] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -47.457270 | E_var:     0.1591 | E_err:   0.006233
[2025-10-06 17:25:09] 🔄 RESTART #2 | Period: 600
[2025-10-06 17:25:14] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -47.460410 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 17:25:19] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -47.444964 | E_var:     0.1603 | E_err:   0.006256
[2025-10-06 17:25:24] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -47.460497 | E_var:     0.1673 | E_err:   0.006392
[2025-10-06 17:25:30] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -47.457945 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 17:25:35] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -47.459742 | E_var:     0.1602 | E_err:   0.006254
[2025-10-06 17:25:40] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -47.450248 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 17:25:45] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -47.471050 | E_var:     0.1353 | E_err:   0.005748
[2025-10-06 17:25:50] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -47.457799 | E_var:     0.1529 | E_err:   0.006109
[2025-10-06 17:25:55] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -47.443975 | E_var:     0.2175 | E_err:   0.007288
[2025-10-06 17:26:00] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -47.465166 | E_var:     0.1850 | E_err:   0.006720
[2025-10-06 17:26:05] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -47.452003 | E_var:     0.1664 | E_err:   0.006374
[2025-10-06 17:26:11] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -47.463609 | E_var:     0.1800 | E_err:   0.006630
[2025-10-06 17:26:16] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -47.462489 | E_var:     0.2200 | E_err:   0.007329
[2025-10-06 17:26:21] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -47.467677 | E_var:     0.1440 | E_err:   0.005930
[2025-10-06 17:26:26] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -47.461988 | E_var:     0.1823 | E_err:   0.006672
[2025-10-06 17:26:31] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -47.451180 | E_var:     0.1836 | E_err:   0.006696
[2025-10-06 17:26:36] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -47.451206 | E_var:     0.1679 | E_err:   0.006403
[2025-10-06 17:26:41] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -47.447345 | E_var:     0.2760 | E_err:   0.008209
[2025-10-06 17:26:46] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -47.449786 | E_var:     0.1759 | E_err:   0.006552
[2025-10-06 17:26:52] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -47.456591 | E_var:     0.1503 | E_err:   0.006058
[2025-10-06 17:26:57] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -47.457702 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 17:27:02] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -47.459430 | E_var:     0.2761 | E_err:   0.008211
[2025-10-06 17:27:07] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -47.441349 | E_var:     0.1598 | E_err:   0.006247
[2025-10-06 17:27:12] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -47.453920 | E_var:     0.2036 | E_err:   0.007051
[2025-10-06 17:27:17] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -47.465163 | E_var:     0.1950 | E_err:   0.006901
[2025-10-06 17:27:22] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -47.459701 | E_var:     0.1508 | E_err:   0.006067
[2025-10-06 17:27:27] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -47.450122 | E_var:     0.2799 | E_err:   0.008266
[2025-10-06 17:27:33] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -47.455639 | E_var:     0.1897 | E_err:   0.006805
[2025-10-06 17:27:38] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -47.456847 | E_var:     0.1617 | E_err:   0.006283
[2025-10-06 17:27:43] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -47.449779 | E_var:     0.1950 | E_err:   0.006900
[2025-10-06 17:27:48] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -47.453048 | E_var:     0.6272 | E_err:   0.012374
[2025-10-06 17:27:53] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -47.451861 | E_var:     0.2720 | E_err:   0.008148
[2025-10-06 17:27:58] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -47.461231 | E_var:     0.1613 | E_err:   0.006275
[2025-10-06 17:28:03] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -47.463912 | E_var:     0.1460 | E_err:   0.005971
[2025-10-06 17:28:08] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -47.459393 | E_var:     0.1539 | E_err:   0.006131
[2025-10-06 17:28:14] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -47.459482 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 17:28:19] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -47.451618 | E_var:     0.1840 | E_err:   0.006703
[2025-10-06 17:28:24] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -47.451060 | E_var:     0.1669 | E_err:   0.006384
[2025-10-06 17:28:29] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -47.453834 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 17:28:34] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -47.453948 | E_var:     0.1969 | E_err:   0.006934
[2025-10-06 17:28:39] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -47.458141 | E_var:     0.1620 | E_err:   0.006289
[2025-10-06 17:28:44] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -47.442723 | E_var:     0.1651 | E_err:   0.006350
[2025-10-06 17:28:49] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -47.446366 | E_var:     0.2366 | E_err:   0.007600
[2025-10-06 17:28:54] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -47.448167 | E_var:     0.1716 | E_err:   0.006472
[2025-10-06 17:29:00] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -47.464814 | E_var:     0.2170 | E_err:   0.007278
[2025-10-06 17:29:05] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -47.460433 | E_var:     0.1667 | E_err:   0.006379
[2025-10-06 17:29:10] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -47.451394 | E_var:     0.1549 | E_err:   0.006150
[2025-10-06 17:29:15] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -47.455756 | E_var:     0.1521 | E_err:   0.006094
[2025-10-06 17:29:20] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -47.447589 | E_var:     0.1765 | E_err:   0.006564
[2025-10-06 17:29:25] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -47.449572 | E_var:     0.1847 | E_err:   0.006716
[2025-10-06 17:29:25] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 17:29:30] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -47.461047 | E_var:     0.1933 | E_err:   0.006869
[2025-10-06 17:29:35] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -47.456283 | E_var:     0.1365 | E_err:   0.005773
[2025-10-06 17:29:41] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -47.454521 | E_var:     0.2283 | E_err:   0.007465
[2025-10-06 17:29:46] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -47.451915 | E_var:     0.1662 | E_err:   0.006369
[2025-10-06 17:29:51] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -47.464680 | E_var:     0.1671 | E_err:   0.006387
[2025-10-06 17:29:56] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -47.459750 | E_var:     0.2147 | E_err:   0.007240
[2025-10-06 17:30:01] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -47.468067 | E_var:     0.1689 | E_err:   0.006421
[2025-10-06 17:30:06] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -47.461860 | E_var:     0.1883 | E_err:   0.006780
[2025-10-06 17:30:11] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -47.453955 | E_var:     0.1562 | E_err:   0.006176
[2025-10-06 17:30:16] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -47.464363 | E_var:     0.1734 | E_err:   0.006506
[2025-10-06 17:30:22] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -47.465227 | E_var:     0.1619 | E_err:   0.006287
[2025-10-06 17:30:27] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -47.453567 | E_var:     0.2677 | E_err:   0.008084
[2025-10-06 17:30:32] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -47.454852 | E_var:     0.1782 | E_err:   0.006595
[2025-10-06 17:30:37] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -47.461492 | E_var:     0.1692 | E_err:   0.006427
[2025-10-06 17:30:42] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -47.452967 | E_var:     0.1617 | E_err:   0.006282
[2025-10-06 17:30:47] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -47.451398 | E_var:     0.1770 | E_err:   0.006573
[2025-10-06 17:30:52] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -47.467180 | E_var:     0.1538 | E_err:   0.006128
[2025-10-06 17:30:57] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -47.462445 | E_var:     0.1656 | E_err:   0.006359
[2025-10-06 17:31:03] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -47.459915 | E_var:     0.1694 | E_err:   0.006430
[2025-10-06 17:31:08] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -47.457610 | E_var:     0.3347 | E_err:   0.009039
[2025-10-06 17:31:13] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -47.453489 | E_var:     0.1698 | E_err:   0.006439
[2025-10-06 17:31:18] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -47.461157 | E_var:     0.1429 | E_err:   0.005907
[2025-10-06 17:31:23] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -47.455259 | E_var:     0.1646 | E_err:   0.006338
[2025-10-06 17:31:28] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -47.455810 | E_var:     0.1951 | E_err:   0.006902
[2025-10-06 17:31:33] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -47.456793 | E_var:     0.1962 | E_err:   0.006922
[2025-10-06 17:31:38] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -47.450134 | E_var:     0.1692 | E_err:   0.006428
[2025-10-06 17:31:43] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -47.458871 | E_var:     0.1754 | E_err:   0.006544
[2025-10-06 17:31:49] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -47.457890 | E_var:     0.1673 | E_err:   0.006391
[2025-10-06 17:31:54] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -47.448740 | E_var:     0.1477 | E_err:   0.006006
[2025-10-06 17:31:59] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -47.451849 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 17:32:04] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -47.456986 | E_var:     0.2139 | E_err:   0.007227
[2025-10-06 17:32:09] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -47.462174 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 17:32:14] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -47.449944 | E_var:     0.1950 | E_err:   0.006900
[2025-10-06 17:32:19] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -47.444847 | E_var:     0.2424 | E_err:   0.007692
[2025-10-06 17:32:24] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -47.454341 | E_var:     0.2118 | E_err:   0.007191
[2025-10-06 17:32:30] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -47.465449 | E_var:     0.2061 | E_err:   0.007094
[2025-10-06 17:32:35] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -47.452858 | E_var:     0.1667 | E_err:   0.006380
[2025-10-06 17:32:40] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -47.460074 | E_var:     0.1559 | E_err:   0.006170
[2025-10-06 17:32:45] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -47.447689 | E_var:     0.1748 | E_err:   0.006533
[2025-10-06 17:32:50] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -47.464556 | E_var:     0.1534 | E_err:   0.006120
[2025-10-06 17:32:55] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -47.460150 | E_var:     0.2131 | E_err:   0.007212
[2025-10-06 17:33:00] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -47.454847 | E_var:     0.1718 | E_err:   0.006477
[2025-10-06 17:33:05] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -47.448155 | E_var:     0.1456 | E_err:   0.005962
[2025-10-06 17:33:11] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -47.454533 | E_var:     0.2069 | E_err:   0.007108
[2025-10-06 17:33:16] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -47.459666 | E_var:     0.1566 | E_err:   0.006184
[2025-10-06 17:33:21] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -47.454054 | E_var:     0.1790 | E_err:   0.006610
[2025-10-06 17:33:26] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -47.458809 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 17:33:31] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -47.458316 | E_var:     0.1600 | E_err:   0.006250
[2025-10-06 17:33:36] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -47.455294 | E_var:     0.2059 | E_err:   0.007090
[2025-10-06 17:33:41] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -47.450923 | E_var:     0.1555 | E_err:   0.006161
[2025-10-06 17:33:46] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -47.436971 | E_var:     0.3284 | E_err:   0.008954
[2025-10-06 17:33:52] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -47.452705 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 17:33:57] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -47.452632 | E_var:     0.1744 | E_err:   0.006525
[2025-10-06 17:34:02] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -47.455863 | E_var:     0.1396 | E_err:   0.005837
[2025-10-06 17:34:07] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -47.459303 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 17:34:12] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -47.461925 | E_var:     0.1772 | E_err:   0.006577
[2025-10-06 17:34:17] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -47.438561 | E_var:     0.1445 | E_err:   0.005939
[2025-10-06 17:34:22] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -47.451675 | E_var:     0.1513 | E_err:   0.006077
[2025-10-06 17:34:27] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -47.446737 | E_var:     0.1819 | E_err:   0.006663
[2025-10-06 17:34:33] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -47.457400 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 17:34:38] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -47.459248 | E_var:     0.1416 | E_err:   0.005879
[2025-10-06 17:34:43] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -47.455228 | E_var:     0.1542 | E_err:   0.006135
[2025-10-06 17:34:48] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -47.447635 | E_var:     0.2370 | E_err:   0.007606
[2025-10-06 17:34:53] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -47.452043 | E_var:     0.1805 | E_err:   0.006639
[2025-10-06 17:34:58] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -47.451380 | E_var:     0.2399 | E_err:   0.007653
[2025-10-06 17:35:03] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -47.460729 | E_var:     0.1560 | E_err:   0.006171
[2025-10-06 17:35:08] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -47.455535 | E_var:     0.3891 | E_err:   0.009747
[2025-10-06 17:35:14] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -47.460073 | E_var:     0.1706 | E_err:   0.006454
[2025-10-06 17:35:19] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -47.445352 | E_var:     0.1674 | E_err:   0.006393
[2025-10-06 17:35:24] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -47.442074 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 17:35:29] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -47.472560 | E_var:     0.1784 | E_err:   0.006599
[2025-10-06 17:35:34] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -47.449352 | E_var:     0.1728 | E_err:   0.006496
[2025-10-06 17:35:39] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -47.457966 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 17:35:44] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -47.460438 | E_var:     0.1816 | E_err:   0.006658
[2025-10-06 17:35:49] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -47.462973 | E_var:     0.2123 | E_err:   0.007200
[2025-10-06 17:35:55] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -47.452195 | E_var:     0.2074 | E_err:   0.007115
[2025-10-06 17:36:00] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -47.450003 | E_var:     0.2124 | E_err:   0.007202
[2025-10-06 17:36:05] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -47.457587 | E_var:     0.1900 | E_err:   0.006811
[2025-10-06 17:36:10] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -47.465950 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 17:36:15] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -47.452873 | E_var:     0.1844 | E_err:   0.006709
[2025-10-06 17:36:21] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -47.453264 | E_var:     0.2700 | E_err:   0.008120
[2025-10-06 17:36:26] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -47.457150 | E_var:     0.1532 | E_err:   0.006115
[2025-10-06 17:36:31] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -47.455618 | E_var:     0.1667 | E_err:   0.006380
[2025-10-06 17:36:36] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -47.462308 | E_var:     0.1607 | E_err:   0.006263
[2025-10-06 17:36:41] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -47.451752 | E_var:     0.1566 | E_err:   0.006182
[2025-10-06 17:36:46] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -47.451314 | E_var:     0.1846 | E_err:   0.006713
[2025-10-06 17:36:51] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -47.463700 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 17:36:56] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -47.458755 | E_var:     0.1875 | E_err:   0.006766
[2025-10-06 17:37:02] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -47.452920 | E_var:     0.1534 | E_err:   0.006119
[2025-10-06 17:37:07] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -47.451583 | E_var:     0.2289 | E_err:   0.007476
[2025-10-06 17:37:12] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -47.460686 | E_var:     0.1462 | E_err:   0.005975
[2025-10-06 17:37:17] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -47.462769 | E_var:     0.2014 | E_err:   0.007012
[2025-10-06 17:37:22] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -47.457938 | E_var:     0.1716 | E_err:   0.006472
[2025-10-06 17:37:27] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -47.452840 | E_var:     0.1704 | E_err:   0.006450
[2025-10-06 17:37:32] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -47.455239 | E_var:     0.1645 | E_err:   0.006336
[2025-10-06 17:37:37] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -47.451915 | E_var:     0.1747 | E_err:   0.006532
[2025-10-06 17:37:42] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -47.446904 | E_var:     0.1785 | E_err:   0.006602
[2025-10-06 17:37:48] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -47.462093 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 17:37:53] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -47.457037 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 17:37:58] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -47.447481 | E_var:     0.1499 | E_err:   0.006050
[2025-10-06 17:37:58] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 17:38:03] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -47.451596 | E_var:     0.1533 | E_err:   0.006118
[2025-10-06 17:38:08] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -47.462180 | E_var:     0.1852 | E_err:   0.006725
[2025-10-06 17:38:13] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -47.460241 | E_var:     0.1572 | E_err:   0.006194
[2025-10-06 17:38:18] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -47.461702 | E_var:     0.1344 | E_err:   0.005727
[2025-10-06 17:38:23] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -47.452898 | E_var:     0.1506 | E_err:   0.006064
[2025-10-06 17:38:29] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -47.458677 | E_var:     0.1873 | E_err:   0.006761
[2025-10-06 17:38:34] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -47.451603 | E_var:     0.1569 | E_err:   0.006188
[2025-10-06 17:38:39] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -47.454875 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 17:38:44] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -47.445854 | E_var:     0.1836 | E_err:   0.006695
[2025-10-06 17:38:49] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -47.456869 | E_var:     0.2114 | E_err:   0.007184
[2025-10-06 17:38:54] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -47.460599 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 17:38:59] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -47.461292 | E_var:     0.1388 | E_err:   0.005822
[2025-10-06 17:39:04] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -47.456694 | E_var:     0.1771 | E_err:   0.006575
[2025-10-06 17:39:10] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -47.446512 | E_var:     0.1849 | E_err:   0.006720
[2025-10-06 17:39:15] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -47.462763 | E_var:     0.1555 | E_err:   0.006161
[2025-10-06 17:39:20] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -47.468179 | E_var:     0.2512 | E_err:   0.007832
[2025-10-06 17:39:25] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -47.460364 | E_var:     0.1721 | E_err:   0.006482
[2025-10-06 17:39:30] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -47.454494 | E_var:     0.1599 | E_err:   0.006248
[2025-10-06 17:39:35] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -47.449402 | E_var:     0.1554 | E_err:   0.006159
[2025-10-06 17:39:40] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -47.447144 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 17:39:45] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -47.453727 | E_var:     0.1788 | E_err:   0.006606
[2025-10-06 17:39:51] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -47.456452 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 17:39:56] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -47.458371 | E_var:     0.1363 | E_err:   0.005768
[2025-10-06 17:40:01] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -47.458503 | E_var:     0.1836 | E_err:   0.006696
[2025-10-06 17:40:06] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -47.467262 | E_var:     0.2050 | E_err:   0.007074
[2025-10-06 17:40:11] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -47.461967 | E_var:     0.1774 | E_err:   0.006581
[2025-10-06 17:40:16] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -47.449162 | E_var:     0.1787 | E_err:   0.006605
[2025-10-06 17:40:21] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -47.454755 | E_var:     0.1771 | E_err:   0.006576
[2025-10-06 17:40:26] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -47.457612 | E_var:     0.1733 | E_err:   0.006505
[2025-10-06 17:40:31] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -47.462264 | E_var:     0.1738 | E_err:   0.006513
[2025-10-06 17:40:37] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -47.456639 | E_var:     0.1923 | E_err:   0.006853
[2025-10-06 17:40:42] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -47.451153 | E_var:     0.1864 | E_err:   0.006747
[2025-10-06 17:40:47] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -47.447386 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 17:40:52] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -47.452127 | E_var:     0.1504 | E_err:   0.006059
[2025-10-06 17:40:57] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -47.453199 | E_var:     0.1726 | E_err:   0.006492
[2025-10-06 17:41:02] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -47.443700 | E_var:     0.1727 | E_err:   0.006494
[2025-10-06 17:41:07] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -47.446129 | E_var:     0.1608 | E_err:   0.006265
[2025-10-06 17:41:12] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -47.450579 | E_var:     0.1884 | E_err:   0.006782
[2025-10-06 17:41:18] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -47.452592 | E_var:     0.1777 | E_err:   0.006586
[2025-10-06 17:41:23] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -47.459130 | E_var:     0.1965 | E_err:   0.006926
[2025-10-06 17:41:28] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -47.448267 | E_var:     0.2126 | E_err:   0.007204
[2025-10-06 17:41:33] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -47.454897 | E_var:     0.1926 | E_err:   0.006858
[2025-10-06 17:41:38] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -47.453835 | E_var:     0.2091 | E_err:   0.007144
[2025-10-06 17:41:43] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -47.461569 | E_var:     0.1875 | E_err:   0.006766
[2025-10-06 17:41:48] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -47.456578 | E_var:     0.1820 | E_err:   0.006667
[2025-10-06 17:41:53] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -47.468036 | E_var:     0.1500 | E_err:   0.006052
[2025-10-06 17:41:59] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -47.448444 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 17:42:04] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -47.457910 | E_var:     0.2015 | E_err:   0.007015
[2025-10-06 17:42:09] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -47.453255 | E_var:     0.1757 | E_err:   0.006550
[2025-10-06 17:42:14] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -47.454757 | E_var:     0.2019 | E_err:   0.007020
[2025-10-06 17:42:19] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -47.456995 | E_var:     0.1671 | E_err:   0.006388
[2025-10-06 17:42:24] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -47.459754 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 17:42:29] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -47.461991 | E_var:     0.1702 | E_err:   0.006447
[2025-10-06 17:42:34] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -47.452354 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 17:42:39] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -47.436631 | E_var:     0.1814 | E_err:   0.006655
[2025-10-06 17:42:45] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -47.450940 | E_var:     0.2259 | E_err:   0.007427
[2025-10-06 17:42:50] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -47.452162 | E_var:     0.1999 | E_err:   0.006985
[2025-10-06 17:42:55] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -47.439781 | E_var:     0.1611 | E_err:   0.006272
[2025-10-06 17:43:00] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -47.458157 | E_var:     0.1595 | E_err:   0.006241
[2025-10-06 17:43:05] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -47.466524 | E_var:     0.1992 | E_err:   0.006975
[2025-10-06 17:43:10] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -47.452660 | E_var:     0.1871 | E_err:   0.006758
[2025-10-06 17:43:15] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -47.454192 | E_var:     0.1833 | E_err:   0.006691
[2025-10-06 17:43:20] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -47.448223 | E_var:     0.5197 | E_err:   0.011264
[2025-10-06 17:43:26] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -47.458063 | E_var:     0.1709 | E_err:   0.006459
[2025-10-06 17:43:31] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -47.462435 | E_var:     0.1613 | E_err:   0.006275
[2025-10-06 17:43:36] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -47.449934 | E_var:     0.1481 | E_err:   0.006014
[2025-10-06 17:43:41] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -47.459266 | E_var:     0.1833 | E_err:   0.006689
[2025-10-06 17:43:46] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -47.457950 | E_var:     0.2179 | E_err:   0.007294
[2025-10-06 17:43:51] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -47.454855 | E_var:     0.1888 | E_err:   0.006790
[2025-10-06 17:43:56] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -47.450713 | E_var:     0.1953 | E_err:   0.006905
[2025-10-06 17:44:01] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -47.444289 | E_var:     0.1803 | E_err:   0.006635
[2025-10-06 17:44:07] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -47.462507 | E_var:     0.1689 | E_err:   0.006422
[2025-10-06 17:44:12] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -47.445782 | E_var:     0.2397 | E_err:   0.007650
[2025-10-06 17:44:17] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -47.451442 | E_var:     0.1742 | E_err:   0.006521
[2025-10-06 17:44:22] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -47.452686 | E_var:     0.1874 | E_err:   0.006764
[2025-10-06 17:44:27] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -47.458896 | E_var:     0.1986 | E_err:   0.006964
[2025-10-06 17:44:32] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -47.456947 | E_var:     0.2486 | E_err:   0.007791
[2025-10-06 17:44:37] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -47.457204 | E_var:     0.1748 | E_err:   0.006533
[2025-10-06 17:44:42] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -47.458200 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 17:44:48] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -47.461809 | E_var:     0.1493 | E_err:   0.006037
[2025-10-06 17:44:53] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -47.458033 | E_var:     0.2121 | E_err:   0.007196
[2025-10-06 17:44:58] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -47.460832 | E_var:     0.1550 | E_err:   0.006151
[2025-10-06 17:45:03] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -47.458717 | E_var:     0.1596 | E_err:   0.006242
[2025-10-06 17:45:08] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -47.455590 | E_var:     0.1526 | E_err:   0.006104
[2025-10-06 17:45:13] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -47.455478 | E_var:     0.1796 | E_err:   0.006622
[2025-10-06 17:45:18] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -47.452180 | E_var:     0.1507 | E_err:   0.006066
[2025-10-06 17:45:23] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -47.449375 | E_var:     0.1643 | E_err:   0.006333
[2025-10-06 17:45:29] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -47.466891 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 17:45:34] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -47.451208 | E_var:     0.1966 | E_err:   0.006928
[2025-10-06 17:45:39] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -47.454080 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 17:45:44] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -47.450274 | E_var:     0.1830 | E_err:   0.006684
[2025-10-06 17:45:49] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -47.459430 | E_var:     0.2889 | E_err:   0.008398
[2025-10-06 17:45:54] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -47.440795 | E_var:     0.2385 | E_err:   0.007631
[2025-10-06 17:45:59] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -47.461555 | E_var:     0.1542 | E_err:   0.006136
[2025-10-06 17:46:04] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -47.463550 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 17:46:09] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -47.458310 | E_var:     0.1550 | E_err:   0.006152
[2025-10-06 17:46:15] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -47.455952 | E_var:     0.1595 | E_err:   0.006240
[2025-10-06 17:46:20] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -47.466716 | E_var:     0.1727 | E_err:   0.006494
[2025-10-06 17:46:25] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -47.453098 | E_var:     0.1715 | E_err:   0.006470
[2025-10-06 17:46:30] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -47.451007 | E_var:     0.1449 | E_err:   0.005947
[2025-10-06 17:46:30] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 17:46:35] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -47.452637 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 17:46:40] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -47.456421 | E_var:     0.1957 | E_err:   0.006912
[2025-10-06 17:46:45] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -47.461096 | E_var:     0.2199 | E_err:   0.007327
[2025-10-06 17:46:50] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -47.453707 | E_var:     0.2629 | E_err:   0.008012
[2025-10-06 17:46:56] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -47.465213 | E_var:     0.2576 | E_err:   0.007930
[2025-10-06 17:47:01] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -47.457046 | E_var:     0.1663 | E_err:   0.006372
[2025-10-06 17:47:06] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -47.461371 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 17:47:11] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -47.463329 | E_var:     0.1869 | E_err:   0.006755
[2025-10-06 17:47:16] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -47.447063 | E_var:     0.1944 | E_err:   0.006889
[2025-10-06 17:47:21] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -47.458635 | E_var:     0.1742 | E_err:   0.006521
[2025-10-06 17:47:26] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -47.459810 | E_var:     0.1605 | E_err:   0.006260
[2025-10-06 17:47:31] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -47.450070 | E_var:     0.1553 | E_err:   0.006157
[2025-10-06 17:47:37] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -47.458611 | E_var:     0.1782 | E_err:   0.006595
[2025-10-06 17:47:42] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -47.453154 | E_var:     0.1632 | E_err:   0.006312
[2025-10-06 17:47:47] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -47.458866 | E_var:     0.1675 | E_err:   0.006395
[2025-10-06 17:47:52] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -47.463605 | E_var:     0.2054 | E_err:   0.007081
[2025-10-06 17:47:57] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -47.464823 | E_var:     0.2409 | E_err:   0.007669
[2025-10-06 17:48:02] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -47.455833 | E_var:     0.1929 | E_err:   0.006862
[2025-10-06 17:48:07] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -47.454353 | E_var:     0.1621 | E_err:   0.006290
[2025-10-06 17:48:12] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -47.451488 | E_var:     0.1919 | E_err:   0.006846
[2025-10-06 17:48:18] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -47.463112 | E_var:     0.2473 | E_err:   0.007770
[2025-10-06 17:48:23] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -47.466037 | E_var:     0.1578 | E_err:   0.006206
[2025-10-06 17:48:28] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -47.451301 | E_var:     0.1677 | E_err:   0.006399
[2025-10-06 17:48:33] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -47.452648 | E_var:     0.1709 | E_err:   0.006460
[2025-10-06 17:48:38] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -47.470061 | E_var:     0.1551 | E_err:   0.006154
[2025-10-06 17:48:43] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -47.437009 | E_var:     0.1717 | E_err:   0.006474
[2025-10-06 17:48:48] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -47.458268 | E_var:     0.1644 | E_err:   0.006336
[2025-10-06 17:48:53] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -47.463928 | E_var:     0.1761 | E_err:   0.006556
[2025-10-06 17:48:58] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -47.459573 | E_var:     0.1853 | E_err:   0.006726
[2025-10-06 17:49:04] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -47.451689 | E_var:     0.2685 | E_err:   0.008096
[2025-10-06 17:49:09] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -47.458271 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 17:49:14] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -47.441224 | E_var:     0.2094 | E_err:   0.007151
[2025-10-06 17:49:19] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -47.458681 | E_var:     0.1788 | E_err:   0.006607
[2025-10-06 17:49:24] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -47.461405 | E_var:     0.1870 | E_err:   0.006756
[2025-10-06 17:49:29] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -47.447446 | E_var:     0.1669 | E_err:   0.006383
[2025-10-06 17:49:34] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -47.463143 | E_var:     0.2390 | E_err:   0.007639
[2025-10-06 17:49:39] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -47.458296 | E_var:     0.3362 | E_err:   0.009060
[2025-10-06 17:49:45] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -47.454968 | E_var:     0.1687 | E_err:   0.006417
[2025-10-06 17:49:50] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -47.457450 | E_var:     0.2240 | E_err:   0.007395
[2025-10-06 17:49:55] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -47.448447 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 17:50:00] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -47.454827 | E_var:     0.1336 | E_err:   0.005712
[2025-10-06 17:50:05] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -47.473361 | E_var:     0.1681 | E_err:   0.006406
[2025-10-06 17:50:10] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -47.465348 | E_var:     0.1574 | E_err:   0.006198
[2025-10-06 17:50:15] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -47.439644 | E_var:     0.2020 | E_err:   0.007022
[2025-10-06 17:50:20] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -47.446901 | E_var:     0.3080 | E_err:   0.008671
[2025-10-06 17:50:26] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -47.462646 | E_var:     0.1565 | E_err:   0.006182
[2025-10-06 17:50:31] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -47.460053 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 17:50:36] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -47.453870 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 17:50:41] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -47.453188 | E_var:     0.1533 | E_err:   0.006118
[2025-10-06 17:50:46] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -47.456058 | E_var:     0.2033 | E_err:   0.007045
[2025-10-06 17:50:51] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -47.455124 | E_var:     0.1652 | E_err:   0.006351
[2025-10-06 17:50:56] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -47.463926 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 17:51:01] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -47.455764 | E_var:     0.1758 | E_err:   0.006552
[2025-10-06 17:51:07] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -47.447184 | E_var:     0.2262 | E_err:   0.007431
[2025-10-06 17:51:12] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -47.462144 | E_var:     0.1668 | E_err:   0.006381
[2025-10-06 17:51:17] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -47.454683 | E_var:     0.2467 | E_err:   0.007761
[2025-10-06 17:51:22] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -47.446456 | E_var:     0.3712 | E_err:   0.009519
[2025-10-06 17:51:27] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -47.464899 | E_var:     0.1763 | E_err:   0.006560
[2025-10-06 17:51:32] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -47.454701 | E_var:     0.1684 | E_err:   0.006412
[2025-10-06 17:51:37] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -47.466275 | E_var:     0.1829 | E_err:   0.006682
[2025-10-06 17:51:42] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -47.461066 | E_var:     0.1506 | E_err:   0.006064
[2025-10-06 17:51:48] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -47.456281 | E_var:     0.1458 | E_err:   0.005967
[2025-10-06 17:51:53] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -47.453427 | E_var:     0.1548 | E_err:   0.006147
[2025-10-06 17:51:58] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -47.448986 | E_var:     0.1499 | E_err:   0.006049
[2025-10-06 17:52:03] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -47.462288 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 17:52:08] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -47.458199 | E_var:     0.2115 | E_err:   0.007186
[2025-10-06 17:52:13] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -47.451437 | E_var:     0.1914 | E_err:   0.006835
[2025-10-06 17:52:18] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -47.453673 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 17:52:23] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -47.456866 | E_var:     0.1533 | E_err:   0.006117
[2025-10-06 17:52:29] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -47.461666 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 17:52:34] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -47.456289 | E_var:     0.1934 | E_err:   0.006871
[2025-10-06 17:52:39] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -47.455632 | E_var:     0.1881 | E_err:   0.006777
[2025-10-06 17:52:44] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -47.463059 | E_var:     0.1864 | E_err:   0.006746
[2025-10-06 17:52:49] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -47.467428 | E_var:     0.2014 | E_err:   0.007013
[2025-10-06 17:52:54] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -47.468291 | E_var:     0.2354 | E_err:   0.007582
[2025-10-06 17:52:59] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -47.447528 | E_var:     0.1656 | E_err:   0.006359
[2025-10-06 17:53:04] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -47.448009 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 17:53:10] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -47.429113 | E_var:     0.2928 | E_err:   0.008455
[2025-10-06 17:53:15] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -47.463803 | E_var:     0.2245 | E_err:   0.007403
[2025-10-06 17:53:20] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -47.466043 | E_var:     0.1550 | E_err:   0.006151
[2025-10-06 17:53:25] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -47.470724 | E_var:     0.1653 | E_err:   0.006354
[2025-10-06 17:53:30] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -47.438353 | E_var:     0.1834 | E_err:   0.006691
[2025-10-06 17:53:35] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -47.443597 | E_var:     0.1716 | E_err:   0.006472
[2025-10-06 17:53:40] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -47.454496 | E_var:     0.1690 | E_err:   0.006424
[2025-10-06 17:53:45] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -47.451908 | E_var:     0.1611 | E_err:   0.006271
[2025-10-06 17:53:51] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -47.452120 | E_var:     0.1789 | E_err:   0.006609
[2025-10-06 17:53:56] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -47.451422 | E_var:     0.1572 | E_err:   0.006195
[2025-10-06 17:54:01] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -47.452357 | E_var:     0.1741 | E_err:   0.006519
[2025-10-06 17:54:06] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -47.458958 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 17:54:11] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -47.460953 | E_var:     0.1846 | E_err:   0.006713
[2025-10-06 17:54:16] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -47.467587 | E_var:     0.1536 | E_err:   0.006123
[2025-10-06 17:54:21] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -47.451286 | E_var:     0.2428 | E_err:   0.007699
[2025-10-06 17:54:26] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -47.451751 | E_var:     0.1587 | E_err:   0.006225
[2025-10-06 17:54:32] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -47.463632 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 17:54:37] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -47.455838 | E_var:     0.1433 | E_err:   0.005916
[2025-10-06 17:54:42] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -47.465624 | E_var:     0.1584 | E_err:   0.006218
[2025-10-06 17:54:47] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -47.464166 | E_var:     0.1618 | E_err:   0.006286
[2025-10-06 17:54:52] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -47.446834 | E_var:     0.1913 | E_err:   0.006835
[2025-10-06 17:54:57] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -47.460237 | E_var:     0.1649 | E_err:   0.006346
[2025-10-06 17:55:02] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -47.452268 | E_var:     0.1647 | E_err:   0.006340
[2025-10-06 17:55:02] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 17:55:07] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -47.449136 | E_var:     0.1986 | E_err:   0.006963
[2025-10-06 17:55:13] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -47.449301 | E_var:     0.1764 | E_err:   0.006562
[2025-10-06 17:55:18] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -47.472292 | E_var:     0.1297 | E_err:   0.005627
[2025-10-06 17:55:23] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -47.454187 | E_var:     0.1561 | E_err:   0.006174
[2025-10-06 17:55:28] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -47.463501 | E_var:     0.1876 | E_err:   0.006767
[2025-10-06 17:55:33] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -47.448027 | E_var:     0.1822 | E_err:   0.006670
[2025-10-06 17:55:38] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -47.453057 | E_var:     0.1711 | E_err:   0.006464
[2025-10-06 17:55:43] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -47.450045 | E_var:     0.1404 | E_err:   0.005854
[2025-10-06 17:55:48] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -47.455796 | E_var:     0.2135 | E_err:   0.007220
[2025-10-06 17:55:54] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -47.447581 | E_var:     0.2022 | E_err:   0.007027
[2025-10-06 17:55:59] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -47.459617 | E_var:     0.1474 | E_err:   0.005998
[2025-10-06 17:56:04] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -47.452498 | E_var:     0.1655 | E_err:   0.006356
[2025-10-06 17:56:09] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -47.459492 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 17:56:14] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -47.466714 | E_var:     0.1679 | E_err:   0.006403
[2025-10-06 17:56:19] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -47.461114 | E_var:     0.1790 | E_err:   0.006611
[2025-10-06 17:56:24] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -47.467276 | E_var:     0.1462 | E_err:   0.005974
[2025-10-06 17:56:29] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -47.465394 | E_var:     0.1645 | E_err:   0.006338
[2025-10-06 17:56:35] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -47.454921 | E_var:     0.1613 | E_err:   0.006275
[2025-10-06 17:56:40] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -47.458910 | E_var:     0.1498 | E_err:   0.006047
[2025-10-06 17:56:45] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -47.442656 | E_var:     0.1750 | E_err:   0.006537
[2025-10-06 17:56:50] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -47.451671 | E_var:     0.1867 | E_err:   0.006751
[2025-10-06 17:56:55] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -47.451228 | E_var:     0.2285 | E_err:   0.007469
[2025-10-06 17:57:00] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -47.455827 | E_var:     0.2150 | E_err:   0.007246
[2025-10-06 17:57:05] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -47.461857 | E_var:     0.1586 | E_err:   0.006222
[2025-10-06 17:57:10] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -47.468924 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 17:57:16] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -47.456973 | E_var:     0.3346 | E_err:   0.009038
[2025-10-06 17:57:21] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -47.456141 | E_var:     0.1387 | E_err:   0.005820
[2025-10-06 17:57:26] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -47.459858 | E_var:     0.1432 | E_err:   0.005914
[2025-10-06 17:57:31] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -47.461182 | E_var:     0.2105 | E_err:   0.007168
[2025-10-06 17:57:36] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -47.449422 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 17:57:41] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -47.455977 | E_var:     0.1820 | E_err:   0.006666
[2025-10-06 17:57:46] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -47.451901 | E_var:     0.1527 | E_err:   0.006105
[2025-10-06 17:57:51] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -47.453293 | E_var:     0.1663 | E_err:   0.006372
[2025-10-06 17:57:57] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -47.449997 | E_var:     0.2272 | E_err:   0.007447
[2025-10-06 17:58:02] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -47.450390 | E_var:     0.1520 | E_err:   0.006092
[2025-10-06 17:58:07] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -47.460852 | E_var:     0.1855 | E_err:   0.006730
[2025-10-06 17:58:12] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -47.453038 | E_var:     0.2023 | E_err:   0.007028
[2025-10-06 17:58:17] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -47.450813 | E_var:     0.1921 | E_err:   0.006848
[2025-10-06 17:58:22] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -47.466787 | E_var:     0.1782 | E_err:   0.006596
[2025-10-06 17:58:27] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -47.463915 | E_var:     0.1638 | E_err:   0.006325
[2025-10-06 17:58:32] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -47.457132 | E_var:     0.1811 | E_err:   0.006648
[2025-10-06 17:58:38] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -47.455239 | E_var:     0.1585 | E_err:   0.006221
[2025-10-06 17:58:43] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -47.454481 | E_var:     0.1425 | E_err:   0.005899
[2025-10-06 17:58:48] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -47.455402 | E_var:     0.1837 | E_err:   0.006697
[2025-10-06 17:58:53] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -47.467389 | E_var:     0.3571 | E_err:   0.009337
[2025-10-06 17:58:58] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -47.455388 | E_var:     0.1477 | E_err:   0.006004
[2025-10-06 17:59:03] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -47.465782 | E_var:     0.1611 | E_err:   0.006271
[2025-10-06 17:59:08] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -47.464782 | E_var:     0.1841 | E_err:   0.006704
[2025-10-06 17:59:13] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -47.455606 | E_var:     0.1663 | E_err:   0.006372
[2025-10-06 17:59:19] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -47.465228 | E_var:     0.1549 | E_err:   0.006150
[2025-10-06 17:59:24] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -47.466072 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 17:59:29] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -47.449957 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 17:59:34] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -47.465022 | E_var:     0.1319 | E_err:   0.005676
[2025-10-06 17:59:39] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -47.460999 | E_var:     0.2010 | E_err:   0.007005
[2025-10-06 17:59:44] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -47.448330 | E_var:     0.1963 | E_err:   0.006923
[2025-10-06 17:59:49] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -47.444917 | E_var:     0.1682 | E_err:   0.006409
[2025-10-06 17:59:54] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -47.453839 | E_var:     0.2052 | E_err:   0.007078
[2025-10-06 18:00:00] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -47.459720 | E_var:     0.2849 | E_err:   0.008341
[2025-10-06 18:00:05] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -47.459392 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 18:00:10] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -47.461784 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 18:00:15] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -47.460182 | E_var:     0.1638 | E_err:   0.006324
[2025-10-06 18:00:20] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -47.455484 | E_var:     0.1381 | E_err:   0.005806
[2025-10-06 18:00:25] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -47.459056 | E_var:     0.1460 | E_err:   0.005971
[2025-10-06 18:00:30] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -47.462407 | E_var:     0.1707 | E_err:   0.006455
[2025-10-06 18:00:35] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -47.447482 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 18:00:40] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -47.455570 | E_var:     0.1727 | E_err:   0.006494
[2025-10-06 18:00:46] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -47.453362 | E_var:     0.1690 | E_err:   0.006424
[2025-10-06 18:00:51] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -47.453703 | E_var:     0.1667 | E_err:   0.006380
[2025-10-06 18:00:56] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -47.452110 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 18:01:01] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -47.442814 | E_var:     0.2172 | E_err:   0.007282
[2025-10-06 18:01:06] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -47.462327 | E_var:     0.1476 | E_err:   0.006002
[2025-10-06 18:01:11] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -47.458954 | E_var:     0.2399 | E_err:   0.007653
[2025-10-06 18:01:16] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -47.452643 | E_var:     0.2034 | E_err:   0.007048
[2025-10-06 18:01:21] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -47.454644 | E_var:     0.1631 | E_err:   0.006311
[2025-10-06 18:01:27] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -47.453058 | E_var:     0.1823 | E_err:   0.006672
[2025-10-06 18:01:32] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -47.444441 | E_var:     0.2149 | E_err:   0.007243
[2025-10-06 18:01:37] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -47.455432 | E_var:     0.1905 | E_err:   0.006821
[2025-10-06 18:01:42] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -47.446796 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 18:01:47] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -47.462301 | E_var:     0.1588 | E_err:   0.006227
[2025-10-06 18:01:52] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -47.452549 | E_var:     0.1660 | E_err:   0.006365
[2025-10-06 18:01:57] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -47.461994 | E_var:     0.1742 | E_err:   0.006521
[2025-10-06 18:02:02] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -47.457480 | E_var:     0.1885 | E_err:   0.006785
[2025-10-06 18:02:08] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -47.464861 | E_var:     0.1611 | E_err:   0.006271
[2025-10-06 18:02:13] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -47.459696 | E_var:     0.2644 | E_err:   0.008034
[2025-10-06 18:02:18] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -47.451553 | E_var:     0.3706 | E_err:   0.009511
[2025-10-06 18:02:23] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -47.451034 | E_var:     0.1759 | E_err:   0.006553
[2025-10-06 18:02:28] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -47.454705 | E_var:     0.1247 | E_err:   0.005518
[2025-10-06 18:02:33] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -47.459734 | E_var:     0.1626 | E_err:   0.006301
[2025-10-06 18:02:38] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -47.453117 | E_var:     0.2371 | E_err:   0.007608
[2025-10-06 18:02:43] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -47.444513 | E_var:     0.1480 | E_err:   0.006011
[2025-10-06 18:02:49] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -47.447382 | E_var:     0.1590 | E_err:   0.006231
[2025-10-06 18:02:54] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -47.461515 | E_var:     0.1686 | E_err:   0.006416
[2025-10-06 18:02:59] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -47.453781 | E_var:     0.1650 | E_err:   0.006347
[2025-10-06 18:03:04] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -47.465092 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 18:03:09] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -47.458844 | E_var:     0.1696 | E_err:   0.006434
[2025-10-06 18:03:14] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -47.447746 | E_var:     0.1415 | E_err:   0.005879
[2025-10-06 18:03:19] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -47.457984 | E_var:     0.1896 | E_err:   0.006803
[2025-10-06 18:03:24] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -47.444993 | E_var:     0.1637 | E_err:   0.006323
[2025-10-06 18:03:29] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -47.450916 | E_var:     0.1632 | E_err:   0.006313
[2025-10-06 18:03:35] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -47.452652 | E_var:     0.1864 | E_err:   0.006746
[2025-10-06 18:03:35] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 18:03:40] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -47.454127 | E_var:     0.1440 | E_err:   0.005929
[2025-10-06 18:03:45] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -47.457124 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 18:03:50] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -47.457236 | E_var:     0.1986 | E_err:   0.006963
[2025-10-06 18:03:55] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -47.443327 | E_var:     0.1955 | E_err:   0.006909
[2025-10-06 18:04:00] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -47.446534 | E_var:     0.1561 | E_err:   0.006173
[2025-10-06 18:04:05] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -47.454880 | E_var:     0.1902 | E_err:   0.006814
[2025-10-06 18:04:11] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -47.473950 | E_var:     0.1457 | E_err:   0.005965
[2025-10-06 18:04:16] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -47.456217 | E_var:     0.1672 | E_err:   0.006390
[2025-10-06 18:04:21] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -47.458238 | E_var:     0.1800 | E_err:   0.006628
[2025-10-06 18:04:26] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -47.469029 | E_var:     0.1635 | E_err:   0.006317
[2025-10-06 18:04:31] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -47.456581 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 18:04:36] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -47.462105 | E_var:     0.1918 | E_err:   0.006844
[2025-10-06 18:04:41] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -47.454198 | E_var:     0.1892 | E_err:   0.006797
[2025-10-06 18:04:46] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -47.452189 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 18:04:52] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -47.455000 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 18:04:57] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -47.461618 | E_var:     0.1651 | E_err:   0.006348
[2025-10-06 18:05:02] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -47.463813 | E_var:     0.1619 | E_err:   0.006286
[2025-10-06 18:05:07] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -47.460001 | E_var:     0.1538 | E_err:   0.006128
[2025-10-06 18:05:12] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -47.461855 | E_var:     0.1668 | E_err:   0.006382
[2025-10-06 18:05:17] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -47.455902 | E_var:     0.1883 | E_err:   0.006780
[2025-10-06 18:05:22] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -47.465621 | E_var:     0.1876 | E_err:   0.006767
[2025-10-06 18:05:27] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -47.458258 | E_var:     0.1969 | E_err:   0.006934
[2025-10-06 18:05:32] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -47.459087 | E_var:     0.1682 | E_err:   0.006409
[2025-10-06 18:05:38] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -47.466520 | E_var:     0.1686 | E_err:   0.006416
[2025-10-06 18:05:43] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -47.453235 | E_var:     0.1433 | E_err:   0.005914
[2025-10-06 18:05:48] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -47.447662 | E_var:     0.2333 | E_err:   0.007547
[2025-10-06 18:05:53] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -47.471653 | E_var:     0.1516 | E_err:   0.006083
[2025-10-06 18:05:58] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -47.465764 | E_var:     0.1443 | E_err:   0.005935
[2025-10-06 18:06:03] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -47.447688 | E_var:     0.2568 | E_err:   0.007918
[2025-10-06 18:06:08] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -47.454948 | E_var:     0.1634 | E_err:   0.006317
[2025-10-06 18:06:13] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -47.460492 | E_var:     0.1595 | E_err:   0.006241
[2025-10-06 18:06:19] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -47.461777 | E_var:     0.1477 | E_err:   0.006006
[2025-10-06 18:06:24] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -47.449858 | E_var:     0.2031 | E_err:   0.007041
[2025-10-06 18:06:29] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -47.453624 | E_var:     0.1743 | E_err:   0.006524
[2025-10-06 18:06:34] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -47.464012 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 18:06:39] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -47.452997 | E_var:     0.1530 | E_err:   0.006111
[2025-10-06 18:06:44] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -47.461735 | E_var:     0.1774 | E_err:   0.006582
[2025-10-06 18:06:49] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -47.458844 | E_var:     0.1636 | E_err:   0.006319
[2025-10-06 18:06:54] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -47.453004 | E_var:     0.1970 | E_err:   0.006935
[2025-10-06 18:07:00] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -47.451741 | E_var:     0.2031 | E_err:   0.007041
[2025-10-06 18:07:05] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -47.445748 | E_var:     0.1624 | E_err:   0.006297
[2025-10-06 18:07:10] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -47.461347 | E_var:     0.1740 | E_err:   0.006518
[2025-10-06 18:07:15] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -47.460399 | E_var:     0.1685 | E_err:   0.006414
[2025-10-06 18:07:20] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -47.454534 | E_var:     0.1752 | E_err:   0.006540
[2025-10-06 18:07:25] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -47.458083 | E_var:     0.1477 | E_err:   0.006005
[2025-10-06 18:07:30] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -47.457036 | E_var:     0.1596 | E_err:   0.006242
[2025-10-06 18:07:35] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -47.459646 | E_var:     0.1708 | E_err:   0.006458
[2025-10-06 18:07:40] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -47.461982 | E_var:     0.1648 | E_err:   0.006343
[2025-10-06 18:07:46] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -47.466606 | E_var:     0.1745 | E_err:   0.006527
[2025-10-06 18:07:51] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -47.452927 | E_var:     0.1758 | E_err:   0.006551
[2025-10-06 18:07:56] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -47.458290 | E_var:     0.1879 | E_err:   0.006773
[2025-10-06 18:08:01] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -47.448452 | E_var:     0.2570 | E_err:   0.007921
[2025-10-06 18:08:06] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -47.466665 | E_var:     0.1745 | E_err:   0.006528
[2025-10-06 18:08:11] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -47.449927 | E_var:     0.2230 | E_err:   0.007378
[2025-10-06 18:08:16] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -47.463328 | E_var:     0.1505 | E_err:   0.006061
[2025-10-06 18:08:21] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -47.451782 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 18:08:27] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -47.456052 | E_var:     0.5423 | E_err:   0.011507
[2025-10-06 18:08:32] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -47.468122 | E_var:     0.1642 | E_err:   0.006331
[2025-10-06 18:08:37] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -47.444187 | E_var:     0.1815 | E_err:   0.006658
[2025-10-06 18:08:42] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -47.456870 | E_var:     0.1441 | E_err:   0.005932
[2025-10-06 18:08:47] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -47.464959 | E_var:     0.1366 | E_err:   0.005774
[2025-10-06 18:08:52] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -47.454890 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 18:08:57] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -47.457047 | E_var:     0.1642 | E_err:   0.006332
[2025-10-06 18:09:02] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -47.445017 | E_var:     0.2239 | E_err:   0.007394
[2025-10-06 18:09:07] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -47.455353 | E_var:     0.3480 | E_err:   0.009217
[2025-10-06 18:09:13] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -47.456528 | E_var:     0.1518 | E_err:   0.006087
[2025-10-06 18:09:18] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -47.457769 | E_var:     0.2192 | E_err:   0.007315
[2025-10-06 18:09:23] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -47.447432 | E_var:     0.1693 | E_err:   0.006429
[2025-10-06 18:09:28] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -47.461436 | E_var:     0.1701 | E_err:   0.006444
[2025-10-06 18:09:33] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -47.451827 | E_var:     0.1704 | E_err:   0.006451
[2025-10-06 18:09:38] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -47.455190 | E_var:     0.1611 | E_err:   0.006272
[2025-10-06 18:09:43] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -47.442931 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 18:09:48] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -47.466285 | E_var:     0.2156 | E_err:   0.007256
[2025-10-06 18:09:54] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -47.458633 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 18:09:59] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -47.450243 | E_var:     0.2172 | E_err:   0.007282
[2025-10-06 18:10:04] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -47.448454 | E_var:     0.1711 | E_err:   0.006464
[2025-10-06 18:10:09] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -47.462674 | E_var:     0.2029 | E_err:   0.007038
[2025-10-06 18:10:14] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -47.454097 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 18:10:19] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -47.459912 | E_var:     0.1898 | E_err:   0.006807
[2025-10-06 18:10:24] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -47.457993 | E_var:     0.1327 | E_err:   0.005693
[2025-10-06 18:10:29] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -47.461960 | E_var:     0.1535 | E_err:   0.006122
[2025-10-06 18:10:34] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -47.461717 | E_var:     0.2180 | E_err:   0.007295
[2025-10-06 18:10:40] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -47.457506 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 18:10:45] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -47.466847 | E_var:     0.1809 | E_err:   0.006646
[2025-10-06 18:10:50] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -47.462074 | E_var:     0.1721 | E_err:   0.006483
[2025-10-06 18:10:55] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -47.453511 | E_var:     0.1423 | E_err:   0.005894
[2025-10-06 18:11:00] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -47.471126 | E_var:     0.1758 | E_err:   0.006551
[2025-10-06 18:11:05] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -47.460745 | E_var:     0.1442 | E_err:   0.005933
[2025-10-06 18:11:10] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -47.460495 | E_var:     0.1455 | E_err:   0.005961
[2025-10-06 18:11:15] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -47.452739 | E_var:     0.1730 | E_err:   0.006499
[2025-10-06 18:11:21] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -47.450940 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 18:11:26] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -47.453081 | E_var:     0.1421 | E_err:   0.005890
[2025-10-06 18:11:31] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -47.453293 | E_var:     0.1350 | E_err:   0.005741
[2025-10-06 18:11:36] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -47.478648 | E_var:     0.2394 | E_err:   0.007645
[2025-10-06 18:11:41] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -47.455276 | E_var:     0.1575 | E_err:   0.006200
[2025-10-06 18:11:46] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -47.453603 | E_var:     0.1880 | E_err:   0.006774
[2025-10-06 18:11:51] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -47.442058 | E_var:     0.1938 | E_err:   0.006879
[2025-10-06 18:11:56] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -47.456325 | E_var:     0.1552 | E_err:   0.006156
[2025-10-06 18:12:02] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -47.451226 | E_var:     0.1556 | E_err:   0.006164
[2025-10-06 18:12:07] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -47.450994 | E_var:     0.1665 | E_err:   0.006375
[2025-10-06 18:12:07] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 18:12:12] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -47.448661 | E_var:     0.1686 | E_err:   0.006416
[2025-10-06 18:12:17] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -47.453529 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 18:12:22] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -47.458274 | E_var:     0.1345 | E_err:   0.005731
[2025-10-06 18:12:27] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -47.469966 | E_var:     0.1922 | E_err:   0.006849
[2025-10-06 18:12:32] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -47.450509 | E_var:     0.1668 | E_err:   0.006381
[2025-10-06 18:12:37] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -47.448576 | E_var:     0.1638 | E_err:   0.006324
[2025-10-06 18:12:42] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -47.448669 | E_var:     0.1593 | E_err:   0.006237
[2025-10-06 18:12:48] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -47.460468 | E_var:     0.1505 | E_err:   0.006061
[2025-10-06 18:12:53] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -47.460837 | E_var:     0.1606 | E_err:   0.006262
[2025-10-06 18:12:58] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -47.464581 | E_var:     0.2024 | E_err:   0.007029
[2025-10-06 18:13:03] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -47.467867 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 18:13:08] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -47.455810 | E_var:     0.1727 | E_err:   0.006493
[2025-10-06 18:13:13] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -47.455068 | E_var:     0.1786 | E_err:   0.006603
[2025-10-06 18:13:18] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -47.458988 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 18:13:23] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -47.458004 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 18:13:29] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -47.455576 | E_var:     0.1382 | E_err:   0.005809
[2025-10-06 18:13:34] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -47.452633 | E_var:     0.1440 | E_err:   0.005929
[2025-10-06 18:13:39] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -47.469086 | E_var:     0.1750 | E_err:   0.006537
[2025-10-06 18:13:44] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -47.460622 | E_var:     0.2055 | E_err:   0.007083
[2025-10-06 18:13:49] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -47.464857 | E_var:     0.1631 | E_err:   0.006311
[2025-10-06 18:13:54] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -47.458338 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 18:13:59] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -47.455949 | E_var:     0.1370 | E_err:   0.005782
[2025-10-06 18:14:04] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -47.446690 | E_var:     0.1526 | E_err:   0.006103
[2025-10-06 18:14:10] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -47.467197 | E_var:     0.1798 | E_err:   0.006626
[2025-10-06 18:14:15] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -47.454766 | E_var:     0.1451 | E_err:   0.005952
[2025-10-06 18:14:20] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -47.456361 | E_var:     0.1242 | E_err:   0.005508
[2025-10-06 18:14:25] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -47.462878 | E_var:     0.1581 | E_err:   0.006213
[2025-10-06 18:14:30] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -47.456473 | E_var:     0.1761 | E_err:   0.006557
[2025-10-06 18:14:35] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -47.469553 | E_var:     0.1726 | E_err:   0.006491
[2025-10-06 18:14:40] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -47.449803 | E_var:     0.1684 | E_err:   0.006412
[2025-10-06 18:14:45] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -47.451920 | E_var:     0.1425 | E_err:   0.005899
[2025-10-06 18:14:51] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -47.470144 | E_var:     0.1956 | E_err:   0.006911
[2025-10-06 18:14:56] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -47.466590 | E_var:     0.1672 | E_err:   0.006389
[2025-10-06 18:15:01] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -47.458063 | E_var:     0.1909 | E_err:   0.006826
[2025-10-06 18:15:06] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -47.454999 | E_var:     0.1870 | E_err:   0.006757
[2025-10-06 18:15:11] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -47.461125 | E_var:     0.1775 | E_err:   0.006583
[2025-10-06 18:15:16] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -47.455824 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 18:15:21] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -47.461254 | E_var:     0.2428 | E_err:   0.007699
[2025-10-06 18:15:26] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -47.458623 | E_var:     0.2031 | E_err:   0.007042
[2025-10-06 18:15:32] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -47.459244 | E_var:     0.1753 | E_err:   0.006541
[2025-10-06 18:15:37] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -47.464174 | E_var:     0.1886 | E_err:   0.006785
[2025-10-06 18:15:42] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -47.456824 | E_var:     0.2004 | E_err:   0.006994
[2025-10-06 18:15:47] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -47.452150 | E_var:     0.1747 | E_err:   0.006531
[2025-10-06 18:15:52] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -47.452437 | E_var:     0.1693 | E_err:   0.006429
[2025-10-06 18:15:57] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -47.470278 | E_var:     0.1427 | E_err:   0.005903
[2025-10-06 18:16:02] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -47.457343 | E_var:     0.1554 | E_err:   0.006160
[2025-10-06 18:16:07] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -47.449180 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 18:16:13] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -47.465689 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 18:16:18] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -47.451712 | E_var:     0.2227 | E_err:   0.007373
[2025-10-06 18:16:23] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -47.463590 | E_var:     0.1553 | E_err:   0.006157
[2025-10-06 18:16:23] ======================================================================================================
[2025-10-06 18:16:23] ✅ Training completed successfully
[2025-10-06 18:16:23] Total restarts: 2
[2025-10-06 18:16:25] Final Energy: -47.46358963 ± 0.00615699
[2025-10-06 18:16:25] Final Variance: 0.155274
[2025-10-06 18:16:25] ======================================================================================================
[2025-10-06 18:16:25] ======================================================================================================
[2025-10-06 18:16:25] Training completed | Runtime: 5430.8s
[2025-10-06 18:16:26] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 18:16:26] ======================================================================================================
