[2025-10-06 15:13:50] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.82/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 15:13:50]   - 迭代次数: final
[2025-10-06 15:13:50]   - 能量: -46.148167-0.002373j ± 0.008538, Var: 0.298598
[2025-10-06 15:13:50]   - 时间戳: 2025-10-06T15:13:33.711076+08:00
[2025-10-06 15:14:09] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 15:14:09] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 15:14:09] ======================================================================================================
[2025-10-06 15:14:09] GCNN for Shastry-Sutherland Model
[2025-10-06 15:14:09] ======================================================================================================
[2025-10-06 15:14:09] System parameters:
[2025-10-06 15:14:09]   - System size: L=5, N=100
[2025-10-06 15:14:09]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-10-06 15:14:09] ------------------------------------------------------------------------------------------------------
[2025-10-06 15:14:10] Model parameters:
[2025-10-06 15:14:10]   - Number of layers = 4
[2025-10-06 15:14:10]   - Number of features = 4
[2025-10-06 15:14:10]   - Total parameters = 19628
[2025-10-06 15:14:10] ------------------------------------------------------------------------------------------------------
[2025-10-06 15:14:10] Training parameters:
[2025-10-06 15:14:10]   - Total iterations: 1050
[2025-10-06 15:14:10]   - Annealing cycles: 3
[2025-10-06 15:14:10]   - Initial period: 150
[2025-10-06 15:14:10]   - Period multiplier: 2.0
[2025-10-06 15:14:10]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 15:14:10]   - Samples: 4096
[2025-10-06 15:14:10]   - Discarded samples: 0
[2025-10-06 15:14:10]   - Chunk size: 4096
[2025-10-06 15:14:10]   - Diagonal shift: 0.15
[2025-10-06 15:14:10]   - Gradient clipping: 1.0
[2025-10-06 15:14:10]   - Checkpoint enabled: interval=100
[2025-10-06 15:14:10]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.83/model_L4F4/training/checkpoints
[2025-10-06 15:14:10] ------------------------------------------------------------------------------------------------------
[2025-10-06 15:14:10] Device status:
[2025-10-06 15:14:10]   - Devices model: NVIDIA H200 NVL
[2025-10-06 15:14:10]   - Number of devices: 1
[2025-10-06 15:14:10]   - Sharding: True
[2025-10-06 15:14:10] ======================================================================================================
[2025-10-06 15:14:42] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -46.815676 | E_var:     0.5775 | E_err:   0.011874
[2025-10-06 15:15:03] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -46.818663 | E_var:     0.3222 | E_err:   0.008869
[2025-10-06 15:15:08] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -46.810482 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 15:15:13] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -46.813136 | E_var:     0.2805 | E_err:   0.008275
[2025-10-06 15:15:18] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -46.807114 | E_var:     0.2804 | E_err:   0.008274
[2025-10-06 15:15:23] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -46.819852 | E_var:     0.1816 | E_err:   0.006659
[2025-10-06 15:15:29] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -46.803311 | E_var:     0.2130 | E_err:   0.007211
[2025-10-06 15:15:34] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -46.814346 | E_var:     0.1601 | E_err:   0.006252
[2025-10-06 15:15:39] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -46.812941 | E_var:     0.2452 | E_err:   0.007737
[2025-10-06 15:15:44] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -46.801484 | E_var:     0.2205 | E_err:   0.007338
[2025-10-06 15:15:49] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -46.818337 | E_var:     0.1931 | E_err:   0.006866
[2025-10-06 15:15:54] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -46.815208 | E_var:     0.1892 | E_err:   0.006797
[2025-10-06 15:16:00] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -46.800022 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 15:16:05] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -46.821177 | E_var:     0.1557 | E_err:   0.006165
[2025-10-06 15:16:10] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -46.801555 | E_var:     0.1966 | E_err:   0.006928
[2025-10-06 15:16:15] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -46.811948 | E_var:     0.2303 | E_err:   0.007499
[2025-10-06 15:16:20] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -46.815846 | E_var:     0.2609 | E_err:   0.007980
[2025-10-06 15:16:25] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -46.819132 | E_var:     0.2415 | E_err:   0.007679
[2025-10-06 15:16:31] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -46.803938 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 15:16:36] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -46.817870 | E_var:     0.1589 | E_err:   0.006229
[2025-10-06 15:16:41] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -46.808499 | E_var:     0.1754 | E_err:   0.006544
[2025-10-06 15:16:46] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -46.805898 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 15:16:51] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -46.798494 | E_var:     0.1601 | E_err:   0.006252
[2025-10-06 15:16:56] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -46.812118 | E_var:     0.2258 | E_err:   0.007424
[2025-10-06 15:17:02] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -46.812898 | E_var:     0.1629 | E_err:   0.006307
[2025-10-06 15:17:07] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -46.807314 | E_var:     0.2713 | E_err:   0.008139
[2025-10-06 15:17:12] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -46.800042 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 15:17:17] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -46.811298 | E_var:     0.1913 | E_err:   0.006834
[2025-10-06 15:17:22] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -46.797928 | E_var:     0.2609 | E_err:   0.007981
[2025-10-06 15:17:27] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -46.809464 | E_var:     0.1737 | E_err:   0.006511
[2025-10-06 15:17:33] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -46.809666 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 15:17:38] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -46.825758 | E_var:     0.1941 | E_err:   0.006884
[2025-10-06 15:17:43] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -46.823306 | E_var:     0.3806 | E_err:   0.009639
[2025-10-06 15:17:48] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -46.814945 | E_var:     0.2540 | E_err:   0.007875
[2025-10-06 15:17:53] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -46.813553 | E_var:     0.1995 | E_err:   0.006978
[2025-10-06 15:17:58] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -46.810161 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 15:18:04] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -46.811317 | E_var:     0.1479 | E_err:   0.006010
[2025-10-06 15:18:09] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -46.810143 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 15:18:14] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -46.817703 | E_var:     0.2501 | E_err:   0.007814
[2025-10-06 15:18:19] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -46.806728 | E_var:     0.1907 | E_err:   0.006824
[2025-10-06 15:18:24] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -46.810611 | E_var:     0.1965 | E_err:   0.006926
[2025-10-06 15:18:29] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -46.813869 | E_var:     0.2649 | E_err:   0.008042
[2025-10-06 15:18:35] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -46.819409 | E_var:     0.1616 | E_err:   0.006281
[2025-10-06 15:18:40] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -46.814883 | E_var:     0.1887 | E_err:   0.006787
[2025-10-06 15:18:45] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -46.805152 | E_var:     0.1830 | E_err:   0.006684
[2025-10-06 15:18:50] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -46.809364 | E_var:     0.1835 | E_err:   0.006694
[2025-10-06 15:18:55] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -46.816227 | E_var:     0.1750 | E_err:   0.006537
[2025-10-06 15:19:00] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -46.801999 | E_var:     0.2146 | E_err:   0.007239
[2025-10-06 15:19:06] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -46.814377 | E_var:     0.2358 | E_err:   0.007587
[2025-10-06 15:19:11] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -46.808053 | E_var:     0.5290 | E_err:   0.011365
[2025-10-06 15:19:16] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -46.810801 | E_var:     0.2246 | E_err:   0.007405
[2025-10-06 15:19:21] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -46.816947 | E_var:     0.5365 | E_err:   0.011445
[2025-10-06 15:19:26] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -46.821064 | E_var:     0.1693 | E_err:   0.006428
[2025-10-06 15:19:31] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -46.808720 | E_var:     0.1825 | E_err:   0.006675
[2025-10-06 15:19:37] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -46.807853 | E_var:     0.2222 | E_err:   0.007365
[2025-10-06 15:19:42] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -46.813704 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 15:19:47] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -46.820732 | E_var:     0.1938 | E_err:   0.006879
[2025-10-06 15:19:52] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -46.809690 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 15:19:57] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -46.813477 | E_var:     0.1766 | E_err:   0.006567
[2025-10-06 15:20:02] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -46.816705 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 15:20:08] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -46.810121 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 15:20:13] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -46.804543 | E_var:     0.1907 | E_err:   0.006822
[2025-10-06 15:20:18] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -46.806551 | E_var:     0.2059 | E_err:   0.007090
[2025-10-06 15:20:23] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -46.807322 | E_var:     0.1979 | E_err:   0.006952
[2025-10-06 15:20:28] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -46.818348 | E_var:     0.2047 | E_err:   0.007070
[2025-10-06 15:20:33] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -46.808455 | E_var:     0.2249 | E_err:   0.007411
[2025-10-06 15:20:39] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -46.821375 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 15:20:44] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -46.813974 | E_var:     0.1602 | E_err:   0.006254
[2025-10-06 15:20:49] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -46.811012 | E_var:     0.2229 | E_err:   0.007377
[2025-10-06 15:20:54] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -46.805756 | E_var:     0.1857 | E_err:   0.006734
[2025-10-06 15:20:59] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -46.810664 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 15:21:04] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -46.823722 | E_var:     0.1958 | E_err:   0.006914
[2025-10-06 15:21:10] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -46.805153 | E_var:     0.2039 | E_err:   0.007056
[2025-10-06 15:21:15] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -46.823293 | E_var:     0.1910 | E_err:   0.006829
[2025-10-06 15:21:20] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -46.810922 | E_var:     0.2169 | E_err:   0.007277
[2025-10-06 15:21:25] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -46.816538 | E_var:     0.1720 | E_err:   0.006479
[2025-10-06 15:21:30] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -46.803294 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 15:21:35] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -46.805033 | E_var:     0.1652 | E_err:   0.006350
[2025-10-06 15:21:41] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -46.809686 | E_var:     0.1976 | E_err:   0.006945
[2025-10-06 15:21:46] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -46.819836 | E_var:     0.1785 | E_err:   0.006602
[2025-10-06 15:21:51] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -46.805670 | E_var:     0.2743 | E_err:   0.008184
[2025-10-06 15:21:56] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -46.818197 | E_var:     0.1971 | E_err:   0.006937
[2025-10-06 15:22:01] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -46.819138 | E_var:     0.3246 | E_err:   0.008903
[2025-10-06 15:22:06] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -46.816785 | E_var:     0.1934 | E_err:   0.006872
[2025-10-06 15:22:12] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -46.799679 | E_var:     0.1688 | E_err:   0.006420
[2025-10-06 15:22:17] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -46.802071 | E_var:     0.5330 | E_err:   0.011407
[2025-10-06 15:22:22] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -46.816147 | E_var:     0.2012 | E_err:   0.007008
[2025-10-06 15:22:27] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -46.815707 | E_var:     0.1869 | E_err:   0.006754
[2025-10-06 15:22:32] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -46.821418 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 15:22:37] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -46.820133 | E_var:     0.2026 | E_err:   0.007032
[2025-10-06 15:22:42] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -46.800266 | E_var:     0.2826 | E_err:   0.008306
[2025-10-06 15:22:48] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -46.814942 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 15:22:54] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -46.799448 | E_var:     0.1896 | E_err:   0.006803
[2025-10-06 15:22:59] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -46.812274 | E_var:     0.1752 | E_err:   0.006540
[2025-10-06 15:23:04] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -46.820380 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 15:23:09] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -46.814219 | E_var:     0.2016 | E_err:   0.007015
[2025-10-06 15:23:15] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -46.823829 | E_var:     0.2003 | E_err:   0.006993
[2025-10-06 15:23:20] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -46.821356 | E_var:     0.1591 | E_err:   0.006231
[2025-10-06 15:23:25] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -46.804437 | E_var:     0.1972 | E_err:   0.006938
[2025-10-06 15:23:30] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -46.820044 | E_var:     0.2374 | E_err:   0.007614
[2025-10-06 15:23:30] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 15:23:35] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -46.802004 | E_var:     0.1444 | E_err:   0.005938
[2025-10-06 15:23:40] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -46.812062 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 15:23:45] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -46.818977 | E_var:     0.1534 | E_err:   0.006120
[2025-10-06 15:23:51] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -46.810722 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 15:23:56] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -46.815958 | E_var:     0.2536 | E_err:   0.007869
[2025-10-06 15:24:01] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -46.825668 | E_var:     0.1755 | E_err:   0.006545
[2025-10-06 15:24:06] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -46.817609 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 15:24:12] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -46.812901 | E_var:     0.1432 | E_err:   0.005912
[2025-10-06 15:24:17] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -46.813335 | E_var:     0.2708 | E_err:   0.008131
[2025-10-06 15:24:22] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -46.817559 | E_var:     0.1934 | E_err:   0.006872
[2025-10-06 15:24:27] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -46.814885 | E_var:     0.1608 | E_err:   0.006266
[2025-10-06 15:24:32] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -46.815857 | E_var:     0.1743 | E_err:   0.006523
[2025-10-06 15:24:38] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -46.800355 | E_var:     0.2048 | E_err:   0.007071
[2025-10-06 15:24:43] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -46.814796 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 15:24:48] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -46.809424 | E_var:     0.1986 | E_err:   0.006963
[2025-10-06 15:24:53] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -46.826494 | E_var:     0.1956 | E_err:   0.006910
[2025-10-06 15:24:58] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -46.797769 | E_var:     0.2914 | E_err:   0.008434
[2025-10-06 15:25:03] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -46.802389 | E_var:     0.1781 | E_err:   0.006594
[2025-10-06 15:25:09] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -46.815308 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 15:25:14] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -46.811534 | E_var:     0.1661 | E_err:   0.006369
[2025-10-06 15:25:19] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -46.807868 | E_var:     0.1762 | E_err:   0.006558
[2025-10-06 15:25:24] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -46.816824 | E_var:     0.1704 | E_err:   0.006449
[2025-10-06 15:25:29] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -46.819746 | E_var:     0.1868 | E_err:   0.006754
[2025-10-06 15:25:34] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -46.816431 | E_var:     0.1475 | E_err:   0.006002
[2025-10-06 15:25:40] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -46.798328 | E_var:     0.2218 | E_err:   0.007359
[2025-10-06 15:25:45] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -46.813384 | E_var:     0.1923 | E_err:   0.006852
[2025-10-06 15:25:50] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -46.819250 | E_var:     0.1524 | E_err:   0.006100
[2025-10-06 15:25:55] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -46.810878 | E_var:     0.1730 | E_err:   0.006500
[2025-10-06 15:26:00] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -46.791777 | E_var:     0.3013 | E_err:   0.008577
[2025-10-06 15:26:06] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -46.825424 | E_var:     0.2199 | E_err:   0.007327
[2025-10-06 15:26:11] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -46.797715 | E_var:     0.3311 | E_err:   0.008991
[2025-10-06 15:26:16] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -46.804475 | E_var:     0.1934 | E_err:   0.006872
[2025-10-06 15:26:21] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -46.814293 | E_var:     0.2141 | E_err:   0.007230
[2025-10-06 15:26:26] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -46.809701 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 15:26:31] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -46.819607 | E_var:     0.3040 | E_err:   0.008615
[2025-10-06 15:26:36] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -46.795144 | E_var:     0.2610 | E_err:   0.007983
[2025-10-06 15:26:42] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -46.815684 | E_var:     0.2079 | E_err:   0.007124
[2025-10-06 15:26:47] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -46.814723 | E_var:     0.1843 | E_err:   0.006708
[2025-10-06 15:26:52] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -46.807477 | E_var:     0.1461 | E_err:   0.005973
[2025-10-06 15:26:57] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -46.809463 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 15:27:02] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -46.806919 | E_var:     0.4545 | E_err:   0.010534
[2025-10-06 15:27:07] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -46.817803 | E_var:     0.3717 | E_err:   0.009526
[2025-10-06 15:27:12] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -46.810809 | E_var:     0.1458 | E_err:   0.005966
[2025-10-06 15:27:18] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -46.806996 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 15:27:23] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -46.815518 | E_var:     0.1519 | E_err:   0.006089
[2025-10-06 15:27:28] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -46.805853 | E_var:     0.2309 | E_err:   0.007508
[2025-10-06 15:27:33] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -46.793783 | E_var:     0.1959 | E_err:   0.006917
[2025-10-06 15:27:38] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -46.815260 | E_var:     0.1959 | E_err:   0.006916
[2025-10-06 15:27:43] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -46.815327 | E_var:     0.1980 | E_err:   0.006953
[2025-10-06 15:27:49] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -46.808921 | E_var:     0.1711 | E_err:   0.006464
[2025-10-06 15:27:49] 🔄 RESTART #1 | Period: 300
[2025-10-06 15:27:54] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -46.824273 | E_var:     0.2280 | E_err:   0.007461
[2025-10-06 15:27:59] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -46.818093 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 15:28:04] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -46.811247 | E_var:     0.2464 | E_err:   0.007756
[2025-10-06 15:28:09] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -46.803056 | E_var:     0.2002 | E_err:   0.006990
[2025-10-06 15:28:14] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -46.797759 | E_var:     0.2544 | E_err:   0.007881
[2025-10-06 15:28:19] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -46.812091 | E_var:     0.2135 | E_err:   0.007219
[2025-10-06 15:28:25] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -46.831989 | E_var:     0.1646 | E_err:   0.006339
[2025-10-06 15:28:30] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -46.811975 | E_var:     0.1983 | E_err:   0.006958
[2025-10-06 15:28:35] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -46.808942 | E_var:     0.1905 | E_err:   0.006820
[2025-10-06 15:28:40] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -46.802884 | E_var:     0.1899 | E_err:   0.006809
[2025-10-06 15:28:45] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -46.826526 | E_var:     0.1715 | E_err:   0.006470
[2025-10-06 15:28:50] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -46.813480 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 15:28:55] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -46.808802 | E_var:     0.1801 | E_err:   0.006632
[2025-10-06 15:29:01] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -46.810357 | E_var:     0.1782 | E_err:   0.006596
[2025-10-06 15:29:06] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -46.810346 | E_var:     0.2091 | E_err:   0.007144
[2025-10-06 15:29:11] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -46.814728 | E_var:     0.1485 | E_err:   0.006021
[2025-10-06 15:29:16] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -46.818567 | E_var:     0.1968 | E_err:   0.006931
[2025-10-06 15:29:21] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -46.810321 | E_var:     0.1888 | E_err:   0.006789
[2025-10-06 15:29:26] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -46.817628 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 15:29:31] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -46.815948 | E_var:     0.1614 | E_err:   0.006277
[2025-10-06 15:29:37] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -46.812399 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 15:29:42] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -46.808094 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 15:29:47] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -46.814839 | E_var:     0.1613 | E_err:   0.006276
[2025-10-06 15:29:52] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -46.812073 | E_var:     0.1723 | E_err:   0.006485
[2025-10-06 15:29:57] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -46.807450 | E_var:     0.1813 | E_err:   0.006653
[2025-10-06 15:30:02] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -46.812533 | E_var:     0.2063 | E_err:   0.007098
[2025-10-06 15:30:07] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -46.802719 | E_var:     0.1805 | E_err:   0.006638
[2025-10-06 15:30:13] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -46.812515 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 15:30:18] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -46.806342 | E_var:     0.1900 | E_err:   0.006811
[2025-10-06 15:30:23] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -46.812142 | E_var:     0.2744 | E_err:   0.008185
[2025-10-06 15:30:28] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -46.813111 | E_var:     0.1659 | E_err:   0.006365
[2025-10-06 15:30:33] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -46.801576 | E_var:     0.1656 | E_err:   0.006358
[2025-10-06 15:30:38] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -46.806219 | E_var:     0.1668 | E_err:   0.006382
[2025-10-06 15:30:44] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -46.806332 | E_var:     0.1788 | E_err:   0.006607
[2025-10-06 15:30:49] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -46.825741 | E_var:     0.1928 | E_err:   0.006860
[2025-10-06 15:30:54] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -46.813345 | E_var:     0.1890 | E_err:   0.006793
[2025-10-06 15:30:59] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -46.809847 | E_var:     0.2127 | E_err:   0.007207
[2025-10-06 15:31:04] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -46.812288 | E_var:     0.1919 | E_err:   0.006845
[2025-10-06 15:31:09] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -46.802889 | E_var:     0.1566 | E_err:   0.006184
[2025-10-06 15:31:14] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -46.786925 | E_var:     0.2929 | E_err:   0.008456
[2025-10-06 15:31:20] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -46.823897 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 15:31:25] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -46.806017 | E_var:     0.1597 | E_err:   0.006244
[2025-10-06 15:31:30] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -46.809730 | E_var:     0.1786 | E_err:   0.006602
[2025-10-06 15:31:35] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -46.810005 | E_var:     0.1760 | E_err:   0.006554
[2025-10-06 15:31:40] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -46.820356 | E_var:     0.1933 | E_err:   0.006871
[2025-10-06 15:31:45] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -46.809730 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 15:31:50] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -46.817346 | E_var:     0.1622 | E_err:   0.006292
[2025-10-06 15:31:56] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -46.812434 | E_var:     0.1730 | E_err:   0.006500
[2025-10-06 15:32:01] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -46.805718 | E_var:     0.1977 | E_err:   0.006947
[2025-10-06 15:32:06] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -46.812951 | E_var:     0.1459 | E_err:   0.005969
[2025-10-06 15:32:06] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 15:32:11] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -46.805132 | E_var:     0.1763 | E_err:   0.006562
[2025-10-06 15:32:16] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -46.815706 | E_var:     0.2157 | E_err:   0.007257
[2025-10-06 15:32:21] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -46.811052 | E_var:     0.1957 | E_err:   0.006913
[2025-10-06 15:32:26] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -46.789322 | E_var:     0.2011 | E_err:   0.007007
[2025-10-06 15:32:32] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -46.807115 | E_var:     0.2019 | E_err:   0.007020
[2025-10-06 15:32:37] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -46.808199 | E_var:     0.2603 | E_err:   0.007971
[2025-10-06 15:32:42] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -46.811510 | E_var:     0.1544 | E_err:   0.006139
[2025-10-06 15:32:47] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -46.809430 | E_var:     0.2368 | E_err:   0.007604
[2025-10-06 15:32:52] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -46.812171 | E_var:     0.2241 | E_err:   0.007396
[2025-10-06 15:32:57] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -46.804298 | E_var:     0.1525 | E_err:   0.006102
[2025-10-06 15:33:03] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -46.814905 | E_var:     0.1437 | E_err:   0.005923
[2025-10-06 15:33:08] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -46.815937 | E_var:     0.1696 | E_err:   0.006434
[2025-10-06 15:33:13] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -46.808828 | E_var:     0.1612 | E_err:   0.006274
[2025-10-06 15:33:18] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -46.811134 | E_var:     0.1977 | E_err:   0.006947
[2025-10-06 15:33:23] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -46.805472 | E_var:     0.1597 | E_err:   0.006244
[2025-10-06 15:33:28] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -46.817765 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 15:33:33] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -46.803552 | E_var:     0.2882 | E_err:   0.008388
[2025-10-06 15:33:39] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -46.814145 | E_var:     0.1652 | E_err:   0.006350
[2025-10-06 15:33:44] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -46.817335 | E_var:     0.1917 | E_err:   0.006841
[2025-10-06 15:33:49] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -46.809507 | E_var:     0.1804 | E_err:   0.006637
[2025-10-06 15:33:54] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -46.810228 | E_var:     0.1629 | E_err:   0.006306
[2025-10-06 15:33:59] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -46.825500 | E_var:     0.1895 | E_err:   0.006802
[2025-10-06 15:34:04] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -46.806045 | E_var:     0.2229 | E_err:   0.007377
[2025-10-06 15:34:09] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -46.814059 | E_var:     0.2117 | E_err:   0.007190
[2025-10-06 15:34:15] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -46.811458 | E_var:     0.2468 | E_err:   0.007762
[2025-10-06 15:34:20] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -46.800628 | E_var:     0.1806 | E_err:   0.006640
[2025-10-06 15:34:25] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -46.812044 | E_var:     0.1848 | E_err:   0.006718
[2025-10-06 15:34:30] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -46.809191 | E_var:     0.1919 | E_err:   0.006845
[2025-10-06 15:34:35] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -46.816288 | E_var:     0.1758 | E_err:   0.006551
[2025-10-06 15:34:40] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -46.796382 | E_var:     0.1711 | E_err:   0.006463
[2025-10-06 15:34:45] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -46.814104 | E_var:     0.1939 | E_err:   0.006881
[2025-10-06 15:34:51] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -46.807581 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 15:34:56] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -46.805486 | E_var:     0.1613 | E_err:   0.006276
[2025-10-06 15:35:01] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -46.819834 | E_var:     0.2091 | E_err:   0.007144
[2025-10-06 15:35:06] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -46.812359 | E_var:     0.2685 | E_err:   0.008096
[2025-10-06 15:35:11] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -46.821805 | E_var:     0.2012 | E_err:   0.007009
[2025-10-06 15:35:16] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -46.812059 | E_var:     0.2198 | E_err:   0.007326
[2025-10-06 15:35:21] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -46.813397 | E_var:     0.2261 | E_err:   0.007430
[2025-10-06 15:35:27] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -46.811737 | E_var:     0.2712 | E_err:   0.008137
[2025-10-06 15:35:32] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -46.809182 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 15:35:37] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -46.805388 | E_var:     0.1747 | E_err:   0.006531
[2025-10-06 15:35:42] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -46.809280 | E_var:     0.2097 | E_err:   0.007155
[2025-10-06 15:35:47] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -46.812050 | E_var:     0.1594 | E_err:   0.006239
[2025-10-06 15:35:52] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -46.819424 | E_var:     0.1823 | E_err:   0.006672
[2025-10-06 15:35:58] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -46.810230 | E_var:     0.1652 | E_err:   0.006350
[2025-10-06 15:36:03] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -46.807136 | E_var:     0.1893 | E_err:   0.006798
[2025-10-06 15:36:08] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -46.813716 | E_var:     0.2482 | E_err:   0.007785
[2025-10-06 15:36:13] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -46.813366 | E_var:     0.1914 | E_err:   0.006835
[2025-10-06 15:36:18] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -46.802452 | E_var:     0.2008 | E_err:   0.007002
[2025-10-06 15:36:23] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -46.818466 | E_var:     0.2006 | E_err:   0.006998
[2025-10-06 15:36:28] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -46.808954 | E_var:     0.2158 | E_err:   0.007258
[2025-10-06 15:36:34] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -46.813385 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 15:36:39] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -46.805314 | E_var:     0.2991 | E_err:   0.008546
[2025-10-06 15:36:44] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -46.814924 | E_var:     0.2355 | E_err:   0.007582
[2025-10-06 15:36:49] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -46.812813 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 15:36:54] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -46.812979 | E_var:     0.2126 | E_err:   0.007204
[2025-10-06 15:36:59] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -46.820764 | E_var:     0.1814 | E_err:   0.006654
[2025-10-06 15:37:04] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -46.816845 | E_var:     0.1896 | E_err:   0.006803
[2025-10-06 15:37:10] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -46.800897 | E_var:     0.2835 | E_err:   0.008319
[2025-10-06 15:37:15] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -46.800118 | E_var:     0.1658 | E_err:   0.006362
[2025-10-06 15:37:20] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -46.806564 | E_var:     0.1683 | E_err:   0.006410
[2025-10-06 15:37:25] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -46.818778 | E_var:     0.1900 | E_err:   0.006812
[2025-10-06 15:37:30] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -46.814338 | E_var:     0.1907 | E_err:   0.006823
[2025-10-06 15:37:35] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -46.804645 | E_var:     0.2023 | E_err:   0.007028
[2025-10-06 15:37:46] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -46.816368 | E_var:     0.1775 | E_err:   0.006584
[2025-10-06 15:37:52] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -46.815928 | E_var:     0.1552 | E_err:   0.006156
[2025-10-06 15:37:57] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -46.804501 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 15:38:02] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -46.798711 | E_var:     0.2401 | E_err:   0.007656
[2025-10-06 15:38:07] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -46.813688 | E_var:     0.1759 | E_err:   0.006554
[2025-10-06 15:38:12] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -46.807848 | E_var:     0.1382 | E_err:   0.005809
[2025-10-06 15:38:17] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -46.810948 | E_var:     0.1805 | E_err:   0.006638
[2025-10-06 15:38:23] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -46.807571 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 15:38:28] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -46.824119 | E_var:     0.2164 | E_err:   0.007269
[2025-10-06 15:38:33] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -46.817116 | E_var:     0.2030 | E_err:   0.007040
[2025-10-06 15:38:38] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -46.816550 | E_var:     0.2356 | E_err:   0.007585
[2025-10-06 15:38:43] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -46.813532 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 15:38:48] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -46.803655 | E_var:     0.1934 | E_err:   0.006871
[2025-10-06 15:38:53] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -46.816124 | E_var:     0.1501 | E_err:   0.006053
[2025-10-06 15:38:59] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -46.806678 | E_var:     0.1507 | E_err:   0.006066
[2025-10-06 15:39:04] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -46.815014 | E_var:     0.2620 | E_err:   0.007998
[2025-10-06 15:39:09] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -46.814059 | E_var:     0.1624 | E_err:   0.006296
[2025-10-06 15:39:14] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -46.835268 | E_var:     0.8835 | E_err:   0.014686
[2025-10-06 15:39:19] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -46.810724 | E_var:     0.1568 | E_err:   0.006187
[2025-10-06 15:39:24] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -46.803587 | E_var:     0.1499 | E_err:   0.006050
[2025-10-06 15:39:29] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -46.807864 | E_var:     0.1647 | E_err:   0.006341
[2025-10-06 15:39:35] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -46.803401 | E_var:     0.1381 | E_err:   0.005806
[2025-10-06 15:39:40] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -46.812409 | E_var:     0.2350 | E_err:   0.007574
[2025-10-06 15:39:45] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -46.825493 | E_var:     0.2831 | E_err:   0.008314
[2025-10-06 15:39:50] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -46.815250 | E_var:     0.1544 | E_err:   0.006140
[2025-10-06 15:39:55] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -46.807514 | E_var:     0.2019 | E_err:   0.007021
[2025-10-06 15:40:00] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -46.798630 | E_var:     0.1895 | E_err:   0.006801
[2025-10-06 15:40:06] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -46.810056 | E_var:     0.1641 | E_err:   0.006330
[2025-10-06 15:40:11] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -46.813638 | E_var:     0.1923 | E_err:   0.006852
[2025-10-06 15:40:16] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -46.808744 | E_var:     0.2457 | E_err:   0.007746
[2025-10-06 15:40:21] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -46.809596 | E_var:     0.1821 | E_err:   0.006667
[2025-10-06 15:40:26] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -46.816291 | E_var:     0.1719 | E_err:   0.006479
[2025-10-06 15:40:31] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -46.805512 | E_var:     0.2017 | E_err:   0.007018
[2025-10-06 15:40:36] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -46.809800 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 15:40:42] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -46.808870 | E_var:     0.1536 | E_err:   0.006124
[2025-10-06 15:40:47] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -46.792133 | E_var:     0.5562 | E_err:   0.011653
[2025-10-06 15:40:47] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 15:40:52] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -46.811849 | E_var:     0.1602 | E_err:   0.006254
[2025-10-06 15:40:57] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -46.814187 | E_var:     0.2157 | E_err:   0.007256
[2025-10-06 15:41:02] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -46.796023 | E_var:     0.2215 | E_err:   0.007354
[2025-10-06 15:41:07] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -46.806818 | E_var:     0.1754 | E_err:   0.006544
[2025-10-06 15:41:13] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -46.808332 | E_var:     0.1702 | E_err:   0.006447
[2025-10-06 15:41:18] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -46.811118 | E_var:     0.1486 | E_err:   0.006022
[2025-10-06 15:41:23] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -46.819297 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 15:41:28] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -46.810211 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 15:41:33] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -46.812400 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 15:41:38] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -46.799683 | E_var:     0.2376 | E_err:   0.007616
[2025-10-06 15:41:43] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -46.820642 | E_var:     0.1724 | E_err:   0.006487
[2025-10-06 15:41:49] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -46.818377 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 15:41:54] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -46.822137 | E_var:     0.1506 | E_err:   0.006063
[2025-10-06 15:41:59] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -46.814261 | E_var:     0.2183 | E_err:   0.007301
[2025-10-06 15:42:04] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -46.813916 | E_var:     0.1433 | E_err:   0.005914
[2025-10-06 15:42:09] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -46.800828 | E_var:     0.1918 | E_err:   0.006843
[2025-10-06 15:42:14] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -46.815752 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 15:42:19] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -46.821652 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 15:42:25] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -46.806274 | E_var:     0.1857 | E_err:   0.006733
[2025-10-06 15:42:30] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -46.806824 | E_var:     0.2010 | E_err:   0.007005
[2025-10-06 15:42:35] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -46.824542 | E_var:     0.1816 | E_err:   0.006659
[2025-10-06 15:42:40] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -46.813571 | E_var:     0.2149 | E_err:   0.007243
[2025-10-06 15:42:45] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -46.805577 | E_var:     0.1526 | E_err:   0.006104
[2025-10-06 15:42:50] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -46.816524 | E_var:     0.1729 | E_err:   0.006496
[2025-10-06 15:42:55] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -46.808413 | E_var:     0.1711 | E_err:   0.006463
[2025-10-06 15:43:01] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -46.813553 | E_var:     0.1685 | E_err:   0.006415
[2025-10-06 15:43:06] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -46.821847 | E_var:     0.1284 | E_err:   0.005599
[2025-10-06 15:43:11] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -46.802514 | E_var:     0.1902 | E_err:   0.006815
[2025-10-06 15:43:16] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -46.802612 | E_var:     0.1824 | E_err:   0.006673
[2025-10-06 15:43:21] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -46.815020 | E_var:     0.1804 | E_err:   0.006636
[2025-10-06 15:43:26] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -46.820197 | E_var:     0.1548 | E_err:   0.006148
[2025-10-06 15:43:32] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -46.812434 | E_var:     0.2096 | E_err:   0.007154
[2025-10-06 15:43:37] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -46.810088 | E_var:     0.2230 | E_err:   0.007378
[2025-10-06 15:43:42] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -46.812235 | E_var:     0.1591 | E_err:   0.006232
[2025-10-06 15:43:47] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -46.810478 | E_var:     0.1965 | E_err:   0.006927
[2025-10-06 15:43:52] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -46.820207 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 15:43:57] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -46.814544 | E_var:     0.2083 | E_err:   0.007131
[2025-10-06 15:44:02] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -46.814583 | E_var:     0.1757 | E_err:   0.006550
[2025-10-06 15:44:08] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -46.812339 | E_var:     0.1765 | E_err:   0.006565
[2025-10-06 15:44:13] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -46.800338 | E_var:     0.1882 | E_err:   0.006779
[2025-10-06 15:44:18] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -46.801548 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 15:44:23] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -46.801414 | E_var:     0.1558 | E_err:   0.006168
[2025-10-06 15:44:28] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -46.819745 | E_var:     0.1746 | E_err:   0.006530
[2025-10-06 15:44:33] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -46.810411 | E_var:     0.3438 | E_err:   0.009161
[2025-10-06 15:44:38] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -46.822345 | E_var:     0.1881 | E_err:   0.006776
[2025-10-06 15:44:44] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -46.818296 | E_var:     0.2056 | E_err:   0.007085
[2025-10-06 15:44:49] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -46.806832 | E_var:     0.2585 | E_err:   0.007945
[2025-10-06 15:44:54] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -46.799024 | E_var:     0.2147 | E_err:   0.007240
[2025-10-06 15:44:59] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -46.815138 | E_var:     0.2110 | E_err:   0.007177
[2025-10-06 15:45:04] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -46.803210 | E_var:     0.1826 | E_err:   0.006676
[2025-10-06 15:45:09] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -46.816988 | E_var:     0.1932 | E_err:   0.006867
[2025-10-06 15:45:14] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -46.812219 | E_var:     0.1533 | E_err:   0.006117
[2025-10-06 15:45:20] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -46.812183 | E_var:     0.1809 | E_err:   0.006645
[2025-10-06 15:45:25] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -46.813206 | E_var:     0.2039 | E_err:   0.007056
[2025-10-06 15:45:30] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -46.809798 | E_var:     0.2027 | E_err:   0.007035
[2025-10-06 15:45:35] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -46.815091 | E_var:     0.1659 | E_err:   0.006365
[2025-10-06 15:45:40] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -46.803220 | E_var:     0.2646 | E_err:   0.008037
[2025-10-06 15:45:45] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -46.807430 | E_var:     0.1645 | E_err:   0.006337
[2025-10-06 15:45:51] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -46.811853 | E_var:     0.1524 | E_err:   0.006101
[2025-10-06 15:45:56] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -46.800719 | E_var:     0.1740 | E_err:   0.006518
[2025-10-06 15:46:01] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -46.820125 | E_var:     0.1883 | E_err:   0.006780
[2025-10-06 15:46:06] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -46.817475 | E_var:     0.1661 | E_err:   0.006368
[2025-10-06 15:46:11] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -46.807712 | E_var:     0.2407 | E_err:   0.007665
[2025-10-06 15:46:16] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -46.814920 | E_var:     0.1986 | E_err:   0.006964
[2025-10-06 15:46:21] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -46.812280 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 15:46:27] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -46.808826 | E_var:     0.2841 | E_err:   0.008328
[2025-10-06 15:46:32] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -46.803163 | E_var:     0.1938 | E_err:   0.006879
[2025-10-06 15:46:37] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -46.807735 | E_var:     0.1921 | E_err:   0.006849
[2025-10-06 15:46:42] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -46.806932 | E_var:     0.2006 | E_err:   0.006999
[2025-10-06 15:46:47] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -46.803643 | E_var:     0.2013 | E_err:   0.007010
[2025-10-06 15:46:52] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -46.813287 | E_var:     0.4054 | E_err:   0.009948
[2025-10-06 15:46:57] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -46.804268 | E_var:     0.1417 | E_err:   0.005881
[2025-10-06 15:47:03] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -46.805140 | E_var:     0.1759 | E_err:   0.006552
[2025-10-06 15:47:08] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -46.827116 | E_var:     0.1867 | E_err:   0.006751
[2025-10-06 15:47:13] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -46.813286 | E_var:     0.1610 | E_err:   0.006269
[2025-10-06 15:47:18] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -46.813212 | E_var:     0.1592 | E_err:   0.006234
[2025-10-06 15:47:23] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -46.814927 | E_var:     0.1526 | E_err:   0.006103
[2025-10-06 15:47:28] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -46.806143 | E_var:     0.1738 | E_err:   0.006514
[2025-10-06 15:47:33] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -46.819674 | E_var:     0.1917 | E_err:   0.006840
[2025-10-06 15:47:39] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -46.824801 | E_var:     0.2105 | E_err:   0.007168
[2025-10-06 15:47:44] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -46.801706 | E_var:     0.2383 | E_err:   0.007628
[2025-10-06 15:47:49] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -46.811190 | E_var:     0.1641 | E_err:   0.006330
[2025-10-06 15:47:54] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -46.806565 | E_var:     0.1728 | E_err:   0.006495
[2025-10-06 15:47:59] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -46.794602 | E_var:     0.1665 | E_err:   0.006375
[2025-10-06 15:48:04] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -46.823656 | E_var:     0.1801 | E_err:   0.006631
[2025-10-06 15:48:09] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -46.811441 | E_var:     0.2054 | E_err:   0.007081
[2025-10-06 15:48:15] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -46.802060 | E_var:     0.1585 | E_err:   0.006220
[2025-10-06 15:48:20] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -46.811717 | E_var:     0.2372 | E_err:   0.007609
[2025-10-06 15:48:25] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -46.814965 | E_var:     0.1646 | E_err:   0.006340
[2025-10-06 15:48:30] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -46.819803 | E_var:     0.1821 | E_err:   0.006667
[2025-10-06 15:48:35] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -46.819186 | E_var:     0.3406 | E_err:   0.009119
[2025-10-06 15:48:40] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -46.809903 | E_var:     0.1673 | E_err:   0.006391
[2025-10-06 15:48:46] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -46.812207 | E_var:     0.1631 | E_err:   0.006310
[2025-10-06 15:48:51] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -46.789734 | E_var:     0.6357 | E_err:   0.012458
[2025-10-06 15:48:56] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -46.804523 | E_var:     0.2248 | E_err:   0.007409
[2025-10-06 15:49:01] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -46.821624 | E_var:     0.1949 | E_err:   0.006898
[2025-10-06 15:49:06] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -46.814445 | E_var:     0.2634 | E_err:   0.008019
[2025-10-06 15:49:11] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -46.815776 | E_var:     0.1719 | E_err:   0.006479
[2025-10-06 15:49:16] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -46.816165 | E_var:     0.1688 | E_err:   0.006420
[2025-10-06 15:49:22] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -46.799994 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 15:49:22] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 15:49:27] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -46.821977 | E_var:     0.2021 | E_err:   0.007025
[2025-10-06 15:49:32] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -46.824354 | E_var:     0.2122 | E_err:   0.007197
[2025-10-06 15:49:37] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -46.804051 | E_var:     0.1819 | E_err:   0.006663
[2025-10-06 15:49:42] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -46.803967 | E_var:     0.1896 | E_err:   0.006803
[2025-10-06 15:49:47] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -46.813811 | E_var:     0.3355 | E_err:   0.009050
[2025-10-06 15:49:53] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -46.816681 | E_var:     0.1782 | E_err:   0.006595
[2025-10-06 15:49:58] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -46.801656 | E_var:     0.2870 | E_err:   0.008371
[2025-10-06 15:50:03] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -46.809481 | E_var:     0.1905 | E_err:   0.006819
[2025-10-06 15:50:08] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -46.808859 | E_var:     0.1904 | E_err:   0.006817
[2025-10-06 15:50:13] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -46.816167 | E_var:     0.1862 | E_err:   0.006743
[2025-10-06 15:50:18] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -46.803800 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 15:50:23] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -46.796493 | E_var:     0.1853 | E_err:   0.006726
[2025-10-06 15:50:29] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -46.813464 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 15:50:34] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -46.814395 | E_var:     0.1610 | E_err:   0.006269
[2025-10-06 15:50:39] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -46.813368 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 15:50:44] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -46.818398 | E_var:     0.2166 | E_err:   0.007273
[2025-10-06 15:50:49] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -46.811556 | E_var:     0.2317 | E_err:   0.007521
[2025-10-06 15:50:54] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -46.808464 | E_var:     0.1756 | E_err:   0.006548
[2025-10-06 15:50:59] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -46.799956 | E_var:     0.3311 | E_err:   0.008990
[2025-10-06 15:51:05] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -46.813012 | E_var:     0.1943 | E_err:   0.006887
[2025-10-06 15:51:10] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -46.804972 | E_var:     0.1831 | E_err:   0.006686
[2025-10-06 15:51:15] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -46.810707 | E_var:     0.1490 | E_err:   0.006032
[2025-10-06 15:51:20] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -46.813972 | E_var:     0.2391 | E_err:   0.007640
[2025-10-06 15:51:25] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -46.820417 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 15:51:30] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -46.820099 | E_var:     0.1912 | E_err:   0.006832
[2025-10-06 15:51:35] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -46.806125 | E_var:     0.1648 | E_err:   0.006344
[2025-10-06 15:51:41] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -46.827000 | E_var:     0.1732 | E_err:   0.006504
[2025-10-06 15:51:46] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -46.808014 | E_var:     0.1546 | E_err:   0.006144
[2025-10-06 15:51:51] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -46.814725 | E_var:     0.1968 | E_err:   0.006932
[2025-10-06 15:51:56] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -46.805901 | E_var:     0.1632 | E_err:   0.006312
[2025-10-06 15:52:01] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -46.798317 | E_var:     0.2267 | E_err:   0.007439
[2025-10-06 15:52:06] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -46.805013 | E_var:     0.1671 | E_err:   0.006387
[2025-10-06 15:52:11] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -46.804359 | E_var:     0.1742 | E_err:   0.006522
[2025-10-06 15:52:17] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -46.810898 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 15:52:22] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -46.818750 | E_var:     0.1620 | E_err:   0.006290
[2025-10-06 15:52:27] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -46.806133 | E_var:     0.1744 | E_err:   0.006526
[2025-10-06 15:52:32] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -46.829273 | E_var:     0.2237 | E_err:   0.007390
[2025-10-06 15:52:37] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -46.806987 | E_var:     0.2344 | E_err:   0.007564
[2025-10-06 15:52:42] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -46.812181 | E_var:     0.2185 | E_err:   0.007304
[2025-10-06 15:52:48] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -46.806873 | E_var:     0.2173 | E_err:   0.007284
[2025-10-06 15:52:53] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -46.809751 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 15:52:58] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -46.814047 | E_var:     0.1791 | E_err:   0.006612
[2025-10-06 15:53:03] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -46.820304 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 15:53:08] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -46.802279 | E_var:     0.2018 | E_err:   0.007019
[2025-10-06 15:53:13] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -46.803927 | E_var:     0.2259 | E_err:   0.007426
[2025-10-06 15:53:18] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -46.807569 | E_var:     0.2311 | E_err:   0.007511
[2025-10-06 15:53:24] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -46.820615 | E_var:     0.2249 | E_err:   0.007410
[2025-10-06 15:53:29] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -46.809389 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 15:53:34] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -46.814271 | E_var:     0.2061 | E_err:   0.007093
[2025-10-06 15:53:39] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -46.801912 | E_var:     0.2140 | E_err:   0.007228
[2025-10-06 15:53:39] 🔄 RESTART #2 | Period: 600
[2025-10-06 15:53:44] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -46.817043 | E_var:     0.1745 | E_err:   0.006527
[2025-10-06 15:53:49] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -46.812403 | E_var:     0.2546 | E_err:   0.007884
[2025-10-06 15:53:54] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -46.807780 | E_var:     0.2025 | E_err:   0.007032
[2025-10-06 15:54:00] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -46.811222 | E_var:     0.2017 | E_err:   0.007018
[2025-10-06 15:54:05] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -46.806051 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 15:54:10] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -46.815942 | E_var:     0.1535 | E_err:   0.006123
[2025-10-06 15:54:15] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -46.810615 | E_var:     0.3097 | E_err:   0.008695
[2025-10-06 15:54:20] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -46.803799 | E_var:     0.1601 | E_err:   0.006252
[2025-10-06 15:54:25] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -46.814919 | E_var:     0.2003 | E_err:   0.006993
[2025-10-06 15:54:30] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -46.805155 | E_var:     0.2529 | E_err:   0.007857
[2025-10-06 15:54:36] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -46.808398 | E_var:     0.2098 | E_err:   0.007157
[2025-10-06 15:54:41] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -46.796155 | E_var:     0.1699 | E_err:   0.006440
[2025-10-06 15:54:46] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -46.814135 | E_var:     0.1697 | E_err:   0.006436
[2025-10-06 15:54:51] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -46.817375 | E_var:     0.2773 | E_err:   0.008228
[2025-10-06 15:54:56] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -46.800493 | E_var:     0.2785 | E_err:   0.008246
[2025-10-06 15:55:01] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -46.818264 | E_var:     0.1580 | E_err:   0.006212
[2025-10-06 15:55:06] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -46.808719 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 15:55:12] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -46.814585 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 15:55:17] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -46.825512 | E_var:     0.2472 | E_err:   0.007769
[2025-10-06 15:55:22] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -46.804089 | E_var:     0.2338 | E_err:   0.007555
[2025-10-06 15:55:27] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -46.813093 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 15:55:32] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -46.800172 | E_var:     0.1896 | E_err:   0.006803
[2025-10-06 15:55:37] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -46.809495 | E_var:     0.1433 | E_err:   0.005915
[2025-10-06 15:55:43] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -46.811164 | E_var:     0.2521 | E_err:   0.007845
[2025-10-06 15:55:48] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -46.804154 | E_var:     0.1791 | E_err:   0.006612
[2025-10-06 15:55:53] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -46.818388 | E_var:     0.3503 | E_err:   0.009248
[2025-10-06 15:55:58] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -46.818062 | E_var:     0.2426 | E_err:   0.007695
[2025-10-06 15:56:03] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -46.821587 | E_var:     0.1930 | E_err:   0.006864
[2025-10-06 15:56:08] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -46.817754 | E_var:     0.1588 | E_err:   0.006227
[2025-10-06 15:56:13] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -46.814469 | E_var:     0.1594 | E_err:   0.006238
[2025-10-06 15:56:19] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -46.813205 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 15:56:24] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -46.818256 | E_var:     0.1918 | E_err:   0.006842
[2025-10-06 15:56:29] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -46.808714 | E_var:     0.2580 | E_err:   0.007936
[2025-10-06 15:56:34] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -46.805726 | E_var:     0.2115 | E_err:   0.007186
[2025-10-06 15:56:39] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -46.818407 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 15:56:44] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -46.814085 | E_var:     0.1921 | E_err:   0.006848
[2025-10-06 15:56:49] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -46.806430 | E_var:     0.3127 | E_err:   0.008738
[2025-10-06 15:56:55] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -46.815208 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 15:57:00] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -46.816007 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 15:57:05] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -46.809706 | E_var:     0.1727 | E_err:   0.006492
[2025-10-06 15:57:10] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -46.814366 | E_var:     0.1914 | E_err:   0.006837
[2025-10-06 15:57:15] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -46.816639 | E_var:     0.2134 | E_err:   0.007218
[2025-10-06 15:57:20] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -46.811048 | E_var:     0.2924 | E_err:   0.008448
[2025-10-06 15:57:25] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -46.822369 | E_var:     0.1723 | E_err:   0.006486
[2025-10-06 15:57:31] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -46.813024 | E_var:     0.1715 | E_err:   0.006470
[2025-10-06 15:57:36] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -46.806635 | E_var:     0.1793 | E_err:   0.006615
[2025-10-06 15:57:41] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -46.816304 | E_var:     0.1692 | E_err:   0.006428
[2025-10-06 15:57:46] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -46.802714 | E_var:     0.2451 | E_err:   0.007735
[2025-10-06 15:57:51] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -46.816748 | E_var:     0.1963 | E_err:   0.006923
[2025-10-06 15:57:56] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -46.822293 | E_var:     0.1788 | E_err:   0.006608
[2025-10-06 15:57:56] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 15:58:02] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -46.818852 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 15:58:07] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -46.812812 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 15:58:12] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -46.818365 | E_var:     0.1698 | E_err:   0.006438
[2025-10-06 15:58:17] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -46.795004 | E_var:     0.2406 | E_err:   0.007664
[2025-10-06 15:58:22] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -46.818901 | E_var:     0.2407 | E_err:   0.007666
[2025-10-06 15:58:27] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -46.805380 | E_var:     0.1713 | E_err:   0.006467
[2025-10-06 15:58:32] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -46.821056 | E_var:     0.1462 | E_err:   0.005974
[2025-10-06 15:58:38] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -46.811141 | E_var:     0.2071 | E_err:   0.007111
[2025-10-06 15:58:43] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -46.804706 | E_var:     0.1673 | E_err:   0.006392
[2025-10-06 15:58:48] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -46.814828 | E_var:     0.3398 | E_err:   0.009108
[2025-10-06 15:58:53] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -46.810513 | E_var:     0.2127 | E_err:   0.007206
[2025-10-06 15:58:58] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -46.810099 | E_var:     0.3379 | E_err:   0.009083
[2025-10-06 15:59:03] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -46.806568 | E_var:     0.1899 | E_err:   0.006809
[2025-10-06 15:59:08] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -46.806497 | E_var:     0.1760 | E_err:   0.006555
[2025-10-06 15:59:14] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -46.803448 | E_var:     0.1579 | E_err:   0.006208
[2025-10-06 15:59:19] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -46.814450 | E_var:     0.1525 | E_err:   0.006103
[2025-10-06 15:59:24] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -46.806507 | E_var:     0.2626 | E_err:   0.008007
[2025-10-06 15:59:29] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -46.813624 | E_var:     0.1954 | E_err:   0.006906
[2025-10-06 15:59:34] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -46.808557 | E_var:     0.1882 | E_err:   0.006778
[2025-10-06 15:59:39] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -46.800073 | E_var:     0.2478 | E_err:   0.007778
[2025-10-06 15:59:44] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -46.809847 | E_var:     0.1838 | E_err:   0.006700
[2025-10-06 15:59:50] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -46.816609 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 15:59:55] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -46.813053 | E_var:     0.1725 | E_err:   0.006490
[2025-10-06 16:00:00] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -46.820758 | E_var:     0.1793 | E_err:   0.006616
[2025-10-06 16:00:05] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -46.818957 | E_var:     0.1620 | E_err:   0.006288
[2025-10-06 16:00:10] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -46.816141 | E_var:     0.1603 | E_err:   0.006255
[2025-10-06 16:00:15] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -46.810938 | E_var:     0.2000 | E_err:   0.006987
[2025-10-06 16:00:20] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -46.810721 | E_var:     0.2020 | E_err:   0.007023
[2025-10-06 16:00:26] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -46.821539 | E_var:     0.1484 | E_err:   0.006018
[2025-10-06 16:00:31] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -46.806996 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 16:00:36] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -46.815732 | E_var:     0.2205 | E_err:   0.007337
[2025-10-06 16:00:41] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -46.810701 | E_var:     0.2492 | E_err:   0.007800
[2025-10-06 16:00:46] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -46.819354 | E_var:     0.1478 | E_err:   0.006006
[2025-10-06 16:00:51] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -46.813535 | E_var:     0.2242 | E_err:   0.007399
[2025-10-06 16:00:57] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -46.816364 | E_var:     0.2152 | E_err:   0.007249
[2025-10-06 16:01:02] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -46.812855 | E_var:     0.1415 | E_err:   0.005878
[2025-10-06 16:01:07] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -46.817880 | E_var:     0.1950 | E_err:   0.006900
[2025-10-06 16:01:12] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -46.812060 | E_var:     0.1529 | E_err:   0.006110
[2025-10-06 16:01:17] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -46.807629 | E_var:     0.1805 | E_err:   0.006638
[2025-10-06 16:01:22] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -46.808183 | E_var:     0.3242 | E_err:   0.008896
[2025-10-06 16:01:27] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -46.800126 | E_var:     0.2265 | E_err:   0.007437
[2025-10-06 16:01:33] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -46.802246 | E_var:     0.1653 | E_err:   0.006352
[2025-10-06 16:01:38] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -46.823554 | E_var:     0.1410 | E_err:   0.005867
[2025-10-06 16:01:43] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -46.820993 | E_var:     0.1629 | E_err:   0.006306
[2025-10-06 16:01:48] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -46.809986 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 16:01:53] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -46.818592 | E_var:     0.1512 | E_err:   0.006075
[2025-10-06 16:01:58] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -46.813168 | E_var:     0.1832 | E_err:   0.006687
[2025-10-06 16:02:03] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -46.813439 | E_var:     0.2116 | E_err:   0.007187
[2025-10-06 16:02:09] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -46.790701 | E_var:     0.1960 | E_err:   0.006918
[2025-10-06 16:02:14] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -46.810151 | E_var:     0.1864 | E_err:   0.006745
[2025-10-06 16:02:19] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -46.808382 | E_var:     0.1894 | E_err:   0.006799
[2025-10-06 16:02:24] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -46.813610 | E_var:     0.1478 | E_err:   0.006007
[2025-10-06 16:02:29] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -46.814659 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 16:02:34] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -46.811866 | E_var:     0.1803 | E_err:   0.006635
[2025-10-06 16:02:39] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -46.820987 | E_var:     0.1640 | E_err:   0.006328
[2025-10-06 16:02:45] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -46.815505 | E_var:     0.2432 | E_err:   0.007705
[2025-10-06 16:02:50] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -46.807688 | E_var:     0.1659 | E_err:   0.006365
[2025-10-06 16:02:55] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -46.811294 | E_var:     0.2514 | E_err:   0.007834
[2025-10-06 16:03:00] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -46.809453 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 16:03:05] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -46.803201 | E_var:     0.2817 | E_err:   0.008293
[2025-10-06 16:03:10] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -46.808869 | E_var:     0.1983 | E_err:   0.006958
[2025-10-06 16:03:15] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -46.812456 | E_var:     0.1788 | E_err:   0.006607
[2025-10-06 16:03:21] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -46.812479 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 16:03:26] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -46.811549 | E_var:     0.2537 | E_err:   0.007870
[2025-10-06 16:03:31] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -46.807601 | E_var:     0.1722 | E_err:   0.006484
[2025-10-06 16:03:36] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -46.813828 | E_var:     0.1973 | E_err:   0.006941
[2025-10-06 16:03:41] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -46.815711 | E_var:     0.2308 | E_err:   0.007506
[2025-10-06 16:03:46] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -46.813263 | E_var:     0.1582 | E_err:   0.006216
[2025-10-06 16:03:51] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -46.813624 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 16:03:57] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -46.802329 | E_var:     0.2026 | E_err:   0.007033
[2025-10-06 16:04:02] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -46.814991 | E_var:     0.1516 | E_err:   0.006084
[2025-10-06 16:04:07] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -46.806492 | E_var:     0.1556 | E_err:   0.006164
[2025-10-06 16:04:12] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -46.811802 | E_var:     0.2164 | E_err:   0.007269
[2025-10-06 16:04:17] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -46.806599 | E_var:     0.1968 | E_err:   0.006932
[2025-10-06 16:04:22] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -46.810930 | E_var:     0.1561 | E_err:   0.006173
[2025-10-06 16:04:27] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -46.806801 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 16:04:33] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -46.811114 | E_var:     0.2178 | E_err:   0.007291
[2025-10-06 16:04:38] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -46.816921 | E_var:     0.1949 | E_err:   0.006898
[2025-10-06 16:04:43] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -46.806780 | E_var:     0.2866 | E_err:   0.008365
[2025-10-06 16:04:48] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -46.809647 | E_var:     0.1902 | E_err:   0.006814
[2025-10-06 16:04:53] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -46.813984 | E_var:     0.1910 | E_err:   0.006829
[2025-10-06 16:04:58] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -46.825148 | E_var:     0.1932 | E_err:   0.006867
[2025-10-06 16:05:03] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -46.819916 | E_var:     0.1768 | E_err:   0.006570
[2025-10-06 16:05:09] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -46.807176 | E_var:     0.1751 | E_err:   0.006539
[2025-10-06 16:05:14] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -46.807205 | E_var:     0.1717 | E_err:   0.006475
[2025-10-06 16:05:19] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -46.817468 | E_var:     0.1781 | E_err:   0.006594
[2025-10-06 16:05:24] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -46.802355 | E_var:     0.1764 | E_err:   0.006563
[2025-10-06 16:05:29] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -46.811521 | E_var:     0.2663 | E_err:   0.008063
[2025-10-06 16:05:34] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -46.813284 | E_var:     0.1870 | E_err:   0.006757
[2025-10-06 16:05:40] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -46.819776 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 16:05:45] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -46.799413 | E_var:     0.2289 | E_err:   0.007475
[2025-10-06 16:05:50] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -46.808708 | E_var:     0.2203 | E_err:   0.007333
[2025-10-06 16:05:55] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -46.816613 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 16:06:00] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -46.817091 | E_var:     0.1516 | E_err:   0.006084
[2025-10-06 16:06:05] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -46.818196 | E_var:     0.1824 | E_err:   0.006674
[2025-10-06 16:06:10] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -46.816726 | E_var:     0.1704 | E_err:   0.006450
[2025-10-06 16:06:16] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -46.807767 | E_var:     0.1916 | E_err:   0.006840
[2025-10-06 16:06:21] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -46.816078 | E_var:     0.1634 | E_err:   0.006316
[2025-10-06 16:06:26] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -46.806523 | E_var:     0.1772 | E_err:   0.006578
[2025-10-06 16:06:31] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -46.811279 | E_var:     0.1992 | E_err:   0.006974
[2025-10-06 16:06:31] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 16:06:36] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -46.805882 | E_var:     0.1636 | E_err:   0.006319
[2025-10-06 16:06:41] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -46.818517 | E_var:     0.1853 | E_err:   0.006726
[2025-10-06 16:06:47] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -46.822806 | E_var:     0.1882 | E_err:   0.006779
[2025-10-06 16:06:52] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -46.808292 | E_var:     0.1945 | E_err:   0.006890
[2025-10-06 16:06:57] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -46.807325 | E_var:     0.2906 | E_err:   0.008423
[2025-10-06 16:07:02] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -46.808730 | E_var:     0.1825 | E_err:   0.006675
[2025-10-06 16:07:07] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -46.812849 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 16:07:12] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -46.811844 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 16:07:17] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -46.811807 | E_var:     0.1552 | E_err:   0.006156
[2025-10-06 16:07:23] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -46.815576 | E_var:     0.2083 | E_err:   0.007131
[2025-10-06 16:07:28] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -46.802939 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 16:07:33] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -46.817512 | E_var:     0.2153 | E_err:   0.007250
[2025-10-06 16:07:38] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -46.804863 | E_var:     0.1878 | E_err:   0.006772
[2025-10-06 16:07:43] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -46.815271 | E_var:     0.2511 | E_err:   0.007829
[2025-10-06 16:07:48] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -46.809535 | E_var:     0.1683 | E_err:   0.006410
[2025-10-06 16:07:53] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -46.787111 | E_var:     1.4423 | E_err:   0.018765
[2025-10-06 16:07:59] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -46.799450 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 16:08:04] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -46.815265 | E_var:     0.2195 | E_err:   0.007321
[2025-10-06 16:08:09] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -46.808019 | E_var:     0.1838 | E_err:   0.006699
[2025-10-06 16:08:14] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -46.801372 | E_var:     0.1673 | E_err:   0.006391
[2025-10-06 16:08:19] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -46.809387 | E_var:     0.1888 | E_err:   0.006788
[2025-10-06 16:08:24] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -46.809675 | E_var:     0.1738 | E_err:   0.006514
[2025-10-06 16:08:29] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -46.808998 | E_var:     0.2043 | E_err:   0.007063
[2025-10-06 16:08:35] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -46.805594 | E_var:     0.1624 | E_err:   0.006297
[2025-10-06 16:08:40] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -46.814602 | E_var:     0.2073 | E_err:   0.007115
[2025-10-06 16:08:45] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -46.808565 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 16:08:50] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -46.811815 | E_var:     0.3051 | E_err:   0.008630
[2025-10-06 16:08:55] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -46.807868 | E_var:     0.1541 | E_err:   0.006133
[2025-10-06 16:09:00] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -46.813230 | E_var:     0.1615 | E_err:   0.006278
[2025-10-06 16:09:06] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -46.802673 | E_var:     0.2078 | E_err:   0.007123
[2025-10-06 16:09:11] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -46.815872 | E_var:     0.1738 | E_err:   0.006513
[2025-10-06 16:09:16] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -46.813517 | E_var:     0.1652 | E_err:   0.006351
[2025-10-06 16:09:21] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -46.821300 | E_var:     0.1983 | E_err:   0.006958
[2025-10-06 16:09:26] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -46.817658 | E_var:     0.2009 | E_err:   0.007003
[2025-10-06 16:09:31] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -46.803059 | E_var:     0.1438 | E_err:   0.005924
[2025-10-06 16:09:36] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -46.812583 | E_var:     0.1650 | E_err:   0.006347
[2025-10-06 16:09:42] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -46.811722 | E_var:     0.1837 | E_err:   0.006697
[2025-10-06 16:09:47] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -46.811066 | E_var:     0.1536 | E_err:   0.006124
[2025-10-06 16:09:52] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -46.812687 | E_var:     0.2065 | E_err:   0.007100
[2025-10-06 16:09:57] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -46.810289 | E_var:     0.2065 | E_err:   0.007100
[2025-10-06 16:10:02] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -46.818202 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 16:10:07] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -46.814858 | E_var:     0.1554 | E_err:   0.006159
[2025-10-06 16:10:12] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -46.808758 | E_var:     0.2109 | E_err:   0.007176
[2025-10-06 16:10:18] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -46.817181 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 16:10:23] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -46.803431 | E_var:     0.2114 | E_err:   0.007185
[2025-10-06 16:10:28] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -46.806045 | E_var:     0.1653 | E_err:   0.006352
[2025-10-06 16:10:33] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -46.797292 | E_var:     0.2156 | E_err:   0.007255
[2025-10-06 16:10:38] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -46.809196 | E_var:     0.1694 | E_err:   0.006432
[2025-10-06 16:10:43] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -46.818260 | E_var:     0.1616 | E_err:   0.006281
[2025-10-06 16:10:48] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -46.805787 | E_var:     0.2195 | E_err:   0.007321
[2025-10-06 16:10:54] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -46.812188 | E_var:     0.1596 | E_err:   0.006243
[2025-10-06 16:10:59] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -46.818418 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 16:11:04] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -46.810864 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 16:11:09] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -46.805740 | E_var:     0.2421 | E_err:   0.007688
[2025-10-06 16:11:14] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -46.819512 | E_var:     0.2496 | E_err:   0.007806
[2025-10-06 16:11:19] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -46.807283 | E_var:     0.1681 | E_err:   0.006407
[2025-10-06 16:11:24] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -46.821205 | E_var:     0.2825 | E_err:   0.008304
[2025-10-06 16:11:30] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -46.820031 | E_var:     0.2106 | E_err:   0.007170
[2025-10-06 16:11:35] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -46.810735 | E_var:     0.2527 | E_err:   0.007855
[2025-10-06 16:11:40] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -46.808072 | E_var:     0.1690 | E_err:   0.006424
[2025-10-06 16:11:45] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -46.825135 | E_var:     0.2101 | E_err:   0.007162
[2025-10-06 16:11:50] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -46.806950 | E_var:     0.1653 | E_err:   0.006352
[2025-10-06 16:11:55] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -46.807478 | E_var:     0.1753 | E_err:   0.006542
[2025-10-06 16:12:00] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -46.827440 | E_var:     0.1834 | E_err:   0.006691
[2025-10-06 16:12:06] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -46.808018 | E_var:     0.1958 | E_err:   0.006913
[2025-10-06 16:12:11] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -46.820885 | E_var:     0.1885 | E_err:   0.006783
[2025-10-06 16:12:16] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -46.803669 | E_var:     0.2429 | E_err:   0.007700
[2025-10-06 16:12:21] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -46.813959 | E_var:     0.1720 | E_err:   0.006480
[2025-10-06 16:12:26] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -46.815226 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 16:12:31] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -46.820439 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 16:12:37] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -46.810584 | E_var:     0.2279 | E_err:   0.007458
[2025-10-06 16:12:42] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -46.808506 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 16:12:47] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -46.808903 | E_var:     0.1772 | E_err:   0.006578
[2025-10-06 16:12:52] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -46.812147 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 16:12:57] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -46.807391 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 16:13:02] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -46.828277 | E_var:     0.1726 | E_err:   0.006491
[2025-10-06 16:13:07] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -46.814192 | E_var:     0.3262 | E_err:   0.008924
[2025-10-06 16:13:13] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -46.814931 | E_var:     0.2185 | E_err:   0.007304
[2025-10-06 16:13:18] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -46.813541 | E_var:     0.2362 | E_err:   0.007593
[2025-10-06 16:13:23] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -46.805038 | E_var:     0.1900 | E_err:   0.006810
[2025-10-06 16:13:28] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -46.813842 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 16:13:33] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -46.826275 | E_var:     0.1903 | E_err:   0.006816
[2025-10-06 16:13:38] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -46.819438 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 16:13:43] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -46.802231 | E_var:     0.1858 | E_err:   0.006734
[2025-10-06 16:13:49] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -46.809554 | E_var:     0.2767 | E_err:   0.008219
[2025-10-06 16:13:54] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -46.803899 | E_var:     0.1588 | E_err:   0.006226
[2025-10-06 16:13:59] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -46.809967 | E_var:     0.1465 | E_err:   0.005980
[2025-10-06 16:14:04] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -46.805540 | E_var:     0.1730 | E_err:   0.006499
[2025-10-06 16:14:09] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -46.819602 | E_var:     0.2640 | E_err:   0.008029
[2025-10-06 16:14:14] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -46.803054 | E_var:     0.1623 | E_err:   0.006295
[2025-10-06 16:14:19] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -46.807343 | E_var:     0.1937 | E_err:   0.006877
[2025-10-06 16:14:25] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -46.812546 | E_var:     0.1867 | E_err:   0.006752
[2025-10-06 16:14:30] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -46.816411 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 16:14:35] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -46.815100 | E_var:     0.1777 | E_err:   0.006586
[2025-10-06 16:14:40] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -46.806929 | E_var:     0.1989 | E_err:   0.006968
[2025-10-06 16:14:45] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -46.815440 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 16:14:50] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -46.816451 | E_var:     0.1605 | E_err:   0.006259
[2025-10-06 16:14:56] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -46.808698 | E_var:     0.2239 | E_err:   0.007393
[2025-10-06 16:15:01] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -46.810748 | E_var:     0.1879 | E_err:   0.006773
[2025-10-06 16:15:06] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -46.812227 | E_var:     0.2701 | E_err:   0.008121
[2025-10-06 16:15:06] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 16:15:11] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -46.806420 | E_var:     0.1646 | E_err:   0.006340
[2025-10-06 16:15:16] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -46.821184 | E_var:     0.1836 | E_err:   0.006696
[2025-10-06 16:15:21] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -46.813038 | E_var:     0.1572 | E_err:   0.006196
[2025-10-06 16:15:27] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -46.801796 | E_var:     0.6527 | E_err:   0.012623
[2025-10-06 16:15:32] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -46.812327 | E_var:     0.1723 | E_err:   0.006485
[2025-10-06 16:15:37] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -46.810933 | E_var:     0.1982 | E_err:   0.006956
[2025-10-06 16:15:42] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -46.810734 | E_var:     0.1682 | E_err:   0.006409
[2025-10-06 16:15:47] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -46.816255 | E_var:     0.1986 | E_err:   0.006964
[2025-10-06 16:15:52] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -46.807329 | E_var:     0.1578 | E_err:   0.006208
[2025-10-06 16:15:57] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -46.805682 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 16:16:03] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -46.818626 | E_var:     0.2189 | E_err:   0.007311
[2025-10-06 16:16:08] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -46.814711 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 16:16:13] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -46.810946 | E_var:     0.2483 | E_err:   0.007785
[2025-10-06 16:16:18] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -46.820115 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 16:16:23] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -46.800837 | E_var:     0.9756 | E_err:   0.015433
[2025-10-06 16:16:28] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -46.823868 | E_var:     0.2040 | E_err:   0.007058
[2025-10-06 16:16:33] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -46.802935 | E_var:     0.1471 | E_err:   0.005993
[2025-10-06 16:16:39] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -46.804266 | E_var:     0.2148 | E_err:   0.007242
[2025-10-06 16:16:44] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -46.811429 | E_var:     0.1743 | E_err:   0.006523
[2025-10-06 16:16:49] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -46.821047 | E_var:     0.2263 | E_err:   0.007433
[2025-10-06 16:16:54] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -46.805060 | E_var:     0.1835 | E_err:   0.006693
[2025-10-06 16:16:59] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -46.818368 | E_var:     0.2042 | E_err:   0.007060
[2025-10-06 16:17:04] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -46.809293 | E_var:     0.2397 | E_err:   0.007649
[2025-10-06 16:17:09] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -46.821772 | E_var:     0.1806 | E_err:   0.006639
[2025-10-06 16:17:15] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -46.813937 | E_var:     0.1483 | E_err:   0.006017
[2025-10-06 16:17:20] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -46.820653 | E_var:     0.2639 | E_err:   0.008027
[2025-10-06 16:17:25] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -46.808254 | E_var:     0.1566 | E_err:   0.006183
[2025-10-06 16:17:30] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -46.809944 | E_var:     0.2481 | E_err:   0.007783
[2025-10-06 16:17:35] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -46.808943 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 16:17:40] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -46.820748 | E_var:     0.1994 | E_err:   0.006977
[2025-10-06 16:17:45] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -46.816890 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 16:17:51] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -46.816699 | E_var:     0.2013 | E_err:   0.007010
[2025-10-06 16:17:56] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -46.811647 | E_var:     0.1897 | E_err:   0.006805
[2025-10-06 16:18:01] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -46.807993 | E_var:     0.2036 | E_err:   0.007050
[2025-10-06 16:18:06] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -46.812614 | E_var:     0.1929 | E_err:   0.006863
[2025-10-06 16:18:11] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -46.804227 | E_var:     0.2673 | E_err:   0.008079
[2025-10-06 16:18:17] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -46.811930 | E_var:     0.1641 | E_err:   0.006331
[2025-10-06 16:18:22] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -46.807066 | E_var:     0.2181 | E_err:   0.007297
[2025-10-06 16:18:27] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -46.813240 | E_var:     0.2031 | E_err:   0.007042
[2025-10-06 16:18:32] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -46.807620 | E_var:     0.1695 | E_err:   0.006434
[2025-10-06 16:18:37] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -46.813507 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 16:18:42] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -46.813061 | E_var:     0.2466 | E_err:   0.007759
[2025-10-06 16:18:47] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -46.813040 | E_var:     0.1792 | E_err:   0.006614
[2025-10-06 16:18:53] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -46.809326 | E_var:     0.2487 | E_err:   0.007793
[2025-10-06 16:18:58] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -46.807705 | E_var:     0.1406 | E_err:   0.005858
[2025-10-06 16:19:03] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -46.814525 | E_var:     0.1704 | E_err:   0.006449
[2025-10-06 16:19:08] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -46.812874 | E_var:     0.1518 | E_err:   0.006089
[2025-10-06 16:19:13] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -46.813386 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 16:19:18] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -46.810830 | E_var:     0.1647 | E_err:   0.006340
[2025-10-06 16:19:24] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -46.817840 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 16:19:29] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -46.804958 | E_var:     0.2163 | E_err:   0.007267
[2025-10-06 16:19:34] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -46.820484 | E_var:     0.1585 | E_err:   0.006221
[2025-10-06 16:19:39] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -46.810485 | E_var:     0.2117 | E_err:   0.007189
[2025-10-06 16:19:44] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -46.809641 | E_var:     0.1598 | E_err:   0.006245
[2025-10-06 16:19:49] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -46.818657 | E_var:     0.2144 | E_err:   0.007235
[2025-10-06 16:19:54] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -46.811317 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 16:20:00] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -46.799239 | E_var:     0.1670 | E_err:   0.006386
[2025-10-06 16:20:05] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -46.812433 | E_var:     0.1525 | E_err:   0.006102
[2025-10-06 16:20:10] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -46.818223 | E_var:     0.1572 | E_err:   0.006194
[2025-10-06 16:20:15] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -46.819862 | E_var:     0.2242 | E_err:   0.007398
[2025-10-06 16:20:20] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -46.809492 | E_var:     0.1837 | E_err:   0.006696
[2025-10-06 16:20:25] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -46.810770 | E_var:     0.2507 | E_err:   0.007823
[2025-10-06 16:20:30] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -46.796765 | E_var:     0.2576 | E_err:   0.007930
[2025-10-06 16:20:36] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -46.814014 | E_var:     0.1648 | E_err:   0.006343
[2025-10-06 16:20:41] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -46.813108 | E_var:     0.1966 | E_err:   0.006928
[2025-10-06 16:20:46] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -46.812716 | E_var:     0.2338 | E_err:   0.007556
[2025-10-06 16:20:51] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -46.799940 | E_var:     0.1840 | E_err:   0.006703
[2025-10-06 16:20:56] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -46.808425 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 16:21:01] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -46.803883 | E_var:     0.1986 | E_err:   0.006963
[2025-10-06 16:21:06] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -46.814707 | E_var:     0.1951 | E_err:   0.006901
[2025-10-06 16:21:12] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -46.804376 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 16:21:17] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -46.810283 | E_var:     0.1656 | E_err:   0.006359
[2025-10-06 16:21:22] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -46.819406 | E_var:     0.1759 | E_err:   0.006554
[2025-10-06 16:21:27] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -46.803978 | E_var:     0.1556 | E_err:   0.006163
[2025-10-06 16:21:32] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -46.820855 | E_var:     0.1874 | E_err:   0.006765
[2025-10-06 16:21:37] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -46.813332 | E_var:     0.1503 | E_err:   0.006057
[2025-10-06 16:21:42] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -46.814094 | E_var:     0.1743 | E_err:   0.006523
[2025-10-06 16:21:48] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -46.808480 | E_var:     0.1672 | E_err:   0.006388
[2025-10-06 16:21:53] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -46.812128 | E_var:     0.1555 | E_err:   0.006161
[2025-10-06 16:21:58] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -46.811202 | E_var:     0.1748 | E_err:   0.006533
[2025-10-06 16:22:03] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -46.818449 | E_var:     0.1495 | E_err:   0.006042
[2025-10-06 16:22:08] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -46.806555 | E_var:     0.2076 | E_err:   0.007120
[2025-10-06 16:22:13] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -46.815414 | E_var:     0.1720 | E_err:   0.006479
[2025-10-06 16:22:18] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -46.814876 | E_var:     0.1816 | E_err:   0.006658
[2025-10-06 16:22:24] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -46.819013 | E_var:     0.1843 | E_err:   0.006707
[2025-10-06 16:22:29] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -46.812564 | E_var:     0.1997 | E_err:   0.006982
[2025-10-06 16:22:34] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -46.814705 | E_var:     0.1732 | E_err:   0.006503
[2025-10-06 16:22:39] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -46.821980 | E_var:     0.1646 | E_err:   0.006338
[2025-10-06 16:22:44] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -46.790791 | E_var:     0.2584 | E_err:   0.007942
[2025-10-06 16:22:49] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -46.805809 | E_var:     0.1442 | E_err:   0.005934
[2025-10-06 16:22:55] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -46.802131 | E_var:     0.1566 | E_err:   0.006183
[2025-10-06 16:23:00] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -46.807319 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 16:23:05] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -46.803476 | E_var:     0.1914 | E_err:   0.006836
[2025-10-06 16:23:10] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -46.809882 | E_var:     0.2259 | E_err:   0.007426
[2025-10-06 16:23:15] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -46.820541 | E_var:     0.1735 | E_err:   0.006508
[2025-10-06 16:23:20] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -46.816222 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 16:23:25] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -46.814904 | E_var:     0.1745 | E_err:   0.006528
[2025-10-06 16:23:31] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -46.809436 | E_var:     0.1827 | E_err:   0.006679
[2025-10-06 16:23:36] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -46.798968 | E_var:     0.1940 | E_err:   0.006881
[2025-10-06 16:23:41] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -46.818100 | E_var:     0.1787 | E_err:   0.006605
[2025-10-06 16:23:46] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 16:23:51] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -46.813849 | E_var:     0.1407 | E_err:   0.005861
[2025-10-06 16:23:56] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -46.816906 | E_var:     0.1717 | E_err:   0.006475
[2025-10-06 16:24:01] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -46.813298 | E_var:     0.2155 | E_err:   0.007253
[2025-10-06 16:24:07] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -46.802779 | E_var:     0.1890 | E_err:   0.006792
[2025-10-06 16:24:12] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -46.813169 | E_var:     0.2683 | E_err:   0.008094
[2025-10-06 16:24:17] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -46.803795 | E_var:     0.1670 | E_err:   0.006385
[2025-10-06 16:24:22] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -46.814750 | E_var:     0.1793 | E_err:   0.006617
[2025-10-06 16:24:27] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -46.804416 | E_var:     0.2445 | E_err:   0.007726
[2025-10-06 16:24:32] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -46.806402 | E_var:     0.2252 | E_err:   0.007415
[2025-10-06 16:24:37] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -46.805285 | E_var:     0.2034 | E_err:   0.007048
[2025-10-06 16:24:43] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -46.815772 | E_var:     0.1667 | E_err:   0.006380
[2025-10-06 16:24:48] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -46.821948 | E_var:     0.1605 | E_err:   0.006260
[2025-10-06 16:24:53] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -46.817436 | E_var:     0.2155 | E_err:   0.007254
[2025-10-06 16:24:58] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -46.805199 | E_var:     0.2009 | E_err:   0.007004
[2025-10-06 16:25:03] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -46.809627 | E_var:     0.1735 | E_err:   0.006508
[2025-10-06 16:25:08] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -46.828227 | E_var:     0.3615 | E_err:   0.009395
[2025-10-06 16:25:13] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -46.825233 | E_var:     0.1829 | E_err:   0.006683
[2025-10-06 16:25:19] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -46.822363 | E_var:     0.1653 | E_err:   0.006354
[2025-10-06 16:25:24] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -46.817700 | E_var:     0.1776 | E_err:   0.006585
[2025-10-06 16:25:29] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -46.811794 | E_var:     0.1815 | E_err:   0.006657
[2025-10-06 16:25:34] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -46.801412 | E_var:     0.1605 | E_err:   0.006260
[2025-10-06 16:25:39] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -46.824257 | E_var:     0.1764 | E_err:   0.006562
[2025-10-06 16:25:44] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -46.804356 | E_var:     0.1700 | E_err:   0.006443
[2025-10-06 16:25:49] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -46.823435 | E_var:     0.1625 | E_err:   0.006299
[2025-10-06 16:25:55] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -46.815358 | E_var:     0.1616 | E_err:   0.006281
[2025-10-06 16:26:00] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -46.817355 | E_var:     0.2407 | E_err:   0.007666
[2025-10-06 16:26:05] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -46.813078 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 16:26:10] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -46.819015 | E_var:     0.1842 | E_err:   0.006706
[2025-10-06 16:26:15] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -46.811543 | E_var:     0.1767 | E_err:   0.006567
[2025-10-06 16:26:20] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -46.814532 | E_var:     0.1706 | E_err:   0.006454
[2025-10-06 16:26:25] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -46.808633 | E_var:     0.1524 | E_err:   0.006100
[2025-10-06 16:26:31] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -46.811311 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 16:26:36] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -46.790400 | E_var:     0.2568 | E_err:   0.007918
[2025-10-06 16:26:41] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -46.819956 | E_var:     0.1995 | E_err:   0.006980
[2025-10-06 16:26:46] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -46.811386 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 16:26:51] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -46.814243 | E_var:     0.1689 | E_err:   0.006421
[2025-10-06 16:26:56] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -46.815039 | E_var:     0.1947 | E_err:   0.006895
[2025-10-06 16:27:02] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -46.808543 | E_var:     0.1774 | E_err:   0.006580
[2025-10-06 16:27:07] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -46.814494 | E_var:     0.2147 | E_err:   0.007240
[2025-10-06 16:27:12] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -46.807287 | E_var:     0.1939 | E_err:   0.006880
[2025-10-06 16:27:17] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -46.797540 | E_var:     0.1901 | E_err:   0.006813
[2025-10-06 16:27:22] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -46.810873 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 16:27:27] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -46.813015 | E_var:     0.1742 | E_err:   0.006522
[2025-10-06 16:27:32] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -46.814044 | E_var:     0.3089 | E_err:   0.008684
[2025-10-06 16:27:38] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -46.807299 | E_var:     0.1932 | E_err:   0.006869
[2025-10-06 16:27:43] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -46.820283 | E_var:     0.2333 | E_err:   0.007547
[2025-10-06 16:27:48] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -46.805884 | E_var:     0.1804 | E_err:   0.006636
[2025-10-06 16:27:53] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -46.812068 | E_var:     0.1825 | E_err:   0.006675
[2025-10-06 16:27:58] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -46.811038 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 16:28:03] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -46.798699 | E_var:     0.2322 | E_err:   0.007530
[2025-10-06 16:28:09] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -46.811619 | E_var:     0.1544 | E_err:   0.006141
[2025-10-06 16:28:14] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -46.796730 | E_var:     0.1996 | E_err:   0.006980
[2025-10-06 16:28:19] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -46.804937 | E_var:     0.1775 | E_err:   0.006583
[2025-10-06 16:28:24] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -46.808154 | E_var:     0.1618 | E_err:   0.006286
[2025-10-06 16:28:29] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -46.824759 | E_var:     0.1829 | E_err:   0.006682
[2025-10-06 16:28:34] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -46.805305 | E_var:     0.1806 | E_err:   0.006640
[2025-10-06 16:28:39] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -46.802684 | E_var:     0.2237 | E_err:   0.007389
[2025-10-06 16:28:45] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -46.820868 | E_var:     0.1917 | E_err:   0.006841
[2025-10-06 16:28:50] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -46.814053 | E_var:     0.1629 | E_err:   0.006306
[2025-10-06 16:28:55] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -46.818293 | E_var:     0.1848 | E_err:   0.006717
[2025-10-06 16:29:00] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -46.800547 | E_var:     0.2896 | E_err:   0.008409
[2025-10-06 16:29:05] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -46.803048 | E_var:     0.1884 | E_err:   0.006781
[2025-10-06 16:29:10] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -46.810623 | E_var:     0.2239 | E_err:   0.007394
[2025-10-06 16:29:15] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -46.809639 | E_var:     0.1877 | E_err:   0.006769
[2025-10-06 16:29:21] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -46.822290 | E_var:     0.2560 | E_err:   0.007906
[2025-10-06 16:29:26] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -46.803215 | E_var:     0.2053 | E_err:   0.007079
[2025-10-06 16:29:31] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -46.816506 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 16:29:36] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -46.810654 | E_var:     0.2122 | E_err:   0.007197
[2025-10-06 16:29:41] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -46.817387 | E_var:     0.1480 | E_err:   0.006010
[2025-10-06 16:29:46] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -46.812501 | E_var:     0.1810 | E_err:   0.006647
[2025-10-06 16:29:51] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -46.804723 | E_var:     0.1677 | E_err:   0.006399
[2025-10-06 16:29:57] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -46.807717 | E_var:     0.2800 | E_err:   0.008268
[2025-10-06 16:30:02] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -46.809641 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 16:30:07] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -46.812462 | E_var:     0.1612 | E_err:   0.006274
[2025-10-06 16:30:12] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -46.810604 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 16:30:17] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -46.809641 | E_var:     0.1717 | E_err:   0.006475
[2025-10-06 16:30:22] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -46.811601 | E_var:     0.1687 | E_err:   0.006417
[2025-10-06 16:30:27] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -46.805752 | E_var:     0.2027 | E_err:   0.007035
[2025-10-06 16:30:33] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -46.819321 | E_var:     0.1900 | E_err:   0.006811
[2025-10-06 16:30:38] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -46.812200 | E_var:     0.2082 | E_err:   0.007130
[2025-10-06 16:30:43] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -46.808003 | E_var:     0.1650 | E_err:   0.006346
[2025-10-06 16:30:48] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -46.799404 | E_var:     0.1811 | E_err:   0.006648
[2025-10-06 16:30:53] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -46.813332 | E_var:     0.1879 | E_err:   0.006774
[2025-10-06 16:30:58] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -46.812849 | E_var:     0.1308 | E_err:   0.005650
[2025-10-06 16:31:03] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -46.819857 | E_var:     0.1598 | E_err:   0.006246
[2025-10-06 16:31:09] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -46.811196 | E_var:     0.1375 | E_err:   0.005793
[2025-10-06 16:31:14] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -46.809750 | E_var:     0.1580 | E_err:   0.006210
[2025-10-06 16:31:19] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -46.807726 | E_var:     0.1834 | E_err:   0.006691
[2025-10-06 16:31:24] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -46.811732 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 16:31:29] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -46.814079 | E_var:     0.1974 | E_err:   0.006943
[2025-10-06 16:31:34] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -46.803931 | E_var:     0.1903 | E_err:   0.006817
[2025-10-06 16:31:39] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -46.829076 | E_var:     0.1954 | E_err:   0.006907
[2025-10-06 16:31:45] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -46.806673 | E_var:     0.2479 | E_err:   0.007780
[2025-10-06 16:31:50] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -46.815974 | E_var:     0.1997 | E_err:   0.006983
[2025-10-06 16:31:55] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -46.807431 | E_var:     0.1685 | E_err:   0.006414
[2025-10-06 16:32:00] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -46.811710 | E_var:     0.2231 | E_err:   0.007380
[2025-10-06 16:32:05] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -46.806135 | E_var:     0.1575 | E_err:   0.006201
[2025-10-06 16:32:10] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -46.810816 | E_var:     0.2342 | E_err:   0.007561
[2025-10-06 16:32:15] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -46.823339 | E_var:     0.1702 | E_err:   0.006447
[2025-10-06 16:32:21] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -46.804223 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 16:32:21] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 16:32:26] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -46.811122 | E_var:     0.1849 | E_err:   0.006719
[2025-10-06 16:32:31] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -46.816106 | E_var:     0.2214 | E_err:   0.007353
[2025-10-06 16:32:36] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -46.795796 | E_var:     0.3505 | E_err:   0.009251
[2025-10-06 16:32:41] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -46.819846 | E_var:     0.1865 | E_err:   0.006748
[2025-10-06 16:32:46] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -46.806481 | E_var:     0.1915 | E_err:   0.006837
[2025-10-06 16:32:52] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -46.815383 | E_var:     0.2558 | E_err:   0.007902
[2025-10-06 16:32:57] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -46.814764 | E_var:     0.1653 | E_err:   0.006352
[2025-10-06 16:33:02] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -46.814065 | E_var:     0.1744 | E_err:   0.006526
[2025-10-06 16:33:07] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -46.812339 | E_var:     0.1887 | E_err:   0.006788
[2025-10-06 16:33:12] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -46.811756 | E_var:     0.2026 | E_err:   0.007033
[2025-10-06 16:33:17] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -46.801056 | E_var:     0.1989 | E_err:   0.006969
[2025-10-06 16:33:23] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -46.798495 | E_var:     0.2034 | E_err:   0.007047
[2025-10-06 16:33:28] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -46.812366 | E_var:     0.1867 | E_err:   0.006752
[2025-10-06 16:33:33] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -46.807550 | E_var:     0.1725 | E_err:   0.006489
[2025-10-06 16:33:38] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -46.822108 | E_var:     0.1702 | E_err:   0.006446
[2025-10-06 16:33:43] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -46.814284 | E_var:     0.1507 | E_err:   0.006065
[2025-10-06 16:33:48] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -46.817074 | E_var:     0.2687 | E_err:   0.008099
[2025-10-06 16:33:53] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -46.809985 | E_var:     0.2057 | E_err:   0.007086
[2025-10-06 16:33:59] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -46.822600 | E_var:     0.1515 | E_err:   0.006082
[2025-10-06 16:34:04] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -46.797568 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 16:34:09] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -46.809008 | E_var:     0.1516 | E_err:   0.006084
[2025-10-06 16:34:14] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -46.810317 | E_var:     0.1771 | E_err:   0.006576
[2025-10-06 16:34:19] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -46.815530 | E_var:     0.2254 | E_err:   0.007419
[2025-10-06 16:34:24] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -46.824174 | E_var:     0.1864 | E_err:   0.006747
[2025-10-06 16:34:29] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -46.809420 | E_var:     0.4927 | E_err:   0.010968
[2025-10-06 16:34:35] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -46.818922 | E_var:     0.1713 | E_err:   0.006467
[2025-10-06 16:34:40] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -46.803111 | E_var:     0.1832 | E_err:   0.006689
[2025-10-06 16:34:45] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -46.815109 | E_var:     0.1739 | E_err:   0.006515
[2025-10-06 16:34:50] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -46.800778 | E_var:     0.1668 | E_err:   0.006382
[2025-10-06 16:34:55] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -46.806210 | E_var:     0.1645 | E_err:   0.006337
[2025-10-06 16:35:00] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -46.818931 | E_var:     0.1534 | E_err:   0.006119
[2025-10-06 16:35:05] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -46.810874 | E_var:     0.1801 | E_err:   0.006631
[2025-10-06 16:35:11] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -46.809222 | E_var:     0.2445 | E_err:   0.007726
[2025-10-06 16:35:16] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -46.809382 | E_var:     0.1498 | E_err:   0.006047
[2025-10-06 16:35:21] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -46.811236 | E_var:     0.1457 | E_err:   0.005964
[2025-10-06 16:35:26] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -46.812294 | E_var:     0.1857 | E_err:   0.006733
[2025-10-06 16:35:31] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -46.823007 | E_var:     0.1749 | E_err:   0.006534
[2025-10-06 16:35:36] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -46.808596 | E_var:     0.1741 | E_err:   0.006519
[2025-10-06 16:35:41] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -46.809321 | E_var:     0.1849 | E_err:   0.006718
[2025-10-06 16:35:47] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -46.799280 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 16:35:52] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -46.804475 | E_var:     0.1796 | E_err:   0.006621
[2025-10-06 16:35:57] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -46.803034 | E_var:     0.2042 | E_err:   0.007061
[2025-10-06 16:36:02] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -46.805707 | E_var:     0.1668 | E_err:   0.006382
[2025-10-06 16:36:07] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -46.812733 | E_var:     0.1847 | E_err:   0.006714
[2025-10-06 16:36:12] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -46.804642 | E_var:     0.5758 | E_err:   0.011856
[2025-10-06 16:36:17] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -46.817257 | E_var:     0.1712 | E_err:   0.006466
[2025-10-06 16:36:23] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -46.809427 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 16:36:28] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -46.810773 | E_var:     0.1649 | E_err:   0.006345
[2025-10-06 16:36:33] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -46.802137 | E_var:     0.2107 | E_err:   0.007173
[2025-10-06 16:36:38] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -46.812983 | E_var:     0.2124 | E_err:   0.007201
[2025-10-06 16:36:43] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -46.819161 | E_var:     0.1742 | E_err:   0.006522
[2025-10-06 16:36:48] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -46.810083 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 16:36:54] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -46.811396 | E_var:     0.1552 | E_err:   0.006156
[2025-10-06 16:36:59] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -46.820238 | E_var:     0.1703 | E_err:   0.006447
[2025-10-06 16:37:04] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -46.819545 | E_var:     0.1719 | E_err:   0.006477
[2025-10-06 16:37:09] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -46.817096 | E_var:     0.1690 | E_err:   0.006423
[2025-10-06 16:37:14] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -46.823110 | E_var:     0.2152 | E_err:   0.007249
[2025-10-06 16:37:19] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -46.822927 | E_var:     0.1672 | E_err:   0.006390
[2025-10-06 16:37:24] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -46.822462 | E_var:     0.1631 | E_err:   0.006311
[2025-10-06 16:37:30] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -46.814193 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 16:37:35] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -46.814773 | E_var:     0.1837 | E_err:   0.006698
[2025-10-06 16:37:40] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -46.822210 | E_var:     0.1961 | E_err:   0.006920
[2025-10-06 16:37:45] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -46.810122 | E_var:     0.2933 | E_err:   0.008463
[2025-10-06 16:37:50] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -46.807571 | E_var:     0.2098 | E_err:   0.007157
[2025-10-06 16:37:55] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -46.810464 | E_var:     0.1809 | E_err:   0.006646
[2025-10-06 16:38:00] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -46.799095 | E_var:     0.3094 | E_err:   0.008692
[2025-10-06 16:38:06] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -46.813537 | E_var:     0.1849 | E_err:   0.006719
[2025-10-06 16:38:11] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -46.821160 | E_var:     0.1829 | E_err:   0.006683
[2025-10-06 16:38:16] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -46.818257 | E_var:     0.1907 | E_err:   0.006823
[2025-10-06 16:38:21] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -46.810347 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 16:38:26] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -46.813241 | E_var:     0.2295 | E_err:   0.007486
[2025-10-06 16:38:31] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -46.808089 | E_var:     0.3229 | E_err:   0.008879
[2025-10-06 16:38:36] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -46.806639 | E_var:     0.2919 | E_err:   0.008442
[2025-10-06 16:38:42] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -46.808452 | E_var:     0.1806 | E_err:   0.006641
[2025-10-06 16:38:47] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -46.810147 | E_var:     0.1837 | E_err:   0.006697
[2025-10-06 16:38:52] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -46.809982 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 16:38:57] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -46.811786 | E_var:     0.2643 | E_err:   0.008033
[2025-10-06 16:39:02] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -46.822414 | E_var:     0.4688 | E_err:   0.010698
[2025-10-06 16:39:07] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -46.812717 | E_var:     0.2168 | E_err:   0.007275
[2025-10-06 16:39:12] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -46.811399 | E_var:     0.1970 | E_err:   0.006936
[2025-10-06 16:39:18] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -46.804620 | E_var:     0.2214 | E_err:   0.007352
[2025-10-06 16:39:23] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -46.813587 | E_var:     0.1901 | E_err:   0.006812
[2025-10-06 16:39:28] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -46.822826 | E_var:     0.1897 | E_err:   0.006806
[2025-10-06 16:39:33] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -46.824095 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 16:39:38] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -46.805460 | E_var:     0.1688 | E_err:   0.006420
[2025-10-06 16:39:43] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -46.815072 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 16:39:49] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -46.816433 | E_var:     0.1768 | E_err:   0.006570
[2025-10-06 16:39:54] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -46.808017 | E_var:     0.1777 | E_err:   0.006587
[2025-10-06 16:39:59] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -46.811790 | E_var:     0.2061 | E_err:   0.007093
[2025-10-06 16:40:04] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -46.817684 | E_var:     0.1852 | E_err:   0.006724
[2025-10-06 16:40:09] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -46.819267 | E_var:     0.2023 | E_err:   0.007028
[2025-10-06 16:40:14] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -46.814890 | E_var:     0.1627 | E_err:   0.006302
[2025-10-06 16:40:19] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -46.804105 | E_var:     0.1726 | E_err:   0.006492
[2025-10-06 16:40:25] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -46.809979 | E_var:     0.1924 | E_err:   0.006854
[2025-10-06 16:40:30] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -46.815878 | E_var:     0.2091 | E_err:   0.007144
[2025-10-06 16:40:35] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -46.805342 | E_var:     0.2280 | E_err:   0.007461
[2025-10-06 16:40:40] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -46.819691 | E_var:     0.5528 | E_err:   0.011617
[2025-10-06 16:40:45] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -46.811626 | E_var:     0.1814 | E_err:   0.006655
[2025-10-06 16:40:50] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -46.824803 | E_var:     0.2303 | E_err:   0.007499
[2025-10-06 16:40:55] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -46.795073 | E_var:     0.3333 | E_err:   0.009020
[2025-10-06 16:40:55] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 16:41:01] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -46.819456 | E_var:     0.1703 | E_err:   0.006449
[2025-10-06 16:41:06] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -46.805845 | E_var:     0.1894 | E_err:   0.006800
[2025-10-06 16:41:11] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -46.804225 | E_var:     0.2175 | E_err:   0.007287
[2025-10-06 16:41:16] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -46.813457 | E_var:     0.1849 | E_err:   0.006719
[2025-10-06 16:41:21] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -46.817694 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 16:41:26] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -46.825814 | E_var:     0.2089 | E_err:   0.007141
[2025-10-06 16:41:32] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -46.803808 | E_var:     0.2328 | E_err:   0.007538
[2025-10-06 16:41:37] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -46.817606 | E_var:     0.1802 | E_err:   0.006634
[2025-10-06 16:41:42] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -46.814663 | E_var:     0.1592 | E_err:   0.006235
[2025-10-06 16:41:47] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -46.810201 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 16:41:52] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -46.811945 | E_var:     0.1585 | E_err:   0.006222
[2025-10-06 16:41:57] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -46.814278 | E_var:     0.1496 | E_err:   0.006044
[2025-10-06 16:42:02] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -46.802465 | E_var:     0.1701 | E_err:   0.006444
[2025-10-06 16:42:08] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -46.816689 | E_var:     0.1893 | E_err:   0.006798
[2025-10-06 16:42:13] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -46.809097 | E_var:     0.1517 | E_err:   0.006085
[2025-10-06 16:42:18] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -46.813495 | E_var:     0.1727 | E_err:   0.006494
[2025-10-06 16:42:23] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -46.820100 | E_var:     0.1784 | E_err:   0.006599
[2025-10-06 16:42:28] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -46.823366 | E_var:     0.1632 | E_err:   0.006312
[2025-10-06 16:42:33] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -46.804005 | E_var:     0.1983 | E_err:   0.006958
[2025-10-06 16:42:38] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -46.813502 | E_var:     0.1498 | E_err:   0.006047
[2025-10-06 16:42:44] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -46.820694 | E_var:     0.1793 | E_err:   0.006615
[2025-10-06 16:42:49] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -46.813757 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 16:42:54] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -46.815325 | E_var:     0.2076 | E_err:   0.007120
[2025-10-06 16:42:59] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -46.811803 | E_var:     0.1505 | E_err:   0.006062
[2025-10-06 16:43:04] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -46.809422 | E_var:     0.1370 | E_err:   0.005784
[2025-10-06 16:43:10] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -46.809063 | E_var:     0.1764 | E_err:   0.006562
[2025-10-06 16:43:15] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -46.818967 | E_var:     0.1640 | E_err:   0.006327
[2025-10-06 16:43:20] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -46.812796 | E_var:     0.2328 | E_err:   0.007538
[2025-10-06 16:43:25] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -46.799550 | E_var:     0.2178 | E_err:   0.007292
[2025-10-06 16:43:30] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -46.819807 | E_var:     0.1942 | E_err:   0.006886
[2025-10-06 16:43:35] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -46.808788 | E_var:     0.2681 | E_err:   0.008090
[2025-10-06 16:43:40] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -46.831259 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 16:43:46] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -46.812228 | E_var:     0.1772 | E_err:   0.006577
[2025-10-06 16:43:51] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -46.819429 | E_var:     0.2240 | E_err:   0.007395
[2025-10-06 16:43:56] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -46.818980 | E_var:     0.1811 | E_err:   0.006648
[2025-10-06 16:44:01] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -46.812169 | E_var:     0.1708 | E_err:   0.006458
[2025-10-06 16:44:06] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -46.817299 | E_var:     0.1589 | E_err:   0.006229
[2025-10-06 16:44:11] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -46.806985 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 16:44:16] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -46.813130 | E_var:     0.1836 | E_err:   0.006694
[2025-10-06 16:44:22] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -46.811958 | E_var:     0.1878 | E_err:   0.006771
[2025-10-06 16:44:27] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -46.820253 | E_var:     0.1791 | E_err:   0.006613
[2025-10-06 16:44:32] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -46.808379 | E_var:     0.1733 | E_err:   0.006505
[2025-10-06 16:44:37] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -46.814970 | E_var:     0.1952 | E_err:   0.006904
[2025-10-06 16:44:45] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -46.819916 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 16:44:50] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -46.809449 | E_var:     0.3067 | E_err:   0.008653
[2025-10-06 16:44:55] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -46.803975 | E_var:     0.1763 | E_err:   0.006561
[2025-10-06 16:45:00] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -46.813792 | E_var:     0.2050 | E_err:   0.007075
[2025-10-06 16:45:05] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -46.814973 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 16:45:10] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -46.813575 | E_var:     0.1517 | E_err:   0.006086
[2025-10-06 16:45:16] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -46.804904 | E_var:     0.1890 | E_err:   0.006793
[2025-10-06 16:45:16] ======================================================================================================
[2025-10-06 16:45:16] ✅ Training completed successfully
[2025-10-06 16:45:16] Total restarts: 2
[2025-10-06 16:45:17] Final Energy: -46.80490366 ± 0.00679336
[2025-10-06 16:45:17] Final Variance: 0.189029
[2025-10-06 16:45:17] ======================================================================================================
[2025-10-06 16:45:17] ======================================================================================================
[2025-10-06 16:45:17] Training completed | Runtime: 5467.7s
[2025-10-06 16:45:19] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 16:45:19] ======================================================================================================
