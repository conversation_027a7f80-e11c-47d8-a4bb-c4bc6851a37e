[2025-10-06 06:05:35] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 06:05:35]   - 迭代次数: final
[2025-10-06 06:05:36]   - 能量: -42.320216-0.000052j ± 0.009394, Var: 0.361431
[2025-10-06 06:05:36]   - 时间戳: 2025-10-02T19:40:32.803893+08:00
[2025-10-06 06:05:55] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 06:05:55] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 06:05:55] ======================================================================================================
[2025-10-06 06:05:55] GCNN for Shastry-Sutherland Model
[2025-10-06 06:05:55] ======================================================================================================
[2025-10-06 06:05:55] System parameters:
[2025-10-06 06:05:55]   - System size: L=5, N=100
[2025-10-06 06:05:55]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-10-06 06:05:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 06:05:55] Model parameters:
[2025-10-06 06:05:55]   - Number of layers = 4
[2025-10-06 06:05:55]   - Number of features = 4
[2025-10-06 06:05:55]   - Total parameters = 19628
[2025-10-06 06:05:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 06:05:55] Training parameters:
[2025-10-06 06:05:55]   - Total iterations: 1050
[2025-10-06 06:05:55]   - Annealing cycles: 3
[2025-10-06 06:05:55]   - Initial period: 150
[2025-10-06 06:05:55]   - Period multiplier: 2.0
[2025-10-06 06:05:55]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 06:05:55]   - Samples: 4096
[2025-10-06 06:05:55]   - Discarded samples: 0
[2025-10-06 06:05:55]   - Chunk size: 4096
[2025-10-06 06:05:55]   - Diagonal shift: 0.15
[2025-10-06 06:05:55]   - Gradient clipping: 1.0
[2025-10-06 06:05:55]   - Checkpoint enabled: interval=100
[2025-10-06 06:05:55]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.77/model_L4F4/training/checkpoints
[2025-10-06 06:05:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 06:05:55] Device status:
[2025-10-06 06:05:55]   - Devices model: NVIDIA H200 NVL
[2025-10-06 06:05:55]   - Number of devices: 1
[2025-10-06 06:05:55]   - Sharding: True
[2025-10-06 06:05:55] ======================================================================================================
[2025-10-06 06:06:26] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -37.565508 | E_var:   349.6386 | E_err:   0.292166
[2025-10-06 06:06:46] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -42.484401 | E_var:    31.0587 | E_err:   0.087079
[2025-10-06 06:06:51] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -42.914679 | E_var:     2.7820 | E_err:   0.026061
[2025-10-06 06:06:56] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -42.947202 | E_var:     0.4247 | E_err:   0.010183
[2025-10-06 06:07:01] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -42.928604 | E_var:     0.3567 | E_err:   0.009332
[2025-10-06 06:07:07] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -42.956266 | E_var:     0.4371 | E_err:   0.010330
[2025-10-06 06:07:12] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -42.954615 | E_var:     0.6560 | E_err:   0.012655
[2025-10-06 06:07:17] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -42.945623 | E_var:     0.3097 | E_err:   0.008695
[2025-10-06 06:07:22] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -42.962973 | E_var:     0.3271 | E_err:   0.008937
[2025-10-06 06:07:27] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -42.952315 | E_var:     0.3159 | E_err:   0.008781
[2025-10-06 06:07:32] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -42.954160 | E_var:     0.4250 | E_err:   0.010186
[2025-10-06 06:07:37] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -42.951071 | E_var:     0.3060 | E_err:   0.008644
[2025-10-06 06:07:43] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -42.953259 | E_var:     0.3330 | E_err:   0.009017
[2025-10-06 06:07:48] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -42.947138 | E_var:     0.3818 | E_err:   0.009655
[2025-10-06 06:07:53] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -42.945319 | E_var:     0.3061 | E_err:   0.008645
[2025-10-06 06:07:58] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -42.967586 | E_var:     0.3662 | E_err:   0.009456
[2025-10-06 06:08:03] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -42.947305 | E_var:     0.3814 | E_err:   0.009649
[2025-10-06 06:08:08] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -42.941816 | E_var:     0.3787 | E_err:   0.009615
[2025-10-06 06:08:14] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -42.942257 | E_var:     0.3839 | E_err:   0.009681
[2025-10-06 06:08:19] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -42.963123 | E_var:     0.3330 | E_err:   0.009017
[2025-10-06 06:08:24] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -42.943013 | E_var:     0.6883 | E_err:   0.012963
[2025-10-06 06:08:29] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -42.949674 | E_var:     0.3264 | E_err:   0.008927
[2025-10-06 06:08:34] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -42.949285 | E_var:     0.3608 | E_err:   0.009385
[2025-10-06 06:08:39] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -42.933279 | E_var:     0.2709 | E_err:   0.008133
[2025-10-06 06:08:45] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -42.952420 | E_var:     0.5565 | E_err:   0.011656
[2025-10-06 06:08:50] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -42.963256 | E_var:     0.3943 | E_err:   0.009812
[2025-10-06 06:08:55] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -42.941909 | E_var:     0.4378 | E_err:   0.010338
[2025-10-06 06:09:00] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -42.952135 | E_var:     0.3583 | E_err:   0.009353
[2025-10-06 06:09:05] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -42.952848 | E_var:     0.3552 | E_err:   0.009312
[2025-10-06 06:09:10] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -42.956258 | E_var:     0.3069 | E_err:   0.008656
[2025-10-06 06:09:16] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -42.954579 | E_var:     0.2784 | E_err:   0.008244
[2025-10-06 06:09:21] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -42.943230 | E_var:     0.3415 | E_err:   0.009131
[2025-10-06 06:09:26] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -42.953973 | E_var:     0.3813 | E_err:   0.009648
[2025-10-06 06:09:31] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -42.942195 | E_var:     0.3606 | E_err:   0.009383
[2025-10-06 06:09:36] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -42.954435 | E_var:     0.3448 | E_err:   0.009175
[2025-10-06 06:09:41] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -42.956411 | E_var:     0.3028 | E_err:   0.008598
[2025-10-06 06:09:47] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -42.958716 | E_var:     0.3680 | E_err:   0.009478
[2025-10-06 06:09:52] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -42.961199 | E_var:     0.3332 | E_err:   0.009020
[2025-10-06 06:09:57] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -42.947779 | E_var:     0.3010 | E_err:   0.008572
[2025-10-06 06:10:02] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -42.941547 | E_var:     0.4636 | E_err:   0.010639
[2025-10-06 06:10:07] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -42.963849 | E_var:     0.3149 | E_err:   0.008769
[2025-10-06 06:10:12] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -42.935088 | E_var:     0.3557 | E_err:   0.009319
[2025-10-06 06:10:17] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -42.940135 | E_var:     0.3461 | E_err:   0.009192
[2025-10-06 06:10:23] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -42.944729 | E_var:     0.2955 | E_err:   0.008494
[2025-10-06 06:10:28] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -42.956749 | E_var:     0.4582 | E_err:   0.010576
[2025-10-06 06:10:33] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -42.967843 | E_var:     0.8986 | E_err:   0.014812
[2025-10-06 06:10:38] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -42.939827 | E_var:     0.3663 | E_err:   0.009456
[2025-10-06 06:10:43] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -42.949444 | E_var:     0.3627 | E_err:   0.009410
[2025-10-06 06:10:48] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -42.951615 | E_var:     0.3338 | E_err:   0.009028
[2025-10-06 06:10:54] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -42.953613 | E_var:     0.3687 | E_err:   0.009488
[2025-10-06 06:10:59] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -42.951668 | E_var:     0.3576 | E_err:   0.009344
[2025-10-06 06:11:04] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -42.969325 | E_var:     0.3264 | E_err:   0.008927
[2025-10-06 06:11:09] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -42.946235 | E_var:     0.3821 | E_err:   0.009659
[2025-10-06 06:11:14] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -42.938965 | E_var:     0.3251 | E_err:   0.008908
[2025-10-06 06:11:19] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -42.963602 | E_var:     0.3685 | E_err:   0.009485
[2025-10-06 06:11:25] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -42.958844 | E_var:     0.3364 | E_err:   0.009063
[2025-10-06 06:11:30] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -42.936608 | E_var:     0.3380 | E_err:   0.009084
[2025-10-06 06:11:35] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -42.974932 | E_var:     0.6249 | E_err:   0.012352
[2025-10-06 06:11:40] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -42.945718 | E_var:     0.4931 | E_err:   0.010972
[2025-10-06 06:11:45] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -42.952509 | E_var:     0.3278 | E_err:   0.008946
[2025-10-06 06:11:50] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -42.927415 | E_var:     0.3413 | E_err:   0.009129
[2025-10-06 06:11:55] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -42.963366 | E_var:     0.3124 | E_err:   0.008733
[2025-10-06 06:12:01] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -42.948529 | E_var:     0.3589 | E_err:   0.009361
[2025-10-06 06:12:06] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -42.954889 | E_var:     0.3177 | E_err:   0.008807
[2025-10-06 06:12:11] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -42.939012 | E_var:     0.3373 | E_err:   0.009075
[2025-10-06 06:12:16] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -42.950664 | E_var:     0.3044 | E_err:   0.008620
[2025-10-06 06:12:21] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -42.937823 | E_var:     0.4058 | E_err:   0.009954
[2025-10-06 06:12:26] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -42.947105 | E_var:     0.3900 | E_err:   0.009758
[2025-10-06 06:12:32] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -42.954976 | E_var:     0.3647 | E_err:   0.009436
[2025-10-06 06:12:37] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -42.948921 | E_var:     0.2989 | E_err:   0.008542
[2025-10-06 06:12:42] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -42.953973 | E_var:     0.3606 | E_err:   0.009383
[2025-10-06 06:12:47] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -42.936633 | E_var:     0.3716 | E_err:   0.009525
[2025-10-06 06:12:52] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -42.947297 | E_var:     0.3286 | E_err:   0.008957
[2025-10-06 06:12:57] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -42.948509 | E_var:     0.3724 | E_err:   0.009535
[2025-10-06 06:13:03] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -42.954042 | E_var:     0.3022 | E_err:   0.008589
[2025-10-06 06:13:08] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -42.954280 | E_var:     0.3464 | E_err:   0.009197
[2025-10-06 06:13:13] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -42.953068 | E_var:     0.3880 | E_err:   0.009732
[2025-10-06 06:13:18] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -42.954675 | E_var:     0.3361 | E_err:   0.009058
[2025-10-06 06:13:23] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -42.968729 | E_var:     0.3463 | E_err:   0.009196
[2025-10-06 06:13:28] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -42.959565 | E_var:     0.3981 | E_err:   0.009859
[2025-10-06 06:13:33] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -42.963261 | E_var:     0.3282 | E_err:   0.008952
[2025-10-06 06:13:39] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -42.974349 | E_var:     0.6497 | E_err:   0.012595
[2025-10-06 06:13:44] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -42.946702 | E_var:     0.3133 | E_err:   0.008745
[2025-10-06 06:13:49] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -42.950715 | E_var:     0.3477 | E_err:   0.009213
[2025-10-06 06:13:54] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -42.940200 | E_var:     0.3554 | E_err:   0.009315
[2025-10-06 06:13:59] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -42.957547 | E_var:     0.2883 | E_err:   0.008389
[2025-10-06 06:14:04] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -42.959583 | E_var:     0.3719 | E_err:   0.009529
[2025-10-06 06:14:10] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -42.933040 | E_var:     0.2866 | E_err:   0.008364
[2025-10-06 06:14:15] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -42.941068 | E_var:     0.2963 | E_err:   0.008506
[2025-10-06 06:14:20] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -42.953715 | E_var:     0.2944 | E_err:   0.008478
[2025-10-06 06:14:25] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -42.966154 | E_var:     0.3118 | E_err:   0.008724
[2025-10-06 06:14:30] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -42.945129 | E_var:     0.3474 | E_err:   0.009209
[2025-10-06 06:14:35] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -42.958573 | E_var:     0.4997 | E_err:   0.011046
[2025-10-06 06:14:40] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -42.963428 | E_var:     0.3478 | E_err:   0.009214
[2025-10-06 06:14:46] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -42.941366 | E_var:     0.4147 | E_err:   0.010062
[2025-10-06 06:14:51] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -42.952837 | E_var:     0.3651 | E_err:   0.009441
[2025-10-06 06:14:56] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -42.933585 | E_var:     0.2876 | E_err:   0.008380
[2025-10-06 06:15:01] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -42.960630 | E_var:     0.4624 | E_err:   0.010625
[2025-10-06 06:15:06] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -42.965808 | E_var:     0.4356 | E_err:   0.010312
[2025-10-06 06:15:11] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -42.964582 | E_var:     0.3606 | E_err:   0.009383
[2025-10-06 06:15:11] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 06:15:17] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -42.946630 | E_var:     0.4024 | E_err:   0.009911
[2025-10-06 06:15:22] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -42.972791 | E_var:     0.3237 | E_err:   0.008890
[2025-10-06 06:15:27] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -42.947421 | E_var:     0.2832 | E_err:   0.008315
[2025-10-06 06:15:32] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -42.950400 | E_var:     0.2712 | E_err:   0.008137
[2025-10-06 06:15:37] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -42.942701 | E_var:     0.3167 | E_err:   0.008793
[2025-10-06 06:15:42] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -42.957103 | E_var:     0.3198 | E_err:   0.008836
[2025-10-06 06:15:48] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -42.952654 | E_var:     0.3708 | E_err:   0.009514
[2025-10-06 06:15:53] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -42.945720 | E_var:     0.3009 | E_err:   0.008572
[2025-10-06 06:15:58] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -42.948900 | E_var:     0.4122 | E_err:   0.010032
[2025-10-06 06:16:03] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -42.943596 | E_var:     0.3050 | E_err:   0.008629
[2025-10-06 06:16:08] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -42.945202 | E_var:     0.3583 | E_err:   0.009353
[2025-10-06 06:16:13] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -42.953886 | E_var:     0.3043 | E_err:   0.008619
[2025-10-06 06:16:19] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -42.942539 | E_var:     0.5073 | E_err:   0.011129
[2025-10-06 06:16:24] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -42.934685 | E_var:     0.4220 | E_err:   0.010151
[2025-10-06 06:16:29] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -42.950888 | E_var:     0.3339 | E_err:   0.009029
[2025-10-06 06:16:34] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -42.928378 | E_var:     0.4418 | E_err:   0.010385
[2025-10-06 06:16:39] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -42.958035 | E_var:     0.3611 | E_err:   0.009389
[2025-10-06 06:16:44] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -42.956968 | E_var:     0.2891 | E_err:   0.008401
[2025-10-06 06:16:50] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -42.954611 | E_var:     0.4296 | E_err:   0.010241
[2025-10-06 06:16:55] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -42.947521 | E_var:     0.2731 | E_err:   0.008166
[2025-10-06 06:17:00] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -42.944723 | E_var:     0.3473 | E_err:   0.009208
[2025-10-06 06:17:05] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -42.954418 | E_var:     0.3337 | E_err:   0.009027
[2025-10-06 06:17:10] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -42.937607 | E_var:     0.3318 | E_err:   0.009001
[2025-10-06 06:17:15] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -42.954447 | E_var:     0.2834 | E_err:   0.008318
[2025-10-06 06:17:20] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -42.943974 | E_var:     0.3081 | E_err:   0.008673
[2025-10-06 06:17:26] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -42.944119 | E_var:     0.3353 | E_err:   0.009048
[2025-10-06 06:17:31] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -42.959742 | E_var:     0.3703 | E_err:   0.009509
[2025-10-06 06:17:36] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -42.950000 | E_var:     0.4377 | E_err:   0.010337
[2025-10-06 06:17:41] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -42.954240 | E_var:     0.3485 | E_err:   0.009225
[2025-10-06 06:17:46] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -42.958933 | E_var:     0.3830 | E_err:   0.009670
[2025-10-06 06:17:51] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -42.960934 | E_var:     0.3555 | E_err:   0.009316
[2025-10-06 06:17:57] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -42.954475 | E_var:     0.3885 | E_err:   0.009739
[2025-10-06 06:18:02] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -42.957275 | E_var:     0.3113 | E_err:   0.008718
[2025-10-06 06:18:07] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -42.959730 | E_var:     0.4181 | E_err:   0.010103
[2025-10-06 06:18:12] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -42.952522 | E_var:     0.2899 | E_err:   0.008413
[2025-10-06 06:18:17] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -42.945029 | E_var:     0.3254 | E_err:   0.008913
[2025-10-06 06:18:22] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -42.945083 | E_var:     0.2951 | E_err:   0.008487
[2025-10-06 06:18:28] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -42.954186 | E_var:     0.3547 | E_err:   0.009306
[2025-10-06 06:18:33] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -42.949962 | E_var:     0.3174 | E_err:   0.008802
[2025-10-06 06:18:38] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -42.945139 | E_var:     0.3444 | E_err:   0.009169
[2025-10-06 06:18:43] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -42.956310 | E_var:     0.3816 | E_err:   0.009652
[2025-10-06 06:18:48] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -42.976476 | E_var:     0.3248 | E_err:   0.008904
[2025-10-06 06:18:53] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -42.942083 | E_var:     0.4129 | E_err:   0.010040
[2025-10-06 06:18:58] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -42.945677 | E_var:     0.2973 | E_err:   0.008519
[2025-10-06 06:19:04] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -42.960700 | E_var:     0.2807 | E_err:   0.008278
[2025-10-06 06:19:09] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -42.956195 | E_var:     0.3443 | E_err:   0.009168
[2025-10-06 06:19:14] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -42.960928 | E_var:     0.3470 | E_err:   0.009204
[2025-10-06 06:19:19] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -42.943413 | E_var:     0.3526 | E_err:   0.009278
[2025-10-06 06:19:24] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -42.953050 | E_var:     0.3264 | E_err:   0.008927
[2025-10-06 06:19:29] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -42.946980 | E_var:     0.3006 | E_err:   0.008567
[2025-10-06 06:19:29] 🔄 RESTART #1 | Period: 300
[2025-10-06 06:19:35] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -42.967626 | E_var:     0.2842 | E_err:   0.008329
[2025-10-06 06:19:40] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -42.942040 | E_var:     0.3399 | E_err:   0.009109
[2025-10-06 06:19:45] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -42.928523 | E_var:     0.2907 | E_err:   0.008425
[2025-10-06 06:19:50] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -42.949332 | E_var:     0.4475 | E_err:   0.010452
[2025-10-06 06:19:55] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -42.945998 | E_var:     0.2947 | E_err:   0.008482
[2025-10-06 06:20:00] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -42.950585 | E_var:     0.3126 | E_err:   0.008735
[2025-10-06 06:20:05] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -42.958364 | E_var:     0.3321 | E_err:   0.009005
[2025-10-06 06:20:11] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -42.947627 | E_var:     0.3700 | E_err:   0.009504
[2025-10-06 06:20:16] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -42.941615 | E_var:     0.2999 | E_err:   0.008557
[2025-10-06 06:20:21] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -42.964822 | E_var:     0.3293 | E_err:   0.008966
[2025-10-06 06:20:26] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -42.949299 | E_var:     0.3799 | E_err:   0.009630
[2025-10-06 06:20:31] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -42.959438 | E_var:     0.3697 | E_err:   0.009500
[2025-10-06 06:20:36] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -42.956916 | E_var:     0.3737 | E_err:   0.009551
[2025-10-06 06:20:42] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -42.959383 | E_var:     0.2939 | E_err:   0.008470
[2025-10-06 06:20:47] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -42.937375 | E_var:     0.3724 | E_err:   0.009535
[2025-10-06 06:20:52] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -42.960246 | E_var:     0.3669 | E_err:   0.009465
[2025-10-06 06:20:57] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -42.944571 | E_var:     0.3597 | E_err:   0.009371
[2025-10-06 06:21:02] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -42.942505 | E_var:     0.5438 | E_err:   0.011522
[2025-10-06 06:21:07] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -42.947673 | E_var:     0.3340 | E_err:   0.009031
[2025-10-06 06:21:12] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -42.955795 | E_var:     0.3952 | E_err:   0.009823
[2025-10-06 06:21:18] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -42.952530 | E_var:     0.2978 | E_err:   0.008527
[2025-10-06 06:21:23] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -42.965973 | E_var:     0.2746 | E_err:   0.008187
[2025-10-06 06:21:28] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -42.939604 | E_var:     0.4026 | E_err:   0.009914
[2025-10-06 06:21:33] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -42.955688 | E_var:     0.5511 | E_err:   0.011599
[2025-10-06 06:21:38] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -42.948138 | E_var:     0.2715 | E_err:   0.008141
[2025-10-06 06:21:43] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -42.931515 | E_var:     0.3142 | E_err:   0.008758
[2025-10-06 06:21:49] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -42.949598 | E_var:     0.3228 | E_err:   0.008877
[2025-10-06 06:21:54] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -42.941552 | E_var:     0.3739 | E_err:   0.009554
[2025-10-06 06:21:59] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -42.949396 | E_var:     0.3277 | E_err:   0.008945
[2025-10-06 06:22:04] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -42.956798 | E_var:     0.3109 | E_err:   0.008712
[2025-10-06 06:22:09] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -42.965210 | E_var:     0.3577 | E_err:   0.009345
[2025-10-06 06:22:14] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -42.952636 | E_var:     0.3502 | E_err:   0.009246
[2025-10-06 06:22:20] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -42.939385 | E_var:     0.2905 | E_err:   0.008422
[2025-10-06 06:22:25] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -42.945683 | E_var:     0.3046 | E_err:   0.008623
[2025-10-06 06:22:30] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -42.953712 | E_var:     0.3412 | E_err:   0.009127
[2025-10-06 06:22:35] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -42.950993 | E_var:     0.6381 | E_err:   0.012481
[2025-10-06 06:22:40] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -42.938432 | E_var:     0.3303 | E_err:   0.008980
[2025-10-06 06:22:45] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -42.979119 | E_var:     0.2823 | E_err:   0.008302
[2025-10-06 06:22:50] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -42.957570 | E_var:     0.3160 | E_err:   0.008783
[2025-10-06 06:22:56] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -42.947542 | E_var:     0.2697 | E_err:   0.008114
[2025-10-06 06:23:01] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -42.964485 | E_var:     0.3456 | E_err:   0.009185
[2025-10-06 06:23:06] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -42.950983 | E_var:     0.3246 | E_err:   0.008902
[2025-10-06 06:23:11] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -42.944010 | E_var:     0.3340 | E_err:   0.009030
[2025-10-06 06:23:16] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -42.956613 | E_var:     0.3058 | E_err:   0.008640
[2025-10-06 06:23:21] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -42.958199 | E_var:     0.3374 | E_err:   0.009076
[2025-10-06 06:23:27] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -42.946905 | E_var:     0.2792 | E_err:   0.008256
[2025-10-06 06:23:32] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -42.942669 | E_var:     0.3295 | E_err:   0.008970
[2025-10-06 06:23:37] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -42.942826 | E_var:     0.3507 | E_err:   0.009253
[2025-10-06 06:23:42] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -42.944378 | E_var:     0.3255 | E_err:   0.008914
[2025-10-06 06:23:47] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -42.936786 | E_var:     0.3151 | E_err:   0.008771
[2025-10-06 06:23:47] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 06:23:52] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -42.954705 | E_var:     0.3759 | E_err:   0.009579
[2025-10-06 06:23:58] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -42.947675 | E_var:     0.4007 | E_err:   0.009890
[2025-10-06 06:24:03] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -42.951023 | E_var:     0.3600 | E_err:   0.009375
[2025-10-06 06:24:08] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -42.964067 | E_var:     0.2846 | E_err:   0.008335
[2025-10-06 06:24:13] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -42.950338 | E_var:     0.3248 | E_err:   0.008904
[2025-10-06 06:24:18] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -42.955928 | E_var:     0.3200 | E_err:   0.008839
[2025-10-06 06:24:23] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -42.943792 | E_var:     0.2928 | E_err:   0.008455
[2025-10-06 06:24:28] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -42.949843 | E_var:     0.3093 | E_err:   0.008690
[2025-10-06 06:24:34] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -42.945915 | E_var:     0.3615 | E_err:   0.009394
[2025-10-06 06:24:39] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -42.964388 | E_var:     0.2722 | E_err:   0.008152
[2025-10-06 06:24:44] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -42.948331 | E_var:     0.3374 | E_err:   0.009076
[2025-10-06 06:24:49] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -42.931407 | E_var:     0.3627 | E_err:   0.009410
[2025-10-06 06:24:54] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -42.955079 | E_var:     0.3177 | E_err:   0.008807
[2025-10-06 06:24:59] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -42.930564 | E_var:     0.3163 | E_err:   0.008787
[2025-10-06 06:25:05] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -42.950040 | E_var:     0.3273 | E_err:   0.008939
[2025-10-06 06:25:10] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -42.958362 | E_var:     0.3985 | E_err:   0.009863
[2025-10-06 06:25:15] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -42.942148 | E_var:     0.3661 | E_err:   0.009454
[2025-10-06 06:25:20] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -42.949087 | E_var:     0.3997 | E_err:   0.009878
[2025-10-06 06:25:25] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -42.951044 | E_var:     0.4095 | E_err:   0.009998
[2025-10-06 06:25:30] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -42.953541 | E_var:     0.3424 | E_err:   0.009143
[2025-10-06 06:25:35] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -42.963533 | E_var:     0.3204 | E_err:   0.008845
[2025-10-06 06:25:41] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -42.942982 | E_var:     0.3099 | E_err:   0.008699
[2025-10-06 06:25:46] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -42.950139 | E_var:     0.3091 | E_err:   0.008687
[2025-10-06 06:25:51] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -42.936642 | E_var:     0.2946 | E_err:   0.008480
[2025-10-06 06:25:56] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -42.959980 | E_var:     0.3815 | E_err:   0.009651
[2025-10-06 06:26:01] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -42.955428 | E_var:     0.2906 | E_err:   0.008423
[2025-10-06 06:26:06] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -42.981608 | E_var:     0.3750 | E_err:   0.009568
[2025-10-06 06:26:12] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -42.948248 | E_var:     0.2988 | E_err:   0.008541
[2025-10-06 06:26:17] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -42.942071 | E_var:     0.2995 | E_err:   0.008550
[2025-10-06 06:26:22] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -42.960430 | E_var:     0.3512 | E_err:   0.009259
[2025-10-06 06:26:27] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -42.975279 | E_var:     0.2774 | E_err:   0.008230
[2025-10-06 06:26:32] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -42.959029 | E_var:     0.3692 | E_err:   0.009495
[2025-10-06 06:26:37] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -42.964845 | E_var:     0.4567 | E_err:   0.010560
[2025-10-06 06:26:42] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -42.935050 | E_var:     0.3520 | E_err:   0.009271
[2025-10-06 06:26:48] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -42.950219 | E_var:     0.3296 | E_err:   0.008971
[2025-10-06 06:26:53] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -42.954862 | E_var:     0.3333 | E_err:   0.009020
[2025-10-06 06:26:58] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -42.953306 | E_var:     0.3111 | E_err:   0.008714
[2025-10-06 06:27:03] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -42.964646 | E_var:     0.2919 | E_err:   0.008442
[2025-10-06 06:27:08] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -42.955752 | E_var:     0.3199 | E_err:   0.008837
[2025-10-06 06:27:13] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -42.944840 | E_var:     0.3320 | E_err:   0.009003
[2025-10-06 06:27:19] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -42.974908 | E_var:     0.3415 | E_err:   0.009131
[2025-10-06 06:27:24] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -42.942829 | E_var:     0.5206 | E_err:   0.011274
[2025-10-06 06:27:29] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -42.944553 | E_var:     0.3062 | E_err:   0.008646
[2025-10-06 06:27:34] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -42.962724 | E_var:     0.3123 | E_err:   0.008732
[2025-10-06 06:27:39] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -42.963049 | E_var:     0.3042 | E_err:   0.008618
[2025-10-06 06:27:44] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -42.942989 | E_var:     0.3762 | E_err:   0.009584
[2025-10-06 06:27:49] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -42.943970 | E_var:     0.3301 | E_err:   0.008977
[2025-10-06 06:27:55] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -42.956190 | E_var:     0.3256 | E_err:   0.008916
[2025-10-06 06:28:00] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -42.972555 | E_var:     0.3601 | E_err:   0.009377
[2025-10-06 06:28:05] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -42.948710 | E_var:     0.3188 | E_err:   0.008823
[2025-10-06 06:28:10] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -42.938413 | E_var:     0.3687 | E_err:   0.009488
[2025-10-06 06:28:15] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -42.955583 | E_var:     0.5249 | E_err:   0.011321
[2025-10-06 06:28:20] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -42.959584 | E_var:     0.3183 | E_err:   0.008815
[2025-10-06 06:28:26] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -42.952270 | E_var:     0.3440 | E_err:   0.009165
[2025-10-06 06:28:31] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -42.951991 | E_var:     0.2652 | E_err:   0.008047
[2025-10-06 06:28:36] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -42.938416 | E_var:     0.3568 | E_err:   0.009334
[2025-10-06 06:28:41] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -42.963070 | E_var:     0.3101 | E_err:   0.008702
[2025-10-06 06:28:46] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -42.953683 | E_var:     0.2919 | E_err:   0.008442
[2025-10-06 06:28:51] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -42.958003 | E_var:     0.2815 | E_err:   0.008290
[2025-10-06 06:28:56] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -42.941127 | E_var:     0.3844 | E_err:   0.009688
[2025-10-06 06:29:02] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -42.955339 | E_var:     0.3809 | E_err:   0.009644
[2025-10-06 06:29:07] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -42.963930 | E_var:     0.4077 | E_err:   0.009977
[2025-10-06 06:29:12] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -42.937068 | E_var:     0.9088 | E_err:   0.014896
[2025-10-06 06:29:17] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -42.952723 | E_var:     0.4500 | E_err:   0.010482
[2025-10-06 06:29:22] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -42.971342 | E_var:     0.2921 | E_err:   0.008445
[2025-10-06 06:29:27] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -42.983416 | E_var:     0.3155 | E_err:   0.008777
[2025-10-06 06:29:33] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -42.949529 | E_var:     0.3965 | E_err:   0.009839
[2025-10-06 06:29:38] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -42.966093 | E_var:     1.1939 | E_err:   0.017073
[2025-10-06 06:29:43] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -42.957472 | E_var:     0.2842 | E_err:   0.008329
[2025-10-06 06:29:48] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -42.945165 | E_var:     0.2995 | E_err:   0.008552
[2025-10-06 06:29:53] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -42.951182 | E_var:     0.3702 | E_err:   0.009507
[2025-10-06 06:29:58] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -42.958494 | E_var:     0.2903 | E_err:   0.008419
[2025-10-06 06:30:03] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -42.946759 | E_var:     0.3767 | E_err:   0.009590
[2025-10-06 06:30:09] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -42.960659 | E_var:     0.2853 | E_err:   0.008346
[2025-10-06 06:30:14] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -42.956584 | E_var:     0.3146 | E_err:   0.008764
[2025-10-06 06:30:19] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -42.948716 | E_var:     0.3704 | E_err:   0.009510
[2025-10-06 06:30:24] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -42.958713 | E_var:     0.3182 | E_err:   0.008815
[2025-10-06 06:30:29] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -42.961108 | E_var:     0.4254 | E_err:   0.010191
[2025-10-06 06:30:34] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -42.952749 | E_var:     0.3282 | E_err:   0.008951
[2025-10-06 06:30:40] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -42.952075 | E_var:     0.3143 | E_err:   0.008759
[2025-10-06 06:30:45] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -42.940479 | E_var:     0.3603 | E_err:   0.009379
[2025-10-06 06:30:50] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -42.951995 | E_var:     0.5123 | E_err:   0.011183
[2025-10-06 06:30:55] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -42.960370 | E_var:     0.2800 | E_err:   0.008268
[2025-10-06 06:31:00] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -42.959296 | E_var:     0.2936 | E_err:   0.008467
[2025-10-06 06:31:05] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -42.955733 | E_var:     0.2762 | E_err:   0.008211
[2025-10-06 06:31:10] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -42.961219 | E_var:     0.3120 | E_err:   0.008728
[2025-10-06 06:31:16] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -42.956199 | E_var:     0.2987 | E_err:   0.008540
[2025-10-06 06:31:21] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -42.946010 | E_var:     0.3173 | E_err:   0.008801
[2025-10-06 06:31:26] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -42.951515 | E_var:     0.2734 | E_err:   0.008170
[2025-10-06 06:31:31] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -42.955524 | E_var:     0.3639 | E_err:   0.009426
[2025-10-06 06:31:36] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -42.954656 | E_var:     0.3689 | E_err:   0.009490
[2025-10-06 06:31:41] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -42.944128 | E_var:     0.3921 | E_err:   0.009784
[2025-10-06 06:31:47] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -42.967891 | E_var:     0.3537 | E_err:   0.009293
[2025-10-06 06:31:52] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -42.952227 | E_var:     0.4164 | E_err:   0.010082
[2025-10-06 06:31:57] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -42.942037 | E_var:     0.2874 | E_err:   0.008377
[2025-10-06 06:32:02] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -42.964192 | E_var:     0.3459 | E_err:   0.009189
[2025-10-06 06:32:07] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -42.961484 | E_var:     0.3553 | E_err:   0.009314
[2025-10-06 06:32:12] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -42.947441 | E_var:     0.4411 | E_err:   0.010378
[2025-10-06 06:32:17] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -42.951540 | E_var:     0.3787 | E_err:   0.009615
[2025-10-06 06:32:23] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -42.959832 | E_var:     0.3091 | E_err:   0.008688
[2025-10-06 06:32:23] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 06:32:28] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -42.946036 | E_var:     0.3034 | E_err:   0.008606
[2025-10-06 06:32:33] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -42.955333 | E_var:     0.3250 | E_err:   0.008907
[2025-10-06 06:32:38] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -42.967885 | E_var:     0.5142 | E_err:   0.011205
[2025-10-06 06:32:43] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -42.957420 | E_var:     0.3523 | E_err:   0.009274
[2025-10-06 06:32:48] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -42.950462 | E_var:     0.3828 | E_err:   0.009667
[2025-10-06 06:32:54] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -42.941154 | E_var:     0.3039 | E_err:   0.008613
[2025-10-06 06:32:59] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -42.961880 | E_var:     0.3041 | E_err:   0.008616
[2025-10-06 06:33:04] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -42.939002 | E_var:     0.3573 | E_err:   0.009340
[2025-10-06 06:33:09] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -42.950021 | E_var:     0.3525 | E_err:   0.009276
[2025-10-06 06:33:14] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -42.969336 | E_var:     0.3012 | E_err:   0.008575
[2025-10-06 06:33:19] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -42.957349 | E_var:     0.2837 | E_err:   0.008322
[2025-10-06 06:33:25] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -42.953554 | E_var:     0.3968 | E_err:   0.009842
[2025-10-06 06:33:30] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -42.963179 | E_var:     0.2973 | E_err:   0.008519
[2025-10-06 06:33:35] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -42.968470 | E_var:     0.3266 | E_err:   0.008929
[2025-10-06 06:33:40] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -42.964009 | E_var:     0.3227 | E_err:   0.008877
[2025-10-06 06:33:45] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -42.960874 | E_var:     0.3122 | E_err:   0.008730
[2025-10-06 06:33:50] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -42.949061 | E_var:     0.2473 | E_err:   0.007770
[2025-10-06 06:33:55] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -42.955739 | E_var:     0.2758 | E_err:   0.008206
[2025-10-06 06:34:01] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -42.945334 | E_var:     0.2757 | E_err:   0.008205
[2025-10-06 06:34:06] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -42.955339 | E_var:     0.3319 | E_err:   0.009001
[2025-10-06 06:34:11] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -42.950172 | E_var:     0.3576 | E_err:   0.009343
[2025-10-06 06:34:16] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -42.952497 | E_var:     0.2667 | E_err:   0.008069
[2025-10-06 06:34:21] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -42.943084 | E_var:     0.3869 | E_err:   0.009719
[2025-10-06 06:34:26] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -42.952615 | E_var:     0.3942 | E_err:   0.009810
[2025-10-06 06:34:32] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -42.950569 | E_var:     0.3236 | E_err:   0.008888
[2025-10-06 06:34:37] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -42.941907 | E_var:     0.2849 | E_err:   0.008341
[2025-10-06 06:34:42] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -42.948958 | E_var:     0.3482 | E_err:   0.009220
[2025-10-06 06:34:47] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -42.959501 | E_var:     0.2917 | E_err:   0.008439
[2025-10-06 06:34:52] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -42.949244 | E_var:     0.4331 | E_err:   0.010283
[2025-10-06 06:34:57] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -42.955332 | E_var:     0.4129 | E_err:   0.010040
[2025-10-06 06:35:03] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -42.959513 | E_var:     0.2667 | E_err:   0.008069
[2025-10-06 06:35:08] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -42.968897 | E_var:     0.2707 | E_err:   0.008129
[2025-10-06 06:35:13] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -42.950168 | E_var:     0.3295 | E_err:   0.008969
[2025-10-06 06:35:18] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -42.943017 | E_var:     0.4047 | E_err:   0.009940
[2025-10-06 06:35:23] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -42.944297 | E_var:     0.3600 | E_err:   0.009375
[2025-10-06 06:35:28] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -42.942042 | E_var:     0.5360 | E_err:   0.011440
[2025-10-06 06:35:33] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -42.950478 | E_var:     0.2605 | E_err:   0.007975
[2025-10-06 06:35:39] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -42.951639 | E_var:     0.3603 | E_err:   0.009379
[2025-10-06 06:35:44] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -42.945477 | E_var:     0.3319 | E_err:   0.009001
[2025-10-06 06:35:49] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -42.943452 | E_var:     0.3990 | E_err:   0.009869
[2025-10-06 06:35:54] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -42.962119 | E_var:     0.3675 | E_err:   0.009472
[2025-10-06 06:35:59] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -42.947970 | E_var:     0.3366 | E_err:   0.009065
[2025-10-06 06:36:04] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -42.955894 | E_var:     0.4376 | E_err:   0.010336
[2025-10-06 06:36:09] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -42.964275 | E_var:     0.3578 | E_err:   0.009346
[2025-10-06 06:36:15] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -42.958895 | E_var:     0.3855 | E_err:   0.009702
[2025-10-06 06:36:20] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -42.924009 | E_var:     0.4936 | E_err:   0.010978
[2025-10-06 06:36:25] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -42.966225 | E_var:     0.2923 | E_err:   0.008447
[2025-10-06 06:36:30] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -42.943174 | E_var:     0.3806 | E_err:   0.009639
[2025-10-06 06:36:35] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -42.939432 | E_var:     0.3514 | E_err:   0.009263
[2025-10-06 06:36:40] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -42.968184 | E_var:     0.3789 | E_err:   0.009618
[2025-10-06 06:36:46] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -42.952781 | E_var:     0.3152 | E_err:   0.008772
[2025-10-06 06:36:51] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -42.950630 | E_var:     0.3459 | E_err:   0.009190
[2025-10-06 06:36:56] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -42.938493 | E_var:     0.3314 | E_err:   0.008995
[2025-10-06 06:37:01] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -42.957773 | E_var:     0.3453 | E_err:   0.009182
[2025-10-06 06:37:06] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -42.959864 | E_var:     0.3692 | E_err:   0.009494
[2025-10-06 06:37:11] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -42.967308 | E_var:     0.3745 | E_err:   0.009562
[2025-10-06 06:37:16] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -42.954065 | E_var:     0.4257 | E_err:   0.010195
[2025-10-06 06:37:22] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -42.961014 | E_var:     0.3733 | E_err:   0.009547
[2025-10-06 06:37:27] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -42.956747 | E_var:     0.2997 | E_err:   0.008554
[2025-10-06 06:37:32] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -42.948143 | E_var:     0.3645 | E_err:   0.009434
[2025-10-06 06:37:37] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -42.947936 | E_var:     0.3241 | E_err:   0.008896
[2025-10-06 06:37:42] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -42.959580 | E_var:     0.3075 | E_err:   0.008664
[2025-10-06 06:37:47] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -42.952170 | E_var:     0.2763 | E_err:   0.008213
[2025-10-06 06:37:53] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -42.948291 | E_var:     0.5274 | E_err:   0.011348
[2025-10-06 06:37:58] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -42.966207 | E_var:     0.3481 | E_err:   0.009219
[2025-10-06 06:38:03] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -42.960133 | E_var:     0.3503 | E_err:   0.009247
[2025-10-06 06:38:08] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -42.948802 | E_var:     0.3733 | E_err:   0.009547
[2025-10-06 06:38:13] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -42.982118 | E_var:     0.3340 | E_err:   0.009030
[2025-10-06 06:38:18] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -42.960419 | E_var:     0.3842 | E_err:   0.009685
[2025-10-06 06:38:23] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -42.952798 | E_var:     0.3004 | E_err:   0.008564
[2025-10-06 06:38:29] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -42.936718 | E_var:     0.3084 | E_err:   0.008678
[2025-10-06 06:38:34] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -42.964249 | E_var:     0.2520 | E_err:   0.007843
[2025-10-06 06:38:39] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -42.952431 | E_var:     0.3803 | E_err:   0.009635
[2025-10-06 06:38:44] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -42.959914 | E_var:     0.2910 | E_err:   0.008429
[2025-10-06 06:38:49] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -42.945988 | E_var:     0.2998 | E_err:   0.008555
[2025-10-06 06:38:54] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -42.951035 | E_var:     0.2927 | E_err:   0.008454
[2025-10-06 06:39:00] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -42.958490 | E_var:     0.3449 | E_err:   0.009176
[2025-10-06 06:39:05] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -42.958360 | E_var:     0.3051 | E_err:   0.008630
[2025-10-06 06:39:10] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -42.968919 | E_var:     0.4346 | E_err:   0.010300
[2025-10-06 06:39:15] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -42.962416 | E_var:     0.3031 | E_err:   0.008602
[2025-10-06 06:39:20] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -42.954439 | E_var:     0.3412 | E_err:   0.009126
[2025-10-06 06:39:25] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -42.939433 | E_var:     0.2962 | E_err:   0.008503
[2025-10-06 06:39:31] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -42.954956 | E_var:     0.3004 | E_err:   0.008564
[2025-10-06 06:39:36] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -42.950899 | E_var:     0.3170 | E_err:   0.008797
[2025-10-06 06:39:41] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -42.952133 | E_var:     0.3360 | E_err:   0.009058
[2025-10-06 06:39:46] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -42.961079 | E_var:     0.2704 | E_err:   0.008125
[2025-10-06 06:39:51] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -42.931472 | E_var:     1.1707 | E_err:   0.016906
[2025-10-06 06:39:56] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -42.936266 | E_var:     1.2000 | E_err:   0.017116
[2025-10-06 06:40:01] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -42.926722 | E_var:     1.4380 | E_err:   0.018737
[2025-10-06 06:40:07] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -42.934932 | E_var:     1.3943 | E_err:   0.018450
[2025-10-06 06:40:12] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -42.944378 | E_var:     0.3433 | E_err:   0.009154
[2025-10-06 06:40:17] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -42.963723 | E_var:     0.3688 | E_err:   0.009489
[2025-10-06 06:40:22] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -42.951427 | E_var:     0.3099 | E_err:   0.008698
[2025-10-06 06:40:27] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -42.944894 | E_var:     0.2993 | E_err:   0.008548
[2025-10-06 06:40:32] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -42.944134 | E_var:     0.3208 | E_err:   0.008850
[2025-10-06 06:40:38] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -42.953583 | E_var:     0.3069 | E_err:   0.008656
[2025-10-06 06:40:43] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -42.964203 | E_var:     0.3510 | E_err:   0.009258
[2025-10-06 06:40:48] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -42.960563 | E_var:     0.3084 | E_err:   0.008677
[2025-10-06 06:40:53] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -42.974534 | E_var:     0.2864 | E_err:   0.008362
[2025-10-06 06:40:58] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -42.961548 | E_var:     0.2955 | E_err:   0.008494
[2025-10-06 06:40:58] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 06:41:03] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -42.943220 | E_var:     0.3212 | E_err:   0.008855
[2025-10-06 06:41:09] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -42.958682 | E_var:     0.3128 | E_err:   0.008739
[2025-10-06 06:41:14] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -42.959172 | E_var:     0.3447 | E_err:   0.009174
[2025-10-06 06:41:19] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -42.961643 | E_var:     0.3053 | E_err:   0.008634
[2025-10-06 06:41:24] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -42.948031 | E_var:     0.2989 | E_err:   0.008542
[2025-10-06 06:41:29] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -42.944860 | E_var:     0.3814 | E_err:   0.009650
[2025-10-06 06:41:34] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -42.953978 | E_var:     0.4674 | E_err:   0.010682
[2025-10-06 06:41:39] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -42.957815 | E_var:     0.3509 | E_err:   0.009256
[2025-10-06 06:41:45] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -42.950396 | E_var:     0.3959 | E_err:   0.009831
[2025-10-06 06:41:50] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -42.959908 | E_var:     0.3410 | E_err:   0.009124
[2025-10-06 06:41:55] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -42.949994 | E_var:     0.2983 | E_err:   0.008534
[2025-10-06 06:42:00] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -42.960921 | E_var:     0.4026 | E_err:   0.009914
[2025-10-06 06:42:05] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -42.970632 | E_var:     0.3718 | E_err:   0.009528
[2025-10-06 06:42:10] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -42.936833 | E_var:     0.3860 | E_err:   0.009707
[2025-10-06 06:42:16] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -42.953909 | E_var:     0.2769 | E_err:   0.008222
[2025-10-06 06:42:21] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -42.965513 | E_var:     0.4031 | E_err:   0.009920
[2025-10-06 06:42:26] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -42.939859 | E_var:     0.3250 | E_err:   0.008908
[2025-10-06 06:42:31] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -42.962742 | E_var:     0.2914 | E_err:   0.008435
[2025-10-06 06:42:36] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -42.955302 | E_var:     0.2969 | E_err:   0.008514
[2025-10-06 06:42:41] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -42.941685 | E_var:     0.4150 | E_err:   0.010066
[2025-10-06 06:42:46] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -42.949837 | E_var:     0.3510 | E_err:   0.009257
[2025-10-06 06:42:52] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -42.956405 | E_var:     0.3728 | E_err:   0.009540
[2025-10-06 06:42:57] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -42.947162 | E_var:     0.4000 | E_err:   0.009882
[2025-10-06 06:43:02] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -42.964994 | E_var:     0.3421 | E_err:   0.009139
[2025-10-06 06:43:07] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -42.942345 | E_var:     0.3004 | E_err:   0.008564
[2025-10-06 06:43:12] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -42.948325 | E_var:     0.2885 | E_err:   0.008392
[2025-10-06 06:43:17] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -42.951903 | E_var:     0.3302 | E_err:   0.008979
[2025-10-06 06:43:23] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -42.965956 | E_var:     0.4440 | E_err:   0.010412
[2025-10-06 06:43:28] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -42.948128 | E_var:     0.2639 | E_err:   0.008027
[2025-10-06 06:43:33] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -42.973359 | E_var:     0.3391 | E_err:   0.009099
[2025-10-06 06:43:38] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -42.954276 | E_var:     0.2727 | E_err:   0.008160
[2025-10-06 06:43:43] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -42.939603 | E_var:     0.3090 | E_err:   0.008686
[2025-10-06 06:43:48] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -42.961586 | E_var:     0.4318 | E_err:   0.010267
[2025-10-06 06:43:54] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -42.969670 | E_var:     0.4127 | E_err:   0.010037
[2025-10-06 06:43:59] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -42.957008 | E_var:     0.3102 | E_err:   0.008703
[2025-10-06 06:44:04] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -42.964702 | E_var:     0.2798 | E_err:   0.008266
[2025-10-06 06:44:09] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -42.970320 | E_var:     0.3100 | E_err:   0.008699
[2025-10-06 06:44:14] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -42.952297 | E_var:     0.2877 | E_err:   0.008381
[2025-10-06 06:44:19] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -42.967100 | E_var:     0.3174 | E_err:   0.008803
[2025-10-06 06:44:24] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -42.957260 | E_var:     0.3126 | E_err:   0.008736
[2025-10-06 06:44:30] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -42.954890 | E_var:     0.3031 | E_err:   0.008603
[2025-10-06 06:44:35] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -42.955319 | E_var:     0.3800 | E_err:   0.009632
[2025-10-06 06:44:40] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -42.958847 | E_var:     0.4054 | E_err:   0.009949
[2025-10-06 06:44:45] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -42.979274 | E_var:     0.2784 | E_err:   0.008244
[2025-10-06 06:44:50] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -42.964810 | E_var:     0.3764 | E_err:   0.009586
[2025-10-06 06:44:55] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -42.950848 | E_var:     0.2998 | E_err:   0.008555
[2025-10-06 06:45:01] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -42.970037 | E_var:     0.2683 | E_err:   0.008094
[2025-10-06 06:45:06] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -42.944350 | E_var:     0.2915 | E_err:   0.008435
[2025-10-06 06:45:11] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -42.970678 | E_var:     0.3192 | E_err:   0.008828
[2025-10-06 06:45:16] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -42.968530 | E_var:     0.3246 | E_err:   0.008903
[2025-10-06 06:45:16] 🔄 RESTART #2 | Period: 600
[2025-10-06 06:45:21] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -42.935526 | E_var:     0.3590 | E_err:   0.009362
[2025-10-06 06:45:26] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -42.948176 | E_var:     0.4247 | E_err:   0.010182
[2025-10-06 06:45:32] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -42.961396 | E_var:     0.3121 | E_err:   0.008729
[2025-10-06 06:45:37] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -42.959011 | E_var:     0.3112 | E_err:   0.008717
[2025-10-06 06:45:42] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -42.936520 | E_var:     0.2811 | E_err:   0.008284
[2025-10-06 06:45:47] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -42.939562 | E_var:     0.3300 | E_err:   0.008976
[2025-10-06 06:45:52] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -42.961546 | E_var:     0.2814 | E_err:   0.008288
[2025-10-06 06:45:57] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -42.960235 | E_var:     0.2999 | E_err:   0.008557
[2025-10-06 06:46:02] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -42.948694 | E_var:     0.3652 | E_err:   0.009443
[2025-10-06 06:46:08] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -42.953855 | E_var:     0.3163 | E_err:   0.008788
[2025-10-06 06:46:13] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -42.947123 | E_var:     0.3183 | E_err:   0.008815
[2025-10-06 06:46:18] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -42.943756 | E_var:     0.2946 | E_err:   0.008481
[2025-10-06 06:46:23] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -42.949862 | E_var:     0.3657 | E_err:   0.009448
[2025-10-06 06:46:28] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -42.950176 | E_var:     0.2982 | E_err:   0.008532
[2025-10-06 06:46:33] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -42.972443 | E_var:     0.4632 | E_err:   0.010635
[2025-10-06 06:46:39] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -42.962218 | E_var:     0.3611 | E_err:   0.009390
[2025-10-06 06:46:44] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -42.961951 | E_var:     0.3830 | E_err:   0.009670
[2025-10-06 06:46:49] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -42.940241 | E_var:     3.2701 | E_err:   0.028256
[2025-10-06 06:46:54] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -42.957627 | E_var:     0.3346 | E_err:   0.009039
[2025-10-06 06:46:59] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -42.955880 | E_var:     0.3602 | E_err:   0.009377
[2025-10-06 06:47:04] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -42.940122 | E_var:     0.3388 | E_err:   0.009094
[2025-10-06 06:47:09] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -42.943643 | E_var:     0.2566 | E_err:   0.007915
[2025-10-06 06:47:15] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -42.942700 | E_var:     0.4015 | E_err:   0.009901
[2025-10-06 06:47:20] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -42.967984 | E_var:     0.3267 | E_err:   0.008930
[2025-10-06 06:47:25] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -42.949896 | E_var:     0.2786 | E_err:   0.008248
[2025-10-06 06:47:30] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -42.963625 | E_var:     0.3085 | E_err:   0.008679
[2025-10-06 06:47:35] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -42.962150 | E_var:     0.3909 | E_err:   0.009769
[2025-10-06 06:47:40] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -42.950217 | E_var:     0.2891 | E_err:   0.008401
[2025-10-06 06:47:46] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -42.959568 | E_var:     0.5136 | E_err:   0.011198
[2025-10-06 06:47:51] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -42.950360 | E_var:     0.3003 | E_err:   0.008562
[2025-10-06 06:47:56] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -42.946394 | E_var:     0.2612 | E_err:   0.007986
[2025-10-06 06:48:01] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -42.942962 | E_var:     0.3035 | E_err:   0.008608
[2025-10-06 06:48:06] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -42.944343 | E_var:     0.3695 | E_err:   0.009498
[2025-10-06 06:48:11] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -42.947317 | E_var:     0.3295 | E_err:   0.008969
[2025-10-06 06:48:16] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -42.961326 | E_var:     0.3036 | E_err:   0.008609
[2025-10-06 06:48:22] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -42.946530 | E_var:     0.3167 | E_err:   0.008792
[2025-10-06 06:48:27] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -42.957419 | E_var:     0.3267 | E_err:   0.008931
[2025-10-06 06:48:32] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -42.963894 | E_var:     0.3034 | E_err:   0.008606
[2025-10-06 06:48:37] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -42.947614 | E_var:     0.3289 | E_err:   0.008961
[2025-10-06 06:48:42] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -42.954250 | E_var:     0.2905 | E_err:   0.008422
[2025-10-06 06:48:47] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -42.965422 | E_var:     0.3847 | E_err:   0.009692
[2025-10-06 06:48:53] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -42.953636 | E_var:     0.3620 | E_err:   0.009401
[2025-10-06 06:48:58] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -42.943204 | E_var:     0.4627 | E_err:   0.010628
[2025-10-06 06:49:03] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -42.930448 | E_var:     0.6922 | E_err:   0.013000
[2025-10-06 06:49:08] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -42.962758 | E_var:     0.3130 | E_err:   0.008742
[2025-10-06 06:49:13] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -42.953915 | E_var:     0.3346 | E_err:   0.009038
[2025-10-06 06:49:18] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -42.959101 | E_var:     0.2961 | E_err:   0.008503
[2025-10-06 06:49:24] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -42.939632 | E_var:     0.3727 | E_err:   0.009539
[2025-10-06 06:49:29] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -42.952857 | E_var:     0.3109 | E_err:   0.008712
[2025-10-06 06:49:34] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -42.952441 | E_var:     0.4867 | E_err:   0.010901
[2025-10-06 06:49:34] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 06:49:39] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -42.956847 | E_var:     0.3100 | E_err:   0.008700
[2025-10-06 06:49:44] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -42.955668 | E_var:     0.5860 | E_err:   0.011961
[2025-10-06 06:49:49] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -42.953606 | E_var:     0.3670 | E_err:   0.009466
[2025-10-06 06:49:55] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -42.943705 | E_var:     0.3679 | E_err:   0.009477
[2025-10-06 06:50:00] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -42.946478 | E_var:     0.2708 | E_err:   0.008132
[2025-10-06 06:50:05] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -42.961887 | E_var:     0.3733 | E_err:   0.009547
[2025-10-06 06:50:10] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -42.948983 | E_var:     0.6415 | E_err:   0.012514
[2025-10-06 06:50:15] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -42.956705 | E_var:     0.3010 | E_err:   0.008572
[2025-10-06 06:50:20] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -42.947432 | E_var:     0.3398 | E_err:   0.009108
[2025-10-06 06:50:25] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -42.964660 | E_var:     0.3459 | E_err:   0.009189
[2025-10-06 06:50:31] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -42.958387 | E_var:     0.3058 | E_err:   0.008640
[2025-10-06 06:50:36] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -42.967790 | E_var:     0.2676 | E_err:   0.008082
[2025-10-06 06:50:41] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -42.944386 | E_var:     0.3602 | E_err:   0.009378
[2025-10-06 06:50:46] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -42.959027 | E_var:     0.2860 | E_err:   0.008355
[2025-10-06 06:50:51] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -42.963413 | E_var:     0.4153 | E_err:   0.010069
[2025-10-06 06:50:56] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -42.971814 | E_var:     0.3291 | E_err:   0.008964
[2025-10-06 06:51:02] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -42.946243 | E_var:     0.3252 | E_err:   0.008911
[2025-10-06 06:51:07] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -42.945921 | E_var:     0.3295 | E_err:   0.008969
[2025-10-06 06:51:12] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -42.968095 | E_var:     1.3484 | E_err:   0.018144
[2025-10-06 06:51:17] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -42.966492 | E_var:     0.2789 | E_err:   0.008252
[2025-10-06 06:51:22] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -42.945874 | E_var:     0.3118 | E_err:   0.008724
[2025-10-06 06:51:27] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -42.962401 | E_var:     0.3988 | E_err:   0.009867
[2025-10-06 06:51:33] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -42.943848 | E_var:     0.6992 | E_err:   0.013065
[2025-10-06 06:51:38] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -42.949443 | E_var:     0.2490 | E_err:   0.007797
[2025-10-06 06:51:43] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -42.956450 | E_var:     0.3305 | E_err:   0.008983
[2025-10-06 06:51:48] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -42.944194 | E_var:     0.3419 | E_err:   0.009137
[2025-10-06 06:51:53] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -42.966749 | E_var:     0.2792 | E_err:   0.008256
[2025-10-06 06:51:58] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -42.960295 | E_var:     0.2817 | E_err:   0.008293
[2025-10-06 06:52:03] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -42.970582 | E_var:     0.2837 | E_err:   0.008322
[2025-10-06 06:52:09] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -42.950038 | E_var:     0.3531 | E_err:   0.009284
[2025-10-06 06:52:14] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -42.951021 | E_var:     0.3205 | E_err:   0.008845
[2025-10-06 06:52:19] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -42.958808 | E_var:     0.2887 | E_err:   0.008396
[2025-10-06 06:52:24] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -42.953390 | E_var:     0.3076 | E_err:   0.008665
[2025-10-06 06:52:29] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -42.962842 | E_var:     0.2739 | E_err:   0.008178
[2025-10-06 06:52:34] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -42.964652 | E_var:     0.2350 | E_err:   0.007574
[2025-10-06 06:52:40] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -42.956393 | E_var:     0.2784 | E_err:   0.008244
[2025-10-06 06:52:45] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -42.952383 | E_var:     0.6615 | E_err:   0.012709
[2025-10-06 06:52:50] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -42.935323 | E_var:     0.3244 | E_err:   0.008899
[2025-10-06 06:52:55] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -42.961677 | E_var:     0.3635 | E_err:   0.009421
[2025-10-06 06:53:00] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -42.962820 | E_var:     0.2987 | E_err:   0.008539
[2025-10-06 06:53:05] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -42.960796 | E_var:     0.2629 | E_err:   0.008012
[2025-10-06 06:53:11] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -42.957151 | E_var:     0.2594 | E_err:   0.007958
[2025-10-06 06:53:16] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -42.952672 | E_var:     0.3454 | E_err:   0.009182
[2025-10-06 06:53:21] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -42.956823 | E_var:     0.2792 | E_err:   0.008256
[2025-10-06 06:53:26] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -42.960911 | E_var:     0.3678 | E_err:   0.009476
[2025-10-06 06:53:31] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -42.952652 | E_var:     0.3393 | E_err:   0.009101
[2025-10-06 06:53:36] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -42.962982 | E_var:     0.4687 | E_err:   0.010697
[2025-10-06 06:53:41] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -42.952185 | E_var:     0.3339 | E_err:   0.009029
[2025-10-06 06:53:47] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -42.947408 | E_var:     0.3523 | E_err:   0.009274
[2025-10-06 06:53:52] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -42.951482 | E_var:     0.2894 | E_err:   0.008405
[2025-10-06 06:53:57] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -42.950299 | E_var:     0.2551 | E_err:   0.007891
[2025-10-06 06:54:02] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -42.961307 | E_var:     0.3849 | E_err:   0.009694
[2025-10-06 06:54:07] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -42.952821 | E_var:     0.2711 | E_err:   0.008135
[2025-10-06 06:54:12] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -42.955942 | E_var:     0.2874 | E_err:   0.008377
[2025-10-06 06:54:18] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -42.944132 | E_var:     0.4890 | E_err:   0.010927
[2025-10-06 06:54:23] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -42.948643 | E_var:     0.2838 | E_err:   0.008323
[2025-10-06 06:54:28] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -42.944512 | E_var:     0.2748 | E_err:   0.008191
[2025-10-06 06:54:33] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -42.966840 | E_var:     0.4384 | E_err:   0.010345
[2025-10-06 06:54:38] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -42.949125 | E_var:     0.3171 | E_err:   0.008799
[2025-10-06 06:54:43] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -42.971632 | E_var:     0.3520 | E_err:   0.009271
[2025-10-06 06:54:48] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -42.954758 | E_var:     0.2774 | E_err:   0.008229
[2025-10-06 06:54:54] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -42.959588 | E_var:     0.3034 | E_err:   0.008606
[2025-10-06 06:54:59] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -42.954211 | E_var:     0.3266 | E_err:   0.008929
[2025-10-06 06:55:04] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -42.968864 | E_var:     0.3356 | E_err:   0.009052
[2025-10-06 06:55:09] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -42.946586 | E_var:     0.3513 | E_err:   0.009261
[2025-10-06 06:55:14] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -42.955156 | E_var:     0.3384 | E_err:   0.009090
[2025-10-06 06:55:19] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -42.946466 | E_var:     0.2845 | E_err:   0.008335
[2025-10-06 06:55:25] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -42.954718 | E_var:     0.4275 | E_err:   0.010216
[2025-10-06 06:55:30] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -42.952398 | E_var:     0.3378 | E_err:   0.009082
[2025-10-06 06:55:35] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -42.961004 | E_var:     0.4758 | E_err:   0.010778
[2025-10-06 06:55:40] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -42.955512 | E_var:     0.2626 | E_err:   0.008006
[2025-10-06 06:55:45] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -42.945800 | E_var:     0.3037 | E_err:   0.008610
[2025-10-06 06:55:50] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -42.955373 | E_var:     0.2643 | E_err:   0.008033
[2025-10-06 06:55:56] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -42.968927 | E_var:     0.3385 | E_err:   0.009091
[2025-10-06 06:56:01] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -42.965653 | E_var:     0.8655 | E_err:   0.014536
[2025-10-06 06:56:06] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -42.952725 | E_var:     0.2609 | E_err:   0.007981
[2025-10-06 06:56:11] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -42.959333 | E_var:     0.2880 | E_err:   0.008386
[2025-10-06 06:56:16] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -42.952991 | E_var:     0.3283 | E_err:   0.008953
[2025-10-06 06:56:21] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -42.964835 | E_var:     0.3429 | E_err:   0.009149
[2025-10-06 06:56:26] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -42.945376 | E_var:     0.3489 | E_err:   0.009229
[2025-10-06 06:56:32] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -42.960921 | E_var:     0.3090 | E_err:   0.008686
[2025-10-06 06:56:37] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -42.960624 | E_var:     0.3438 | E_err:   0.009161
[2025-10-06 06:56:42] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -42.959641 | E_var:     0.2696 | E_err:   0.008113
[2025-10-06 06:56:47] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -42.960041 | E_var:     0.3053 | E_err:   0.008634
[2025-10-06 06:56:52] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -42.969279 | E_var:     0.3561 | E_err:   0.009324
[2025-10-06 06:56:57] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -42.957756 | E_var:     0.3203 | E_err:   0.008843
[2025-10-06 06:57:03] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -42.976255 | E_var:     0.3045 | E_err:   0.008623
[2025-10-06 06:57:08] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -42.951590 | E_var:     0.2847 | E_err:   0.008337
[2025-10-06 06:57:13] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -42.961206 | E_var:     0.2884 | E_err:   0.008391
[2025-10-06 06:57:18] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -42.962342 | E_var:     0.3864 | E_err:   0.009712
[2025-10-06 06:57:23] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -42.962807 | E_var:     0.3422 | E_err:   0.009140
[2025-10-06 06:57:28] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -42.965674 | E_var:     0.2749 | E_err:   0.008192
[2025-10-06 06:57:33] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -42.959459 | E_var:     0.3365 | E_err:   0.009064
[2025-10-06 06:57:39] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -42.950426 | E_var:     0.2749 | E_err:   0.008193
[2025-10-06 06:57:44] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -42.950337 | E_var:     0.2769 | E_err:   0.008222
[2025-10-06 06:57:49] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -42.959475 | E_var:     0.3465 | E_err:   0.009197
[2025-10-06 06:57:54] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -42.961698 | E_var:     0.2849 | E_err:   0.008341
[2025-10-06 06:57:59] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -42.963019 | E_var:     0.3069 | E_err:   0.008656
[2025-10-06 06:58:04] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -42.952328 | E_var:     0.3469 | E_err:   0.009203
[2025-10-06 06:58:10] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -42.951509 | E_var:     0.2694 | E_err:   0.008110
[2025-10-06 06:58:10] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 06:58:15] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -42.963077 | E_var:     0.3133 | E_err:   0.008745
[2025-10-06 06:58:20] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -42.949928 | E_var:     0.2352 | E_err:   0.007577
[2025-10-06 06:58:25] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -42.968751 | E_var:     0.3352 | E_err:   0.009046
[2025-10-06 06:58:30] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -42.962556 | E_var:     0.2393 | E_err:   0.007644
[2025-10-06 06:58:35] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -42.953767 | E_var:     0.3412 | E_err:   0.009127
[2025-10-06 06:58:41] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -42.955753 | E_var:     0.2546 | E_err:   0.007883
[2025-10-06 06:58:46] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -42.951051 | E_var:     0.3402 | E_err:   0.009114
[2025-10-06 06:58:51] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -42.954314 | E_var:     0.2792 | E_err:   0.008256
[2025-10-06 06:58:56] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -42.950526 | E_var:     0.3074 | E_err:   0.008663
[2025-10-06 06:59:01] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -42.961673 | E_var:     0.4038 | E_err:   0.009929
[2025-10-06 06:59:06] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -42.961989 | E_var:     0.3114 | E_err:   0.008719
[2025-10-06 06:59:11] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -42.981273 | E_var:     0.3905 | E_err:   0.009765
[2025-10-06 06:59:17] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -42.965041 | E_var:     0.2393 | E_err:   0.007644
[2025-10-06 06:59:22] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -42.944939 | E_var:     0.2882 | E_err:   0.008388
[2025-10-06 06:59:27] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -42.954606 | E_var:     0.3520 | E_err:   0.009270
[2025-10-06 06:59:32] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -42.964701 | E_var:     0.3172 | E_err:   0.008801
[2025-10-06 06:59:37] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -42.961043 | E_var:     0.3275 | E_err:   0.008941
[2025-10-06 06:59:42] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -42.950048 | E_var:     0.3392 | E_err:   0.009100
[2025-10-06 06:59:48] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -42.962891 | E_var:     0.2658 | E_err:   0.008055
[2025-10-06 06:59:53] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -42.966673 | E_var:     0.2860 | E_err:   0.008356
[2025-10-06 06:59:58] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -42.952496 | E_var:     0.3100 | E_err:   0.008700
[2025-10-06 07:00:03] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -42.936836 | E_var:     0.4301 | E_err:   0.010248
[2025-10-06 07:00:08] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -42.963820 | E_var:     0.3017 | E_err:   0.008583
[2025-10-06 07:00:13] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -42.964546 | E_var:     0.2930 | E_err:   0.008458
[2025-10-06 07:00:18] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -42.967296 | E_var:     0.3515 | E_err:   0.009263
[2025-10-06 07:00:24] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -42.956593 | E_var:     0.3191 | E_err:   0.008826
[2025-10-06 07:00:29] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -42.961194 | E_var:     0.2771 | E_err:   0.008225
[2025-10-06 07:00:34] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -42.941364 | E_var:     0.2673 | E_err:   0.008078
[2025-10-06 07:00:39] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -42.957163 | E_var:     0.2824 | E_err:   0.008303
[2025-10-06 07:00:44] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -42.948751 | E_var:     0.4271 | E_err:   0.010211
[2025-10-06 07:00:49] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -42.966376 | E_var:     0.3225 | E_err:   0.008873
[2025-10-06 07:00:55] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -42.956826 | E_var:     0.2939 | E_err:   0.008470
[2025-10-06 07:01:00] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -42.968936 | E_var:     0.3959 | E_err:   0.009831
[2025-10-06 07:01:05] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -42.960413 | E_var:     0.4458 | E_err:   0.010432
[2025-10-06 07:01:10] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -42.960591 | E_var:     0.2990 | E_err:   0.008544
[2025-10-06 07:01:15] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -42.951778 | E_var:     0.3420 | E_err:   0.009138
[2025-10-06 07:01:20] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -42.969702 | E_var:     0.3570 | E_err:   0.009336
[2025-10-06 07:01:26] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -42.962294 | E_var:     0.3036 | E_err:   0.008609
[2025-10-06 07:01:31] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -42.971072 | E_var:     0.4655 | E_err:   0.010661
[2025-10-06 07:01:36] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -42.949342 | E_var:     0.2293 | E_err:   0.007483
[2025-10-06 07:01:41] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -42.947234 | E_var:     0.3860 | E_err:   0.009708
[2025-10-06 07:01:46] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -42.970957 | E_var:     0.6484 | E_err:   0.012582
[2025-10-06 07:01:51] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -42.949590 | E_var:     0.3544 | E_err:   0.009302
[2025-10-06 07:01:57] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -42.955758 | E_var:     0.2887 | E_err:   0.008396
[2025-10-06 07:02:02] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -42.961317 | E_var:     0.2651 | E_err:   0.008044
[2025-10-06 07:02:07] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -42.952541 | E_var:     0.2954 | E_err:   0.008493
[2025-10-06 07:02:12] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -42.950773 | E_var:     0.2751 | E_err:   0.008195
[2025-10-06 07:02:17] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -42.956056 | E_var:     0.2845 | E_err:   0.008334
[2025-10-06 07:02:22] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -42.954414 | E_var:     0.3044 | E_err:   0.008621
[2025-10-06 07:02:27] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -42.957389 | E_var:     0.2948 | E_err:   0.008484
[2025-10-06 07:02:33] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -42.955976 | E_var:     0.4584 | E_err:   0.010579
[2025-10-06 07:02:38] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -42.954667 | E_var:     0.3392 | E_err:   0.009100
[2025-10-06 07:02:43] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -42.963335 | E_var:     0.3428 | E_err:   0.009148
[2025-10-06 07:02:48] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -42.966227 | E_var:     0.4110 | E_err:   0.010017
[2025-10-06 07:02:53] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -42.950919 | E_var:     0.2847 | E_err:   0.008336
[2025-10-06 07:02:58] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -42.963179 | E_var:     0.2951 | E_err:   0.008488
[2025-10-06 07:03:04] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -42.961427 | E_var:     0.2975 | E_err:   0.008523
[2025-10-06 07:03:09] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -42.969286 | E_var:     0.2570 | E_err:   0.007922
[2025-10-06 07:03:14] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -42.964892 | E_var:     0.4250 | E_err:   0.010186
[2025-10-06 07:03:19] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -42.957851 | E_var:     0.3465 | E_err:   0.009197
[2025-10-06 07:03:24] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -42.963390 | E_var:     0.2776 | E_err:   0.008233
[2025-10-06 07:03:29] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -42.949216 | E_var:     0.3543 | E_err:   0.009300
[2025-10-06 07:03:34] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -42.948741 | E_var:     0.2993 | E_err:   0.008548
[2025-10-06 07:03:40] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -42.971964 | E_var:     0.3120 | E_err:   0.008728
[2025-10-06 07:03:45] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -42.953090 | E_var:     0.3519 | E_err:   0.009269
[2025-10-06 07:03:50] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -42.957294 | E_var:     0.3350 | E_err:   0.009044
[2025-10-06 07:03:55] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -42.956384 | E_var:     0.2761 | E_err:   0.008210
[2025-10-06 07:04:00] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -42.962004 | E_var:     0.3163 | E_err:   0.008788
[2025-10-06 07:04:05] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -42.969637 | E_var:     0.3309 | E_err:   0.008988
[2025-10-06 07:04:11] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -42.954676 | E_var:     0.2909 | E_err:   0.008428
[2025-10-06 07:04:16] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -42.964617 | E_var:     0.2999 | E_err:   0.008557
[2025-10-06 07:04:21] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -42.955548 | E_var:     0.3347 | E_err:   0.009039
[2025-10-06 07:04:26] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -42.954256 | E_var:     0.3114 | E_err:   0.008719
[2025-10-06 07:04:31] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -42.959213 | E_var:     0.3624 | E_err:   0.009406
[2025-10-06 07:04:36] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -42.962644 | E_var:     0.3854 | E_err:   0.009701
[2025-10-06 07:04:41] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -42.944452 | E_var:     0.3212 | E_err:   0.008856
[2025-10-06 07:04:47] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -42.954701 | E_var:     0.3441 | E_err:   0.009165
[2025-10-06 07:04:52] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -42.963069 | E_var:     0.2940 | E_err:   0.008472
[2025-10-06 07:04:57] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -42.973448 | E_var:     0.2918 | E_err:   0.008441
[2025-10-06 07:05:02] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -42.958077 | E_var:     0.3079 | E_err:   0.008670
[2025-10-06 07:05:07] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -42.956871 | E_var:     0.2772 | E_err:   0.008226
[2025-10-06 07:05:12] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -42.959922 | E_var:     0.3080 | E_err:   0.008672
[2025-10-06 07:05:18] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -42.962710 | E_var:     0.2973 | E_err:   0.008520
[2025-10-06 07:05:23] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -42.961445 | E_var:     0.2656 | E_err:   0.008052
[2025-10-06 07:05:28] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -42.953124 | E_var:     0.2655 | E_err:   0.008050
[2025-10-06 07:05:33] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -42.968240 | E_var:     0.3262 | E_err:   0.008924
[2025-10-06 07:05:38] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -42.964660 | E_var:     0.2936 | E_err:   0.008466
[2025-10-06 07:05:43] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -42.946410 | E_var:     0.3479 | E_err:   0.009216
[2025-10-06 07:05:48] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -42.957383 | E_var:     0.2938 | E_err:   0.008470
[2025-10-06 07:05:54] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -42.936427 | E_var:     0.2687 | E_err:   0.008099
[2025-10-06 07:05:59] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -42.963616 | E_var:     0.2899 | E_err:   0.008412
[2025-10-06 07:06:04] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -42.967310 | E_var:     0.3207 | E_err:   0.008849
[2025-10-06 07:06:09] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -42.950353 | E_var:     0.3104 | E_err:   0.008705
[2025-10-06 07:06:14] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -42.958101 | E_var:     0.3651 | E_err:   0.009442
[2025-10-06 07:06:19] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -42.964808 | E_var:     0.2788 | E_err:   0.008251
[2025-10-06 07:06:25] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -42.938551 | E_var:     0.3979 | E_err:   0.009856
[2025-10-06 07:06:30] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -42.962988 | E_var:     0.2754 | E_err:   0.008199
[2025-10-06 07:06:35] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -42.949955 | E_var:     0.2885 | E_err:   0.008392
[2025-10-06 07:06:40] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -42.953216 | E_var:     0.3691 | E_err:   0.009492
[2025-10-06 07:06:45] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -42.964324 | E_var:     0.2847 | E_err:   0.008337
[2025-10-06 07:06:45] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 07:06:50] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -42.964208 | E_var:     0.3078 | E_err:   0.008669
[2025-10-06 07:06:56] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -42.946781 | E_var:     0.3025 | E_err:   0.008593
[2025-10-06 07:07:01] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -42.955692 | E_var:     0.3852 | E_err:   0.009697
[2025-10-06 07:07:06] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -42.958225 | E_var:     0.3249 | E_err:   0.008907
[2025-10-06 07:07:11] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -42.965225 | E_var:     0.2893 | E_err:   0.008404
[2025-10-06 07:07:16] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -42.966332 | E_var:     0.2985 | E_err:   0.008537
[2025-10-06 07:07:21] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -42.957781 | E_var:     0.4833 | E_err:   0.010863
[2025-10-06 07:07:27] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -42.951574 | E_var:     0.3355 | E_err:   0.009051
[2025-10-06 07:07:32] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -42.957657 | E_var:     0.3673 | E_err:   0.009469
[2025-10-06 07:07:37] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -42.975520 | E_var:     0.2990 | E_err:   0.008544
[2025-10-06 07:07:42] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -42.959083 | E_var:     0.3682 | E_err:   0.009481
[2025-10-06 07:07:47] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -42.957647 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 07:07:52] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -42.949967 | E_var:     0.3197 | E_err:   0.008835
[2025-10-06 07:07:57] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -42.950515 | E_var:     0.2780 | E_err:   0.008239
[2025-10-06 07:08:03] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -42.970448 | E_var:     0.3855 | E_err:   0.009702
[2025-10-06 07:08:08] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -42.970226 | E_var:     0.3520 | E_err:   0.009270
[2025-10-06 07:08:13] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -42.949495 | E_var:     0.4013 | E_err:   0.009898
[2025-10-06 07:08:18] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -42.960113 | E_var:     0.3228 | E_err:   0.008877
[2025-10-06 07:08:23] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -42.954503 | E_var:     0.3914 | E_err:   0.009776
[2025-10-06 07:08:28] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -42.971953 | E_var:     0.4328 | E_err:   0.010279
[2025-10-06 07:08:34] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -42.957943 | E_var:     0.3666 | E_err:   0.009461
[2025-10-06 07:08:39] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -42.955189 | E_var:     0.2619 | E_err:   0.007996
[2025-10-06 07:08:44] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -42.962661 | E_var:     0.3249 | E_err:   0.008907
[2025-10-06 07:08:49] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -42.961771 | E_var:     0.2848 | E_err:   0.008339
[2025-10-06 07:08:54] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -42.955506 | E_var:     0.3472 | E_err:   0.009206
[2025-10-06 07:08:59] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -42.946363 | E_var:     0.2938 | E_err:   0.008469
[2025-10-06 07:09:04] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -42.958676 | E_var:     0.2493 | E_err:   0.007801
[2025-10-06 07:09:10] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -42.954911 | E_var:     0.3521 | E_err:   0.009271
[2025-10-06 07:09:15] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -42.966429 | E_var:     0.2839 | E_err:   0.008325
[2025-10-06 07:09:20] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -42.951565 | E_var:     0.4570 | E_err:   0.010562
[2025-10-06 07:09:25] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -42.955044 | E_var:     0.2591 | E_err:   0.007954
[2025-10-06 07:09:30] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -42.968106 | E_var:     0.2878 | E_err:   0.008382
[2025-10-06 07:09:35] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -42.954303 | E_var:     0.2632 | E_err:   0.008016
[2025-10-06 07:09:40] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -42.951844 | E_var:     0.3346 | E_err:   0.009039
[2025-10-06 07:09:46] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -42.963535 | E_var:     0.3268 | E_err:   0.008932
[2025-10-06 07:09:51] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -42.963987 | E_var:     0.3619 | E_err:   0.009400
[2025-10-06 07:09:56] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -42.957117 | E_var:     0.3132 | E_err:   0.008744
[2025-10-06 07:10:01] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -42.961074 | E_var:     0.3024 | E_err:   0.008593
[2025-10-06 07:10:06] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -42.961232 | E_var:     0.2848 | E_err:   0.008338
[2025-10-06 07:10:11] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -42.969121 | E_var:     0.2570 | E_err:   0.007922
[2025-10-06 07:10:17] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -42.954850 | E_var:     0.4303 | E_err:   0.010250
[2025-10-06 07:10:22] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -42.961975 | E_var:     0.3486 | E_err:   0.009226
[2025-10-06 07:10:27] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -42.960178 | E_var:     0.4045 | E_err:   0.009937
[2025-10-06 07:10:32] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -42.965823 | E_var:     0.4050 | E_err:   0.009944
[2025-10-06 07:10:37] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -42.953791 | E_var:     0.2957 | E_err:   0.008497
[2025-10-06 07:10:42] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -42.949900 | E_var:     0.3245 | E_err:   0.008900
[2025-10-06 07:10:48] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -42.959463 | E_var:     0.3114 | E_err:   0.008720
[2025-10-06 07:10:53] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -42.955551 | E_var:     0.2996 | E_err:   0.008552
[2025-10-06 07:10:58] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -42.973695 | E_var:     0.2991 | E_err:   0.008545
[2025-10-06 07:11:03] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -42.970117 | E_var:     0.2905 | E_err:   0.008421
[2025-10-06 07:11:08] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -42.948946 | E_var:     0.3211 | E_err:   0.008853
[2025-10-06 07:11:13] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -42.942681 | E_var:     0.2757 | E_err:   0.008205
[2025-10-06 07:11:18] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -42.960744 | E_var:     0.2949 | E_err:   0.008485
[2025-10-06 07:11:24] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -42.963815 | E_var:     0.3299 | E_err:   0.008975
[2025-10-06 07:11:29] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -42.962134 | E_var:     0.4178 | E_err:   0.010099
[2025-10-06 07:11:34] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -42.951195 | E_var:     0.3182 | E_err:   0.008814
[2025-10-06 07:11:39] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -42.964667 | E_var:     0.3165 | E_err:   0.008790
[2025-10-06 07:11:44] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -42.957078 | E_var:     0.3025 | E_err:   0.008593
[2025-10-06 07:11:49] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -42.952686 | E_var:     0.2523 | E_err:   0.007848
[2025-10-06 07:11:55] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -42.964578 | E_var:     0.2766 | E_err:   0.008218
[2025-10-06 07:12:00] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -42.966452 | E_var:     0.2876 | E_err:   0.008379
[2025-10-06 07:12:05] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -42.966923 | E_var:     0.3161 | E_err:   0.008785
[2025-10-06 07:12:10] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -42.951793 | E_var:     0.2581 | E_err:   0.007937
[2025-10-06 07:12:15] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -42.972024 | E_var:     0.3020 | E_err:   0.008586
[2025-10-06 07:12:20] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -42.956139 | E_var:     0.2853 | E_err:   0.008345
[2025-10-06 07:12:25] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -42.949665 | E_var:     0.3214 | E_err:   0.008858
[2025-10-06 07:12:31] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -42.966142 | E_var:     0.3086 | E_err:   0.008680
[2025-10-06 07:12:36] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -42.958078 | E_var:     0.3315 | E_err:   0.008997
[2025-10-06 07:12:41] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -42.956162 | E_var:     0.2834 | E_err:   0.008317
[2025-10-06 07:12:46] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -42.962832 | E_var:     0.2959 | E_err:   0.008500
[2025-10-06 07:12:51] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -42.959275 | E_var:     0.2912 | E_err:   0.008432
[2025-10-06 07:12:56] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -42.957896 | E_var:     0.2984 | E_err:   0.008535
[2025-10-06 07:13:02] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -42.957452 | E_var:     0.3226 | E_err:   0.008875
[2025-10-06 07:13:07] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -42.955730 | E_var:     0.2853 | E_err:   0.008346
[2025-10-06 07:13:12] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -42.956537 | E_var:     0.3305 | E_err:   0.008983
[2025-10-06 07:13:17] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -42.955685 | E_var:     0.3049 | E_err:   0.008628
[2025-10-06 07:13:22] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -42.944797 | E_var:     0.3595 | E_err:   0.009368
[2025-10-06 07:13:27] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -42.960190 | E_var:     0.4245 | E_err:   0.010180
[2025-10-06 07:13:32] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -42.943688 | E_var:     0.4311 | E_err:   0.010259
[2025-10-06 07:13:38] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -42.969338 | E_var:     0.2757 | E_err:   0.008205
[2025-10-06 07:13:43] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -42.960079 | E_var:     0.4007 | E_err:   0.009890
[2025-10-06 07:13:48] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -42.972390 | E_var:     0.3596 | E_err:   0.009370
[2025-10-06 07:13:53] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -42.955659 | E_var:     0.2748 | E_err:   0.008191
[2025-10-06 07:13:58] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -42.954702 | E_var:     0.2895 | E_err:   0.008407
[2025-10-06 07:14:03] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -42.968841 | E_var:     0.4947 | E_err:   0.010990
[2025-10-06 07:14:09] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -42.965599 | E_var:     0.4802 | E_err:   0.010828
[2025-10-06 07:14:14] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -42.968988 | E_var:     0.3230 | E_err:   0.008880
[2025-10-06 07:14:19] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -42.957042 | E_var:     0.4243 | E_err:   0.010178
[2025-10-06 07:14:24] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -42.959206 | E_var:     0.3381 | E_err:   0.009085
[2025-10-06 07:14:29] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -42.949997 | E_var:     0.2949 | E_err:   0.008485
[2025-10-06 07:14:34] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -42.971179 | E_var:     0.3468 | E_err:   0.009201
[2025-10-06 07:14:39] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -42.967692 | E_var:     0.2829 | E_err:   0.008311
[2025-10-06 07:14:45] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -42.961561 | E_var:     0.3331 | E_err:   0.009017
[2025-10-06 07:14:50] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -42.969276 | E_var:     0.3855 | E_err:   0.009701
[2025-10-06 07:14:55] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -42.953183 | E_var:     0.3047 | E_err:   0.008625
[2025-10-06 07:15:00] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -42.943937 | E_var:     0.4547 | E_err:   0.010536
[2025-10-06 07:15:05] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -42.950957 | E_var:     0.3948 | E_err:   0.009818
[2025-10-06 07:15:10] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -42.950957 | E_var:     0.3455 | E_err:   0.009185
[2025-10-06 07:15:16] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -42.960757 | E_var:     0.2925 | E_err:   0.008450
[2025-10-06 07:15:21] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -42.949748 | E_var:     0.4831 | E_err:   0.010860
[2025-10-06 07:15:21] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 07:15:26] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -42.962789 | E_var:     0.2592 | E_err:   0.007955
[2025-10-06 07:15:31] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -42.956558 | E_var:     0.3054 | E_err:   0.008636
[2025-10-06 07:15:36] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -42.962308 | E_var:     0.2646 | E_err:   0.008037
[2025-10-06 07:15:41] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -42.963527 | E_var:     0.2886 | E_err:   0.008393
[2025-10-06 07:15:47] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -42.960903 | E_var:     0.3667 | E_err:   0.009461
[2025-10-06 07:15:52] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -42.943045 | E_var:     1.1244 | E_err:   0.016568
[2025-10-06 07:15:57] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -42.963117 | E_var:     0.3237 | E_err:   0.008890
[2025-10-06 07:16:02] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -42.945638 | E_var:     0.3864 | E_err:   0.009713
[2025-10-06 07:16:07] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -42.966617 | E_var:     0.3250 | E_err:   0.008908
[2025-10-06 07:16:12] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -42.968111 | E_var:     0.2782 | E_err:   0.008242
[2025-10-06 07:16:17] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -42.958572 | E_var:     0.9209 | E_err:   0.014994
[2025-10-06 07:16:23] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -42.964932 | E_var:     0.2770 | E_err:   0.008223
[2025-10-06 07:16:28] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -42.958125 | E_var:     0.2713 | E_err:   0.008138
[2025-10-06 07:16:33] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -42.938241 | E_var:     0.3114 | E_err:   0.008719
[2025-10-06 07:16:38] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -42.970227 | E_var:     0.2814 | E_err:   0.008289
[2025-10-06 07:16:43] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -42.961435 | E_var:     0.2797 | E_err:   0.008264
[2025-10-06 07:16:48] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -42.958786 | E_var:     0.3211 | E_err:   0.008854
[2025-10-06 07:16:54] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -42.948594 | E_var:     0.3088 | E_err:   0.008682
[2025-10-06 07:16:59] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -42.977797 | E_var:     0.3698 | E_err:   0.009502
[2025-10-06 07:17:04] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -42.965170 | E_var:     0.3818 | E_err:   0.009655
[2025-10-06 07:17:09] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -42.971579 | E_var:     0.3718 | E_err:   0.009527
[2025-10-06 07:17:14] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -42.947617 | E_var:     0.3176 | E_err:   0.008806
[2025-10-06 07:17:19] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -42.949898 | E_var:     0.5404 | E_err:   0.011487
[2025-10-06 07:17:24] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -42.947903 | E_var:     0.3341 | E_err:   0.009032
[2025-10-06 07:17:30] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -42.964979 | E_var:     0.3195 | E_err:   0.008832
[2025-10-06 07:17:35] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -42.953486 | E_var:     0.2531 | E_err:   0.007862
[2025-10-06 07:17:40] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -42.942971 | E_var:     0.2897 | E_err:   0.008410
[2025-10-06 07:17:45] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -42.971624 | E_var:     0.3948 | E_err:   0.009817
[2025-10-06 07:17:50] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -42.956081 | E_var:     0.2475 | E_err:   0.007773
[2025-10-06 07:17:55] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -42.959118 | E_var:     0.2604 | E_err:   0.007973
[2025-10-06 07:18:01] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -42.962339 | E_var:     0.3836 | E_err:   0.009678
[2025-10-06 07:18:06] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -42.961875 | E_var:     0.3112 | E_err:   0.008716
[2025-10-06 07:18:11] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -42.972539 | E_var:     0.2923 | E_err:   0.008448
[2025-10-06 07:18:16] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -42.959543 | E_var:     0.3161 | E_err:   0.008785
[2025-10-06 07:18:21] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -42.963526 | E_var:     0.3977 | E_err:   0.009854
[2025-10-06 07:18:26] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -42.956446 | E_var:     0.2540 | E_err:   0.007875
[2025-10-06 07:18:31] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -42.946954 | E_var:     0.5111 | E_err:   0.011171
[2025-10-06 07:18:37] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -42.954132 | E_var:     0.2782 | E_err:   0.008241
[2025-10-06 07:18:42] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -42.952572 | E_var:     0.3696 | E_err:   0.009499
[2025-10-06 07:18:47] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -42.981382 | E_var:     0.2921 | E_err:   0.008444
[2025-10-06 07:18:52] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -42.974577 | E_var:     0.3007 | E_err:   0.008568
[2025-10-06 07:18:57] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -42.951614 | E_var:     0.4671 | E_err:   0.010678
[2025-10-06 07:19:02] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -42.945737 | E_var:     0.4751 | E_err:   0.010770
[2025-10-06 07:19:08] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -42.961008 | E_var:     0.3298 | E_err:   0.008974
[2025-10-06 07:19:13] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -42.958495 | E_var:     0.3041 | E_err:   0.008616
[2025-10-06 07:19:18] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -42.964969 | E_var:     0.3476 | E_err:   0.009212
[2025-10-06 07:19:23] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -42.962975 | E_var:     0.4582 | E_err:   0.010577
[2025-10-06 07:19:28] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -42.973203 | E_var:     0.2579 | E_err:   0.007935
[2025-10-06 07:19:33] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -42.964258 | E_var:     0.3440 | E_err:   0.009165
[2025-10-06 07:19:38] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -42.960772 | E_var:     0.5231 | E_err:   0.011301
[2025-10-06 07:19:44] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -42.957813 | E_var:     0.3217 | E_err:   0.008863
[2025-10-06 07:19:49] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -42.951953 | E_var:     0.4743 | E_err:   0.010761
[2025-10-06 07:19:54] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -42.958559 | E_var:     0.3253 | E_err:   0.008912
[2025-10-06 07:19:59] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -42.961345 | E_var:     0.3265 | E_err:   0.008929
[2025-10-06 07:20:04] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -42.950586 | E_var:     0.2953 | E_err:   0.008490
[2025-10-06 07:20:09] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -42.971584 | E_var:     0.4208 | E_err:   0.010135
[2025-10-06 07:20:15] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -42.959105 | E_var:     0.3544 | E_err:   0.009302
[2025-10-06 07:20:20] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -42.963617 | E_var:     0.2949 | E_err:   0.008485
[2025-10-06 07:20:25] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -42.956527 | E_var:     0.2987 | E_err:   0.008539
[2025-10-06 07:20:30] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -42.950113 | E_var:     0.3772 | E_err:   0.009596
[2025-10-06 07:20:35] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -42.961804 | E_var:     0.2865 | E_err:   0.008364
[2025-10-06 07:20:40] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -42.948218 | E_var:     0.3562 | E_err:   0.009326
[2025-10-06 07:20:45] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -42.969538 | E_var:     0.2806 | E_err:   0.008276
[2025-10-06 07:20:51] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -42.966515 | E_var:     0.3104 | E_err:   0.008705
[2025-10-06 07:20:56] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -42.966258 | E_var:     0.2988 | E_err:   0.008540
[2025-10-06 07:21:01] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -42.953155 | E_var:     0.3050 | E_err:   0.008629
[2025-10-06 07:21:06] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -42.958053 | E_var:     0.2838 | E_err:   0.008324
[2025-10-06 07:21:11] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -42.955408 | E_var:     0.2976 | E_err:   0.008524
[2025-10-06 07:21:16] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -42.958908 | E_var:     0.4742 | E_err:   0.010760
[2025-10-06 07:21:22] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -42.948968 | E_var:     0.3647 | E_err:   0.009435
[2025-10-06 07:21:27] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -42.947531 | E_var:     0.3604 | E_err:   0.009380
[2025-10-06 07:21:32] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -42.950377 | E_var:     0.4247 | E_err:   0.010182
[2025-10-06 07:21:37] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -42.960671 | E_var:     0.3050 | E_err:   0.008629
[2025-10-06 07:21:42] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -42.961593 | E_var:     0.2867 | E_err:   0.008367
[2025-10-06 07:21:47] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -42.954554 | E_var:     0.3512 | E_err:   0.009260
[2025-10-06 07:21:52] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -42.951769 | E_var:     0.2687 | E_err:   0.008099
[2025-10-06 07:21:58] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -42.952751 | E_var:     0.2953 | E_err:   0.008490
[2025-10-06 07:22:03] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -42.959894 | E_var:     0.2897 | E_err:   0.008409
[2025-10-06 07:22:08] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -42.940515 | E_var:     0.3037 | E_err:   0.008610
[2025-10-06 07:22:13] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -42.957885 | E_var:     0.3367 | E_err:   0.009067
[2025-10-06 07:22:18] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -42.942993 | E_var:     0.2960 | E_err:   0.008501
[2025-10-06 07:22:23] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -42.962388 | E_var:     0.3445 | E_err:   0.009171
[2025-10-06 07:22:29] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -42.943461 | E_var:     0.3564 | E_err:   0.009328
[2025-10-06 07:22:34] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -42.945578 | E_var:     0.3148 | E_err:   0.008767
[2025-10-06 07:22:39] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -42.948666 | E_var:     0.2738 | E_err:   0.008176
[2025-10-06 07:22:44] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -42.949488 | E_var:     0.3453 | E_err:   0.009182
[2025-10-06 07:22:49] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -42.948848 | E_var:     0.2701 | E_err:   0.008121
[2025-10-06 07:22:54] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -42.950640 | E_var:     0.3148 | E_err:   0.008767
[2025-10-06 07:22:59] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -42.957998 | E_var:     0.2985 | E_err:   0.008536
[2025-10-06 07:23:05] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -42.956720 | E_var:     0.2837 | E_err:   0.008322
[2025-10-06 07:23:10] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -42.954005 | E_var:     0.3535 | E_err:   0.009290
[2025-10-06 07:23:15] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -42.959594 | E_var:     0.4029 | E_err:   0.009918
[2025-10-06 07:23:20] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -42.954177 | E_var:     0.2651 | E_err:   0.008044
[2025-10-06 07:23:25] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -42.967483 | E_var:     0.2975 | E_err:   0.008523
[2025-10-06 07:23:30] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -42.957594 | E_var:     0.4388 | E_err:   0.010350
[2025-10-06 07:23:35] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -42.969976 | E_var:     0.2669 | E_err:   0.008073
[2025-10-06 07:23:41] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -42.970793 | E_var:     0.2678 | E_err:   0.008087
[2025-10-06 07:23:46] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -42.963372 | E_var:     0.3578 | E_err:   0.009347
[2025-10-06 07:23:51] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -42.946040 | E_var:     0.3495 | E_err:   0.009237
[2025-10-06 07:23:56] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -42.969240 | E_var:     0.2742 | E_err:   0.008181
[2025-10-06 07:23:56] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 07:24:01] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -42.958665 | E_var:     0.3483 | E_err:   0.009221
[2025-10-06 07:24:06] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -42.956492 | E_var:     0.3536 | E_err:   0.009291
[2025-10-06 07:24:12] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -42.954475 | E_var:     0.3142 | E_err:   0.008759
[2025-10-06 07:24:17] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -42.931704 | E_var:     0.3773 | E_err:   0.009598
[2025-10-06 07:24:22] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -42.952327 | E_var:     0.3443 | E_err:   0.009169
[2025-10-06 07:24:27] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -42.969984 | E_var:     0.2644 | E_err:   0.008034
[2025-10-06 07:24:32] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -42.959444 | E_var:     0.3332 | E_err:   0.009019
[2025-10-06 07:24:37] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -42.957600 | E_var:     0.5260 | E_err:   0.011332
[2025-10-06 07:24:43] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -42.947042 | E_var:     0.3225 | E_err:   0.008873
[2025-10-06 07:24:48] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -42.968444 | E_var:     0.3427 | E_err:   0.009146
[2025-10-06 07:24:53] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -42.943434 | E_var:     1.5923 | E_err:   0.019717
[2025-10-06 07:24:58] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -42.968255 | E_var:     0.3303 | E_err:   0.008979
[2025-10-06 07:25:03] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -42.959818 | E_var:     0.2982 | E_err:   0.008533
[2025-10-06 07:25:08] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -42.946729 | E_var:     0.3138 | E_err:   0.008752
[2025-10-06 07:25:13] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -42.949296 | E_var:     0.2908 | E_err:   0.008427
[2025-10-06 07:25:19] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -42.962709 | E_var:     0.3588 | E_err:   0.009359
[2025-10-06 07:25:24] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -42.974021 | E_var:     0.3066 | E_err:   0.008651
[2025-10-06 07:25:29] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -42.945836 | E_var:     0.3319 | E_err:   0.009001
[2025-10-06 07:25:34] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -42.948017 | E_var:     0.3247 | E_err:   0.008904
[2025-10-06 07:25:39] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -42.972509 | E_var:     0.3512 | E_err:   0.009260
[2025-10-06 07:25:44] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -42.960946 | E_var:     0.2680 | E_err:   0.008088
[2025-10-06 07:25:50] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -42.973710 | E_var:     0.2631 | E_err:   0.008014
[2025-10-06 07:25:55] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -42.961845 | E_var:     0.3246 | E_err:   0.008902
[2025-10-06 07:26:00] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -42.967379 | E_var:     0.3187 | E_err:   0.008821
[2025-10-06 07:26:05] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -42.977790 | E_var:     0.2904 | E_err:   0.008421
[2025-10-06 07:26:10] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -42.959684 | E_var:     0.3562 | E_err:   0.009325
[2025-10-06 07:26:15] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -42.967235 | E_var:     0.3856 | E_err:   0.009703
[2025-10-06 07:26:21] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -42.964410 | E_var:     0.2817 | E_err:   0.008293
[2025-10-06 07:26:26] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -42.961181 | E_var:     0.2971 | E_err:   0.008517
[2025-10-06 07:26:31] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -42.962356 | E_var:     0.3297 | E_err:   0.008972
[2025-10-06 07:26:36] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -42.978455 | E_var:     0.3139 | E_err:   0.008755
[2025-10-06 07:26:41] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -42.954080 | E_var:     0.2772 | E_err:   0.008227
[2025-10-06 07:26:46] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -42.961293 | E_var:     0.3005 | E_err:   0.008566
[2025-10-06 07:26:51] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -42.964075 | E_var:     0.3318 | E_err:   0.009000
[2025-10-06 07:26:57] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -42.969041 | E_var:     0.2684 | E_err:   0.008095
[2025-10-06 07:27:02] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -42.969314 | E_var:     0.2747 | E_err:   0.008189
[2025-10-06 07:27:07] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -42.964616 | E_var:     0.3823 | E_err:   0.009661
[2025-10-06 07:27:12] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -42.954103 | E_var:     0.4767 | E_err:   0.010789
[2025-10-06 07:27:17] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -42.961133 | E_var:     0.3186 | E_err:   0.008820
[2025-10-06 07:27:22] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -42.959506 | E_var:     0.3102 | E_err:   0.008702
[2025-10-06 07:27:27] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -42.959453 | E_var:     0.2972 | E_err:   0.008518
[2025-10-06 07:27:33] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -42.956982 | E_var:     0.2539 | E_err:   0.007873
[2025-10-06 07:27:38] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -42.962656 | E_var:     0.3031 | E_err:   0.008602
[2025-10-06 07:27:43] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -42.966964 | E_var:     0.2708 | E_err:   0.008131
[2025-10-06 07:27:48] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -42.952612 | E_var:     0.3364 | E_err:   0.009062
[2025-10-06 07:27:53] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -42.954683 | E_var:     0.3734 | E_err:   0.009548
[2025-10-06 07:27:58] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -42.951964 | E_var:     0.3000 | E_err:   0.008557
[2025-10-06 07:28:04] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -42.963483 | E_var:     0.2773 | E_err:   0.008229
[2025-10-06 07:28:09] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -42.965681 | E_var:     0.3194 | E_err:   0.008831
[2025-10-06 07:28:14] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -42.951726 | E_var:     0.3560 | E_err:   0.009322
[2025-10-06 07:28:19] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -42.973026 | E_var:     0.3531 | E_err:   0.009285
[2025-10-06 07:28:24] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -42.956431 | E_var:     0.3061 | E_err:   0.008644
[2025-10-06 07:28:29] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -42.952817 | E_var:     0.3209 | E_err:   0.008851
[2025-10-06 07:28:34] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -42.957126 | E_var:     0.2513 | E_err:   0.007833
[2025-10-06 07:28:40] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -42.962951 | E_var:     0.2647 | E_err:   0.008039
[2025-10-06 07:28:45] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -42.964094 | E_var:     0.3089 | E_err:   0.008685
[2025-10-06 07:28:50] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -42.947563 | E_var:     0.2930 | E_err:   0.008458
[2025-10-06 07:28:55] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -42.963549 | E_var:     0.2919 | E_err:   0.008441
[2025-10-06 07:29:00] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -42.970285 | E_var:     0.3769 | E_err:   0.009593
[2025-10-06 07:29:05] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -42.980665 | E_var:     0.3051 | E_err:   0.008631
[2025-10-06 07:29:10] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -42.964128 | E_var:     0.2849 | E_err:   0.008340
[2025-10-06 07:29:16] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -42.943541 | E_var:     0.3255 | E_err:   0.008914
[2025-10-06 07:29:21] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -42.961938 | E_var:     0.3278 | E_err:   0.008946
[2025-10-06 07:29:26] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -42.949388 | E_var:     0.2998 | E_err:   0.008556
[2025-10-06 07:29:31] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -42.962073 | E_var:     0.3829 | E_err:   0.009668
[2025-10-06 07:29:36] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -42.950235 | E_var:     0.3534 | E_err:   0.009288
[2025-10-06 07:29:41] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -42.970172 | E_var:     0.3266 | E_err:   0.008930
[2025-10-06 07:29:47] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -42.963328 | E_var:     0.3149 | E_err:   0.008769
[2025-10-06 07:29:52] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -42.955173 | E_var:     0.3158 | E_err:   0.008781
[2025-10-06 07:29:57] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -42.975425 | E_var:     0.5048 | E_err:   0.011101
[2025-10-06 07:30:02] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -42.977094 | E_var:     0.3982 | E_err:   0.009860
[2025-10-06 07:30:07] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -42.952120 | E_var:     0.7624 | E_err:   0.013643
[2025-10-06 07:30:12] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -42.950630 | E_var:     0.3158 | E_err:   0.008780
[2025-10-06 07:30:17] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -42.946686 | E_var:     0.3184 | E_err:   0.008816
[2025-10-06 07:30:23] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -42.967361 | E_var:     0.3095 | E_err:   0.008693
[2025-10-06 07:30:28] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -42.949021 | E_var:     0.3353 | E_err:   0.009048
[2025-10-06 07:30:33] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -42.958846 | E_var:     0.3188 | E_err:   0.008823
[2025-10-06 07:30:38] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -42.945685 | E_var:     0.3467 | E_err:   0.009201
[2025-10-06 07:30:43] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -42.966127 | E_var:     0.2928 | E_err:   0.008455
[2025-10-06 07:30:48] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -42.963337 | E_var:     0.3166 | E_err:   0.008792
[2025-10-06 07:30:54] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -42.948219 | E_var:     0.3234 | E_err:   0.008885
[2025-10-06 07:30:59] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -42.959921 | E_var:     0.3411 | E_err:   0.009125
[2025-10-06 07:31:04] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -42.960973 | E_var:     0.3113 | E_err:   0.008717
[2025-10-06 07:31:09] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -42.959394 | E_var:     0.3172 | E_err:   0.008800
[2025-10-06 07:31:14] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -42.958738 | E_var:     0.2827 | E_err:   0.008307
[2025-10-06 07:31:19] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -42.966034 | E_var:     0.2565 | E_err:   0.007914
[2025-10-06 07:31:24] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -42.963206 | E_var:     0.3964 | E_err:   0.009838
[2025-10-06 07:31:30] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -42.958380 | E_var:     0.3056 | E_err:   0.008638
[2025-10-06 07:31:35] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -42.960907 | E_var:     0.3218 | E_err:   0.008864
[2025-10-06 07:31:40] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -42.965715 | E_var:     0.2698 | E_err:   0.008116
[2025-10-06 07:31:45] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -42.959631 | E_var:     0.2935 | E_err:   0.008465
[2025-10-06 07:31:50] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -42.959570 | E_var:     0.3260 | E_err:   0.008921
[2025-10-06 07:31:55] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -42.930691 | E_var:     0.3871 | E_err:   0.009722
[2025-10-06 07:32:01] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -42.960531 | E_var:     0.2935 | E_err:   0.008466
[2025-10-06 07:32:06] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -42.974236 | E_var:     0.2583 | E_err:   0.007941
[2025-10-06 07:32:11] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -42.965092 | E_var:     0.2699 | E_err:   0.008117
[2025-10-06 07:32:16] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -42.978255 | E_var:     0.2418 | E_err:   0.007683
[2025-10-06 07:32:21] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -42.959848 | E_var:     0.3041 | E_err:   0.008617
[2025-10-06 07:32:26] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -42.939966 | E_var:     0.3349 | E_err:   0.009043
[2025-10-06 07:32:31] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -42.956687 | E_var:     0.3506 | E_err:   0.009252
[2025-10-06 07:32:32] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 07:32:37] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -42.944822 | E_var:     0.3033 | E_err:   0.008605
[2025-10-06 07:32:42] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -42.957503 | E_var:     0.2795 | E_err:   0.008260
[2025-10-06 07:32:47] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -42.970327 | E_var:     0.3029 | E_err:   0.008599
[2025-10-06 07:32:52] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -42.980550 | E_var:     0.3174 | E_err:   0.008802
[2025-10-06 07:32:57] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -42.960405 | E_var:     0.2628 | E_err:   0.008011
[2025-10-06 07:33:03] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -42.974814 | E_var:     0.2714 | E_err:   0.008139
[2025-10-06 07:33:08] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -42.969812 | E_var:     0.2573 | E_err:   0.007926
[2025-10-06 07:33:13] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -42.951829 | E_var:     0.6628 | E_err:   0.012721
[2025-10-06 07:33:18] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -42.960290 | E_var:     0.6429 | E_err:   0.012529
[2025-10-06 07:33:23] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -42.969448 | E_var:     0.3899 | E_err:   0.009757
[2025-10-06 07:33:28] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -42.952345 | E_var:     0.3352 | E_err:   0.009046
[2025-10-06 07:33:33] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -42.956307 | E_var:     0.2997 | E_err:   0.008554
[2025-10-06 07:33:39] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -42.965162 | E_var:     0.3429 | E_err:   0.009150
[2025-10-06 07:33:44] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -42.949836 | E_var:     0.3471 | E_err:   0.009205
[2025-10-06 07:33:49] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -42.966131 | E_var:     0.2961 | E_err:   0.008502
[2025-10-06 07:33:54] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -42.963695 | E_var:     0.2642 | E_err:   0.008031
[2025-10-06 07:33:59] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -42.950278 | E_var:     0.3801 | E_err:   0.009633
[2025-10-06 07:34:04] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -42.960058 | E_var:     0.3662 | E_err:   0.009456
[2025-10-06 07:34:10] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -42.964818 | E_var:     0.2863 | E_err:   0.008360
[2025-10-06 07:34:15] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -42.954991 | E_var:     0.4022 | E_err:   0.009910
[2025-10-06 07:34:20] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -42.949333 | E_var:     0.3199 | E_err:   0.008837
[2025-10-06 07:34:25] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -42.958935 | E_var:     0.2754 | E_err:   0.008199
[2025-10-06 07:34:30] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -42.948721 | E_var:     0.3512 | E_err:   0.009259
[2025-10-06 07:34:35] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -42.958958 | E_var:     0.2734 | E_err:   0.008170
[2025-10-06 07:34:40] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -42.949131 | E_var:     0.3218 | E_err:   0.008864
[2025-10-06 07:34:46] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -42.974460 | E_var:     0.2776 | E_err:   0.008233
[2025-10-06 07:34:51] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -42.972322 | E_var:     0.3380 | E_err:   0.009084
[2025-10-06 07:34:56] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -42.952039 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 07:35:01] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -42.970897 | E_var:     0.2996 | E_err:   0.008552
[2025-10-06 07:35:06] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -42.960582 | E_var:     0.2857 | E_err:   0.008352
[2025-10-06 07:35:11] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -42.959161 | E_var:     0.2651 | E_err:   0.008045
[2025-10-06 07:35:17] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -42.960395 | E_var:     0.3931 | E_err:   0.009797
[2025-10-06 07:35:22] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -42.947833 | E_var:     0.3586 | E_err:   0.009356
[2025-10-06 07:35:27] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -42.961951 | E_var:     0.2884 | E_err:   0.008391
[2025-10-06 07:35:32] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -42.955350 | E_var:     0.3376 | E_err:   0.009079
[2025-10-06 07:35:37] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -42.957476 | E_var:     0.2928 | E_err:   0.008455
[2025-10-06 07:35:42] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -42.955414 | E_var:     0.3325 | E_err:   0.009009
[2025-10-06 07:35:48] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -42.956231 | E_var:     0.3612 | E_err:   0.009390
[2025-10-06 07:35:53] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -42.961132 | E_var:     0.3100 | E_err:   0.008699
[2025-10-06 07:35:58] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -42.963589 | E_var:     0.2691 | E_err:   0.008105
[2025-10-06 07:36:03] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -42.964517 | E_var:     0.2839 | E_err:   0.008325
[2025-10-06 07:36:08] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -42.963188 | E_var:     0.2798 | E_err:   0.008264
[2025-10-06 07:36:13] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -42.958334 | E_var:     0.2782 | E_err:   0.008241
[2025-10-06 07:36:18] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -42.977301 | E_var:     0.3592 | E_err:   0.009364
[2025-10-06 07:36:24] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -42.967422 | E_var:     0.3440 | E_err:   0.009165
[2025-10-06 07:36:29] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -42.960201 | E_var:     0.3898 | E_err:   0.009756
[2025-10-06 07:36:34] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -42.936854 | E_var:     0.3245 | E_err:   0.008901
[2025-10-06 07:36:39] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -42.972217 | E_var:     0.5469 | E_err:   0.011555
[2025-10-06 07:36:44] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -42.968566 | E_var:     0.2996 | E_err:   0.008552
[2025-10-06 07:36:49] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -42.968537 | E_var:     0.2806 | E_err:   0.008277
[2025-10-06 07:36:49] ======================================================================================================
[2025-10-06 07:36:49] ✅ Training completed successfully
[2025-10-06 07:36:49] Total restarts: 2
[2025-10-06 07:36:51] Final Energy: -42.96853659 ± 0.00827666
[2025-10-06 07:36:51] Final Variance: 0.280589
[2025-10-06 07:36:51] ======================================================================================================
[2025-10-06 07:36:51] ======================================================================================================
[2025-10-06 07:36:51] Training completed | Runtime: 5456.4s
[2025-10-06 07:36:53] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 07:36:53] ======================================================================================================
