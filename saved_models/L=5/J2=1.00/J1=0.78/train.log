[2025-10-06 07:37:17] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.77/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 07:37:17]   - 迭代次数: final
[2025-10-06 07:37:18]   - 能量: -42.968537-0.001554j ± 0.008277, Var: 0.280589
[2025-10-06 07:37:18]   - 时间戳: 2025-10-06T07:36:53.341962+08:00
[2025-10-06 07:37:37] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 07:37:37] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 07:37:37] ======================================================================================================
[2025-10-06 07:37:37] GCNN for Shastry-Sutherland Model
[2025-10-06 07:37:37] ======================================================================================================
[2025-10-06 07:37:37] System parameters:
[2025-10-06 07:37:37]   - System size: L=5, N=100
[2025-10-06 07:37:37]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-10-06 07:37:37] ------------------------------------------------------------------------------------------------------
[2025-10-06 07:37:37] Model parameters:
[2025-10-06 07:37:37]   - Number of layers = 4
[2025-10-06 07:37:37]   - Number of features = 4
[2025-10-06 07:37:37]   - Total parameters = 19628
[2025-10-06 07:37:37] ------------------------------------------------------------------------------------------------------
[2025-10-06 07:37:37] Training parameters:
[2025-10-06 07:37:37]   - Total iterations: 1050
[2025-10-06 07:37:37]   - Annealing cycles: 3
[2025-10-06 07:37:37]   - Initial period: 150
[2025-10-06 07:37:37]   - Period multiplier: 2.0
[2025-10-06 07:37:37]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 07:37:37]   - Samples: 4096
[2025-10-06 07:37:37]   - Discarded samples: 0
[2025-10-06 07:37:37]   - Chunk size: 4096
[2025-10-06 07:37:37]   - Diagonal shift: 0.15
[2025-10-06 07:37:37]   - Gradient clipping: 1.0
[2025-10-06 07:37:37]   - Checkpoint enabled: interval=100
[2025-10-06 07:37:37]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.78/model_L4F4/training/checkpoints
[2025-10-06 07:37:37] ------------------------------------------------------------------------------------------------------
[2025-10-06 07:37:37] Device status:
[2025-10-06 07:37:37]   - Devices model: NVIDIA H200 NVL
[2025-10-06 07:37:37]   - Number of devices: 1
[2025-10-06 07:37:37]   - Sharding: True
[2025-10-06 07:37:37] ======================================================================================================
[2025-10-06 07:38:09] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -41.933383 | E_var:   115.6968 | E_err:   0.168066
[2025-10-06 07:38:29] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -43.486617 | E_var:     5.9418 | E_err:   0.038087
[2025-10-06 07:38:34] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -43.591461 | E_var:     0.4473 | E_err:   0.010450
[2025-10-06 07:38:39] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -43.602100 | E_var:     0.3770 | E_err:   0.009594
[2025-10-06 07:38:44] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -43.603784 | E_var:     0.3878 | E_err:   0.009731
[2025-10-06 07:38:49] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -43.578158 | E_var:     0.4138 | E_err:   0.010051
[2025-10-06 07:38:54] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -43.619486 | E_var:     0.9939 | E_err:   0.015577
[2025-10-06 07:38:59] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -43.583540 | E_var:     0.3478 | E_err:   0.009214
[2025-10-06 07:39:04] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -43.597920 | E_var:     0.4051 | E_err:   0.009945
[2025-10-06 07:39:10] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -43.601816 | E_var:     0.2951 | E_err:   0.008487
[2025-10-06 07:39:15] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -43.607059 | E_var:     0.6580 | E_err:   0.012674
[2025-10-06 07:39:20] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -43.608487 | E_var:     0.2813 | E_err:   0.008288
[2025-10-06 07:39:25] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -43.592121 | E_var:     0.4259 | E_err:   0.010197
[2025-10-06 07:39:30] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -43.613179 | E_var:     0.3133 | E_err:   0.008745
[2025-10-06 07:39:35] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -43.586032 | E_var:     0.3098 | E_err:   0.008697
[2025-10-06 07:39:40] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -43.610113 | E_var:     0.2698 | E_err:   0.008116
[2025-10-06 07:39:45] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -43.595565 | E_var:     0.2744 | E_err:   0.008185
[2025-10-06 07:39:51] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -43.590750 | E_var:     0.3115 | E_err:   0.008720
[2025-10-06 07:39:56] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -43.599908 | E_var:     0.3037 | E_err:   0.008611
[2025-10-06 07:40:01] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -43.575894 | E_var:     0.4354 | E_err:   0.010311
[2025-10-06 07:40:06] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -43.595955 | E_var:     0.3098 | E_err:   0.008697
[2025-10-06 07:40:11] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -43.592784 | E_var:     0.2297 | E_err:   0.007489
[2025-10-06 07:40:16] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -43.603650 | E_var:     0.2810 | E_err:   0.008283
[2025-10-06 07:40:21] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -43.607527 | E_var:     0.3167 | E_err:   0.008793
[2025-10-06 07:40:27] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -43.601269 | E_var:     0.2963 | E_err:   0.008505
[2025-10-06 07:40:32] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -43.610175 | E_var:     0.2791 | E_err:   0.008254
[2025-10-06 07:40:37] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -43.595828 | E_var:     0.3330 | E_err:   0.009017
[2025-10-06 07:40:42] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -43.587836 | E_var:     0.3287 | E_err:   0.008959
[2025-10-06 07:40:47] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -43.610480 | E_var:     0.3199 | E_err:   0.008838
[2025-10-06 07:40:52] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -43.605269 | E_var:     0.2911 | E_err:   0.008430
[2025-10-06 07:40:57] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -43.594388 | E_var:     0.2814 | E_err:   0.008289
[2025-10-06 07:41:03] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -43.603937 | E_var:     0.3165 | E_err:   0.008791
[2025-10-06 07:41:08] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -43.594376 | E_var:     0.2648 | E_err:   0.008040
[2025-10-06 07:41:13] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -43.604246 | E_var:     0.3278 | E_err:   0.008946
[2025-10-06 07:41:18] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -43.584801 | E_var:     0.2971 | E_err:   0.008516
[2025-10-06 07:41:23] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -43.606704 | E_var:     0.3412 | E_err:   0.009127
[2025-10-06 07:41:28] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -43.586187 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 07:41:33] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -43.584607 | E_var:     0.3128 | E_err:   0.008739
[2025-10-06 07:41:39] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -43.597239 | E_var:     0.2926 | E_err:   0.008452
[2025-10-06 07:41:44] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -43.602598 | E_var:     0.2963 | E_err:   0.008505
[2025-10-06 07:41:49] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -43.591875 | E_var:     0.2555 | E_err:   0.007898
[2025-10-06 07:41:54] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -43.592786 | E_var:     0.3197 | E_err:   0.008835
[2025-10-06 07:41:59] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -43.603800 | E_var:     0.3946 | E_err:   0.009815
[2025-10-06 07:42:04] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -43.588570 | E_var:     0.3400 | E_err:   0.009111
[2025-10-06 07:42:09] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -43.600319 | E_var:     0.2935 | E_err:   0.008464
[2025-10-06 07:42:15] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -43.611810 | E_var:     0.2639 | E_err:   0.008026
[2025-10-06 07:42:20] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -43.608075 | E_var:     0.3070 | E_err:   0.008657
[2025-10-06 07:42:25] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -43.592853 | E_var:     0.3413 | E_err:   0.009128
[2025-10-06 07:42:30] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -43.590484 | E_var:     0.3606 | E_err:   0.009383
[2025-10-06 07:42:35] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -43.590074 | E_var:     0.2612 | E_err:   0.007985
[2025-10-06 07:42:40] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -43.588813 | E_var:     0.3069 | E_err:   0.008656
[2025-10-06 07:42:45] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -43.600304 | E_var:     0.2544 | E_err:   0.007881
[2025-10-06 07:42:51] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -43.576059 | E_var:     0.3163 | E_err:   0.008787
[2025-10-06 07:42:56] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -43.605844 | E_var:     0.2422 | E_err:   0.007690
[2025-10-06 07:43:01] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -43.604822 | E_var:     0.2647 | E_err:   0.008039
[2025-10-06 07:43:06] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -43.603847 | E_var:     0.2607 | E_err:   0.007979
[2025-10-06 07:43:11] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -43.609690 | E_var:     0.2395 | E_err:   0.007647
[2025-10-06 07:43:16] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -43.612077 | E_var:     0.3479 | E_err:   0.009216
[2025-10-06 07:43:21] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -43.596298 | E_var:     0.2744 | E_err:   0.008185
[2025-10-06 07:43:26] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -43.607078 | E_var:     0.2942 | E_err:   0.008476
[2025-10-06 07:43:32] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -43.591858 | E_var:     0.2640 | E_err:   0.008028
[2025-10-06 07:43:37] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -43.606865 | E_var:     0.3100 | E_err:   0.008700
[2025-10-06 07:43:42] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -43.598491 | E_var:     0.2316 | E_err:   0.007519
[2025-10-06 07:43:47] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -43.593159 | E_var:     0.3086 | E_err:   0.008681
[2025-10-06 07:43:52] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -43.599048 | E_var:     0.2689 | E_err:   0.008103
[2025-10-06 07:43:57] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -43.599689 | E_var:     0.2584 | E_err:   0.007943
[2025-10-06 07:44:02] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -43.597452 | E_var:     0.3152 | E_err:   0.008773
[2025-10-06 07:44:08] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -43.617556 | E_var:     0.2501 | E_err:   0.007814
[2025-10-06 07:44:13] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -43.585665 | E_var:     0.3243 | E_err:   0.008898
[2025-10-06 07:44:18] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -43.584001 | E_var:     0.3147 | E_err:   0.008765
[2025-10-06 07:44:23] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -43.615563 | E_var:     0.2242 | E_err:   0.007399
[2025-10-06 07:44:28] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -43.593646 | E_var:     0.3199 | E_err:   0.008838
[2025-10-06 07:44:33] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -43.605427 | E_var:     0.3094 | E_err:   0.008691
[2025-10-06 07:44:38] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -43.600270 | E_var:     0.2747 | E_err:   0.008189
[2025-10-06 07:44:44] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -43.606944 | E_var:     0.2993 | E_err:   0.008549
[2025-10-06 07:44:49] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -43.619928 | E_var:     0.2372 | E_err:   0.007611
[2025-10-06 07:44:54] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -43.596734 | E_var:     0.3900 | E_err:   0.009758
[2025-10-06 07:44:59] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -43.593687 | E_var:     0.4137 | E_err:   0.010050
[2025-10-06 07:45:04] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -43.597739 | E_var:     0.2464 | E_err:   0.007755
[2025-10-06 07:45:09] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -43.597491 | E_var:     0.3158 | E_err:   0.008781
[2025-10-06 07:45:14] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -43.609202 | E_var:     0.7084 | E_err:   0.013151
[2025-10-06 07:45:19] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -43.615844 | E_var:     0.2735 | E_err:   0.008171
[2025-10-06 07:45:25] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -43.593028 | E_var:     0.2845 | E_err:   0.008334
[2025-10-06 07:45:30] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -43.596077 | E_var:     0.2761 | E_err:   0.008209
[2025-10-06 07:45:35] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -43.603312 | E_var:     0.3007 | E_err:   0.008568
[2025-10-06 07:45:40] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -43.607803 | E_var:     0.2989 | E_err:   0.008543
[2025-10-06 07:45:45] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -43.592706 | E_var:     0.3598 | E_err:   0.009373
[2025-10-06 07:45:50] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -43.590052 | E_var:     0.2815 | E_err:   0.008291
[2025-10-06 07:45:55] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -43.587297 | E_var:     0.4198 | E_err:   0.010124
[2025-10-06 07:46:01] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -43.587144 | E_var:     0.2910 | E_err:   0.008429
[2025-10-06 07:46:06] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -43.595311 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 07:46:11] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -43.588253 | E_var:     0.2848 | E_err:   0.008338
[2025-10-06 07:46:16] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -43.583341 | E_var:     0.2839 | E_err:   0.008325
[2025-10-06 07:46:21] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -43.595933 | E_var:     0.2724 | E_err:   0.008155
[2025-10-06 07:46:26] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -43.594479 | E_var:     0.6104 | E_err:   0.012207
[2025-10-06 07:46:31] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -43.605995 | E_var:     0.3271 | E_err:   0.008936
[2025-10-06 07:46:37] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -43.593800 | E_var:     0.3069 | E_err:   0.008656
[2025-10-06 07:46:42] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -43.589564 | E_var:     0.4010 | E_err:   0.009894
[2025-10-06 07:46:47] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -43.587153 | E_var:     0.2868 | E_err:   0.008367
[2025-10-06 07:46:52] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -43.602373 | E_var:     0.2981 | E_err:   0.008530
[2025-10-06 07:46:52] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 07:46:57] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -43.602712 | E_var:     0.3425 | E_err:   0.009144
[2025-10-06 07:47:02] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -43.590756 | E_var:     0.3920 | E_err:   0.009783
[2025-10-06 07:47:08] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -43.604009 | E_var:     0.3918 | E_err:   0.009781
[2025-10-06 07:47:13] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -43.588968 | E_var:     0.3588 | E_err:   0.009359
[2025-10-06 07:47:18] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -43.591626 | E_var:     0.2206 | E_err:   0.007339
[2025-10-06 07:47:23] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -43.571396 | E_var:     0.5164 | E_err:   0.011229
[2025-10-06 07:47:28] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -43.596833 | E_var:     0.2717 | E_err:   0.008145
[2025-10-06 07:47:33] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -43.602912 | E_var:     0.2701 | E_err:   0.008121
[2025-10-06 07:47:38] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -43.598234 | E_var:     0.2640 | E_err:   0.008028
[2025-10-06 07:47:44] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -43.608918 | E_var:     0.3553 | E_err:   0.009313
[2025-10-06 07:47:49] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -43.605508 | E_var:     0.3063 | E_err:   0.008647
[2025-10-06 07:47:54] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -43.598685 | E_var:     0.2847 | E_err:   0.008337
[2025-10-06 07:47:59] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -43.582162 | E_var:     0.3057 | E_err:   0.008639
[2025-10-06 07:48:04] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -43.593720 | E_var:     0.2562 | E_err:   0.007909
[2025-10-06 07:48:09] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -43.593798 | E_var:     0.2985 | E_err:   0.008537
[2025-10-06 07:48:14] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -43.590643 | E_var:     0.2974 | E_err:   0.008521
[2025-10-06 07:48:20] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -43.598771 | E_var:     0.2836 | E_err:   0.008321
[2025-10-06 07:48:25] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -43.591563 | E_var:     0.2658 | E_err:   0.008056
[2025-10-06 07:48:30] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -43.590109 | E_var:     0.2607 | E_err:   0.007979
[2025-10-06 07:48:35] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -43.599772 | E_var:     0.3083 | E_err:   0.008676
[2025-10-06 07:48:40] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -43.584437 | E_var:     0.2077 | E_err:   0.007121
[2025-10-06 07:48:45] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -43.599562 | E_var:     0.5983 | E_err:   0.012086
[2025-10-06 07:48:50] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -43.597424 | E_var:     0.3403 | E_err:   0.009115
[2025-10-06 07:48:56] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -43.585601 | E_var:     0.3991 | E_err:   0.009871
[2025-10-06 07:49:01] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -43.599818 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 07:49:06] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -43.601807 | E_var:     0.2489 | E_err:   0.007795
[2025-10-06 07:49:11] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -43.603594 | E_var:     0.2621 | E_err:   0.007999
[2025-10-06 07:49:16] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -43.594645 | E_var:     0.2921 | E_err:   0.008444
[2025-10-06 07:49:21] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -43.599657 | E_var:     0.2785 | E_err:   0.008246
[2025-10-06 07:49:26] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -43.600506 | E_var:     0.2647 | E_err:   0.008039
[2025-10-06 07:49:32] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -43.589021 | E_var:     0.2164 | E_err:   0.007269
[2025-10-06 07:49:37] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -43.599761 | E_var:     0.2770 | E_err:   0.008224
[2025-10-06 07:49:42] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -43.615306 | E_var:     0.2733 | E_err:   0.008169
[2025-10-06 07:49:47] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -43.610967 | E_var:     0.2541 | E_err:   0.007877
[2025-10-06 07:49:52] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -43.594116 | E_var:     0.2434 | E_err:   0.007708
[2025-10-06 07:49:57] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -43.606886 | E_var:     0.2981 | E_err:   0.008530
[2025-10-06 07:50:02] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -43.602344 | E_var:     0.2500 | E_err:   0.007813
[2025-10-06 07:50:07] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -43.605746 | E_var:     0.2441 | E_err:   0.007719
[2025-10-06 07:50:13] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -43.596722 | E_var:     0.2579 | E_err:   0.007935
[2025-10-06 07:50:18] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -43.605914 | E_var:     0.2983 | E_err:   0.008534
[2025-10-06 07:50:23] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -43.596633 | E_var:     0.3400 | E_err:   0.009111
[2025-10-06 07:50:28] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -43.585426 | E_var:     0.2951 | E_err:   0.008489
[2025-10-06 07:50:33] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -43.590873 | E_var:     0.2936 | E_err:   0.008467
[2025-10-06 07:50:38] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -43.609367 | E_var:     0.3170 | E_err:   0.008797
[2025-10-06 07:50:43] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -43.600038 | E_var:     0.3445 | E_err:   0.009170
[2025-10-06 07:50:49] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -43.598503 | E_var:     0.2938 | E_err:   0.008470
[2025-10-06 07:50:54] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -43.609738 | E_var:     0.3592 | E_err:   0.009364
[2025-10-06 07:50:59] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -43.607006 | E_var:     0.3324 | E_err:   0.009009
[2025-10-06 07:51:04] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -43.602239 | E_var:     0.3343 | E_err:   0.009034
[2025-10-06 07:51:09] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -43.602357 | E_var:     0.2692 | E_err:   0.008107
[2025-10-06 07:51:09] 🔄 RESTART #1 | Period: 300
[2025-10-06 07:51:14] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -43.596518 | E_var:     0.2802 | E_err:   0.008272
[2025-10-06 07:51:19] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -43.591490 | E_var:     0.3252 | E_err:   0.008910
[2025-10-06 07:51:25] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -43.588958 | E_var:     0.2886 | E_err:   0.008394
[2025-10-06 07:51:30] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -43.587971 | E_var:     0.3286 | E_err:   0.008956
[2025-10-06 07:51:35] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -43.595201 | E_var:     0.3122 | E_err:   0.008730
[2025-10-06 07:51:40] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -43.589553 | E_var:     0.4043 | E_err:   0.009935
[2025-10-06 07:51:45] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -43.600957 | E_var:     0.2914 | E_err:   0.008434
[2025-10-06 07:51:50] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -43.597743 | E_var:     0.2677 | E_err:   0.008085
[2025-10-06 07:51:55] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -43.601325 | E_var:     0.3253 | E_err:   0.008912
[2025-10-06 07:52:01] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -43.595325 | E_var:     0.2726 | E_err:   0.008158
[2025-10-06 07:52:06] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -43.600004 | E_var:     0.2380 | E_err:   0.007623
[2025-10-06 07:52:11] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -43.610076 | E_var:     0.3247 | E_err:   0.008903
[2025-10-06 07:52:16] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -43.619721 | E_var:     0.2825 | E_err:   0.008305
[2025-10-06 07:52:21] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -43.607122 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 07:52:26] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -43.590614 | E_var:     0.2434 | E_err:   0.007709
[2025-10-06 07:52:31] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -43.604490 | E_var:     0.4221 | E_err:   0.010152
[2025-10-06 07:52:37] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -43.599976 | E_var:     0.2524 | E_err:   0.007850
[2025-10-06 07:52:42] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -43.605994 | E_var:     0.2763 | E_err:   0.008214
[2025-10-06 07:52:47] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -43.596198 | E_var:     0.2546 | E_err:   0.007884
[2025-10-06 07:52:52] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -43.602335 | E_var:     0.3516 | E_err:   0.009265
[2025-10-06 07:52:57] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -43.596268 | E_var:     0.2382 | E_err:   0.007626
[2025-10-06 07:53:02] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -43.587305 | E_var:     0.2764 | E_err:   0.008214
[2025-10-06 07:53:07] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -43.587808 | E_var:     0.3072 | E_err:   0.008661
[2025-10-06 07:53:13] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -43.573517 | E_var:     0.3211 | E_err:   0.008854
[2025-10-06 07:53:18] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -43.591962 | E_var:     0.2433 | E_err:   0.007706
[2025-10-06 07:53:23] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -43.601866 | E_var:     0.3832 | E_err:   0.009672
[2025-10-06 07:53:28] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -43.606494 | E_var:     0.2626 | E_err:   0.008007
[2025-10-06 07:53:33] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -43.600756 | E_var:     0.2768 | E_err:   0.008220
[2025-10-06 07:53:38] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -43.598336 | E_var:     0.2884 | E_err:   0.008392
[2025-10-06 07:53:43] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -43.579693 | E_var:     0.4084 | E_err:   0.009986
[2025-10-06 07:53:48] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -43.590548 | E_var:     1.2938 | E_err:   0.017773
[2025-10-06 07:53:54] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -43.592010 | E_var:     0.2837 | E_err:   0.008322
[2025-10-06 07:53:59] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -43.594720 | E_var:     0.5845 | E_err:   0.011946
[2025-10-06 07:54:04] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -43.580552 | E_var:     0.3099 | E_err:   0.008698
[2025-10-06 07:54:09] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -43.607871 | E_var:     0.2986 | E_err:   0.008538
[2025-10-06 07:54:14] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -43.602755 | E_var:     0.2964 | E_err:   0.008506
[2025-10-06 07:54:19] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -43.597332 | E_var:     0.3552 | E_err:   0.009312
[2025-10-06 07:54:24] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -43.596238 | E_var:     0.4453 | E_err:   0.010427
[2025-10-06 07:54:30] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -43.586616 | E_var:     0.3155 | E_err:   0.008777
[2025-10-06 07:54:35] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -43.588492 | E_var:     0.2234 | E_err:   0.007385
[2025-10-06 07:54:40] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -43.590488 | E_var:     0.3170 | E_err:   0.008798
[2025-10-06 07:54:45] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -43.604782 | E_var:     0.3281 | E_err:   0.008950
[2025-10-06 07:54:50] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -43.602255 | E_var:     0.2921 | E_err:   0.008445
[2025-10-06 07:54:55] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -43.596867 | E_var:     0.2803 | E_err:   0.008272
[2025-10-06 07:55:00] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -43.593207 | E_var:     0.2458 | E_err:   0.007746
[2025-10-06 07:55:06] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -43.598911 | E_var:     0.2984 | E_err:   0.008535
[2025-10-06 07:55:11] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -43.590570 | E_var:     0.4171 | E_err:   0.010092
[2025-10-06 07:55:16] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -43.604198 | E_var:     0.2338 | E_err:   0.007555
[2025-10-06 07:55:21] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -43.607535 | E_var:     0.4474 | E_err:   0.010451
[2025-10-06 07:55:26] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -43.605570 | E_var:     0.2710 | E_err:   0.008133
[2025-10-06 07:55:26] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 07:55:31] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -43.604326 | E_var:     0.4219 | E_err:   0.010148
[2025-10-06 07:55:36] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -43.588060 | E_var:     0.2567 | E_err:   0.007916
[2025-10-06 07:55:42] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -43.592138 | E_var:     0.2429 | E_err:   0.007701
[2025-10-06 07:55:47] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -43.586362 | E_var:     0.3063 | E_err:   0.008648
[2025-10-06 07:55:52] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -43.599479 | E_var:     0.2638 | E_err:   0.008025
[2025-10-06 07:55:57] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -43.623827 | E_var:     0.3392 | E_err:   0.009100
[2025-10-06 07:56:02] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -43.588056 | E_var:     0.3895 | E_err:   0.009751
[2025-10-06 07:56:07] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -43.584211 | E_var:     0.3745 | E_err:   0.009561
[2025-10-06 07:56:12] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -43.593673 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 07:56:18] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -43.602811 | E_var:     0.2695 | E_err:   0.008111
[2025-10-06 07:56:23] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -43.600314 | E_var:     0.3251 | E_err:   0.008909
[2025-10-06 07:56:28] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -43.605006 | E_var:     0.2428 | E_err:   0.007699
[2025-10-06 07:56:33] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -43.595665 | E_var:     0.3492 | E_err:   0.009234
[2025-10-06 07:56:38] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -43.605377 | E_var:     0.5187 | E_err:   0.011253
[2025-10-06 07:56:43] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -43.593562 | E_var:     0.3020 | E_err:   0.008586
[2025-10-06 07:56:48] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -43.600211 | E_var:     0.3037 | E_err:   0.008611
[2025-10-06 07:56:54] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -43.604603 | E_var:     0.2628 | E_err:   0.008010
[2025-10-06 07:56:59] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -43.599573 | E_var:     0.3008 | E_err:   0.008569
[2025-10-06 07:57:04] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -43.603058 | E_var:     0.2660 | E_err:   0.008059
[2025-10-06 07:57:09] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -43.600184 | E_var:     0.2444 | E_err:   0.007724
[2025-10-06 07:57:14] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -43.574609 | E_var:     0.2738 | E_err:   0.008176
[2025-10-06 07:57:19] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -43.585982 | E_var:     0.4185 | E_err:   0.010109
[2025-10-06 07:57:24] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -43.597967 | E_var:     0.3030 | E_err:   0.008601
[2025-10-06 07:57:29] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -43.594272 | E_var:     0.3383 | E_err:   0.009088
[2025-10-06 07:57:35] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -43.594622 | E_var:     0.2958 | E_err:   0.008498
[2025-10-06 07:57:40] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -43.600440 | E_var:     0.3077 | E_err:   0.008667
[2025-10-06 07:57:45] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -43.597541 | E_var:     0.3040 | E_err:   0.008616
[2025-10-06 07:57:50] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -43.591849 | E_var:     0.3100 | E_err:   0.008699
[2025-10-06 07:57:55] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -43.599979 | E_var:     0.3221 | E_err:   0.008868
[2025-10-06 07:58:00] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -43.578209 | E_var:     0.5157 | E_err:   0.011220
[2025-10-06 07:58:05] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -43.594921 | E_var:     0.3017 | E_err:   0.008583
[2025-10-06 07:58:11] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -43.591956 | E_var:     0.3265 | E_err:   0.008928
[2025-10-06 07:58:16] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -43.593534 | E_var:     0.2461 | E_err:   0.007751
[2025-10-06 07:58:21] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -43.595872 | E_var:     0.2834 | E_err:   0.008317
[2025-10-06 07:58:26] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -43.601054 | E_var:     0.4020 | E_err:   0.009907
[2025-10-06 07:58:31] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -43.596242 | E_var:     0.3654 | E_err:   0.009445
[2025-10-06 07:58:36] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -43.580102 | E_var:     0.3010 | E_err:   0.008573
[2025-10-06 07:58:41] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -43.592286 | E_var:     0.2964 | E_err:   0.008507
[2025-10-06 07:58:46] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -43.598994 | E_var:     0.3290 | E_err:   0.008963
[2025-10-06 07:58:52] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -43.583804 | E_var:     0.3405 | E_err:   0.009117
[2025-10-06 07:58:57] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -43.592918 | E_var:     0.2206 | E_err:   0.007339
[2025-10-06 07:59:02] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -43.595115 | E_var:     0.2630 | E_err:   0.008013
[2025-10-06 07:59:07] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -43.621882 | E_var:     0.2680 | E_err:   0.008088
[2025-10-06 07:59:12] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -43.596329 | E_var:     0.2656 | E_err:   0.008053
[2025-10-06 07:59:17] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -43.593269 | E_var:     0.3413 | E_err:   0.009129
[2025-10-06 07:59:22] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -43.599849 | E_var:     0.3828 | E_err:   0.009667
[2025-10-06 07:59:28] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -43.584518 | E_var:     0.3668 | E_err:   0.009463
[2025-10-06 07:59:33] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -43.602834 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 07:59:38] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -43.592499 | E_var:     0.2739 | E_err:   0.008177
[2025-10-06 07:59:43] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -43.599850 | E_var:     0.3298 | E_err:   0.008974
[2025-10-06 07:59:48] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -43.583171 | E_var:     0.2504 | E_err:   0.007819
[2025-10-06 07:59:53] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -43.595414 | E_var:     0.3375 | E_err:   0.009077
[2025-10-06 07:59:58] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -43.583955 | E_var:     0.2977 | E_err:   0.008525
[2025-10-06 08:00:04] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -43.593605 | E_var:     0.2676 | E_err:   0.008082
[2025-10-06 08:00:09] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -43.593633 | E_var:     0.2750 | E_err:   0.008194
[2025-10-06 08:00:14] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -43.607165 | E_var:     0.4203 | E_err:   0.010130
[2025-10-06 08:00:19] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -43.612850 | E_var:     0.2579 | E_err:   0.007934
[2025-10-06 08:00:24] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -43.618635 | E_var:     0.3593 | E_err:   0.009366
[2025-10-06 08:00:29] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -43.605190 | E_var:     0.2816 | E_err:   0.008291
[2025-10-06 08:00:34] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -43.597303 | E_var:     0.2941 | E_err:   0.008473
[2025-10-06 08:00:39] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -43.593081 | E_var:     0.2773 | E_err:   0.008227
[2025-10-06 08:00:45] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -43.607000 | E_var:     0.2594 | E_err:   0.007958
[2025-10-06 08:00:50] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -43.587527 | E_var:     0.3002 | E_err:   0.008560
[2025-10-06 08:00:55] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -43.590407 | E_var:     0.2423 | E_err:   0.007691
[2025-10-06 08:01:00] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -43.589887 | E_var:     0.2460 | E_err:   0.007750
[2025-10-06 08:01:05] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -43.606161 | E_var:     0.2521 | E_err:   0.007846
[2025-10-06 08:01:10] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -43.576632 | E_var:     0.3249 | E_err:   0.008906
[2025-10-06 08:01:15] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -43.592286 | E_var:     0.2878 | E_err:   0.008383
[2025-10-06 08:01:21] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -43.592870 | E_var:     0.3089 | E_err:   0.008684
[2025-10-06 08:01:26] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -43.593263 | E_var:     0.2976 | E_err:   0.008524
[2025-10-06 08:01:31] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -43.611735 | E_var:     0.4768 | E_err:   0.010789
[2025-10-06 08:01:36] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -43.605155 | E_var:     0.2646 | E_err:   0.008038
[2025-10-06 08:01:41] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -43.592094 | E_var:     0.3164 | E_err:   0.008789
[2025-10-06 08:01:46] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -43.599103 | E_var:     0.2497 | E_err:   0.007807
[2025-10-06 08:01:51] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -43.605503 | E_var:     0.3295 | E_err:   0.008968
[2025-10-06 08:01:57] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -43.582478 | E_var:     0.4056 | E_err:   0.009951
[2025-10-06 08:02:02] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -43.597322 | E_var:     0.2601 | E_err:   0.007968
[2025-10-06 08:02:07] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -43.602893 | E_var:     0.3427 | E_err:   0.009148
[2025-10-06 08:02:12] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -43.597423 | E_var:     0.2396 | E_err:   0.007649
[2025-10-06 08:02:17] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -43.612528 | E_var:     0.3094 | E_err:   0.008691
[2025-10-06 08:02:22] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -43.598863 | E_var:     0.2565 | E_err:   0.007914
[2025-10-06 08:02:27] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -43.587983 | E_var:     0.3368 | E_err:   0.009069
[2025-10-06 08:02:32] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -43.589570 | E_var:     0.2857 | E_err:   0.008351
[2025-10-06 08:02:38] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -43.584420 | E_var:     0.2856 | E_err:   0.008350
[2025-10-06 08:02:43] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -43.625235 | E_var:     0.7201 | E_err:   0.013259
[2025-10-06 08:02:48] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -43.602070 | E_var:     0.3627 | E_err:   0.009410
[2025-10-06 08:02:53] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -43.605375 | E_var:     0.2868 | E_err:   0.008368
[2025-10-06 08:02:58] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -43.586949 | E_var:     0.3299 | E_err:   0.008975
[2025-10-06 08:03:03] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -43.593177 | E_var:     0.2670 | E_err:   0.008074
[2025-10-06 08:03:08] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -43.600591 | E_var:     0.3589 | E_err:   0.009361
[2025-10-06 08:03:14] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -43.600766 | E_var:     0.2856 | E_err:   0.008351
[2025-10-06 08:03:19] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -43.604440 | E_var:     0.2324 | E_err:   0.007533
[2025-10-06 08:03:24] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -43.601441 | E_var:     0.3244 | E_err:   0.008899
[2025-10-06 08:03:29] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -43.597928 | E_var:     0.3554 | E_err:   0.009315
[2025-10-06 08:03:34] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -43.581380 | E_var:     0.3470 | E_err:   0.009204
[2025-10-06 08:03:39] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -43.607526 | E_var:     0.3108 | E_err:   0.008710
[2025-10-06 08:03:44] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -43.600506 | E_var:     0.3245 | E_err:   0.008901
[2025-10-06 08:03:49] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -43.588272 | E_var:     0.2872 | E_err:   0.008373
[2025-10-06 08:03:55] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -43.604491 | E_var:     0.3535 | E_err:   0.009290
[2025-10-06 08:04:00] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -43.612505 | E_var:     0.3057 | E_err:   0.008640
[2025-10-06 08:04:00] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 08:04:05] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -43.601211 | E_var:     0.2679 | E_err:   0.008087
[2025-10-06 08:04:10] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -43.607848 | E_var:     0.3354 | E_err:   0.009050
[2025-10-06 08:04:15] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -43.596609 | E_var:     0.2924 | E_err:   0.008449
[2025-10-06 08:04:21] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -43.615718 | E_var:     0.3080 | E_err:   0.008671
[2025-10-06 08:04:26] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -43.585577 | E_var:     0.2744 | E_err:   0.008184
[2025-10-06 08:04:31] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -43.604025 | E_var:     0.2972 | E_err:   0.008518
[2025-10-06 08:04:36] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -43.601403 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 08:04:41] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -43.592976 | E_var:     0.2841 | E_err:   0.008329
[2025-10-06 08:04:46] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -43.609300 | E_var:     0.2988 | E_err:   0.008541
[2025-10-06 08:04:51] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -43.615746 | E_var:     0.2946 | E_err:   0.008481
[2025-10-06 08:04:56] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -43.605532 | E_var:     0.2448 | E_err:   0.007731
[2025-10-06 08:05:02] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -43.601427 | E_var:     0.3556 | E_err:   0.009317
[2025-10-06 08:05:07] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -43.581695 | E_var:     0.2709 | E_err:   0.008132
[2025-10-06 08:05:12] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -43.578523 | E_var:     0.2853 | E_err:   0.008346
[2025-10-06 08:05:17] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -43.603186 | E_var:     0.3184 | E_err:   0.008817
[2025-10-06 08:05:22] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -43.598247 | E_var:     0.2704 | E_err:   0.008124
[2025-10-06 08:05:27] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -43.613110 | E_var:     0.3624 | E_err:   0.009406
[2025-10-06 08:05:32] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -43.604197 | E_var:     0.3086 | E_err:   0.008680
[2025-10-06 08:05:38] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -43.604642 | E_var:     0.2345 | E_err:   0.007567
[2025-10-06 08:05:43] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -43.589399 | E_var:     0.2936 | E_err:   0.008467
[2025-10-06 08:05:48] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -43.598936 | E_var:     0.3499 | E_err:   0.009242
[2025-10-06 08:05:53] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -43.606771 | E_var:     0.3413 | E_err:   0.009128
[2025-10-06 08:05:58] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -43.608301 | E_var:     0.2677 | E_err:   0.008084
[2025-10-06 08:06:03] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -43.616700 | E_var:     0.2271 | E_err:   0.007446
[2025-10-06 08:06:08] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -43.609406 | E_var:     0.3528 | E_err:   0.009281
[2025-10-06 08:06:14] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -43.596756 | E_var:     0.5105 | E_err:   0.011164
[2025-10-06 08:06:19] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -43.576554 | E_var:     0.3001 | E_err:   0.008559
[2025-10-06 08:06:24] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -43.590611 | E_var:     0.2688 | E_err:   0.008101
[2025-10-06 08:06:29] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -43.603214 | E_var:     0.2686 | E_err:   0.008098
[2025-10-06 08:06:34] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -43.594049 | E_var:     0.2713 | E_err:   0.008138
[2025-10-06 08:06:39] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -43.601336 | E_var:     0.3142 | E_err:   0.008758
[2025-10-06 08:06:44] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -43.577110 | E_var:     0.2834 | E_err:   0.008318
[2025-10-06 08:06:49] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -43.591057 | E_var:     0.2578 | E_err:   0.007933
[2025-10-06 08:06:55] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -43.594604 | E_var:     0.2523 | E_err:   0.007849
[2025-10-06 08:07:00] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -43.604548 | E_var:     0.2625 | E_err:   0.008006
[2025-10-06 08:07:05] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -43.586747 | E_var:     0.2900 | E_err:   0.008415
[2025-10-06 08:07:10] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -43.599813 | E_var:     0.2741 | E_err:   0.008181
[2025-10-06 08:07:15] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -43.601452 | E_var:     0.2900 | E_err:   0.008414
[2025-10-06 08:07:20] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -43.605986 | E_var:     0.2516 | E_err:   0.007837
[2025-10-06 08:07:25] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -43.598654 | E_var:     0.2870 | E_err:   0.008370
[2025-10-06 08:07:31] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -43.590972 | E_var:     0.3012 | E_err:   0.008575
[2025-10-06 08:07:36] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -43.599256 | E_var:     0.3805 | E_err:   0.009639
[2025-10-06 08:07:41] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -43.602615 | E_var:     0.2574 | E_err:   0.007928
[2025-10-06 08:07:46] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -43.604599 | E_var:     0.3316 | E_err:   0.008997
[2025-10-06 08:07:51] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -43.607338 | E_var:     0.3826 | E_err:   0.009664
[2025-10-06 08:07:56] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -43.598507 | E_var:     0.3163 | E_err:   0.008788
[2025-10-06 08:08:01] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -43.587260 | E_var:     0.2890 | E_err:   0.008400
[2025-10-06 08:08:07] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -43.593149 | E_var:     0.2616 | E_err:   0.007992
[2025-10-06 08:08:12] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -43.595699 | E_var:     0.3023 | E_err:   0.008591
[2025-10-06 08:08:17] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -43.582747 | E_var:     0.3262 | E_err:   0.008924
[2025-10-06 08:08:22] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -43.602307 | E_var:     0.2699 | E_err:   0.008118
[2025-10-06 08:08:27] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -43.610977 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 08:08:32] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -43.592346 | E_var:     0.2901 | E_err:   0.008416
[2025-10-06 08:08:37] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -43.588090 | E_var:     0.3606 | E_err:   0.009383
[2025-10-06 08:08:42] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -43.607169 | E_var:     0.2914 | E_err:   0.008435
[2025-10-06 08:08:48] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -43.598343 | E_var:     0.2673 | E_err:   0.008078
[2025-10-06 08:08:53] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -43.609995 | E_var:     0.2646 | E_err:   0.008037
[2025-10-06 08:08:58] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -43.599767 | E_var:     0.2439 | E_err:   0.007716
[2025-10-06 08:09:03] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -43.602533 | E_var:     0.2795 | E_err:   0.008260
[2025-10-06 08:09:08] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -43.613303 | E_var:     0.2871 | E_err:   0.008372
[2025-10-06 08:09:13] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -43.588194 | E_var:     0.3230 | E_err:   0.008881
[2025-10-06 08:09:18] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -43.589798 | E_var:     0.2613 | E_err:   0.007987
[2025-10-06 08:09:24] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -43.590783 | E_var:     0.3112 | E_err:   0.008717
[2025-10-06 08:09:29] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -43.606039 | E_var:     0.3099 | E_err:   0.008698
[2025-10-06 08:09:34] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -43.601576 | E_var:     0.2795 | E_err:   0.008261
[2025-10-06 08:09:39] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -43.603755 | E_var:     0.2851 | E_err:   0.008343
[2025-10-06 08:09:44] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -43.586649 | E_var:     0.2960 | E_err:   0.008500
[2025-10-06 08:09:49] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -43.605606 | E_var:     0.2814 | E_err:   0.008289
[2025-10-06 08:09:54] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -43.608136 | E_var:     0.3635 | E_err:   0.009421
[2025-10-06 08:09:59] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -43.596093 | E_var:     0.2514 | E_err:   0.007835
[2025-10-06 08:10:05] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -43.612237 | E_var:     0.3090 | E_err:   0.008686
[2025-10-06 08:10:10] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -43.597802 | E_var:     0.2976 | E_err:   0.008524
[2025-10-06 08:10:15] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -43.598786 | E_var:     0.2201 | E_err:   0.007330
[2025-10-06 08:10:20] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -43.596437 | E_var:     0.2945 | E_err:   0.008479
[2025-10-06 08:10:25] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -43.592866 | E_var:     0.2496 | E_err:   0.007806
[2025-10-06 08:10:30] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -43.595913 | E_var:     0.2336 | E_err:   0.007552
[2025-10-06 08:10:35] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -43.596836 | E_var:     0.2684 | E_err:   0.008095
[2025-10-06 08:10:41] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -43.595823 | E_var:     0.2707 | E_err:   0.008130
[2025-10-06 08:10:46] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -43.598707 | E_var:     0.3565 | E_err:   0.009329
[2025-10-06 08:10:51] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -43.590605 | E_var:     0.3050 | E_err:   0.008629
[2025-10-06 08:10:56] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -43.591756 | E_var:     0.2942 | E_err:   0.008475
[2025-10-06 08:11:01] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -43.604178 | E_var:     0.3502 | E_err:   0.009247
[2025-10-06 08:11:06] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -43.605444 | E_var:     0.2993 | E_err:   0.008548
[2025-10-06 08:11:11] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -43.608310 | E_var:     0.2768 | E_err:   0.008221
[2025-10-06 08:11:16] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -43.598762 | E_var:     0.2613 | E_err:   0.007987
[2025-10-06 08:11:22] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -43.597768 | E_var:     0.2779 | E_err:   0.008236
[2025-10-06 08:11:27] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -43.580110 | E_var:     0.3424 | E_err:   0.009143
[2025-10-06 08:11:32] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -43.601247 | E_var:     0.3833 | E_err:   0.009674
[2025-10-06 08:11:37] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -43.607677 | E_var:     0.2608 | E_err:   0.007980
[2025-10-06 08:11:42] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -43.604440 | E_var:     0.3253 | E_err:   0.008912
[2025-10-06 08:11:47] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -43.610985 | E_var:     0.2410 | E_err:   0.007670
[2025-10-06 08:11:52] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -43.599871 | E_var:     0.3112 | E_err:   0.008716
[2025-10-06 08:11:58] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -43.597391 | E_var:     0.2995 | E_err:   0.008551
[2025-10-06 08:12:03] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -43.603054 | E_var:     0.2479 | E_err:   0.007779
[2025-10-06 08:12:08] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -43.597140 | E_var:     0.3316 | E_err:   0.008998
[2025-10-06 08:12:13] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -43.601005 | E_var:     0.2390 | E_err:   0.007638
[2025-10-06 08:12:18] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -43.602720 | E_var:     0.2404 | E_err:   0.007662
[2025-10-06 08:12:23] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -43.597334 | E_var:     0.2573 | E_err:   0.007926
[2025-10-06 08:12:28] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -43.604958 | E_var:     0.2121 | E_err:   0.007196
[2025-10-06 08:12:33] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -43.615680 | E_var:     0.2818 | E_err:   0.008294
[2025-10-06 08:12:33] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 08:12:39] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -43.587540 | E_var:     0.2821 | E_err:   0.008299
[2025-10-06 08:12:44] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -43.603078 | E_var:     0.2805 | E_err:   0.008276
[2025-10-06 08:12:49] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -43.602301 | E_var:     0.2747 | E_err:   0.008189
[2025-10-06 08:12:54] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -43.600044 | E_var:     0.2893 | E_err:   0.008404
[2025-10-06 08:12:59] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -43.603327 | E_var:     0.2873 | E_err:   0.008375
[2025-10-06 08:13:04] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -43.604647 | E_var:     0.2702 | E_err:   0.008122
[2025-10-06 08:13:09] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -43.582101 | E_var:     0.2977 | E_err:   0.008525
[2025-10-06 08:13:15] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -43.582928 | E_var:     0.2660 | E_err:   0.008059
[2025-10-06 08:13:20] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -43.610417 | E_var:     0.2359 | E_err:   0.007589
[2025-10-06 08:13:25] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -43.603526 | E_var:     0.2828 | E_err:   0.008309
[2025-10-06 08:13:30] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -43.599323 | E_var:     0.2136 | E_err:   0.007221
[2025-10-06 08:13:35] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -43.603851 | E_var:     0.3250 | E_err:   0.008907
[2025-10-06 08:13:40] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -43.587282 | E_var:     0.2897 | E_err:   0.008410
[2025-10-06 08:13:45] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -43.597150 | E_var:     0.3134 | E_err:   0.008748
[2025-10-06 08:13:50] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -43.606197 | E_var:     0.2794 | E_err:   0.008259
[2025-10-06 08:13:56] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -43.604422 | E_var:     0.2810 | E_err:   0.008283
[2025-10-06 08:14:01] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -43.611254 | E_var:     0.2486 | E_err:   0.007790
[2025-10-06 08:14:06] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -43.609712 | E_var:     0.3676 | E_err:   0.009473
[2025-10-06 08:14:11] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -43.597252 | E_var:     0.2780 | E_err:   0.008238
[2025-10-06 08:14:16] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -43.606450 | E_var:     0.3886 | E_err:   0.009741
[2025-10-06 08:14:21] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -43.593677 | E_var:     0.3599 | E_err:   0.009374
[2025-10-06 08:14:26] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -43.610431 | E_var:     0.2401 | E_err:   0.007656
[2025-10-06 08:14:32] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -43.599809 | E_var:     0.2586 | E_err:   0.007946
[2025-10-06 08:14:37] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -43.594265 | E_var:     0.2819 | E_err:   0.008296
[2025-10-06 08:14:42] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -43.604215 | E_var:     0.2929 | E_err:   0.008456
[2025-10-06 08:14:47] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -43.603682 | E_var:     0.2575 | E_err:   0.007928
[2025-10-06 08:14:52] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -43.597242 | E_var:     0.3069 | E_err:   0.008657
[2025-10-06 08:14:57] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -43.606219 | E_var:     0.2508 | E_err:   0.007826
[2025-10-06 08:15:02] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -43.604969 | E_var:     0.3477 | E_err:   0.009213
[2025-10-06 08:15:07] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -43.605562 | E_var:     0.2928 | E_err:   0.008454
[2025-10-06 08:15:13] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -43.601638 | E_var:     0.2490 | E_err:   0.007797
[2025-10-06 08:15:18] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -43.603231 | E_var:     0.2694 | E_err:   0.008111
[2025-10-06 08:15:23] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -43.608042 | E_var:     0.2814 | E_err:   0.008289
[2025-10-06 08:15:28] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -43.598239 | E_var:     0.2547 | E_err:   0.007885
[2025-10-06 08:15:33] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -43.594932 | E_var:     0.2470 | E_err:   0.007766
[2025-10-06 08:15:38] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -43.605457 | E_var:     0.2598 | E_err:   0.007964
[2025-10-06 08:15:43] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -43.573829 | E_var:     1.0872 | E_err:   0.016292
[2025-10-06 08:15:49] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -43.591682 | E_var:     0.2815 | E_err:   0.008291
[2025-10-06 08:15:54] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -43.584277 | E_var:     0.2528 | E_err:   0.007856
[2025-10-06 08:15:59] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -43.601180 | E_var:     0.2232 | E_err:   0.007382
[2025-10-06 08:16:04] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -43.613201 | E_var:     0.2795 | E_err:   0.008261
[2025-10-06 08:16:09] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -43.592399 | E_var:     0.3191 | E_err:   0.008826
[2025-10-06 08:16:14] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -43.586152 | E_var:     0.2664 | E_err:   0.008065
[2025-10-06 08:16:19] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -43.581388 | E_var:     0.2849 | E_err:   0.008341
[2025-10-06 08:16:25] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -43.627581 | E_var:     0.4427 | E_err:   0.010397
[2025-10-06 08:16:30] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -43.599735 | E_var:     0.4826 | E_err:   0.010854
[2025-10-06 08:16:35] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -43.608932 | E_var:     0.2711 | E_err:   0.008136
[2025-10-06 08:16:40] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -43.609475 | E_var:     0.2431 | E_err:   0.007704
[2025-10-06 08:16:45] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -43.605396 | E_var:     0.2883 | E_err:   0.008390
[2025-10-06 08:16:50] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -43.613476 | E_var:     0.2749 | E_err:   0.008192
[2025-10-06 08:16:50] 🔄 RESTART #2 | Period: 600
[2025-10-06 08:16:55] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -43.597378 | E_var:     0.2645 | E_err:   0.008035
[2025-10-06 08:17:00] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -43.593992 | E_var:     0.3291 | E_err:   0.008964
[2025-10-06 08:17:06] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -43.597592 | E_var:     0.5506 | E_err:   0.011594
[2025-10-06 08:17:11] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -43.597105 | E_var:     0.2493 | E_err:   0.007802
[2025-10-06 08:17:16] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -43.605668 | E_var:     0.2866 | E_err:   0.008365
[2025-10-06 08:17:21] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -43.604250 | E_var:     0.3377 | E_err:   0.009080
[2025-10-06 08:17:26] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -43.603390 | E_var:     0.2832 | E_err:   0.008315
[2025-10-06 08:17:31] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -43.590947 | E_var:     0.2606 | E_err:   0.007976
[2025-10-06 08:17:36] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -43.601981 | E_var:     0.2457 | E_err:   0.007745
[2025-10-06 08:17:42] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -43.610809 | E_var:     0.2762 | E_err:   0.008212
[2025-10-06 08:17:47] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -43.600917 | E_var:     0.3548 | E_err:   0.009307
[2025-10-06 08:17:52] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -43.585945 | E_var:     0.2796 | E_err:   0.008262
[2025-10-06 08:17:57] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -43.611667 | E_var:     0.2490 | E_err:   0.007797
[2025-10-06 08:18:02] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -43.603409 | E_var:     0.3621 | E_err:   0.009403
[2025-10-06 08:18:07] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -43.596137 | E_var:     0.2720 | E_err:   0.008149
[2025-10-06 08:18:12] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -43.605423 | E_var:     0.2346 | E_err:   0.007569
[2025-10-06 08:18:18] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -43.591820 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 08:18:23] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -43.585113 | E_var:     0.2705 | E_err:   0.008127
[2025-10-06 08:18:28] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -43.603111 | E_var:     0.3496 | E_err:   0.009239
[2025-10-06 08:18:33] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -43.593930 | E_var:     0.2638 | E_err:   0.008025
[2025-10-06 08:18:38] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -43.614100 | E_var:     0.2038 | E_err:   0.007054
[2025-10-06 08:18:43] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -43.616052 | E_var:     0.2556 | E_err:   0.007899
[2025-10-06 08:18:48] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -43.588191 | E_var:     0.2213 | E_err:   0.007351
[2025-10-06 08:18:53] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -43.599392 | E_var:     0.5419 | E_err:   0.011502
[2025-10-06 08:18:59] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -43.602846 | E_var:     0.2343 | E_err:   0.007564
[2025-10-06 08:19:04] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -43.615551 | E_var:     0.3079 | E_err:   0.008670
[2025-10-06 08:19:09] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -43.590093 | E_var:     0.2919 | E_err:   0.008442
[2025-10-06 08:19:14] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -43.595091 | E_var:     0.2582 | E_err:   0.007940
[2025-10-06 08:19:19] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -43.605220 | E_var:     0.3345 | E_err:   0.009037
[2025-10-06 08:19:24] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -43.601581 | E_var:     0.2983 | E_err:   0.008534
[2025-10-06 08:19:29] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -43.595843 | E_var:     0.2700 | E_err:   0.008119
[2025-10-06 08:19:34] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -43.595175 | E_var:     0.2771 | E_err:   0.008226
[2025-10-06 08:19:40] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -43.596868 | E_var:     0.2401 | E_err:   0.007657
[2025-10-06 08:19:45] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -43.599111 | E_var:     0.3324 | E_err:   0.009009
[2025-10-06 08:19:50] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -43.618867 | E_var:     0.2311 | E_err:   0.007511
[2025-10-06 08:19:55] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -43.595156 | E_var:     0.3674 | E_err:   0.009470
[2025-10-06 08:20:00] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -43.604471 | E_var:     0.2695 | E_err:   0.008112
[2025-10-06 08:20:05] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -43.602541 | E_var:     0.3035 | E_err:   0.008607
[2025-10-06 08:20:10] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -43.593618 | E_var:     0.3721 | E_err:   0.009532
[2025-10-06 08:20:15] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -43.598828 | E_var:     0.3150 | E_err:   0.008770
[2025-10-06 08:20:21] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -43.586133 | E_var:     0.2977 | E_err:   0.008526
[2025-10-06 08:20:26] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -43.602276 | E_var:     0.2522 | E_err:   0.007846
[2025-10-06 08:20:31] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -43.600521 | E_var:     0.2611 | E_err:   0.007984
[2025-10-06 08:20:36] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -43.592339 | E_var:     0.2092 | E_err:   0.007146
[2025-10-06 08:20:41] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -43.599391 | E_var:     0.2885 | E_err:   0.008392
[2025-10-06 08:20:46] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -43.597937 | E_var:     0.2865 | E_err:   0.008363
[2025-10-06 08:20:51] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -43.590431 | E_var:     0.3094 | E_err:   0.008691
[2025-10-06 08:20:56] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -43.593752 | E_var:     0.2893 | E_err:   0.008404
[2025-10-06 08:21:01] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -43.593020 | E_var:     0.2956 | E_err:   0.008496
[2025-10-06 08:21:07] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -43.602038 | E_var:     0.2845 | E_err:   0.008334
[2025-10-06 08:21:07] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 08:21:12] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -43.589485 | E_var:     0.3240 | E_err:   0.008894
[2025-10-06 08:21:17] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -43.600367 | E_var:     0.3440 | E_err:   0.009164
[2025-10-06 08:21:22] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -43.599139 | E_var:     0.2820 | E_err:   0.008298
[2025-10-06 08:21:27] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -43.594161 | E_var:     0.2552 | E_err:   0.007894
[2025-10-06 08:21:32] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -43.598232 | E_var:     0.3767 | E_err:   0.009590
[2025-10-06 08:21:37] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -43.588997 | E_var:     0.2715 | E_err:   0.008141
[2025-10-06 08:21:43] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -43.594562 | E_var:     0.3555 | E_err:   0.009316
[2025-10-06 08:21:48] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -43.602701 | E_var:     0.3168 | E_err:   0.008794
[2025-10-06 08:21:53] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -43.604811 | E_var:     0.3388 | E_err:   0.009094
[2025-10-06 08:21:58] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -43.607277 | E_var:     0.3573 | E_err:   0.009340
[2025-10-06 08:22:03] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -43.604043 | E_var:     0.2292 | E_err:   0.007480
[2025-10-06 08:22:08] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -43.598382 | E_var:     0.2594 | E_err:   0.007958
[2025-10-06 08:22:13] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -43.602593 | E_var:     0.2636 | E_err:   0.008022
[2025-10-06 08:22:18] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -43.595010 | E_var:     0.2612 | E_err:   0.007985
[2025-10-06 08:22:24] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -43.606462 | E_var:     0.3575 | E_err:   0.009342
[2025-10-06 08:22:29] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -43.571445 | E_var:     0.3881 | E_err:   0.009734
[2025-10-06 08:22:34] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -43.610223 | E_var:     0.3055 | E_err:   0.008637
[2025-10-06 08:22:39] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -43.599395 | E_var:     0.2520 | E_err:   0.007844
[2025-10-06 08:22:44] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -43.595315 | E_var:     0.3305 | E_err:   0.008982
[2025-10-06 08:22:49] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -43.592728 | E_var:     0.3086 | E_err:   0.008680
[2025-10-06 08:22:54] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -43.606227 | E_var:     0.2784 | E_err:   0.008245
[2025-10-06 08:22:59] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -43.602029 | E_var:     0.2223 | E_err:   0.007367
[2025-10-06 08:23:05] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -43.599718 | E_var:     0.4815 | E_err:   0.010843
[2025-10-06 08:23:10] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -43.600741 | E_var:     0.2406 | E_err:   0.007664
[2025-10-06 08:23:15] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -43.592567 | E_var:     0.3376 | E_err:   0.009079
[2025-10-06 08:23:20] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -43.613117 | E_var:     0.2886 | E_err:   0.008394
[2025-10-06 08:23:25] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -43.589137 | E_var:     0.5049 | E_err:   0.011103
[2025-10-06 08:23:30] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -43.613400 | E_var:     0.2628 | E_err:   0.008010
[2025-10-06 08:23:35] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -43.605356 | E_var:     0.6546 | E_err:   0.012642
[2025-10-06 08:23:40] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -43.606821 | E_var:     0.2690 | E_err:   0.008104
[2025-10-06 08:23:46] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -43.574355 | E_var:     0.3428 | E_err:   0.009148
[2025-10-06 08:23:51] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -43.590273 | E_var:     0.3051 | E_err:   0.008631
[2025-10-06 08:23:56] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -43.591211 | E_var:     0.2531 | E_err:   0.007860
[2025-10-06 08:24:01] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -43.598561 | E_var:     0.2689 | E_err:   0.008102
[2025-10-06 08:24:06] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -43.604543 | E_var:     0.2498 | E_err:   0.007809
[2025-10-06 08:24:11] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -43.593547 | E_var:     0.2711 | E_err:   0.008135
[2025-10-06 08:24:16] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -43.590550 | E_var:     0.2513 | E_err:   0.007833
[2025-10-06 08:24:21] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -43.570392 | E_var:     1.2439 | E_err:   0.017427
[2025-10-06 08:24:27] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -43.603959 | E_var:     0.3349 | E_err:   0.009042
[2025-10-06 08:24:32] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -43.609109 | E_var:     0.2465 | E_err:   0.007758
[2025-10-06 08:24:37] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -43.593476 | E_var:     0.3825 | E_err:   0.009664
[2025-10-06 08:24:42] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -43.592135 | E_var:     0.3364 | E_err:   0.009062
[2025-10-06 08:24:47] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -43.616669 | E_var:     0.3292 | E_err:   0.008965
[2025-10-06 08:24:52] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -43.612568 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 08:24:57] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -43.609835 | E_var:     0.3128 | E_err:   0.008739
[2025-10-06 08:25:02] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -43.598962 | E_var:     0.2430 | E_err:   0.007702
[2025-10-06 08:25:08] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -43.587753 | E_var:     0.2781 | E_err:   0.008241
[2025-10-06 08:25:13] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -43.589894 | E_var:     0.2353 | E_err:   0.007580
[2025-10-06 08:25:18] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -43.611557 | E_var:     0.3207 | E_err:   0.008848
[2025-10-06 08:25:23] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -43.606552 | E_var:     0.4031 | E_err:   0.009920
[2025-10-06 08:25:28] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -43.596710 | E_var:     0.2344 | E_err:   0.007565
[2025-10-06 08:25:33] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -43.613456 | E_var:     0.2492 | E_err:   0.007801
[2025-10-06 08:25:38] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -43.591519 | E_var:     0.2858 | E_err:   0.008353
[2025-10-06 08:25:43] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -43.601131 | E_var:     0.5323 | E_err:   0.011400
[2025-10-06 08:25:49] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -43.597059 | E_var:     0.3765 | E_err:   0.009587
[2025-10-06 08:25:54] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -43.603055 | E_var:     0.2543 | E_err:   0.007879
[2025-10-06 08:25:59] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -43.588671 | E_var:     0.2987 | E_err:   0.008539
[2025-10-06 08:26:04] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -43.604153 | E_var:     0.3127 | E_err:   0.008738
[2025-10-06 08:26:09] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -43.593104 | E_var:     0.2900 | E_err:   0.008414
[2025-10-06 08:26:14] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -43.595147 | E_var:     0.2439 | E_err:   0.007717
[2025-10-06 08:26:19] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -43.591798 | E_var:     0.2371 | E_err:   0.007608
[2025-10-06 08:26:24] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -43.585612 | E_var:     0.2895 | E_err:   0.008407
[2025-10-06 08:26:30] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -43.616748 | E_var:     0.3083 | E_err:   0.008676
[2025-10-06 08:26:35] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -43.593710 | E_var:     0.3158 | E_err:   0.008781
[2025-10-06 08:26:40] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -43.590651 | E_var:     0.3041 | E_err:   0.008617
[2025-10-06 08:26:45] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -43.597404 | E_var:     0.2876 | E_err:   0.008380
[2025-10-06 08:26:50] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -43.597204 | E_var:     0.2958 | E_err:   0.008498
[2025-10-06 08:26:55] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -43.616770 | E_var:     0.2632 | E_err:   0.008017
[2025-10-06 08:27:00] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -43.587449 | E_var:     0.3582 | E_err:   0.009352
[2025-10-06 08:27:05] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -43.598769 | E_var:     0.2393 | E_err:   0.007644
[2025-10-06 08:27:11] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -43.601918 | E_var:     0.4729 | E_err:   0.010744
[2025-10-06 08:27:16] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -43.605370 | E_var:     0.3084 | E_err:   0.008678
[2025-10-06 08:27:21] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -43.592605 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 08:27:26] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -43.608035 | E_var:     0.2633 | E_err:   0.008017
[2025-10-06 08:27:31] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -43.602180 | E_var:     0.3199 | E_err:   0.008838
[2025-10-06 08:27:36] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -43.590706 | E_var:     0.2547 | E_err:   0.007886
[2025-10-06 08:27:41] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -43.602333 | E_var:     0.2733 | E_err:   0.008169
[2025-10-06 08:27:46] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -43.599180 | E_var:     0.2767 | E_err:   0.008220
[2025-10-06 08:27:52] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -43.601714 | E_var:     0.2772 | E_err:   0.008226
[2025-10-06 08:27:57] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -43.607318 | E_var:     0.3181 | E_err:   0.008813
[2025-10-06 08:28:02] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -43.602662 | E_var:     0.3144 | E_err:   0.008762
[2025-10-06 08:28:07] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -43.610813 | E_var:     0.3830 | E_err:   0.009670
[2025-10-06 08:28:12] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -43.602853 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 08:28:17] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -43.589351 | E_var:     0.3140 | E_err:   0.008756
[2025-10-06 08:28:22] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -43.593053 | E_var:     0.2180 | E_err:   0.007296
[2025-10-06 08:28:28] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -43.604796 | E_var:     0.3097 | E_err:   0.008695
[2025-10-06 08:28:33] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -43.602930 | E_var:     0.2552 | E_err:   0.007893
[2025-10-06 08:28:38] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -43.583086 | E_var:     0.2445 | E_err:   0.007727
[2025-10-06 08:28:43] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -43.585682 | E_var:     0.2906 | E_err:   0.008423
[2025-10-06 08:28:48] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -43.594347 | E_var:     0.2585 | E_err:   0.007945
[2025-10-06 08:28:53] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -43.589567 | E_var:     0.3200 | E_err:   0.008839
[2025-10-06 08:28:58] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -43.598646 | E_var:     0.3373 | E_err:   0.009075
[2025-10-06 08:29:03] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -43.602255 | E_var:     0.3115 | E_err:   0.008720
[2025-10-06 08:29:09] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -43.594381 | E_var:     0.2933 | E_err:   0.008463
[2025-10-06 08:29:14] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -43.601343 | E_var:     0.2557 | E_err:   0.007902
[2025-10-06 08:29:19] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -43.604897 | E_var:     0.2518 | E_err:   0.007840
[2025-10-06 08:29:24] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -43.600987 | E_var:     0.2318 | E_err:   0.007522
[2025-10-06 08:29:29] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -43.590308 | E_var:     0.2491 | E_err:   0.007798
[2025-10-06 08:29:34] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -43.602276 | E_var:     0.2992 | E_err:   0.008547
[2025-10-06 08:29:39] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -43.588503 | E_var:     0.2806 | E_err:   0.008276
[2025-10-06 08:29:39] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 08:29:44] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -43.595494 | E_var:     0.3063 | E_err:   0.008647
[2025-10-06 08:29:50] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -43.596700 | E_var:     0.3069 | E_err:   0.008655
[2025-10-06 08:29:55] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -43.597031 | E_var:     0.2955 | E_err:   0.008494
[2025-10-06 08:30:00] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -43.608151 | E_var:     0.2314 | E_err:   0.007516
[2025-10-06 08:30:05] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -43.606153 | E_var:     0.2634 | E_err:   0.008020
[2025-10-06 08:30:10] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -43.591633 | E_var:     0.2281 | E_err:   0.007462
[2025-10-06 08:30:15] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -43.590019 | E_var:     0.2384 | E_err:   0.007629
[2025-10-06 08:30:20] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -43.603009 | E_var:     0.2770 | E_err:   0.008223
[2025-10-06 08:30:26] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -43.612563 | E_var:     0.2803 | E_err:   0.008273
[2025-10-06 08:30:31] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -43.593360 | E_var:     0.2931 | E_err:   0.008459
[2025-10-06 08:30:36] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -43.603415 | E_var:     0.2890 | E_err:   0.008400
[2025-10-06 08:30:41] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -43.587505 | E_var:     0.3382 | E_err:   0.009087
[2025-10-06 08:30:46] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -43.603654 | E_var:     0.2590 | E_err:   0.007952
[2025-10-06 08:30:51] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -43.585131 | E_var:     0.2258 | E_err:   0.007424
[2025-10-06 08:30:56] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -43.602175 | E_var:     0.2848 | E_err:   0.008338
[2025-10-06 08:31:01] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -43.597251 | E_var:     0.3233 | E_err:   0.008885
[2025-10-06 08:31:07] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -43.596379 | E_var:     0.2996 | E_err:   0.008552
[2025-10-06 08:31:12] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -43.609295 | E_var:     0.2753 | E_err:   0.008199
[2025-10-06 08:31:17] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -43.598808 | E_var:     0.2713 | E_err:   0.008138
[2025-10-06 08:31:22] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -43.582462 | E_var:     0.3054 | E_err:   0.008634
[2025-10-06 08:31:27] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -43.605733 | E_var:     0.2702 | E_err:   0.008122
[2025-10-06 08:31:32] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -43.602681 | E_var:     0.2705 | E_err:   0.008126
[2025-10-06 08:31:37] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -43.607350 | E_var:     0.3073 | E_err:   0.008662
[2025-10-06 08:31:42] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -43.601858 | E_var:     0.3238 | E_err:   0.008891
[2025-10-06 08:31:48] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -43.605721 | E_var:     0.3341 | E_err:   0.009032
[2025-10-06 08:31:53] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -43.604972 | E_var:     0.2961 | E_err:   0.008503
[2025-10-06 08:31:58] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -43.614516 | E_var:     0.2847 | E_err:   0.008337
[2025-10-06 08:32:03] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -43.592436 | E_var:     0.2933 | E_err:   0.008462
[2025-10-06 08:32:08] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -43.611354 | E_var:     0.2438 | E_err:   0.007716
[2025-10-06 08:32:13] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -43.611966 | E_var:     0.3182 | E_err:   0.008815
[2025-10-06 08:32:18] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -43.601919 | E_var:     0.2684 | E_err:   0.008095
[2025-10-06 08:32:23] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -43.612839 | E_var:     0.3612 | E_err:   0.009391
[2025-10-06 08:32:29] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -43.606037 | E_var:     0.2529 | E_err:   0.007857
[2025-10-06 08:32:34] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -43.602214 | E_var:     0.2816 | E_err:   0.008291
[2025-10-06 08:32:39] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -43.607109 | E_var:     0.3249 | E_err:   0.008906
[2025-10-06 08:32:44] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -43.597346 | E_var:     0.2926 | E_err:   0.008452
[2025-10-06 08:32:49] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -43.601042 | E_var:     0.3708 | E_err:   0.009514
[2025-10-06 08:32:54] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -43.602362 | E_var:     0.3321 | E_err:   0.009004
[2025-10-06 08:32:59] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -43.601617 | E_var:     0.3199 | E_err:   0.008838
[2025-10-06 08:33:04] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -43.602996 | E_var:     0.2538 | E_err:   0.007871
[2025-10-06 08:33:10] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -43.590437 | E_var:     0.2267 | E_err:   0.007440
[2025-10-06 08:33:15] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -43.609205 | E_var:     0.2490 | E_err:   0.007797
[2025-10-06 08:33:20] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -43.612516 | E_var:     0.2571 | E_err:   0.007922
[2025-10-06 08:33:25] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -43.601624 | E_var:     0.2882 | E_err:   0.008387
[2025-10-06 08:33:30] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -43.597919 | E_var:     0.2918 | E_err:   0.008441
[2025-10-06 08:33:35] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -43.604268 | E_var:     0.2763 | E_err:   0.008213
[2025-10-06 08:33:40] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -43.590713 | E_var:     0.3264 | E_err:   0.008927
[2025-10-06 08:33:45] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -43.602338 | E_var:     0.2905 | E_err:   0.008422
[2025-10-06 08:33:51] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -43.596853 | E_var:     0.3161 | E_err:   0.008784
[2025-10-06 08:33:56] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -43.596692 | E_var:     0.2580 | E_err:   0.007936
[2025-10-06 08:34:01] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -43.604367 | E_var:     0.2603 | E_err:   0.007972
[2025-10-06 08:34:06] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -43.608140 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 08:34:11] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -43.594281 | E_var:     0.2332 | E_err:   0.007546
[2025-10-06 08:34:16] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -43.602532 | E_var:     0.3177 | E_err:   0.008808
[2025-10-06 08:34:21] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -43.600972 | E_var:     0.2800 | E_err:   0.008268
[2025-10-06 08:34:26] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -43.609158 | E_var:     0.3076 | E_err:   0.008665
[2025-10-06 08:34:32] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -43.588965 | E_var:     0.2266 | E_err:   0.007438
[2025-10-06 08:34:37] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -43.589886 | E_var:     0.3182 | E_err:   0.008814
[2025-10-06 08:34:42] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -43.590252 | E_var:     0.2348 | E_err:   0.007572
[2025-10-06 08:34:47] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -43.601556 | E_var:     0.2956 | E_err:   0.008495
[2025-10-06 08:34:52] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -43.606994 | E_var:     0.3973 | E_err:   0.009848
[2025-10-06 08:34:57] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -43.597127 | E_var:     0.2431 | E_err:   0.007704
[2025-10-06 08:35:02] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -43.606235 | E_var:     0.2672 | E_err:   0.008076
[2025-10-06 08:35:07] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -43.599150 | E_var:     0.2951 | E_err:   0.008488
[2025-10-06 08:35:13] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -43.597155 | E_var:     0.2769 | E_err:   0.008222
[2025-10-06 08:35:18] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -43.576214 | E_var:     0.2878 | E_err:   0.008383
[2025-10-06 08:35:23] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -43.604164 | E_var:     0.2700 | E_err:   0.008120
[2025-10-06 08:35:28] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -43.593761 | E_var:     0.2503 | E_err:   0.007817
[2025-10-06 08:35:33] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -43.605836 | E_var:     0.3994 | E_err:   0.009874
[2025-10-06 08:35:38] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -43.608248 | E_var:     0.2562 | E_err:   0.007908
[2025-10-06 08:35:43] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -43.611723 | E_var:     0.2344 | E_err:   0.007565
[2025-10-06 08:35:48] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -43.601237 | E_var:     0.2562 | E_err:   0.007909
[2025-10-06 08:35:54] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -43.600201 | E_var:     0.2405 | E_err:   0.007662
[2025-10-06 08:35:59] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -43.603711 | E_var:     0.3219 | E_err:   0.008865
[2025-10-06 08:36:04] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -43.590624 | E_var:     0.2522 | E_err:   0.007847
[2025-10-06 08:36:09] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -43.590909 | E_var:     0.2914 | E_err:   0.008435
[2025-10-06 08:36:14] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -43.589074 | E_var:     0.3309 | E_err:   0.008988
[2025-10-06 08:36:19] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -43.599088 | E_var:     0.2691 | E_err:   0.008106
[2025-10-06 08:36:24] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -43.596061 | E_var:     0.2556 | E_err:   0.007899
[2025-10-06 08:36:29] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -43.607221 | E_var:     0.3025 | E_err:   0.008594
[2025-10-06 08:36:34] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -43.596730 | E_var:     0.2836 | E_err:   0.008321
[2025-10-06 08:36:40] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -43.611412 | E_var:     0.2230 | E_err:   0.007379
[2025-10-06 08:36:45] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -43.600936 | E_var:     0.2620 | E_err:   0.007998
[2025-10-06 08:36:50] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -43.604253 | E_var:     0.2876 | E_err:   0.008379
[2025-10-06 08:36:55] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -43.623102 | E_var:     0.3632 | E_err:   0.009416
[2025-10-06 08:37:00] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -43.595807 | E_var:     0.2828 | E_err:   0.008309
[2025-10-06 08:37:05] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -43.604012 | E_var:     0.4046 | E_err:   0.009939
[2025-10-06 08:37:10] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -43.596167 | E_var:     0.3212 | E_err:   0.008855
[2025-10-06 08:37:15] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -43.605187 | E_var:     0.2487 | E_err:   0.007792
[2025-10-06 08:37:21] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -43.606488 | E_var:     0.2894 | E_err:   0.008405
[2025-10-06 08:37:26] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -43.594379 | E_var:     0.2451 | E_err:   0.007735
[2025-10-06 08:37:31] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -43.600334 | E_var:     0.2368 | E_err:   0.007604
[2025-10-06 08:37:36] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -43.601466 | E_var:     0.2532 | E_err:   0.007863
[2025-10-06 08:37:41] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -43.603674 | E_var:     0.2268 | E_err:   0.007441
[2025-10-06 08:37:46] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -43.600081 | E_var:     0.2797 | E_err:   0.008263
[2025-10-06 08:37:51] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -43.592115 | E_var:     0.3816 | E_err:   0.009652
[2025-10-06 08:37:56] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -43.596294 | E_var:     0.2688 | E_err:   0.008100
[2025-10-06 08:38:02] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -43.591020 | E_var:     0.2459 | E_err:   0.007748
[2025-10-06 08:38:07] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -43.596691 | E_var:     0.3207 | E_err:   0.008848
[2025-10-06 08:38:12] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -43.596967 | E_var:     0.2342 | E_err:   0.007561
[2025-10-06 08:38:12] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 08:38:17] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -43.606861 | E_var:     0.3426 | E_err:   0.009145
[2025-10-06 08:38:22] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -43.593866 | E_var:     0.2402 | E_err:   0.007657
[2025-10-06 08:38:27] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -43.617539 | E_var:     0.3582 | E_err:   0.009352
[2025-10-06 08:38:32] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -43.593509 | E_var:     0.3137 | E_err:   0.008751
[2025-10-06 08:38:37] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -43.586858 | E_var:     0.2799 | E_err:   0.008266
[2025-10-06 08:38:43] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -43.603220 | E_var:     0.2774 | E_err:   0.008229
[2025-10-06 08:38:48] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -43.584189 | E_var:     0.3920 | E_err:   0.009783
[2025-10-06 08:38:53] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -43.598558 | E_var:     0.2757 | E_err:   0.008204
[2025-10-06 08:38:58] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -43.606559 | E_var:     0.3402 | E_err:   0.009113
[2025-10-06 08:39:03] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -43.609937 | E_var:     0.2764 | E_err:   0.008214
[2025-10-06 08:39:08] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -43.607523 | E_var:     0.2910 | E_err:   0.008428
[2025-10-06 08:39:13] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -43.616813 | E_var:     0.2950 | E_err:   0.008487
[2025-10-06 08:39:18] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -43.601699 | E_var:     0.2456 | E_err:   0.007744
[2025-10-06 08:39:24] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -43.598449 | E_var:     0.2149 | E_err:   0.007244
[2025-10-06 08:39:29] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -43.605953 | E_var:     0.2606 | E_err:   0.007976
[2025-10-06 08:39:34] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -43.603153 | E_var:     0.2872 | E_err:   0.008374
[2025-10-06 08:39:39] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -43.590649 | E_var:     0.2942 | E_err:   0.008475
[2025-10-06 08:39:44] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -43.586966 | E_var:     0.3795 | E_err:   0.009626
[2025-10-06 08:39:49] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -43.613027 | E_var:     0.2189 | E_err:   0.007311
[2025-10-06 08:39:54] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -43.614033 | E_var:     0.3878 | E_err:   0.009730
[2025-10-06 08:39:59] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -43.585413 | E_var:     0.2820 | E_err:   0.008297
[2025-10-06 08:40:05] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -43.596087 | E_var:     0.2441 | E_err:   0.007720
[2025-10-06 08:40:10] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -43.598096 | E_var:     0.2842 | E_err:   0.008330
[2025-10-06 08:40:15] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -43.609026 | E_var:     0.3909 | E_err:   0.009769
[2025-10-06 08:40:20] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -43.593339 | E_var:     0.2720 | E_err:   0.008149
[2025-10-06 08:40:25] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -43.600759 | E_var:     0.2649 | E_err:   0.008042
[2025-10-06 08:40:30] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -43.615134 | E_var:     0.3175 | E_err:   0.008804
[2025-10-06 08:40:35] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -43.618533 | E_var:     0.2587 | E_err:   0.007947
[2025-10-06 08:40:40] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -43.596190 | E_var:     0.3367 | E_err:   0.009067
[2025-10-06 08:40:46] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -43.605459 | E_var:     0.2651 | E_err:   0.008044
[2025-10-06 08:40:51] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -43.605854 | E_var:     0.3313 | E_err:   0.008993
[2025-10-06 08:40:56] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -43.613594 | E_var:     0.2840 | E_err:   0.008326
[2025-10-06 08:41:01] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -43.594462 | E_var:     0.3732 | E_err:   0.009546
[2025-10-06 08:41:06] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -43.588021 | E_var:     0.3599 | E_err:   0.009373
[2025-10-06 08:41:11] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -43.606144 | E_var:     0.2312 | E_err:   0.007512
[2025-10-06 08:41:16] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -43.600641 | E_var:     0.2445 | E_err:   0.007727
[2025-10-06 08:41:22] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -43.586915 | E_var:     0.2440 | E_err:   0.007718
[2025-10-06 08:41:27] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -43.605178 | E_var:     0.2445 | E_err:   0.007727
[2025-10-06 08:41:32] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -43.615002 | E_var:     0.3338 | E_err:   0.009027
[2025-10-06 08:41:37] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -43.577181 | E_var:     0.3164 | E_err:   0.008789
[2025-10-06 08:41:42] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -43.603997 | E_var:     0.2793 | E_err:   0.008257
[2025-10-06 08:41:47] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -43.603275 | E_var:     0.2945 | E_err:   0.008479
[2025-10-06 08:41:52] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -43.598601 | E_var:     0.3719 | E_err:   0.009529
[2025-10-06 08:41:57] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -43.583452 | E_var:     0.2907 | E_err:   0.008425
[2025-10-06 08:42:03] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -43.590579 | E_var:     0.3262 | E_err:   0.008925
[2025-10-06 08:42:08] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -43.603131 | E_var:     0.3069 | E_err:   0.008655
[2025-10-06 08:42:13] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -43.605609 | E_var:     0.3257 | E_err:   0.008918
[2025-10-06 08:42:18] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -43.619241 | E_var:     0.3110 | E_err:   0.008714
[2025-10-06 08:42:23] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -43.602540 | E_var:     0.3426 | E_err:   0.009146
[2025-10-06 08:42:28] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -43.594524 | E_var:     0.2911 | E_err:   0.008430
[2025-10-06 08:42:33] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -43.600467 | E_var:     0.2662 | E_err:   0.008061
[2025-10-06 08:42:38] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -43.592523 | E_var:     0.2627 | E_err:   0.008008
[2025-10-06 08:42:44] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -43.599825 | E_var:     0.3182 | E_err:   0.008814
[2025-10-06 08:42:49] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -43.600798 | E_var:     0.4016 | E_err:   0.009902
[2025-10-06 08:42:54] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -43.616200 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 08:42:59] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -43.601284 | E_var:     0.3223 | E_err:   0.008871
[2025-10-06 08:43:04] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -43.603733 | E_var:     0.2746 | E_err:   0.008187
[2025-10-06 08:43:09] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -43.593086 | E_var:     0.2659 | E_err:   0.008058
[2025-10-06 08:43:14] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -43.588315 | E_var:     0.3452 | E_err:   0.009180
[2025-10-06 08:43:19] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -43.607838 | E_var:     0.3029 | E_err:   0.008599
[2025-10-06 08:43:25] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -43.606027 | E_var:     0.2865 | E_err:   0.008364
[2025-10-06 08:43:30] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -43.614476 | E_var:     0.2678 | E_err:   0.008085
[2025-10-06 08:43:35] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -43.578881 | E_var:     0.3617 | E_err:   0.009397
[2025-10-06 08:43:40] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -43.605662 | E_var:     0.2480 | E_err:   0.007781
[2025-10-06 08:43:45] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -43.608215 | E_var:     0.4905 | E_err:   0.010943
[2025-10-06 08:43:50] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -43.612842 | E_var:     0.3011 | E_err:   0.008574
[2025-10-06 08:43:55] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -43.596216 | E_var:     0.2824 | E_err:   0.008303
[2025-10-06 08:44:00] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -43.595538 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 08:44:06] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -43.598412 | E_var:     0.3663 | E_err:   0.009456
[2025-10-06 08:44:11] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -43.594050 | E_var:     0.2860 | E_err:   0.008356
[2025-10-06 08:44:16] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -43.617067 | E_var:     0.3023 | E_err:   0.008591
[2025-10-06 08:44:21] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -43.602328 | E_var:     0.3430 | E_err:   0.009150
[2025-10-06 08:44:26] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -43.584434 | E_var:     0.3190 | E_err:   0.008824
[2025-10-06 08:44:31] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -43.617704 | E_var:     0.5260 | E_err:   0.011333
[2025-10-06 08:44:36] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -43.611032 | E_var:     0.2772 | E_err:   0.008226
[2025-10-06 08:44:41] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -43.598544 | E_var:     0.2378 | E_err:   0.007620
[2025-10-06 08:44:47] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -43.596767 | E_var:     0.2879 | E_err:   0.008384
[2025-10-06 08:44:52] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -43.598357 | E_var:     0.3118 | E_err:   0.008725
[2025-10-06 08:44:57] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -43.598747 | E_var:     0.3045 | E_err:   0.008622
[2025-10-06 08:45:02] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -43.600964 | E_var:     0.3198 | E_err:   0.008835
[2025-10-06 08:45:12] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -43.602360 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 08:45:17] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -43.615412 | E_var:     0.2867 | E_err:   0.008366
[2025-10-06 08:45:22] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -43.615408 | E_var:     0.2386 | E_err:   0.007633
[2025-10-06 08:45:27] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -43.604957 | E_var:     0.2831 | E_err:   0.008314
[2025-10-06 08:45:33] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -43.598052 | E_var:     0.3074 | E_err:   0.008663
[2025-10-06 08:45:38] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -43.593109 | E_var:     0.3303 | E_err:   0.008980
[2025-10-06 08:45:43] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -43.624184 | E_var:     0.2839 | E_err:   0.008326
[2025-10-06 08:45:48] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -43.585508 | E_var:     0.2621 | E_err:   0.007999
[2025-10-06 08:45:53] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -43.601547 | E_var:     0.3324 | E_err:   0.009009
[2025-10-06 08:45:58] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -43.597125 | E_var:     0.2289 | E_err:   0.007476
[2025-10-06 08:46:03] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -43.614200 | E_var:     0.2535 | E_err:   0.007868
[2025-10-06 08:46:08] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -43.602575 | E_var:     0.2435 | E_err:   0.007710
[2025-10-06 08:46:14] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -43.597969 | E_var:     0.2551 | E_err:   0.007893
[2025-10-06 08:46:19] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -43.593651 | E_var:     0.2642 | E_err:   0.008031
[2025-10-06 08:46:24] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -43.597797 | E_var:     0.2461 | E_err:   0.007751
[2025-10-06 08:46:29] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -43.617936 | E_var:     0.2677 | E_err:   0.008085
[2025-10-06 08:46:34] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -43.600865 | E_var:     0.2498 | E_err:   0.007810
[2025-10-06 08:46:39] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -43.612927 | E_var:     0.2740 | E_err:   0.008179
[2025-10-06 08:46:44] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -43.598150 | E_var:     0.2451 | E_err:   0.007736
[2025-10-06 08:46:50] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -43.600550 | E_var:     0.2625 | E_err:   0.008006
[2025-10-06 08:46:50] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 08:46:55] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -43.601061 | E_var:     0.2515 | E_err:   0.007836
[2025-10-06 08:47:00] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -43.611208 | E_var:     0.2914 | E_err:   0.008434
[2025-10-06 08:47:05] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -43.605627 | E_var:     0.3250 | E_err:   0.008908
[2025-10-06 08:47:10] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -43.604146 | E_var:     0.2201 | E_err:   0.007331
[2025-10-06 08:47:15] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -43.609912 | E_var:     0.2070 | E_err:   0.007109
[2025-10-06 08:47:20] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -43.605800 | E_var:     0.2568 | E_err:   0.007918
[2025-10-06 08:47:26] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -43.613587 | E_var:     0.3269 | E_err:   0.008933
[2025-10-06 08:47:31] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -43.606891 | E_var:     0.3077 | E_err:   0.008668
[2025-10-06 08:47:36] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -43.615710 | E_var:     0.2687 | E_err:   0.008099
[2025-10-06 08:47:41] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -43.600679 | E_var:     0.2493 | E_err:   0.007802
[2025-10-06 08:47:46] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -43.602689 | E_var:     0.2254 | E_err:   0.007419
[2025-10-06 08:47:51] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -43.599898 | E_var:     0.2190 | E_err:   0.007311
[2025-10-06 08:47:56] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -43.608195 | E_var:     0.2477 | E_err:   0.007776
[2025-10-06 08:48:01] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -43.595744 | E_var:     0.3666 | E_err:   0.009461
[2025-10-06 08:48:07] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -43.613792 | E_var:     0.3568 | E_err:   0.009333
[2025-10-06 08:48:12] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -43.601946 | E_var:     0.2114 | E_err:   0.007184
[2025-10-06 08:48:17] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -43.615349 | E_var:     0.3613 | E_err:   0.009392
[2025-10-06 08:48:22] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -43.607728 | E_var:     0.3225 | E_err:   0.008874
[2025-10-06 08:48:27] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -43.588080 | E_var:     0.2334 | E_err:   0.007549
[2025-10-06 08:48:32] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -43.596316 | E_var:     0.2268 | E_err:   0.007441
[2025-10-06 08:48:37] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -43.610631 | E_var:     0.2434 | E_err:   0.007709
[2025-10-06 08:48:42] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -43.605633 | E_var:     0.2724 | E_err:   0.008155
[2025-10-06 08:48:48] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -43.610986 | E_var:     0.2234 | E_err:   0.007385
[2025-10-06 08:48:53] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -43.603744 | E_var:     0.3542 | E_err:   0.009300
[2025-10-06 08:48:58] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -43.603862 | E_var:     0.2471 | E_err:   0.007766
[2025-10-06 08:49:03] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -43.606147 | E_var:     0.2575 | E_err:   0.007928
[2025-10-06 08:49:08] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -43.613619 | E_var:     0.2881 | E_err:   0.008386
[2025-10-06 08:49:13] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -43.593105 | E_var:     0.3105 | E_err:   0.008707
[2025-10-06 08:49:18] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -43.592713 | E_var:     0.2733 | E_err:   0.008169
[2025-10-06 08:49:23] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -43.605666 | E_var:     0.3463 | E_err:   0.009195
[2025-10-06 08:49:29] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -43.598090 | E_var:     0.2539 | E_err:   0.007873
[2025-10-06 08:49:34] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -43.606954 | E_var:     0.2295 | E_err:   0.007485
[2025-10-06 08:49:39] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -43.595505 | E_var:     0.5031 | E_err:   0.011082
[2025-10-06 08:49:44] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -43.603894 | E_var:     0.3213 | E_err:   0.008857
[2025-10-06 08:49:49] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -43.595961 | E_var:     0.6728 | E_err:   0.012816
[2025-10-06 08:49:54] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -43.607600 | E_var:     0.2700 | E_err:   0.008120
[2025-10-06 08:49:59] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -43.601040 | E_var:     0.2540 | E_err:   0.007875
[2025-10-06 08:50:05] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -43.604109 | E_var:     0.2314 | E_err:   0.007517
[2025-10-06 08:50:10] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -43.605525 | E_var:     0.3164 | E_err:   0.008789
[2025-10-06 08:50:15] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -43.599278 | E_var:     0.3551 | E_err:   0.009311
[2025-10-06 08:50:20] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -43.610007 | E_var:     0.2990 | E_err:   0.008545
[2025-10-06 08:50:25] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -43.600694 | E_var:     0.2318 | E_err:   0.007523
[2025-10-06 08:50:30] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -43.606117 | E_var:     0.2700 | E_err:   0.008119
[2025-10-06 08:50:35] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -43.611579 | E_var:     0.3280 | E_err:   0.008948
[2025-10-06 08:50:40] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -43.607036 | E_var:     0.2503 | E_err:   0.007817
[2025-10-06 08:50:46] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -43.592266 | E_var:     0.4306 | E_err:   0.010254
[2025-10-06 08:50:51] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -43.599370 | E_var:     0.2242 | E_err:   0.007398
[2025-10-06 08:50:56] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -43.600597 | E_var:     0.3043 | E_err:   0.008619
[2025-10-06 08:51:01] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -43.605675 | E_var:     0.3349 | E_err:   0.009042
[2025-10-06 08:51:06] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -43.588727 | E_var:     0.2177 | E_err:   0.007291
[2025-10-06 08:51:11] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -43.615051 | E_var:     0.2927 | E_err:   0.008454
[2025-10-06 08:51:16] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -43.596338 | E_var:     0.2545 | E_err:   0.007882
[2025-10-06 08:51:21] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -43.602649 | E_var:     0.2531 | E_err:   0.007861
[2025-10-06 08:51:27] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -43.590007 | E_var:     0.4079 | E_err:   0.009979
[2025-10-06 08:51:32] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -43.602436 | E_var:     0.2725 | E_err:   0.008156
[2025-10-06 08:51:37] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -43.602631 | E_var:     0.2324 | E_err:   0.007533
[2025-10-06 08:51:42] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -43.603773 | E_var:     0.2871 | E_err:   0.008372
[2025-10-06 08:51:47] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -43.594190 | E_var:     0.2670 | E_err:   0.008073
[2025-10-06 08:51:52] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -43.605434 | E_var:     0.2462 | E_err:   0.007753
[2025-10-06 08:51:57] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -43.593177 | E_var:     0.2903 | E_err:   0.008419
[2025-10-06 08:52:03] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -43.606873 | E_var:     0.3309 | E_err:   0.008989
[2025-10-06 08:52:08] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -43.592412 | E_var:     0.2394 | E_err:   0.007645
[2025-10-06 08:52:13] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -43.606413 | E_var:     0.2673 | E_err:   0.008078
[2025-10-06 08:52:18] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -43.604134 | E_var:     0.2984 | E_err:   0.008536
[2025-10-06 08:52:23] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -43.593839 | E_var:     0.2250 | E_err:   0.007412
[2025-10-06 08:52:28] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -43.583176 | E_var:     0.3091 | E_err:   0.008687
[2025-10-06 08:52:33] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -43.602553 | E_var:     0.2803 | E_err:   0.008273
[2025-10-06 08:52:38] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -43.587903 | E_var:     0.2751 | E_err:   0.008195
[2025-10-06 08:52:44] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -43.603374 | E_var:     0.2579 | E_err:   0.007936
[2025-10-06 08:52:49] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -43.581200 | E_var:     1.2477 | E_err:   0.017453
[2025-10-06 08:52:54] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -43.585049 | E_var:     0.9818 | E_err:   0.015482
[2025-10-06 08:52:59] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -43.612262 | E_var:     0.3505 | E_err:   0.009250
[2025-10-06 08:53:04] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -43.607750 | E_var:     0.4521 | E_err:   0.010506
[2025-10-06 08:53:09] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -43.617689 | E_var:     0.2372 | E_err:   0.007610
[2025-10-06 08:53:14] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -43.602892 | E_var:     0.2667 | E_err:   0.008069
[2025-10-06 08:53:19] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -43.601623 | E_var:     0.3492 | E_err:   0.009233
[2025-10-06 08:53:25] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -43.585968 | E_var:     0.3547 | E_err:   0.009305
[2025-10-06 08:53:30] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -43.574574 | E_var:     1.0630 | E_err:   0.016110
[2025-10-06 08:53:35] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -43.591596 | E_var:     0.2407 | E_err:   0.007665
[2025-10-06 08:53:40] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -43.592101 | E_var:     0.2810 | E_err:   0.008283
[2025-10-06 08:53:45] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -43.602855 | E_var:     0.3921 | E_err:   0.009784
[2025-10-06 08:53:50] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -43.599291 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 08:53:55] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -43.603739 | E_var:     0.2319 | E_err:   0.007524
[2025-10-06 08:54:00] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -43.591765 | E_var:     0.2954 | E_err:   0.008492
[2025-10-06 08:54:05] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -43.604009 | E_var:     0.2633 | E_err:   0.008018
[2025-10-06 08:54:11] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -43.601880 | E_var:     0.3010 | E_err:   0.008573
[2025-10-06 08:54:16] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -43.594145 | E_var:     0.2827 | E_err:   0.008308
[2025-10-06 08:54:21] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -43.608460 | E_var:     0.2590 | E_err:   0.007952
[2025-10-06 08:54:26] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -43.588557 | E_var:     0.2713 | E_err:   0.008139
[2025-10-06 08:54:31] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -43.604988 | E_var:     0.2773 | E_err:   0.008228
[2025-10-06 08:54:36] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -43.601532 | E_var:     0.5254 | E_err:   0.011326
[2025-10-06 08:54:41] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -43.613013 | E_var:     0.3674 | E_err:   0.009470
[2025-10-06 08:54:46] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -43.604972 | E_var:     0.2502 | E_err:   0.007816
[2025-10-06 08:54:52] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -43.605732 | E_var:     0.2630 | E_err:   0.008014
[2025-10-06 08:54:57] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -43.595692 | E_var:     0.3566 | E_err:   0.009330
[2025-10-06 08:55:02] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -43.611494 | E_var:     0.2582 | E_err:   0.007940
[2025-10-06 08:55:07] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -43.620608 | E_var:     0.7446 | E_err:   0.013483
[2025-10-06 08:55:12] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -43.594612 | E_var:     0.3409 | E_err:   0.009123
[2025-10-06 08:55:17] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -43.606780 | E_var:     0.2527 | E_err:   0.007854
[2025-10-06 08:55:22] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -43.610028 | E_var:     0.3637 | E_err:   0.009424
[2025-10-06 08:55:22] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 08:55:28] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -43.610131 | E_var:     0.3405 | E_err:   0.009118
[2025-10-06 08:55:33] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -43.588150 | E_var:     0.3408 | E_err:   0.009121
[2025-10-06 08:55:38] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -43.607067 | E_var:     0.3456 | E_err:   0.009185
[2025-10-06 08:55:43] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -43.599451 | E_var:     0.2248 | E_err:   0.007408
[2025-10-06 08:55:48] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -43.609473 | E_var:     0.3030 | E_err:   0.008601
[2025-10-06 08:55:53] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -43.620409 | E_var:     0.5026 | E_err:   0.011077
[2025-10-06 08:55:58] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -43.612371 | E_var:     0.3019 | E_err:   0.008585
[2025-10-06 08:56:03] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -43.591126 | E_var:     0.2724 | E_err:   0.008155
[2025-10-06 08:56:09] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -43.605641 | E_var:     0.2931 | E_err:   0.008459
[2025-10-06 08:56:14] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -43.601414 | E_var:     0.2815 | E_err:   0.008290
[2025-10-06 08:56:19] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -43.610617 | E_var:     0.3692 | E_err:   0.009495
[2025-10-06 08:56:24] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -43.601672 | E_var:     0.3147 | E_err:   0.008765
[2025-10-06 08:56:29] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -43.596108 | E_var:     0.2654 | E_err:   0.008049
[2025-10-06 08:56:34] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -43.611772 | E_var:     0.2582 | E_err:   0.007940
[2025-10-06 08:56:39] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -43.594837 | E_var:     0.2549 | E_err:   0.007889
[2025-10-06 08:56:44] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -43.601369 | E_var:     0.3171 | E_err:   0.008799
[2025-10-06 08:56:50] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -43.593535 | E_var:     0.2861 | E_err:   0.008358
[2025-10-06 08:56:55] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -43.619302 | E_var:     0.3198 | E_err:   0.008836
[2025-10-06 08:57:00] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -43.608989 | E_var:     0.2319 | E_err:   0.007525
[2025-10-06 08:57:05] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -43.601892 | E_var:     0.2556 | E_err:   0.007899
[2025-10-06 08:57:10] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -43.614238 | E_var:     0.2720 | E_err:   0.008149
[2025-10-06 08:57:15] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -43.604400 | E_var:     0.3183 | E_err:   0.008815
[2025-10-06 08:57:20] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -43.588210 | E_var:     0.3055 | E_err:   0.008636
[2025-10-06 08:57:25] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -43.585304 | E_var:     0.2176 | E_err:   0.007288
[2025-10-06 08:57:31] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -43.600071 | E_var:     0.2486 | E_err:   0.007791
[2025-10-06 08:57:36] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -43.618283 | E_var:     0.4800 | E_err:   0.010825
[2025-10-06 08:57:41] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -43.608003 | E_var:     0.3972 | E_err:   0.009848
[2025-10-06 08:57:46] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -43.612118 | E_var:     0.3305 | E_err:   0.008982
[2025-10-06 08:57:51] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -43.606206 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 08:57:56] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -43.602312 | E_var:     0.2541 | E_err:   0.007877
[2025-10-06 08:58:01] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -43.603583 | E_var:     0.2481 | E_err:   0.007783
[2025-10-06 08:58:06] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -43.602774 | E_var:     0.3498 | E_err:   0.009242
[2025-10-06 08:58:12] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -43.605012 | E_var:     0.2415 | E_err:   0.007679
[2025-10-06 08:58:17] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -43.604631 | E_var:     0.3335 | E_err:   0.009023
[2025-10-06 08:58:22] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -43.618066 | E_var:     0.2214 | E_err:   0.007352
[2025-10-06 08:58:27] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -43.611148 | E_var:     0.4484 | E_err:   0.010463
[2025-10-06 08:58:32] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -43.605089 | E_var:     0.2696 | E_err:   0.008113
[2025-10-06 08:58:37] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -43.588337 | E_var:     0.3782 | E_err:   0.009609
[2025-10-06 08:58:42] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -43.593766 | E_var:     0.2513 | E_err:   0.007833
[2025-10-06 08:58:47] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -43.605037 | E_var:     0.2704 | E_err:   0.008125
[2025-10-06 08:58:53] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -43.607400 | E_var:     0.2399 | E_err:   0.007653
[2025-10-06 08:58:58] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -43.602599 | E_var:     0.2848 | E_err:   0.008338
[2025-10-06 08:59:03] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -43.605980 | E_var:     0.2941 | E_err:   0.008473
[2025-10-06 08:59:08] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -43.599835 | E_var:     0.2763 | E_err:   0.008213
[2025-10-06 08:59:13] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -43.596952 | E_var:     0.2541 | E_err:   0.007876
[2025-10-06 08:59:18] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -43.605244 | E_var:     0.2292 | E_err:   0.007480
[2025-10-06 08:59:23] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -43.605794 | E_var:     0.2655 | E_err:   0.008051
[2025-10-06 08:59:28] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -43.585749 | E_var:     0.4012 | E_err:   0.009897
[2025-10-06 08:59:34] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -43.599478 | E_var:     0.3390 | E_err:   0.009098
[2025-10-06 08:59:39] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -43.601612 | E_var:     0.3757 | E_err:   0.009577
[2025-10-06 08:59:44] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -43.590585 | E_var:     0.3227 | E_err:   0.008876
[2025-10-06 08:59:49] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -43.589144 | E_var:     0.3026 | E_err:   0.008595
[2025-10-06 08:59:54] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -43.609746 | E_var:     0.2453 | E_err:   0.007738
[2025-10-06 08:59:59] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -43.602563 | E_var:     0.3396 | E_err:   0.009106
[2025-10-06 09:00:04] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -43.610066 | E_var:     0.2766 | E_err:   0.008217
[2025-10-06 09:00:09] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -43.582177 | E_var:     0.2878 | E_err:   0.008382
[2025-10-06 09:00:15] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -43.613038 | E_var:     0.2697 | E_err:   0.008114
[2025-10-06 09:00:20] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -43.606603 | E_var:     0.2904 | E_err:   0.008420
[2025-10-06 09:00:25] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -43.611092 | E_var:     0.2604 | E_err:   0.007973
[2025-10-06 09:00:30] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -43.603653 | E_var:     0.3176 | E_err:   0.008806
[2025-10-06 09:00:35] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -43.599242 | E_var:     0.2808 | E_err:   0.008280
[2025-10-06 09:00:40] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -43.614588 | E_var:     0.2723 | E_err:   0.008153
[2025-10-06 09:00:45] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -43.607024 | E_var:     0.5995 | E_err:   0.012098
[2025-10-06 09:00:50] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -43.591615 | E_var:     0.2757 | E_err:   0.008204
[2025-10-06 09:00:56] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -43.608641 | E_var:     0.2538 | E_err:   0.007872
[2025-10-06 09:01:01] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -43.616177 | E_var:     0.2289 | E_err:   0.007475
[2025-10-06 09:01:06] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -43.600981 | E_var:     0.2917 | E_err:   0.008439
[2025-10-06 09:01:11] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -43.612183 | E_var:     0.4558 | E_err:   0.010549
[2025-10-06 09:01:16] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -43.583432 | E_var:     0.2230 | E_err:   0.007378
[2025-10-06 09:01:21] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -43.595646 | E_var:     0.3707 | E_err:   0.009513
[2025-10-06 09:01:26] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -43.589014 | E_var:     0.3598 | E_err:   0.009373
[2025-10-06 09:01:31] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -43.587951 | E_var:     0.2555 | E_err:   0.007898
[2025-10-06 09:01:37] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -43.595467 | E_var:     0.2636 | E_err:   0.008022
[2025-10-06 09:01:42] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -43.615378 | E_var:     0.2635 | E_err:   0.008021
[2025-10-06 09:01:47] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -43.611947 | E_var:     0.2406 | E_err:   0.007664
[2025-10-06 09:01:52] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -43.589129 | E_var:     0.4980 | E_err:   0.011026
[2025-10-06 09:01:57] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -43.588550 | E_var:     0.3492 | E_err:   0.009233
[2025-10-06 09:02:02] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -43.598939 | E_var:     0.2630 | E_err:   0.008013
[2025-10-06 09:02:07] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -43.596715 | E_var:     0.2656 | E_err:   0.008053
[2025-10-06 09:02:12] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -43.588609 | E_var:     1.3686 | E_err:   0.018279
[2025-10-06 09:02:18] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -43.617065 | E_var:     0.3280 | E_err:   0.008948
[2025-10-06 09:02:23] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -43.615525 | E_var:     0.2398 | E_err:   0.007652
[2025-10-06 09:02:28] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -43.606775 | E_var:     0.2875 | E_err:   0.008378
[2025-10-06 09:02:33] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -43.615928 | E_var:     0.3871 | E_err:   0.009721
[2025-10-06 09:02:38] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -43.602135 | E_var:     0.2072 | E_err:   0.007112
[2025-10-06 09:02:43] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -43.603109 | E_var:     0.2698 | E_err:   0.008117
[2025-10-06 09:02:48] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -43.613531 | E_var:     0.3410 | E_err:   0.009124
[2025-10-06 09:02:53] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -43.610245 | E_var:     0.2630 | E_err:   0.008013
[2025-10-06 09:02:59] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -43.618824 | E_var:     0.4496 | E_err:   0.010477
[2025-10-06 09:03:04] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -43.591685 | E_var:     0.3286 | E_err:   0.008957
[2025-10-06 09:03:09] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -43.595808 | E_var:     0.2462 | E_err:   0.007754
[2025-10-06 09:03:14] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -43.605938 | E_var:     0.2567 | E_err:   0.007917
[2025-10-06 09:03:19] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -43.606844 | E_var:     0.2536 | E_err:   0.007868
[2025-10-06 09:03:24] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -43.608998 | E_var:     0.2329 | E_err:   0.007540
[2025-10-06 09:03:29] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -43.608564 | E_var:     0.2995 | E_err:   0.008551
[2025-10-06 09:03:34] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -43.596536 | E_var:     0.2389 | E_err:   0.007638
[2025-10-06 09:03:40] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -43.609597 | E_var:     0.2544 | E_err:   0.007880
[2025-10-06 09:03:45] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -43.610945 | E_var:     0.2290 | E_err:   0.007477
[2025-10-06 09:03:50] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -43.586087 | E_var:     0.2977 | E_err:   0.008525
[2025-10-06 09:03:55] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -43.602693 | E_var:     0.3059 | E_err:   0.008642
[2025-10-06 09:03:55] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 09:04:00] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -43.610010 | E_var:     0.2376 | E_err:   0.007616
[2025-10-06 09:04:05] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -43.597790 | E_var:     0.3471 | E_err:   0.009206
[2025-10-06 09:04:10] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -43.605572 | E_var:     0.2921 | E_err:   0.008445
[2025-10-06 09:04:15] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -43.599044 | E_var:     0.3446 | E_err:   0.009172
[2025-10-06 09:04:21] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -43.595557 | E_var:     0.2329 | E_err:   0.007540
[2025-10-06 09:04:26] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -43.601159 | E_var:     0.2997 | E_err:   0.008554
[2025-10-06 09:04:31] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -43.595591 | E_var:     0.2289 | E_err:   0.007475
[2025-10-06 09:04:36] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -43.598463 | E_var:     0.2713 | E_err:   0.008139
[2025-10-06 09:04:41] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -43.595676 | E_var:     0.2910 | E_err:   0.008429
[2025-10-06 09:04:46] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -43.608259 | E_var:     0.2854 | E_err:   0.008348
[2025-10-06 09:04:51] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -43.602895 | E_var:     0.1998 | E_err:   0.006984
[2025-10-06 09:04:56] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -43.608130 | E_var:     0.2666 | E_err:   0.008067
[2025-10-06 09:05:02] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -43.613077 | E_var:     0.2265 | E_err:   0.007436
[2025-10-06 09:05:07] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -43.601642 | E_var:     0.3117 | E_err:   0.008723
[2025-10-06 09:05:12] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -43.609142 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 09:05:17] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -43.613651 | E_var:     0.3674 | E_err:   0.009471
[2025-10-06 09:05:22] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -43.608717 | E_var:     0.2496 | E_err:   0.007806
[2025-10-06 09:05:27] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -43.603273 | E_var:     0.2620 | E_err:   0.007998
[2025-10-06 09:05:32] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -43.601761 | E_var:     0.3045 | E_err:   0.008623
[2025-10-06 09:05:37] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -43.604490 | E_var:     0.2662 | E_err:   0.008062
[2025-10-06 09:05:43] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -43.611064 | E_var:     0.2558 | E_err:   0.007903
[2025-10-06 09:05:48] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -43.591862 | E_var:     0.3669 | E_err:   0.009464
[2025-10-06 09:05:53] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -43.603691 | E_var:     0.2379 | E_err:   0.007620
[2025-10-06 09:05:58] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -43.606540 | E_var:     0.2477 | E_err:   0.007776
[2025-10-06 09:06:03] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -43.581798 | E_var:     1.2576 | E_err:   0.017522
[2025-10-06 09:06:08] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -43.577317 | E_var:     1.0878 | E_err:   0.016296
[2025-10-06 09:06:13] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -43.595282 | E_var:     1.2716 | E_err:   0.017619
[2025-10-06 09:06:18] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -43.592240 | E_var:     0.2527 | E_err:   0.007854
[2025-10-06 09:06:24] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -43.590055 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 09:06:29] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -43.599433 | E_var:     0.2762 | E_err:   0.008212
[2025-10-06 09:06:34] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -43.601920 | E_var:     0.2643 | E_err:   0.008034
[2025-10-06 09:06:39] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -43.612764 | E_var:     0.2821 | E_err:   0.008299
[2025-10-06 09:06:44] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -43.592856 | E_var:     0.3710 | E_err:   0.009517
[2025-10-06 09:06:49] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -43.602444 | E_var:     0.3427 | E_err:   0.009147
[2025-10-06 09:06:54] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -43.605179 | E_var:     0.2881 | E_err:   0.008387
[2025-10-06 09:06:59] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -43.593830 | E_var:     0.2025 | E_err:   0.007031
[2025-10-06 09:07:05] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -43.597726 | E_var:     0.2879 | E_err:   0.008384
[2025-10-06 09:07:10] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -43.603754 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 09:07:15] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -43.603876 | E_var:     0.2287 | E_err:   0.007473
[2025-10-06 09:07:20] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -43.626139 | E_var:     0.3022 | E_err:   0.008589
[2025-10-06 09:07:25] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -43.592554 | E_var:     0.3251 | E_err:   0.008909
[2025-10-06 09:07:30] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -43.601455 | E_var:     0.2441 | E_err:   0.007720
[2025-10-06 09:07:35] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -43.609302 | E_var:     0.2376 | E_err:   0.007616
[2025-10-06 09:07:40] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -43.606294 | E_var:     0.2296 | E_err:   0.007488
[2025-10-06 09:07:46] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -43.601183 | E_var:     0.2675 | E_err:   0.008082
[2025-10-06 09:07:51] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -43.595956 | E_var:     0.3042 | E_err:   0.008617
[2025-10-06 09:07:56] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -43.613582 | E_var:     0.2832 | E_err:   0.008315
[2025-10-06 09:08:01] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -43.608476 | E_var:     0.3330 | E_err:   0.009017
[2025-10-06 09:08:06] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -43.610757 | E_var:     0.3303 | E_err:   0.008981
[2025-10-06 09:08:11] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -43.603305 | E_var:     0.2767 | E_err:   0.008219
[2025-10-06 09:08:11] ======================================================================================================
[2025-10-06 09:08:11] ✅ Training completed successfully
[2025-10-06 09:08:11] Total restarts: 2
[2025-10-06 09:08:13] Final Energy: -43.60330464 ± 0.00821939
[2025-10-06 09:08:13] Final Variance: 0.276719
[2025-10-06 09:08:13] ======================================================================================================
[2025-10-06 09:08:13] ======================================================================================================
[2025-10-06 09:08:13] Training completed | Runtime: 5436.1s
[2025-10-06 09:08:15] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 09:08:15] ======================================================================================================
