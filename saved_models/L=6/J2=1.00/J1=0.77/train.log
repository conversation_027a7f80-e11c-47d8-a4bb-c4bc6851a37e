[2025-10-22 13:23:58] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.76/training/checkpoints/checkpoint_iter_002250.pkl
[2025-10-22 13:23:58]   - 迭代次数: 2250
[2025-10-22 13:23:58]   - 能量: -60.903988+0.003258j ± 0.008471, Var: 0.587805
[2025-10-22 13:23:58]   - 时间戳: 2025-10-22T07:52:14.844787+08:00
[2025-10-22 13:24:23] ✓ 变分状态参数已从checkpoint恢复
[2025-10-22 13:24:23] ======================================================================================================
[2025-10-22 13:24:23] GCNN for Shastry-Sutherland Model
[2025-10-22 13:24:23] ======================================================================================================
[2025-10-22 13:24:23] System parameters:
[2025-10-22 13:24:23]   - System size: L=6, N=144
[2025-10-22 13:24:23]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-10-22 13:24:23] ------------------------------------------------------------------------------------------------------
[2025-10-22 13:24:23] Model parameters:
[2025-10-22 13:24:23]   - Number of layers = 4
[2025-10-22 13:24:23]   - Number of features = 4
[2025-10-22 13:24:23]   - Total parameters = 28252
[2025-10-22 13:24:23] ------------------------------------------------------------------------------------------------------
[2025-10-22 13:24:23] Training parameters:
[2025-10-22 13:24:23]   - Total iterations: 1050
[2025-10-22 13:24:23]   - Annealing cycles: 3
[2025-10-22 13:24:23]   - Initial period: 150
[2025-10-22 13:24:23]   - Period multiplier: 2.0
[2025-10-22 13:24:23]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-22 13:24:23]   - Samples: 4096
[2025-10-22 13:24:23]   - Discarded samples: 0
[2025-10-22 13:24:23]   - Chunk size: 4096
[2025-10-22 13:24:23]   - Diagonal shift: 0.15
[2025-10-22 13:24:23]   - Gradient clipping: 1.0
[2025-10-22 13:24:23]   - Checkpoint enabled: interval=105
[2025-10-22 13:24:23]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.77/training/checkpoints
[2025-10-22 13:24:23]   - Resuming from iteration: 2250
[2025-10-22 13:24:23] ------------------------------------------------------------------------------------------------------
[2025-10-22 13:24:23] Device status:
[2025-10-22 13:24:23]   - Devices model: NVIDIA H200 NVL
[2025-10-22 13:24:23]   - Number of devices: 1
[2025-10-22 13:24:23]   - Sharding: True
[2025-10-22 13:24:24] ======================================================================================================
[2025-10-22 13:25:14] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -61.807621 | E_var:     1.9737 | E_err:   0.021951
[2025-10-22 13:25:48] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -61.810195 | E_var:     1.1623 | E_err:   0.016845
[2025-10-22 13:26:01] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -61.792389 | E_var:     1.0385 | E_err:   0.015923
[2025-10-22 13:26:14] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -61.829163 | E_var:     0.7921 | E_err:   0.013906
[2025-10-22 13:26:27] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -61.821507 | E_var:     0.7815 | E_err:   0.013813
[2025-10-22 13:26:40] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -61.831824 | E_var:     0.7277 | E_err:   0.013329
[2025-10-22 13:26:53] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -61.819307 | E_var:     0.7059 | E_err:   0.013127
[2025-10-22 13:27:07] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -61.826373 | E_var:     0.6701 | E_err:   0.012791
[2025-10-22 13:27:20] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -61.802761 | E_var:     0.6786 | E_err:   0.012871
[2025-10-22 13:27:33] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -61.840782 | E_var:     0.5572 | E_err:   0.011663
[2025-10-22 13:27:46] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -61.804825 | E_var:     0.6787 | E_err:   0.012872
[2025-10-22 13:27:59] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -61.803994 | E_var:     0.7561 | E_err:   0.013587
[2025-10-22 13:28:12] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -61.824902 | E_var:     0.6338 | E_err:   0.012439
[2025-10-22 13:28:25] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -61.826972 | E_var:     0.6254 | E_err:   0.012357
[2025-10-22 13:28:39] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -61.839269 | E_var:     0.8468 | E_err:   0.014378
[2025-10-22 13:28:52] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -61.819578 | E_var:     0.5833 | E_err:   0.011934
[2025-10-22 13:29:05] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -61.795267 | E_var:     0.5547 | E_err:   0.011637
[2025-10-22 13:29:18] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -61.832380 | E_var:     0.6330 | E_err:   0.012432
[2025-10-22 13:29:31] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -61.805188 | E_var:     0.5802 | E_err:   0.011901
[2025-10-22 13:29:45] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -61.797871 | E_var:     0.8463 | E_err:   0.014374
[2025-10-22 13:29:58] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -61.797622 | E_var:     0.6395 | E_err:   0.012495
[2025-10-22 13:30:11] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -61.815768 | E_var:     0.6714 | E_err:   0.012803
[2025-10-22 13:30:24] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -61.805184 | E_var:     0.7124 | E_err:   0.013188
[2025-10-22 13:30:37] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -61.815443 | E_var:     0.5818 | E_err:   0.011918
[2025-10-22 13:30:50] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -61.796034 | E_var:     0.6047 | E_err:   0.012150
[2025-10-22 13:31:04] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -61.802636 | E_var:     0.7578 | E_err:   0.013602
[2025-10-22 13:31:17] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -61.808785 | E_var:     0.7736 | E_err:   0.013743
[2025-10-22 13:31:30] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -61.827048 | E_var:     0.5353 | E_err:   0.011432
[2025-10-22 13:31:43] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -61.829522 | E_var:     0.6156 | E_err:   0.012259
[2025-10-22 13:31:56] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -61.840624 | E_var:     0.5760 | E_err:   0.011859
[2025-10-22 13:32:09] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -61.805311 | E_var:     0.5927 | E_err:   0.012029
[2025-10-22 13:32:22] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -61.799125 | E_var:     0.5498 | E_err:   0.011586
[2025-10-22 13:32:36] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -61.815204 | E_var:     0.6495 | E_err:   0.012592
[2025-10-22 13:32:49] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -61.818444 | E_var:     0.6009 | E_err:   0.012112
[2025-10-22 13:33:02] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -61.811703 | E_var:     0.6908 | E_err:   0.012987
[2025-10-22 13:33:15] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -61.841340 | E_var:     0.6372 | E_err:   0.012473
[2025-10-22 13:33:28] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -61.818667 | E_var:     0.4831 | E_err:   0.010860
[2025-10-22 13:33:41] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -61.811768 | E_var:     0.8807 | E_err:   0.014663
[2025-10-22 13:33:55] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -61.808149 | E_var:     0.6655 | E_err:   0.012747
[2025-10-22 13:34:08] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -61.834896 | E_var:     0.5476 | E_err:   0.011563
[2025-10-22 13:34:21] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -61.820734 | E_var:     0.7759 | E_err:   0.013763
[2025-10-22 13:34:34] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -61.794650 | E_var:     0.6768 | E_err:   0.012854
[2025-10-22 13:34:47] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -61.820697 | E_var:     0.6129 | E_err:   0.012233
[2025-10-22 13:35:00] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -61.828884 | E_var:     0.6190 | E_err:   0.012293
[2025-10-22 13:35:13] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -61.825846 | E_var:     0.5162 | E_err:   0.011226
[2025-10-22 13:35:27] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -61.802226 | E_var:     0.5643 | E_err:   0.011737
[2025-10-22 13:35:40] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -61.803474 | E_var:     0.6668 | E_err:   0.012759
[2025-10-22 13:35:53] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -61.813776 | E_var:     0.5493 | E_err:   0.011581
[2025-10-22 13:36:06] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -61.809836 | E_var:     0.5468 | E_err:   0.011554
[2025-10-22 13:36:19] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -61.804389 | E_var:     0.5839 | E_err:   0.011940
[2025-10-22 13:36:32] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -61.828619 | E_var:     0.7346 | E_err:   0.013392
[2025-10-22 13:36:45] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -61.820867 | E_var:     0.5697 | E_err:   0.011793
[2025-10-22 13:36:59] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -61.808893 | E_var:     0.5082 | E_err:   0.011139
[2025-10-22 13:37:12] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -61.819542 | E_var:     0.5738 | E_err:   0.011836
[2025-10-22 13:37:25] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -61.817367 | E_var:     0.5821 | E_err:   0.011922
[2025-10-22 13:37:38] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -61.821356 | E_var:     0.6405 | E_err:   0.012505
[2025-10-22 13:37:51] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -61.821405 | E_var:     0.7334 | E_err:   0.013381
[2025-10-22 13:38:04] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -61.805499 | E_var:     0.5460 | E_err:   0.011545
[2025-10-22 13:38:17] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -61.810319 | E_var:     0.5604 | E_err:   0.011697
[2025-10-22 13:38:31] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -61.819661 | E_var:     0.6735 | E_err:   0.012823
[2025-10-22 13:38:44] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -61.794312 | E_var:     0.6561 | E_err:   0.012656
[2025-10-22 13:38:57] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -61.808329 | E_var:     0.9188 | E_err:   0.014977
[2025-10-22 13:39:10] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -61.820021 | E_var:     0.5766 | E_err:   0.011865
[2025-10-22 13:39:23] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -61.823578 | E_var:     0.4955 | E_err:   0.010999
[2025-10-22 13:39:36] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -61.798557 | E_var:     0.6726 | E_err:   0.012815
[2025-10-22 13:39:49] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -61.830636 | E_var:     0.8128 | E_err:   0.014087
[2025-10-22 13:40:03] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -61.790446 | E_var:     0.6505 | E_err:   0.012602
[2025-10-22 13:40:16] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -61.814165 | E_var:     0.6390 | E_err:   0.012491
[2025-10-22 13:40:29] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -61.809397 | E_var:     0.8064 | E_err:   0.014031
[2025-10-22 13:40:42] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -61.831824 | E_var:     0.5793 | E_err:   0.011893
[2025-10-22 13:40:55] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -61.826936 | E_var:     0.5142 | E_err:   0.011205
[2025-10-22 13:41:08] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -61.807275 | E_var:     0.5409 | E_err:   0.011491
[2025-10-22 13:41:22] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -61.803270 | E_var:     0.5390 | E_err:   0.011471
[2025-10-22 13:41:35] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -61.822927 | E_var:     0.6176 | E_err:   0.012279
[2025-10-22 13:41:48] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -61.808818 | E_var:     0.6848 | E_err:   0.012930
[2025-10-22 13:42:01] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -61.843900 | E_var:     0.5835 | E_err:   0.011936
[2025-10-22 13:42:14] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -61.804063 | E_var:     0.5725 | E_err:   0.011822
[2025-10-22 13:42:27] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -61.794760 | E_var:     0.6203 | E_err:   0.012306
[2025-10-22 13:42:40] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -61.811179 | E_var:     0.5238 | E_err:   0.011309
[2025-10-22 13:42:54] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -61.820195 | E_var:     0.5367 | E_err:   0.011447
[2025-10-22 13:43:07] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -61.820399 | E_var:     0.5899 | E_err:   0.012000
[2025-10-22 13:43:20] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -61.816609 | E_var:     0.5708 | E_err:   0.011805
[2025-10-22 13:43:33] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -61.797521 | E_var:     0.6084 | E_err:   0.012188
[2025-10-22 13:43:46] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -61.816175 | E_var:     0.6805 | E_err:   0.012890
[2025-10-22 13:43:59] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -61.818580 | E_var:     0.6820 | E_err:   0.012904
[2025-10-22 13:44:12] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -61.822409 | E_var:     0.6257 | E_err:   0.012359
[2025-10-22 13:44:26] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -61.829059 | E_var:     0.5993 | E_err:   0.012096
[2025-10-22 13:44:39] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -61.799687 | E_var:     0.4896 | E_err:   0.010933
[2025-10-22 13:44:52] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -61.816872 | E_var:     0.5674 | E_err:   0.011770
[2025-10-22 13:45:05] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -61.805894 | E_var:     0.5061 | E_err:   0.011115
[2025-10-22 13:45:18] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -61.830647 | E_var:     0.5655 | E_err:   0.011750
[2025-10-22 13:45:31] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -61.827860 | E_var:     0.6267 | E_err:   0.012370
[2025-10-22 13:45:45] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -61.816680 | E_var:     0.5868 | E_err:   0.011969
[2025-10-22 13:45:58] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -61.800350 | E_var:     0.5253 | E_err:   0.011325
[2025-10-22 13:46:11] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -61.800729 | E_var:     0.6070 | E_err:   0.012173
[2025-10-22 13:46:24] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -61.818461 | E_var:     0.5630 | E_err:   0.011724
[2025-10-22 13:46:37] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -61.792387 | E_var:     0.6386 | E_err:   0.012487
[2025-10-22 13:46:50] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -61.838360 | E_var:     0.4881 | E_err:   0.010916
[2025-10-22 13:47:03] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -61.826360 | E_var:     0.5953 | E_err:   0.012055
[2025-10-22 13:47:17] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -61.785729 | E_var:     0.6565 | E_err:   0.012660
[2025-10-22 13:47:30] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -61.808122 | E_var:     0.6821 | E_err:   0.012905
[2025-10-22 13:47:43] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -61.807240 | E_var:     0.9213 | E_err:   0.014997
[2025-10-22 13:47:56] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -61.786800 | E_var:     0.6619 | E_err:   0.012712
[2025-10-22 13:48:09] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -61.822898 | E_var:     0.6173 | E_err:   0.012277
[2025-10-22 13:48:22] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -61.806747 | E_var:     0.6985 | E_err:   0.013059
[2025-10-22 13:48:22] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-22 13:48:36] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -61.823312 | E_var:     0.7238 | E_err:   0.013293
[2025-10-22 13:48:49] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -61.817116 | E_var:     0.5791 | E_err:   0.011890
[2025-10-22 13:49:02] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -61.820954 | E_var:     0.5803 | E_err:   0.011903
[2025-10-22 13:49:15] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -61.826240 | E_var:     0.7452 | E_err:   0.013488
[2025-10-22 13:49:28] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -61.800591 | E_var:     0.5610 | E_err:   0.011703
[2025-10-22 13:49:41] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -61.831334 | E_var:     0.5423 | E_err:   0.011506
[2025-10-22 13:49:54] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -61.828658 | E_var:     0.6632 | E_err:   0.012724
[2025-10-22 13:50:08] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -61.843045 | E_var:     0.5512 | E_err:   0.011600
[2025-10-22 13:50:21] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -61.806796 | E_var:     0.5749 | E_err:   0.011847
[2025-10-22 13:50:34] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -61.809584 | E_var:     0.5409 | E_err:   0.011492
[2025-10-22 13:50:47] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -61.795920 | E_var:     0.6363 | E_err:   0.012463
[2025-10-22 13:51:00] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -61.813670 | E_var:     0.6273 | E_err:   0.012376
[2025-10-22 13:51:13] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -61.816604 | E_var:     0.5276 | E_err:   0.011349
[2025-10-22 13:51:26] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -61.801126 | E_var:     0.6141 | E_err:   0.012245
[2025-10-22 13:51:40] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -61.819510 | E_var:     0.5279 | E_err:   0.011352
[2025-10-22 13:51:53] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -61.807951 | E_var:     0.6067 | E_err:   0.012170
[2025-10-22 13:52:06] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -61.824340 | E_var:     0.5635 | E_err:   0.011729
[2025-10-22 13:52:19] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -61.806620 | E_var:     0.5548 | E_err:   0.011639
[2025-10-22 13:52:32] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -61.810281 | E_var:     0.6142 | E_err:   0.012246
[2025-10-22 13:52:45] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -61.812313 | E_var:     0.6470 | E_err:   0.012568
[2025-10-22 13:52:58] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -61.832650 | E_var:     0.5774 | E_err:   0.011873
[2025-10-22 13:53:12] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -61.834166 | E_var:     0.6039 | E_err:   0.012143
[2025-10-22 13:53:25] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -61.819722 | E_var:     0.5755 | E_err:   0.011853
[2025-10-22 13:53:38] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -61.823901 | E_var:     0.7244 | E_err:   0.013299
[2025-10-22 13:53:51] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -61.813975 | E_var:     0.6166 | E_err:   0.012269
[2025-10-22 13:54:04] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -61.818942 | E_var:     0.6334 | E_err:   0.012436
[2025-10-22 13:54:17] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -61.840759 | E_var:     0.5755 | E_err:   0.011854
[2025-10-22 13:54:31] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -61.809501 | E_var:     0.8299 | E_err:   0.014234
[2025-10-22 13:54:44] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -61.821430 | E_var:     0.5740 | E_err:   0.011838
[2025-10-22 13:54:57] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -61.823349 | E_var:     0.5591 | E_err:   0.011683
[2025-10-22 13:55:10] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -61.813070 | E_var:     0.5860 | E_err:   0.011961
[2025-10-22 13:55:23] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -61.822677 | E_var:     0.7047 | E_err:   0.013117
[2025-10-22 13:55:36] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -61.808542 | E_var:     0.5471 | E_err:   0.011557
[2025-10-22 13:55:49] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -61.811180 | E_var:     0.6567 | E_err:   0.012662
[2025-10-22 13:56:03] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -61.809573 | E_var:     0.6729 | E_err:   0.012817
[2025-10-22 13:56:16] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -61.812333 | E_var:     0.5696 | E_err:   0.011793
[2025-10-22 13:56:29] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -61.835162 | E_var:     0.7023 | E_err:   0.013094
[2025-10-22 13:56:42] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -61.804368 | E_var:     0.5725 | E_err:   0.011823
[2025-10-22 13:56:55] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -61.818498 | E_var:     0.5138 | E_err:   0.011200
[2025-10-22 13:57:08] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -61.821428 | E_var:     0.5467 | E_err:   0.011553
[2025-10-22 13:57:21] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -61.837716 | E_var:     0.8004 | E_err:   0.013979
[2025-10-22 13:57:35] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -61.821369 | E_var:     0.7180 | E_err:   0.013240
[2025-10-22 13:57:48] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -61.823185 | E_var:     0.5424 | E_err:   0.011507
[2025-10-22 13:58:01] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -61.805971 | E_var:     0.6839 | E_err:   0.012921
[2025-10-22 13:58:14] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -61.804317 | E_var:     0.5475 | E_err:   0.011561
[2025-10-22 13:58:14] 🔄 RESTART #1 | Period: 300
[2025-10-22 13:58:27] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -61.805173 | E_var:     0.5994 | E_err:   0.012097
[2025-10-22 13:58:40] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -61.834843 | E_var:     0.6695 | E_err:   0.012785
[2025-10-22 13:58:53] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -61.836188 | E_var:     0.5364 | E_err:   0.011443
[2025-10-22 13:59:07] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -61.811112 | E_var:     0.6050 | E_err:   0.012154
[2025-10-22 13:59:20] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -61.808706 | E_var:     0.6025 | E_err:   0.012129
[2025-10-22 13:59:33] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -61.808731 | E_var:     0.5170 | E_err:   0.011235
[2025-10-22 13:59:46] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -61.829134 | E_var:     0.6278 | E_err:   0.012381
[2025-10-22 13:59:59] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -61.822569 | E_var:     0.5517 | E_err:   0.011605
[2025-10-22 14:00:12] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -61.830187 | E_var:     0.5390 | E_err:   0.011471
[2025-10-22 14:00:26] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -61.831329 | E_var:     0.5179 | E_err:   0.011245
[2025-10-22 14:00:39] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -61.813185 | E_var:     0.6514 | E_err:   0.012611
[2025-10-22 14:00:52] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -61.821233 | E_var:     0.7737 | E_err:   0.013744
[2025-10-22 14:01:05] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -61.819340 | E_var:     0.6562 | E_err:   0.012657
[2025-10-22 14:01:18] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -61.817055 | E_var:     0.6186 | E_err:   0.012289
[2025-10-22 14:01:31] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -61.807355 | E_var:     0.5112 | E_err:   0.011172
[2025-10-22 14:01:44] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -61.816536 | E_var:     0.6306 | E_err:   0.012407
[2025-10-22 14:01:58] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -61.816154 | E_var:     0.6083 | E_err:   0.012187
[2025-10-22 14:02:11] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -61.811800 | E_var:     1.0428 | E_err:   0.015956
[2025-10-22 14:02:24] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -61.827383 | E_var:     0.6804 | E_err:   0.012888
[2025-10-22 14:02:37] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -61.811182 | E_var:     0.6054 | E_err:   0.012157
[2025-10-22 14:02:50] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -61.812898 | E_var:     0.6935 | E_err:   0.013012
[2025-10-22 14:03:03] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -61.789056 | E_var:     0.5366 | E_err:   0.011446
[2025-10-22 14:03:17] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -61.824785 | E_var:     0.6245 | E_err:   0.012348
[2025-10-22 14:03:30] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -61.841825 | E_var:     0.7910 | E_err:   0.013896
[2025-10-22 14:03:43] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -61.810751 | E_var:     0.5068 | E_err:   0.011124
[2025-10-22 14:03:56] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -61.808317 | E_var:     0.6135 | E_err:   0.012239
[2025-10-22 14:04:09] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -61.819454 | E_var:     0.6450 | E_err:   0.012548
[2025-10-22 14:04:22] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -61.811483 | E_var:     0.6130 | E_err:   0.012234
[2025-10-22 14:04:35] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -61.827893 | E_var:     0.5755 | E_err:   0.011854
[2025-10-22 14:04:49] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -61.816126 | E_var:     0.5142 | E_err:   0.011204
[2025-10-22 14:05:02] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -61.822615 | E_var:     0.6167 | E_err:   0.012270
[2025-10-22 14:05:15] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -61.814226 | E_var:     0.5834 | E_err:   0.011934
[2025-10-22 14:05:28] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -61.816438 | E_var:     0.6412 | E_err:   0.012512
[2025-10-22 14:05:41] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -61.804065 | E_var:     0.6044 | E_err:   0.012147
[2025-10-22 14:05:54] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -61.806614 | E_var:     0.5434 | E_err:   0.011518
[2025-10-22 14:06:07] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -61.815408 | E_var:     0.5382 | E_err:   0.011463
[2025-10-22 14:06:21] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -61.801019 | E_var:     0.6256 | E_err:   0.012358
[2025-10-22 14:06:34] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -61.814358 | E_var:     0.5866 | E_err:   0.011967
[2025-10-22 14:06:47] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -61.813576 | E_var:     0.5405 | E_err:   0.011487
[2025-10-22 14:07:00] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -61.825816 | E_var:     0.7050 | E_err:   0.013119
[2025-10-22 14:07:13] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -61.810301 | E_var:     0.5033 | E_err:   0.011085
[2025-10-22 14:07:26] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -61.834925 | E_var:     0.5651 | E_err:   0.011745
[2025-10-22 14:07:39] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -61.821754 | E_var:     0.5292 | E_err:   0.011366
[2025-10-22 14:07:53] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -61.808641 | E_var:     0.5874 | E_err:   0.011975
[2025-10-22 14:08:06] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -61.810901 | E_var:     0.6471 | E_err:   0.012569
[2025-10-22 14:08:19] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -61.793362 | E_var:     0.5897 | E_err:   0.011999
[2025-10-22 14:08:32] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -61.813219 | E_var:     0.5858 | E_err:   0.011959
[2025-10-22 14:08:45] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -61.816128 | E_var:     0.6535 | E_err:   0.012631
[2025-10-22 14:08:58] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -61.804891 | E_var:     0.5573 | E_err:   0.011664
[2025-10-22 14:09:12] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -61.795691 | E_var:     0.5998 | E_err:   0.012101
[2025-10-22 14:09:25] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -61.801790 | E_var:     0.5875 | E_err:   0.011976
[2025-10-22 14:09:38] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -61.799443 | E_var:     0.7535 | E_err:   0.013563
[2025-10-22 14:09:51] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -61.818002 | E_var:     0.7085 | E_err:   0.013152
[2025-10-22 14:10:04] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -61.825157 | E_var:     0.5745 | E_err:   0.011843
[2025-10-22 14:10:17] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -61.811355 | E_var:     0.5308 | E_err:   0.011383
[2025-10-22 14:10:30] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -61.817993 | E_var:     0.5157 | E_err:   0.011221
[2025-10-22 14:10:44] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -61.805308 | E_var:     0.6297 | E_err:   0.012399
[2025-10-22 14:10:57] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -61.804427 | E_var:     0.5895 | E_err:   0.011997
[2025-10-22 14:11:10] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -61.800208 | E_var:     0.6337 | E_err:   0.012439
[2025-10-22 14:11:23] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -61.798096 | E_var:     0.6461 | E_err:   0.012559
[2025-10-22 14:11:23] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-22 14:11:36] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -61.837379 | E_var:     0.8015 | E_err:   0.013988
[2025-10-22 14:11:49] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -61.831402 | E_var:     0.6798 | E_err:   0.012883
[2025-10-22 14:12:03] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -61.811661 | E_var:     0.6339 | E_err:   0.012440
[2025-10-22 14:12:16] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -61.828476 | E_var:     0.5684 | E_err:   0.011781
[2025-10-22 14:12:29] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -61.819651 | E_var:     0.8842 | E_err:   0.014693
[2025-10-22 14:12:42] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -61.803719 | E_var:     0.6654 | E_err:   0.012745
[2025-10-22 14:12:55] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -61.817705 | E_var:     0.5421 | E_err:   0.011504
[2025-10-22 14:13:08] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -61.825705 | E_var:     0.5460 | E_err:   0.011546
[2025-10-22 14:13:21] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -61.821277 | E_var:     0.7821 | E_err:   0.013818
[2025-10-22 14:13:35] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -61.827373 | E_var:     0.5361 | E_err:   0.011440
[2025-10-22 14:13:48] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -61.821315 | E_var:     0.5682 | E_err:   0.011778
[2025-10-22 14:14:01] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -61.806334 | E_var:     0.6178 | E_err:   0.012281
[2025-10-22 14:14:14] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -61.809661 | E_var:     0.7151 | E_err:   0.013213
[2025-10-22 14:14:27] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -61.805592 | E_var:     0.7273 | E_err:   0.013325
[2025-10-22 14:14:40] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -61.827954 | E_var:     0.5727 | E_err:   0.011825
[2025-10-22 14:14:53] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -61.830044 | E_var:     1.2089 | E_err:   0.017180
[2025-10-22 14:15:07] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -61.828129 | E_var:     0.5552 | E_err:   0.011643
[2025-10-22 14:15:20] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -61.827162 | E_var:     0.7795 | E_err:   0.013795
[2025-10-22 14:15:33] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -61.804851 | E_var:     0.6567 | E_err:   0.012662
[2025-10-22 14:15:46] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -61.814487 | E_var:     0.5722 | E_err:   0.011820
[2025-10-22 14:15:59] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -61.820597 | E_var:     0.5283 | E_err:   0.011357
[2025-10-22 14:16:12] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -61.828074 | E_var:     0.5174 | E_err:   0.011239
[2025-10-22 14:16:26] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -61.823599 | E_var:     0.5488 | E_err:   0.011575
[2025-10-22 14:16:39] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -61.820284 | E_var:     0.7264 | E_err:   0.013317
[2025-10-22 14:16:52] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -61.816618 | E_var:     0.5063 | E_err:   0.011118
[2025-10-22 14:17:05] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -61.825378 | E_var:     0.6076 | E_err:   0.012180
[2025-10-22 14:17:18] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -61.832463 | E_var:     0.7243 | E_err:   0.013298
[2025-10-22 14:17:31] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -61.820195 | E_var:     0.6252 | E_err:   0.012354
[2025-10-22 14:17:44] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -61.810904 | E_var:     0.5380 | E_err:   0.011461
[2025-10-22 14:17:58] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -61.805007 | E_var:     0.6118 | E_err:   0.012221
[2025-10-22 14:18:11] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -61.796066 | E_var:     0.6263 | E_err:   0.012365
[2025-10-22 14:18:24] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -61.819298 | E_var:     0.6360 | E_err:   0.012461
[2025-10-22 14:18:37] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -61.833279 | E_var:     0.7771 | E_err:   0.013774
[2025-10-22 14:18:50] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -61.797340 | E_var:     0.5964 | E_err:   0.012067
[2025-10-22 14:19:03] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -61.835872 | E_var:     0.6355 | E_err:   0.012456
[2025-10-22 14:19:16] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -61.831100 | E_var:     0.6113 | E_err:   0.012216
[2025-10-22 14:19:30] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -61.829858 | E_var:     0.5699 | E_err:   0.011795
[2025-10-22 14:19:43] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -61.835880 | E_var:     0.7837 | E_err:   0.013832
[2025-10-22 14:19:56] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -61.816024 | E_var:     0.6724 | E_err:   0.012812
[2025-10-22 14:20:09] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -61.833441 | E_var:     0.5730 | E_err:   0.011827
[2025-10-22 14:20:22] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -61.817057 | E_var:     0.7674 | E_err:   0.013688
[2025-10-22 14:20:35] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -61.816616 | E_var:     0.6112 | E_err:   0.012215
[2025-10-22 14:20:48] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -61.814338 | E_var:     0.5814 | E_err:   0.011914
[2025-10-22 14:21:02] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -61.811854 | E_var:     0.5407 | E_err:   0.011490
[2025-10-22 14:21:15] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -61.824286 | E_var:     0.5518 | E_err:   0.011607
[2025-10-22 14:21:28] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -61.802658 | E_var:     0.6810 | E_err:   0.012894
[2025-10-22 14:21:41] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -61.827222 | E_var:     0.6214 | E_err:   0.012317
[2025-10-22 14:21:54] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -61.817861 | E_var:     0.6011 | E_err:   0.012114
[2025-10-22 14:22:07] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -61.824442 | E_var:     0.5999 | E_err:   0.012103
[2025-10-22 14:22:20] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -61.804199 | E_var:     0.5542 | E_err:   0.011632
[2025-10-22 14:22:34] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -61.820878 | E_var:     0.5927 | E_err:   0.012029
[2025-10-22 14:22:47] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -61.821816 | E_var:     0.7124 | E_err:   0.013188
[2025-10-22 14:23:00] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -61.812162 | E_var:     0.7697 | E_err:   0.013708
[2025-10-22 14:23:13] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -61.816559 | E_var:     0.6532 | E_err:   0.012628
[2025-10-22 14:23:26] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -61.833111 | E_var:     0.7039 | E_err:   0.013110
[2025-10-22 14:23:39] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -61.815257 | E_var:     0.5916 | E_err:   0.012018
[2025-10-22 14:23:53] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -61.808074 | E_var:     0.5646 | E_err:   0.011741
[2025-10-22 14:24:06] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -61.833892 | E_var:     0.5309 | E_err:   0.011385
[2025-10-22 14:24:19] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -61.814501 | E_var:     0.5746 | E_err:   0.011844
[2025-10-22 14:24:32] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -61.802940 | E_var:     0.5811 | E_err:   0.011911
[2025-10-22 14:24:45] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -61.814424 | E_var:     0.5847 | E_err:   0.011948
[2025-10-22 14:24:58] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -61.812994 | E_var:     0.5272 | E_err:   0.011345
[2025-10-22 14:25:11] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -61.831780 | E_var:     0.5575 | E_err:   0.011666
[2025-10-22 14:25:25] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -61.824262 | E_var:     0.5806 | E_err:   0.011906
[2025-10-22 14:25:38] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -61.840438 | E_var:     0.7468 | E_err:   0.013503
[2025-10-22 14:25:51] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -61.817123 | E_var:     0.6632 | E_err:   0.012725
[2025-10-22 14:26:04] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -61.806585 | E_var:     0.9435 | E_err:   0.015178
[2025-10-22 14:26:17] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -61.814052 | E_var:     0.5060 | E_err:   0.011115
[2025-10-22 14:26:30] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -61.840663 | E_var:     0.6321 | E_err:   0.012422
[2025-10-22 14:26:43] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -61.800662 | E_var:     0.5370 | E_err:   0.011450
[2025-10-22 14:26:57] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -61.812692 | E_var:     0.5741 | E_err:   0.011839
[2025-10-22 14:27:10] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -61.825079 | E_var:     0.5737 | E_err:   0.011835
[2025-10-22 14:27:23] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -61.838763 | E_var:     0.5758 | E_err:   0.011856
[2025-10-22 14:27:36] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -61.813276 | E_var:     0.6443 | E_err:   0.012542
[2025-10-22 14:27:49] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -61.828200 | E_var:     0.9939 | E_err:   0.015577
[2025-10-22 14:28:02] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -61.819059 | E_var:     0.6742 | E_err:   0.012830
[2025-10-22 14:28:16] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -61.832709 | E_var:     0.6533 | E_err:   0.012629
[2025-10-22 14:28:29] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -61.802699 | E_var:     0.5662 | E_err:   0.011757
[2025-10-22 14:28:42] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -61.819329 | E_var:     0.5736 | E_err:   0.011833
[2025-10-22 14:28:55] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -61.794504 | E_var:     0.6300 | E_err:   0.012402
[2025-10-22 14:29:08] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -61.827558 | E_var:     0.6089 | E_err:   0.012192
[2025-10-22 14:29:21] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -61.801543 | E_var:     0.5782 | E_err:   0.011881
[2025-10-22 14:29:34] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -61.805036 | E_var:     0.6552 | E_err:   0.012648
[2025-10-22 14:29:48] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -61.822156 | E_var:     0.6277 | E_err:   0.012379
[2025-10-22 14:30:01] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -61.824258 | E_var:     0.5048 | E_err:   0.011102
[2025-10-22 14:30:14] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -61.811668 | E_var:     0.5804 | E_err:   0.011904
[2025-10-22 14:30:27] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -61.829173 | E_var:     0.4856 | E_err:   0.010889
[2025-10-22 14:30:40] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -61.830564 | E_var:     0.5243 | E_err:   0.011314
[2025-10-22 14:30:53] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -61.811450 | E_var:     0.5830 | E_err:   0.011930
[2025-10-22 14:31:06] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -61.813975 | E_var:     0.5179 | E_err:   0.011245
[2025-10-22 14:31:20] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -61.806950 | E_var:     0.6207 | E_err:   0.012310
[2025-10-22 14:31:33] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -61.819525 | E_var:     0.7036 | E_err:   0.013107
[2025-10-22 14:31:46] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -61.831257 | E_var:     0.5417 | E_err:   0.011500
[2025-10-22 14:31:59] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -61.834157 | E_var:     0.5244 | E_err:   0.011315
[2025-10-22 14:32:12] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -61.810469 | E_var:     0.5200 | E_err:   0.011268
[2025-10-22 14:32:25] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -61.834780 | E_var:     0.4769 | E_err:   0.010791
[2025-10-22 14:32:39] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -61.818830 | E_var:     0.5371 | E_err:   0.011451
[2025-10-22 14:32:52] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -61.806999 | E_var:     0.6410 | E_err:   0.012510
[2025-10-22 14:33:05] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -61.817381 | E_var:     0.5516 | E_err:   0.011604
[2025-10-22 14:33:18] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -61.821455 | E_var:     0.5827 | E_err:   0.011927
[2025-10-22 14:33:31] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -61.813871 | E_var:     0.5410 | E_err:   0.011493
[2025-10-22 14:33:44] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -61.822711 | E_var:     0.5432 | E_err:   0.011516
[2025-10-22 14:33:57] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -61.819106 | E_var:     0.5383 | E_err:   0.011464
[2025-10-22 14:34:11] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -61.810994 | E_var:     0.6086 | E_err:   0.012189
[2025-10-22 14:34:24] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -61.813449 | E_var:     0.4873 | E_err:   0.010907
[2025-10-22 14:34:24] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-22 14:34:37] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -61.823229 | E_var:     0.5883 | E_err:   0.011985
[2025-10-22 14:34:50] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -61.814656 | E_var:     0.6947 | E_err:   0.013023
[2025-10-22 14:35:03] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -61.818753 | E_var:     0.5971 | E_err:   0.012074
[2025-10-22 14:35:16] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -61.811911 | E_var:     0.5390 | E_err:   0.011471
[2025-10-22 14:35:30] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -61.815950 | E_var:     0.6488 | E_err:   0.012586
[2025-10-22 14:35:43] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -61.832180 | E_var:     0.5571 | E_err:   0.011663
[2025-10-22 14:35:56] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -61.807546 | E_var:     0.6061 | E_err:   0.012164
[2025-10-22 14:36:09] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -61.798505 | E_var:     0.6703 | E_err:   0.012792
[2025-10-22 14:36:22] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -61.816177 | E_var:     0.5222 | E_err:   0.011291
[2025-10-22 14:36:35] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -61.798001 | E_var:     0.5203 | E_err:   0.011270
[2025-10-22 14:36:48] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -61.856320 | E_var:     0.5467 | E_err:   0.011553
[2025-10-22 14:37:02] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -61.820411 | E_var:     0.5726 | E_err:   0.011824
[2025-10-22 14:37:15] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -61.811393 | E_var:     0.5422 | E_err:   0.011505
[2025-10-22 14:37:28] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -61.807668 | E_var:     0.7041 | E_err:   0.013111
[2025-10-22 14:37:41] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -61.815832 | E_var:     0.5214 | E_err:   0.011282
[2025-10-22 14:37:54] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -61.786482 | E_var:     0.6279 | E_err:   0.012381
[2025-10-22 14:38:07] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -61.808991 | E_var:     0.5895 | E_err:   0.011996
[2025-10-22 14:38:20] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -61.833022 | E_var:     0.8379 | E_err:   0.014302
[2025-10-22 14:38:34] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -61.790662 | E_var:     0.7426 | E_err:   0.013464
[2025-10-22 14:38:47] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -61.818002 | E_var:     0.5852 | E_err:   0.011953
[2025-10-22 14:39:00] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -61.800883 | E_var:     0.5264 | E_err:   0.011336
[2025-10-22 14:39:13] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -61.819186 | E_var:     0.5345 | E_err:   0.011424
[2025-10-22 14:39:26] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -61.829895 | E_var:     0.6627 | E_err:   0.012720
[2025-10-22 14:39:39] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -61.799172 | E_var:     0.5145 | E_err:   0.011207
[2025-10-22 14:39:52] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -61.804244 | E_var:     0.5682 | E_err:   0.011778
[2025-10-22 14:40:06] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -61.797664 | E_var:     0.6056 | E_err:   0.012159
[2025-10-22 14:40:19] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -61.808646 | E_var:     0.4887 | E_err:   0.010923
[2025-10-22 14:40:32] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -61.819734 | E_var:     0.6129 | E_err:   0.012233
[2025-10-22 14:40:45] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -61.820642 | E_var:     0.5753 | E_err:   0.011852
[2025-10-22 14:40:58] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -61.797724 | E_var:     0.6964 | E_err:   0.013039
[2025-10-22 14:41:11] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -61.831682 | E_var:     0.6261 | E_err:   0.012363
[2025-10-22 14:41:25] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -61.813602 | E_var:     0.6084 | E_err:   0.012188
[2025-10-22 14:41:38] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -61.831586 | E_var:     0.5775 | E_err:   0.011874
[2025-10-22 14:41:51] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -61.827056 | E_var:     0.6651 | E_err:   0.012743
[2025-10-22 14:42:04] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -61.815123 | E_var:     0.5460 | E_err:   0.011545
[2025-10-22 14:42:17] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -61.820389 | E_var:     0.6805 | E_err:   0.012889
[2025-10-22 14:42:30] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -61.817288 | E_var:     0.6559 | E_err:   0.012654
[2025-10-22 14:42:43] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -61.807162 | E_var:     0.5201 | E_err:   0.011269
[2025-10-22 14:42:57] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -61.813656 | E_var:     0.5845 | E_err:   0.011946
[2025-10-22 14:43:10] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -61.813091 | E_var:     0.5381 | E_err:   0.011462
[2025-10-22 14:43:23] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -61.803255 | E_var:     0.5719 | E_err:   0.011817
[2025-10-22 14:43:36] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -61.808601 | E_var:     0.7500 | E_err:   0.013532
[2025-10-22 14:43:49] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -61.838463 | E_var:     0.6047 | E_err:   0.012150
[2025-10-22 14:44:02] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -61.811131 | E_var:     0.5707 | E_err:   0.011804
[2025-10-22 14:44:15] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -61.807726 | E_var:     0.6903 | E_err:   0.012982
[2025-10-22 14:44:29] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -61.805858 | E_var:     0.5681 | E_err:   0.011777
[2025-10-22 14:44:42] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -61.829228 | E_var:     0.7558 | E_err:   0.013584
[2025-10-22 14:44:55] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -61.825154 | E_var:     0.5682 | E_err:   0.011778
[2025-10-22 14:45:08] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -61.812810 | E_var:     0.8359 | E_err:   0.014286
[2025-10-22 14:45:21] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -61.819040 | E_var:     0.5777 | E_err:   0.011876
[2025-10-22 14:45:34] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -61.807681 | E_var:     0.5707 | E_err:   0.011804
[2025-10-22 14:45:47] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -61.815697 | E_var:     0.5558 | E_err:   0.011649
[2025-10-22 14:46:01] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -61.820076 | E_var:     0.5958 | E_err:   0.012061
[2025-10-22 14:46:14] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -61.814130 | E_var:     0.5561 | E_err:   0.011651
[2025-10-22 14:46:27] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -61.823836 | E_var:     0.5208 | E_err:   0.011276
[2025-10-22 14:46:40] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -61.814386 | E_var:     0.8454 | E_err:   0.014366
[2025-10-22 14:46:53] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -61.823958 | E_var:     0.6637 | E_err:   0.012730
[2025-10-22 14:47:06] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -61.813786 | E_var:     0.4596 | E_err:   0.010593
[2025-10-22 14:47:20] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -61.819282 | E_var:     0.5673 | E_err:   0.011769
[2025-10-22 14:47:33] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -61.829908 | E_var:     0.6968 | E_err:   0.013043
[2025-10-22 14:47:46] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -61.800155 | E_var:     0.4984 | E_err:   0.011031
[2025-10-22 14:47:59] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -61.827441 | E_var:     0.5148 | E_err:   0.011210
[2025-10-22 14:48:12] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -61.820371 | E_var:     0.4747 | E_err:   0.010765
[2025-10-22 14:48:25] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -61.829122 | E_var:     0.5652 | E_err:   0.011747
[2025-10-22 14:48:38] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -61.814965 | E_var:     0.6625 | E_err:   0.012718
[2025-10-22 14:48:52] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -61.814054 | E_var:     0.5444 | E_err:   0.011528
[2025-10-22 14:49:05] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -61.796810 | E_var:     0.5374 | E_err:   0.011454
[2025-10-22 14:49:18] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -61.820926 | E_var:     0.5828 | E_err:   0.011928
[2025-10-22 14:49:31] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -61.820909 | E_var:     0.5090 | E_err:   0.011148
[2025-10-22 14:49:44] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -61.823623 | E_var:     0.6163 | E_err:   0.012266
[2025-10-22 14:49:57] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -61.782703 | E_var:     1.2698 | E_err:   0.017607
[2025-10-22 14:50:10] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -61.809030 | E_var:     0.5558 | E_err:   0.011649
[2025-10-22 14:50:24] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -61.790194 | E_var:     0.5487 | E_err:   0.011574
[2025-10-22 14:50:37] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -61.816164 | E_var:     0.5040 | E_err:   0.011092
[2025-10-22 14:50:50] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -61.826344 | E_var:     0.5792 | E_err:   0.011892
[2025-10-22 14:51:03] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -61.821393 | E_var:     0.5075 | E_err:   0.011131
[2025-10-22 14:51:16] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -61.839262 | E_var:     0.5007 | E_err:   0.011057
[2025-10-22 14:51:29] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -61.817586 | E_var:     0.5729 | E_err:   0.011826
[2025-10-22 14:51:42] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -61.831422 | E_var:     0.6435 | E_err:   0.012534
[2025-10-22 14:51:56] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -61.820171 | E_var:     1.1224 | E_err:   0.016554
[2025-10-22 14:52:09] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -61.823416 | E_var:     0.5062 | E_err:   0.011117
[2025-10-22 14:52:22] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -61.818766 | E_var:     0.5435 | E_err:   0.011519
[2025-10-22 14:52:35] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -61.838901 | E_var:     0.6653 | E_err:   0.012744
[2025-10-22 14:52:48] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -61.831827 | E_var:     0.5616 | E_err:   0.011710
[2025-10-22 14:53:01] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -61.835916 | E_var:     0.5345 | E_err:   0.011423
[2025-10-22 14:53:15] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -61.803665 | E_var:     0.6652 | E_err:   0.012744
[2025-10-22 14:53:28] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -61.813803 | E_var:     0.6346 | E_err:   0.012447
[2025-10-22 14:53:41] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -61.816714 | E_var:     0.5827 | E_err:   0.011928
[2025-10-22 14:53:54] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -61.840282 | E_var:     0.5402 | E_err:   0.011484
[2025-10-22 14:54:07] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -61.801555 | E_var:     0.5528 | E_err:   0.011617
[2025-10-22 14:54:20] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -61.830864 | E_var:     2.2892 | E_err:   0.023641
[2025-10-22 14:54:33] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -61.827138 | E_var:     0.5275 | E_err:   0.011349
[2025-10-22 14:54:47] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -61.833288 | E_var:     0.6363 | E_err:   0.012464
[2025-10-22 14:55:00] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -61.806380 | E_var:     0.5780 | E_err:   0.011879
[2025-10-22 14:55:13] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -61.816641 | E_var:     0.6068 | E_err:   0.012172
[2025-10-22 14:55:26] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -61.823189 | E_var:     0.6286 | E_err:   0.012388
[2025-10-22 14:55:39] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -61.828499 | E_var:     0.5687 | E_err:   0.011783
[2025-10-22 14:55:52] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -61.826118 | E_var:     0.6123 | E_err:   0.012226
[2025-10-22 14:56:05] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -61.827269 | E_var:     0.5813 | E_err:   0.011913
[2025-10-22 14:56:19] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -61.821248 | E_var:     0.5392 | E_err:   0.011473
[2025-10-22 14:56:32] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -61.816538 | E_var:     0.5099 | E_err:   0.011158
[2025-10-22 14:56:45] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -61.812240 | E_var:     0.5631 | E_err:   0.011725
[2025-10-22 14:56:58] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -61.822997 | E_var:     0.5594 | E_err:   0.011686
[2025-10-22 14:57:11] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -61.828920 | E_var:     0.4855 | E_err:   0.010887
[2025-10-22 14:57:24] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -61.826620 | E_var:     0.5928 | E_err:   0.012030
[2025-10-22 14:57:24] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-22 14:57:38] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -61.813377 | E_var:     0.6046 | E_err:   0.012150
[2025-10-22 14:57:51] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -61.809009 | E_var:     0.6175 | E_err:   0.012278
[2025-10-22 14:58:04] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -61.828635 | E_var:     0.5198 | E_err:   0.011266
[2025-10-22 14:58:17] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -61.823378 | E_var:     0.7348 | E_err:   0.013394
[2025-10-22 14:58:30] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -61.823250 | E_var:     0.5254 | E_err:   0.011326
[2025-10-22 14:58:43] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -61.819671 | E_var:     0.5522 | E_err:   0.011611
[2025-10-22 14:58:56] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -61.802373 | E_var:     0.7212 | E_err:   0.013269
[2025-10-22 14:59:10] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -61.827413 | E_var:     0.4803 | E_err:   0.010829
[2025-10-22 14:59:23] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -61.798394 | E_var:     0.5470 | E_err:   0.011556
[2025-10-22 14:59:36] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -61.820312 | E_var:     0.5219 | E_err:   0.011288
[2025-10-22 14:59:49] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -61.821729 | E_var:     0.5715 | E_err:   0.011812
[2025-10-22 15:00:02] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -61.835676 | E_var:     0.5577 | E_err:   0.011668
[2025-10-22 15:00:15] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -61.827894 | E_var:     0.6413 | E_err:   0.012513
[2025-10-22 15:00:28] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -61.836669 | E_var:     0.5438 | E_err:   0.011523
[2025-10-22 15:00:42] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -61.815277 | E_var:     0.4726 | E_err:   0.010741
[2025-10-22 15:00:55] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -61.805345 | E_var:     0.6509 | E_err:   0.012606
[2025-10-22 15:01:08] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -61.838727 | E_var:     0.5355 | E_err:   0.011434
[2025-10-22 15:01:21] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -61.799883 | E_var:     0.4898 | E_err:   0.010935
[2025-10-22 15:01:34] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -61.825623 | E_var:     0.5472 | E_err:   0.011558
[2025-10-22 15:01:47] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -61.810606 | E_var:     0.6324 | E_err:   0.012426
[2025-10-22 15:02:01] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -61.809747 | E_var:     0.6007 | E_err:   0.012110
[2025-10-22 15:02:14] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -61.798842 | E_var:     0.5075 | E_err:   0.011131
[2025-10-22 15:02:27] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -61.826382 | E_var:     0.5620 | E_err:   0.011714
[2025-10-22 15:02:40] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -61.852457 | E_var:     1.9438 | E_err:   0.021785
[2025-10-22 15:02:53] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -61.822718 | E_var:     0.5132 | E_err:   0.011193
[2025-10-22 15:03:06] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -61.799685 | E_var:     0.6321 | E_err:   0.012423
[2025-10-22 15:03:19] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -61.809120 | E_var:     0.5857 | E_err:   0.011958
[2025-10-22 15:03:33] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -61.813188 | E_var:     0.5930 | E_err:   0.012032
[2025-10-22 15:03:46] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -61.843061 | E_var:     0.6701 | E_err:   0.012791
[2025-10-22 15:03:59] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -61.830901 | E_var:     0.7012 | E_err:   0.013084
[2025-10-22 15:03:59] 🔄 RESTART #2 | Period: 600
[2025-10-22 15:04:12] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -61.812217 | E_var:     0.6111 | E_err:   0.012214
[2025-10-22 15:04:25] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -61.812032 | E_var:     0.6330 | E_err:   0.012432
[2025-10-22 15:04:38] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -61.824666 | E_var:     0.4551 | E_err:   0.010541
[2025-10-22 15:04:52] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -61.810550 | E_var:     0.5925 | E_err:   0.012027
[2025-10-22 15:05:05] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -61.826209 | E_var:     0.6036 | E_err:   0.012139
[2025-10-22 15:05:18] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -61.812577 | E_var:     0.5757 | E_err:   0.011856
[2025-10-22 15:05:31] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -61.833991 | E_var:     0.5866 | E_err:   0.011967
[2025-10-22 15:05:44] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -61.805324 | E_var:     0.6613 | E_err:   0.012706
[2025-10-22 15:05:57] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -61.826401 | E_var:     0.5460 | E_err:   0.011546
[2025-10-22 15:06:10] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -61.821945 | E_var:     0.5539 | E_err:   0.011629
[2025-10-22 15:06:24] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -61.816744 | E_var:     0.5764 | E_err:   0.011863
[2025-10-22 15:06:37] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -61.826896 | E_var:     0.5584 | E_err:   0.011676
[2025-10-22 15:06:50] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -61.822818 | E_var:     0.6568 | E_err:   0.012663
[2025-10-22 15:07:03] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -61.821868 | E_var:     0.6654 | E_err:   0.012746
[2025-10-22 15:07:16] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -61.829875 | E_var:     0.6216 | E_err:   0.012319
[2025-10-22 15:07:29] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -61.820269 | E_var:     0.7399 | E_err:   0.013441
[2025-10-22 15:07:42] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -61.813214 | E_var:     0.5266 | E_err:   0.011339
[2025-10-22 15:07:56] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -61.829144 | E_var:     0.5570 | E_err:   0.011662
[2025-10-22 15:08:09] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -61.823337 | E_var:     0.5519 | E_err:   0.011608
[2025-10-22 15:08:22] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -61.827487 | E_var:     0.6256 | E_err:   0.012359
[2025-10-22 15:08:35] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -61.836615 | E_var:     0.4970 | E_err:   0.011015
[2025-10-22 15:08:48] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -61.822224 | E_var:     0.5021 | E_err:   0.011071
[2025-10-22 15:09:01] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -61.826075 | E_var:     0.8995 | E_err:   0.014819
[2025-10-22 15:09:14] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -61.820189 | E_var:     0.7626 | E_err:   0.013644
[2025-10-22 15:09:28] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -61.808541 | E_var:     0.6073 | E_err:   0.012176
[2025-10-22 15:09:41] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -61.810905 | E_var:     0.6858 | E_err:   0.012940
[2025-10-22 15:09:54] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -61.796743 | E_var:     0.7177 | E_err:   0.013237
[2025-10-22 15:10:07] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -61.810443 | E_var:     0.5505 | E_err:   0.011594
[2025-10-22 15:10:20] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -61.813223 | E_var:     0.6241 | E_err:   0.012344
[2025-10-22 15:10:33] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -61.828679 | E_var:     0.7517 | E_err:   0.013547
[2025-10-22 15:10:47] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -61.824215 | E_var:     0.5746 | E_err:   0.011844
[2025-10-22 15:11:00] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -61.821131 | E_var:     0.5306 | E_err:   0.011381
[2025-10-22 15:11:13] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -61.824897 | E_var:     0.5930 | E_err:   0.012032
[2025-10-22 15:11:26] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -61.844640 | E_var:     0.4891 | E_err:   0.010928
[2025-10-22 15:11:39] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -61.808825 | E_var:     0.5411 | E_err:   0.011494
[2025-10-22 15:11:52] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -61.824404 | E_var:     0.5581 | E_err:   0.011672
[2025-10-22 15:12:05] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -61.837676 | E_var:     0.5898 | E_err:   0.012000
[2025-10-22 15:12:19] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -61.815057 | E_var:     0.5881 | E_err:   0.011982
[2025-10-22 15:12:32] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -61.812959 | E_var:     0.6195 | E_err:   0.012298
[2025-10-22 15:12:45] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -61.839342 | E_var:     0.7030 | E_err:   0.013101
[2025-10-22 15:12:58] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -61.817668 | E_var:     0.5245 | E_err:   0.011316
[2025-10-22 15:13:11] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -61.805096 | E_var:     0.5983 | E_err:   0.012086
[2025-10-22 15:13:24] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -61.836133 | E_var:     0.5239 | E_err:   0.011310
[2025-10-22 15:13:37] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -61.823396 | E_var:     0.5648 | E_err:   0.011743
[2025-10-22 15:13:51] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -61.796700 | E_var:     0.9557 | E_err:   0.015275
[2025-10-22 15:14:04] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -61.807193 | E_var:     0.7420 | E_err:   0.013460
[2025-10-22 15:14:17] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -61.843050 | E_var:     0.5787 | E_err:   0.011886
[2025-10-22 15:14:30] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -61.837520 | E_var:     0.6115 | E_err:   0.012219
[2025-10-22 15:14:43] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -61.820261 | E_var:     0.4831 | E_err:   0.010860
[2025-10-22 15:14:56] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -61.798256 | E_var:     0.5653 | E_err:   0.011748
[2025-10-22 15:15:10] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -61.815409 | E_var:     0.6362 | E_err:   0.012463
[2025-10-22 15:15:23] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -61.830527 | E_var:     0.5041 | E_err:   0.011094
[2025-10-22 15:15:36] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -61.827244 | E_var:     0.5801 | E_err:   0.011901
[2025-10-22 15:15:49] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -61.806072 | E_var:     0.5589 | E_err:   0.011682
[2025-10-22 15:16:02] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -61.790166 | E_var:     0.7200 | E_err:   0.013258
[2025-10-22 15:16:15] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -61.823189 | E_var:     0.6791 | E_err:   0.012876
[2025-10-22 15:16:28] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -61.825130 | E_var:     0.5929 | E_err:   0.012031
[2025-10-22 15:16:42] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -61.817354 | E_var:     0.5455 | E_err:   0.011540
[2025-10-22 15:16:55] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -61.822924 | E_var:     0.5802 | E_err:   0.011902
[2025-10-22 15:17:08] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -61.806565 | E_var:     0.5846 | E_err:   0.011947
[2025-10-22 15:17:21] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -61.814876 | E_var:     0.5042 | E_err:   0.011094
[2025-10-22 15:17:34] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -61.809577 | E_var:     0.5824 | E_err:   0.011924
[2025-10-22 15:17:47] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -61.825862 | E_var:     0.4767 | E_err:   0.010788
[2025-10-22 15:18:00] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -61.817070 | E_var:     0.6552 | E_err:   0.012648
[2025-10-22 15:18:14] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -61.825704 | E_var:     0.6252 | E_err:   0.012355
[2025-10-22 15:18:27] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -61.810465 | E_var:     0.6633 | E_err:   0.012726
[2025-10-22 15:18:40] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -61.839244 | E_var:     0.6805 | E_err:   0.012889
[2025-10-22 15:18:53] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -61.811211 | E_var:     0.6080 | E_err:   0.012184
[2025-10-22 15:19:06] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -61.788057 | E_var:     0.5728 | E_err:   0.011825
[2025-10-22 15:19:19] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -61.810797 | E_var:     0.5595 | E_err:   0.011687
[2025-10-22 15:19:32] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -61.823191 | E_var:     0.5504 | E_err:   0.011592
[2025-10-22 15:19:46] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -61.802304 | E_var:     0.5504 | E_err:   0.011592
[2025-10-22 15:19:59] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -61.834365 | E_var:     0.6506 | E_err:   0.012603
[2025-10-22 15:20:12] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -61.823682 | E_var:     0.6132 | E_err:   0.012236
[2025-10-22 15:20:25] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -61.810777 | E_var:     0.6033 | E_err:   0.012136
[2025-10-22 15:20:25] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-22 15:20:38] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -61.829534 | E_var:     0.7549 | E_err:   0.013575
[2025-10-22 15:20:51] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -61.813575 | E_var:     0.5957 | E_err:   0.012059
[2025-10-22 15:21:05] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -61.819624 | E_var:     0.5029 | E_err:   0.011080
[2025-10-22 15:21:18] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -61.807859 | E_var:     0.5504 | E_err:   0.011592
[2025-10-22 15:21:31] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -61.813894 | E_var:     0.5221 | E_err:   0.011290
[2025-10-22 15:21:44] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -61.822391 | E_var:     0.5373 | E_err:   0.011454
[2025-10-22 15:21:57] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -61.821987 | E_var:     0.6059 | E_err:   0.012162
[2025-10-22 15:22:10] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -61.824385 | E_var:     0.5626 | E_err:   0.011720
[2025-10-22 15:22:23] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -61.806461 | E_var:     0.6780 | E_err:   0.012866
[2025-10-22 15:22:37] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -61.805457 | E_var:     0.6150 | E_err:   0.012254
[2025-10-22 15:22:50] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -61.828070 | E_var:     0.5147 | E_err:   0.011210
[2025-10-22 15:23:03] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -61.817907 | E_var:     0.5593 | E_err:   0.011686
[2025-10-22 15:23:16] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -61.807725 | E_var:     0.6019 | E_err:   0.012122
[2025-10-22 15:23:29] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -61.827277 | E_var:     0.4773 | E_err:   0.010794
[2025-10-22 15:23:42] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -61.832063 | E_var:     0.6160 | E_err:   0.012263
[2025-10-22 15:23:55] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -61.831048 | E_var:     0.5027 | E_err:   0.011078
[2025-10-22 15:24:09] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -61.821852 | E_var:     0.5636 | E_err:   0.011730
[2025-10-22 15:24:22] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -61.805733 | E_var:     0.5809 | E_err:   0.011908
[2025-10-22 15:24:35] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -61.824957 | E_var:     0.5440 | E_err:   0.011525
[2025-10-22 15:24:48] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -61.817640 | E_var:     0.5874 | E_err:   0.011975
[2025-10-22 15:25:01] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -61.823106 | E_var:     0.4985 | E_err:   0.011032
[2025-10-22 15:25:14] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -61.812057 | E_var:     0.7271 | E_err:   0.013323
[2025-10-22 15:25:27] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -61.818187 | E_var:     0.5335 | E_err:   0.011413
[2025-10-22 15:25:41] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -61.806336 | E_var:     0.5304 | E_err:   0.011379
[2025-10-22 15:25:54] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -61.826653 | E_var:     0.9689 | E_err:   0.015380
[2025-10-22 15:26:07] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -61.814300 | E_var:     0.5509 | E_err:   0.011597
[2025-10-22 15:26:20] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -61.801283 | E_var:     0.5561 | E_err:   0.011652
[2025-10-22 15:26:33] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -61.815351 | E_var:     0.5600 | E_err:   0.011693
[2025-10-22 15:26:46] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -61.824642 | E_var:     0.5393 | E_err:   0.011475
[2025-10-22 15:27:00] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -61.811754 | E_var:     0.6761 | E_err:   0.012848
[2025-10-22 15:27:13] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -61.812889 | E_var:     0.5248 | E_err:   0.011319
[2025-10-22 15:27:26] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -61.815561 | E_var:     0.6167 | E_err:   0.012270
[2025-10-22 15:27:39] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -61.799363 | E_var:     0.6395 | E_err:   0.012495
[2025-10-22 15:27:52] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -61.816586 | E_var:     0.6831 | E_err:   0.012914
[2025-10-22 15:28:05] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -61.800169 | E_var:     0.4967 | E_err:   0.011012
[2025-10-22 15:28:18] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -61.820550 | E_var:     0.5423 | E_err:   0.011507
[2025-10-22 15:28:32] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -61.800823 | E_var:     0.5511 | E_err:   0.011599
[2025-10-22 15:28:45] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -61.828785 | E_var:     0.5326 | E_err:   0.011404
[2025-10-22 15:28:58] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -61.820741 | E_var:     0.5125 | E_err:   0.011186
[2025-10-22 15:29:11] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -61.821876 | E_var:     0.5231 | E_err:   0.011300
[2025-10-22 15:29:24] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -61.821942 | E_var:     0.5817 | E_err:   0.011917
[2025-10-22 15:29:37] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -61.826269 | E_var:     0.4501 | E_err:   0.010483
[2025-10-22 15:29:50] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -61.806769 | E_var:     0.5335 | E_err:   0.011413
[2025-10-22 15:30:04] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -61.835347 | E_var:     0.6412 | E_err:   0.012511
[2025-10-22 15:30:17] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -61.791541 | E_var:     0.6477 | E_err:   0.012575
[2025-10-22 15:30:30] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -61.817976 | E_var:     0.5686 | E_err:   0.011782
[2025-10-22 15:30:43] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -61.838619 | E_var:     0.5334 | E_err:   0.011412
[2025-10-22 15:30:56] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -61.810393 | E_var:     0.5488 | E_err:   0.011576
[2025-10-22 15:31:09] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -61.810306 | E_var:     0.6023 | E_err:   0.012126
[2025-10-22 15:31:23] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -61.823586 | E_var:     0.5304 | E_err:   0.011380
[2025-10-22 15:31:36] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -61.822973 | E_var:     0.5662 | E_err:   0.011757
[2025-10-22 15:31:49] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -61.807541 | E_var:     0.6510 | E_err:   0.012607
[2025-10-22 15:32:02] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -61.829071 | E_var:     0.5556 | E_err:   0.011647
[2025-10-22 15:32:15] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -61.821293 | E_var:     0.5659 | E_err:   0.011754
[2025-10-22 15:32:28] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -61.814917 | E_var:     0.4904 | E_err:   0.010942
[2025-10-22 15:32:41] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -61.830361 | E_var:     0.5787 | E_err:   0.011887
[2025-10-22 15:32:55] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -61.802251 | E_var:     0.4999 | E_err:   0.011048
[2025-10-22 15:33:08] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -61.806328 | E_var:     0.7939 | E_err:   0.013922
[2025-10-22 15:33:21] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -61.826990 | E_var:     0.6294 | E_err:   0.012396
[2025-10-22 15:33:34] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -61.797479 | E_var:     0.4938 | E_err:   0.010980
[2025-10-22 15:33:47] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -61.818421 | E_var:     0.6524 | E_err:   0.012621
[2025-10-22 15:34:00] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -61.812033 | E_var:     0.6137 | E_err:   0.012240
[2025-10-22 15:34:14] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -61.808451 | E_var:     0.5402 | E_err:   0.011484
[2025-10-22 15:34:27] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -61.800976 | E_var:     0.5216 | E_err:   0.011285
[2025-10-22 15:34:40] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -61.807097 | E_var:     0.7016 | E_err:   0.013088
[2025-10-22 15:34:53] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -61.817941 | E_var:     0.5038 | E_err:   0.011091
[2025-10-22 15:35:06] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -61.828149 | E_var:     0.5940 | E_err:   0.012042
[2025-10-22 15:35:19] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -61.836326 | E_var:     0.5429 | E_err:   0.011513
[2025-10-22 15:35:32] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -61.828999 | E_var:     0.5032 | E_err:   0.011083
[2025-10-22 15:35:46] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -61.825096 | E_var:     0.5153 | E_err:   0.011216
[2025-10-22 15:35:59] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -61.803046 | E_var:     0.5134 | E_err:   0.011195
[2025-10-22 15:36:12] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -61.824136 | E_var:     0.5208 | E_err:   0.011277
[2025-10-22 15:36:25] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -61.812222 | E_var:     0.6055 | E_err:   0.012158
[2025-10-22 15:36:38] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -61.816199 | E_var:     0.8102 | E_err:   0.014065
[2025-10-22 15:36:51] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -61.835770 | E_var:     0.5156 | E_err:   0.011219
[2025-10-22 15:37:04] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -61.821781 | E_var:     0.5822 | E_err:   0.011922
[2025-10-22 15:37:18] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -61.829960 | E_var:     0.5707 | E_err:   0.011804
[2025-10-22 15:37:31] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -61.797191 | E_var:     0.7316 | E_err:   0.013365
[2025-10-22 15:37:44] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -61.819288 | E_var:     0.5196 | E_err:   0.011263
[2025-10-22 15:37:57] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -61.808504 | E_var:     0.5711 | E_err:   0.011808
[2025-10-22 15:38:10] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -61.820598 | E_var:     0.6160 | E_err:   0.012263
[2025-10-22 15:38:23] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -61.838779 | E_var:     0.4909 | E_err:   0.010948
[2025-10-22 15:38:36] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -61.841565 | E_var:     0.5468 | E_err:   0.011554
[2025-10-22 15:38:50] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -61.815148 | E_var:     0.5453 | E_err:   0.011538
[2025-10-22 15:39:03] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -61.821223 | E_var:     0.5546 | E_err:   0.011636
[2025-10-22 15:39:16] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -61.813350 | E_var:     0.6790 | E_err:   0.012875
[2025-10-22 15:39:29] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -61.820882 | E_var:     0.6789 | E_err:   0.012874
[2025-10-22 15:39:42] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -61.821176 | E_var:     0.5650 | E_err:   0.011745
[2025-10-22 15:39:55] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -61.815008 | E_var:     0.5658 | E_err:   0.011753
[2025-10-22 15:40:09] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -61.836334 | E_var:     0.4858 | E_err:   0.010891
[2025-10-22 15:40:22] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -61.823480 | E_var:     0.5635 | E_err:   0.011729
[2025-10-22 15:40:35] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -61.836932 | E_var:     0.4739 | E_err:   0.010756
[2025-10-22 15:40:48] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -61.839190 | E_var:     0.5670 | E_err:   0.011766
[2025-10-22 15:41:01] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -61.805328 | E_var:     0.5191 | E_err:   0.011258
[2025-10-22 15:41:14] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -61.830305 | E_var:     0.5634 | E_err:   0.011728
[2025-10-22 15:41:27] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -61.815211 | E_var:     0.7763 | E_err:   0.013767
[2025-10-22 15:41:41] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -61.819506 | E_var:     0.5219 | E_err:   0.011288
[2025-10-22 15:41:54] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -61.825706 | E_var:     0.5304 | E_err:   0.011379
[2025-10-22 15:42:07] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -61.832972 | E_var:     0.4972 | E_err:   0.011018
[2025-10-22 15:42:20] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -61.826357 | E_var:     0.6681 | E_err:   0.012772
[2025-10-22 15:42:33] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -61.822491 | E_var:     0.6104 | E_err:   0.012208
[2025-10-22 15:42:46] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -61.829470 | E_var:     0.5275 | E_err:   0.011348
[2025-10-22 15:43:00] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -61.819386 | E_var:     0.5571 | E_err:   0.011662
[2025-10-22 15:43:13] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -61.823922 | E_var:     0.6271 | E_err:   0.012374
[2025-10-22 15:43:26] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -61.833436 | E_var:     0.4757 | E_err:   0.010777
[2025-10-22 15:43:26] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-22 15:43:39] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -61.812286 | E_var:     0.5727 | E_err:   0.011825
[2025-10-22 15:43:52] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -61.812444 | E_var:     0.4992 | E_err:   0.011040
[2025-10-22 15:44:05] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -61.830611 | E_var:     0.5374 | E_err:   0.011455
[2025-10-22 15:44:18] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -61.825116 | E_var:     0.5456 | E_err:   0.011542
[2025-10-22 15:44:32] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -61.836469 | E_var:     0.5838 | E_err:   0.011939
[2025-10-22 15:44:45] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -61.838477 | E_var:     0.5621 | E_err:   0.011715
[2025-10-22 15:44:58] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -61.806981 | E_var:     0.6082 | E_err:   0.012185
[2025-10-22 15:45:11] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -61.819849 | E_var:     0.5549 | E_err:   0.011640
[2025-10-22 15:45:24] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -61.825092 | E_var:     0.5628 | E_err:   0.011722
[2025-10-22 15:45:37] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -61.818030 | E_var:     0.4985 | E_err:   0.011032
[2025-10-22 15:45:51] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -61.817315 | E_var:     0.5454 | E_err:   0.011539
[2025-10-22 15:46:04] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -61.820251 | E_var:     0.7560 | E_err:   0.013585
[2025-10-22 15:46:17] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -61.821637 | E_var:     0.5105 | E_err:   0.011164
[2025-10-22 15:46:30] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -61.812681 | E_var:     0.6429 | E_err:   0.012528
[2025-10-22 15:46:43] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -61.824065 | E_var:     0.4660 | E_err:   0.010666
[2025-10-22 15:46:56] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -61.821546 | E_var:     0.5565 | E_err:   0.011656
[2025-10-22 15:47:09] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -61.829282 | E_var:     0.4963 | E_err:   0.011007
[2025-10-22 15:47:23] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -61.805465 | E_var:     0.7609 | E_err:   0.013629
[2025-10-22 15:47:36] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -61.814724 | E_var:     0.5450 | E_err:   0.011534
[2025-10-22 15:47:49] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -61.828547 | E_var:     0.6048 | E_err:   0.012151
[2025-10-22 15:48:02] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -61.817857 | E_var:     0.6007 | E_err:   0.012110
[2025-10-22 15:48:15] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -61.809051 | E_var:     0.6415 | E_err:   0.012515
[2025-10-22 15:48:28] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -61.825236 | E_var:     0.6604 | E_err:   0.012698
[2025-10-22 15:48:41] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -61.817832 | E_var:     0.5544 | E_err:   0.011634
[2025-10-22 15:48:55] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -61.802947 | E_var:     0.6259 | E_err:   0.012362
[2025-10-22 15:49:08] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -61.815892 | E_var:     0.6328 | E_err:   0.012429
[2025-10-22 15:49:21] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -61.809090 | E_var:     0.6610 | E_err:   0.012704
[2025-10-22 15:49:34] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -61.810300 | E_var:     0.5286 | E_err:   0.011360
[2025-10-22 15:49:47] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -61.822643 | E_var:     0.5189 | E_err:   0.011256
[2025-10-22 15:50:00] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -61.835158 | E_var:     0.5399 | E_err:   0.011481
[2025-10-22 15:50:13] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -61.830548 | E_var:     0.4685 | E_err:   0.010694
[2025-10-22 15:50:27] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -61.820676 | E_var:     0.4988 | E_err:   0.011035
[2025-10-22 15:50:40] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -61.838576 | E_var:     0.5103 | E_err:   0.011162
[2025-10-22 15:50:53] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -61.805191 | E_var:     0.5780 | E_err:   0.011879
[2025-10-22 15:51:06] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -61.826012 | E_var:     0.5533 | E_err:   0.011623
[2025-10-22 15:51:19] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -61.830015 | E_var:     0.5989 | E_err:   0.012092
[2025-10-22 15:51:32] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -61.814894 | E_var:     0.5762 | E_err:   0.011861
[2025-10-22 15:51:46] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -61.826234 | E_var:     0.5693 | E_err:   0.011789
[2025-10-22 15:51:59] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -61.824593 | E_var:     0.5151 | E_err:   0.011214
[2025-10-22 15:52:12] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -61.815468 | E_var:     0.5969 | E_err:   0.012072
[2025-10-22 15:52:25] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -61.824610 | E_var:     0.5093 | E_err:   0.011151
[2025-10-22 15:52:38] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -61.828120 | E_var:     0.7045 | E_err:   0.013115
[2025-10-22 15:52:51] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -61.820099 | E_var:     0.5896 | E_err:   0.011998
[2025-10-22 15:53:04] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -61.802054 | E_var:     0.5254 | E_err:   0.011326
[2025-10-22 15:53:18] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -61.838495 | E_var:     0.4914 | E_err:   0.010953
[2025-10-22 15:53:31] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -61.823414 | E_var:     0.5256 | E_err:   0.011328
[2025-10-22 15:53:44] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -61.834008 | E_var:     0.7328 | E_err:   0.013375
[2025-10-22 15:53:57] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -61.822762 | E_var:     0.6668 | E_err:   0.012759
[2025-10-22 15:54:10] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -61.812581 | E_var:     0.6736 | E_err:   0.012824
[2025-10-22 15:54:23] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -61.832874 | E_var:     0.4868 | E_err:   0.010902
[2025-10-22 15:54:37] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -61.820307 | E_var:     0.7258 | E_err:   0.013311
[2025-10-22 15:54:50] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -61.827772 | E_var:     0.6050 | E_err:   0.012154
[2025-10-22 15:55:03] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -61.829839 | E_var:     0.4985 | E_err:   0.011032
[2025-10-22 15:55:16] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -61.830367 | E_var:     0.5876 | E_err:   0.011978
[2025-10-22 15:55:29] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -61.819834 | E_var:     0.5348 | E_err:   0.011427
[2025-10-22 15:55:42] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -61.811067 | E_var:     0.5492 | E_err:   0.011579
[2025-10-22 15:55:55] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -61.827585 | E_var:     0.5871 | E_err:   0.011972
[2025-10-22 15:56:09] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -61.830348 | E_var:     0.5265 | E_err:   0.011338
[2025-10-22 15:56:22] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -61.821780 | E_var:     0.5540 | E_err:   0.011629
[2025-10-22 15:56:35] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -61.820251 | E_var:     0.5552 | E_err:   0.011642
[2025-10-22 15:56:48] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -61.818258 | E_var:     0.5597 | E_err:   0.011690
[2025-10-22 15:57:01] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -61.817518 | E_var:     0.4912 | E_err:   0.010951
[2025-10-22 15:57:14] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -61.829270 | E_var:     0.4918 | E_err:   0.010958
[2025-10-22 15:57:27] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -61.825105 | E_var:     0.6842 | E_err:   0.012925
[2025-10-22 15:57:41] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -61.807327 | E_var:     0.5267 | E_err:   0.011340
[2025-10-22 15:57:54] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -61.811957 | E_var:     0.6890 | E_err:   0.012970
[2025-10-22 15:58:07] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -61.825848 | E_var:     0.8861 | E_err:   0.014708
[2025-10-22 15:58:20] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -61.816130 | E_var:     0.5532 | E_err:   0.011621
[2025-10-22 15:58:33] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -61.830014 | E_var:     0.5398 | E_err:   0.011480
[2025-10-22 15:58:46] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -61.828083 | E_var:     0.6257 | E_err:   0.012359
[2025-10-22 15:59:00] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -61.813039 | E_var:     0.4882 | E_err:   0.010918
[2025-10-22 15:59:13] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -61.824693 | E_var:     0.6379 | E_err:   0.012480
[2025-10-22 15:59:26] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -61.818931 | E_var:     0.5148 | E_err:   0.011211
[2025-10-22 15:59:39] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -61.837086 | E_var:     0.6011 | E_err:   0.012114
[2025-10-22 15:59:52] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -61.833822 | E_var:     0.4726 | E_err:   0.010741
[2025-10-22 16:00:05] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -61.831700 | E_var:     0.5629 | E_err:   0.011723
[2025-10-22 16:00:18] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -61.847038 | E_var:     0.4747 | E_err:   0.010765
[2025-10-22 16:00:32] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -61.826197 | E_var:     0.4888 | E_err:   0.010924
[2025-10-22 16:00:45] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -61.834070 | E_var:     0.6299 | E_err:   0.012401
[2025-10-22 16:00:58] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -61.796568 | E_var:     0.6202 | E_err:   0.012305
[2025-10-22 16:01:11] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -61.813211 | E_var:     0.5312 | E_err:   0.011388
[2025-10-22 16:01:24] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -61.810444 | E_var:     0.6071 | E_err:   0.012174
[2025-10-22 16:01:37] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -61.825112 | E_var:     0.6160 | E_err:   0.012264
[2025-10-22 16:01:51] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -61.815962 | E_var:     0.5107 | E_err:   0.011166
[2025-10-22 16:02:04] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -61.815803 | E_var:     0.6722 | E_err:   0.012811
[2025-10-22 16:02:17] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -61.837777 | E_var:     0.4928 | E_err:   0.010968
[2025-10-22 16:02:30] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -61.829732 | E_var:     0.7513 | E_err:   0.013544
[2025-10-22 16:02:43] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -61.836621 | E_var:     0.6272 | E_err:   0.012374
[2025-10-22 16:02:56] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -61.835295 | E_var:     0.7512 | E_err:   0.013542
[2025-10-22 16:03:09] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -61.820629 | E_var:     0.5105 | E_err:   0.011164
[2025-10-22 16:03:23] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -61.834468 | E_var:     0.5599 | E_err:   0.011691
[2025-10-22 16:03:36] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -61.830606 | E_var:     0.5215 | E_err:   0.011284
[2025-10-22 16:03:49] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -61.832525 | E_var:     0.7398 | E_err:   0.013439
[2025-10-22 16:04:02] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -61.835140 | E_var:     0.5385 | E_err:   0.011466
[2025-10-22 16:04:15] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -61.832034 | E_var:     0.5101 | E_err:   0.011160
[2025-10-22 16:04:28] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -61.818449 | E_var:     0.6849 | E_err:   0.012931
[2025-10-22 16:04:41] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -61.844660 | E_var:     0.6971 | E_err:   0.013046
[2025-10-22 16:04:55] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -61.825013 | E_var:     0.5170 | E_err:   0.011235
[2025-10-22 16:05:08] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -61.825313 | E_var:     0.5912 | E_err:   0.012014
[2025-10-22 16:05:21] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -61.828290 | E_var:     0.5331 | E_err:   0.011409
[2025-10-22 16:05:34] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -61.838087 | E_var:     0.6024 | E_err:   0.012128
[2025-10-22 16:05:47] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -61.824768 | E_var:     0.4488 | E_err:   0.010467
[2025-10-22 16:06:00] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -61.810268 | E_var:     0.6639 | E_err:   0.012732
[2025-10-22 16:06:14] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -61.831713 | E_var:     0.5891 | E_err:   0.011993
[2025-10-22 16:06:27] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -61.814158 | E_var:     0.9332 | E_err:   0.015094
[2025-10-22 16:06:27] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-22 16:06:40] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -61.817511 | E_var:     0.6358 | E_err:   0.012459
[2025-10-22 16:06:53] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -61.821099 | E_var:     0.5632 | E_err:   0.011726
[2025-10-22 16:07:06] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -61.818789 | E_var:     0.5691 | E_err:   0.011787
[2025-10-22 16:07:19] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -61.817503 | E_var:     0.6060 | E_err:   0.012164
[2025-10-22 16:07:32] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -61.824843 | E_var:     0.4973 | E_err:   0.011019
[2025-10-22 16:07:46] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -61.822614 | E_var:     0.4675 | E_err:   0.010684
[2025-10-22 16:07:59] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -61.824966 | E_var:     0.5546 | E_err:   0.011636
[2025-10-22 16:08:12] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -61.825167 | E_var:     0.5862 | E_err:   0.011963
[2025-10-22 16:08:25] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -61.811771 | E_var:     0.6642 | E_err:   0.012734
[2025-10-22 16:08:38] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -61.825242 | E_var:     0.6065 | E_err:   0.012168
[2025-10-22 16:08:51] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -61.814220 | E_var:     0.5362 | E_err:   0.011441
[2025-10-22 16:09:05] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -61.826692 | E_var:     0.5273 | E_err:   0.011346
[2025-10-22 16:09:18] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -61.831905 | E_var:     0.5701 | E_err:   0.011798
[2025-10-22 16:09:31] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -61.814184 | E_var:     0.6211 | E_err:   0.012314
[2025-10-22 16:09:44] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -61.814594 | E_var:     0.7616 | E_err:   0.013636
[2025-10-22 16:09:57] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -61.815884 | E_var:     0.6052 | E_err:   0.012155
[2025-10-22 16:10:10] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -61.825123 | E_var:     0.6857 | E_err:   0.012938
[2025-10-22 16:10:23] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -61.814227 | E_var:     0.5223 | E_err:   0.011292
[2025-10-22 16:10:37] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -61.834486 | E_var:     0.4996 | E_err:   0.011044
[2025-10-22 16:10:50] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -61.835288 | E_var:     0.6950 | E_err:   0.013026
[2025-10-22 16:11:03] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -61.824766 | E_var:     0.5295 | E_err:   0.011370
[2025-10-22 16:11:16] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -61.817363 | E_var:     0.9029 | E_err:   0.014847
[2025-10-22 16:11:29] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -61.838312 | E_var:     0.6280 | E_err:   0.012383
[2025-10-22 16:11:42] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -61.829616 | E_var:     0.5582 | E_err:   0.011673
[2025-10-22 16:11:55] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -61.810429 | E_var:     0.5603 | E_err:   0.011696
[2025-10-22 16:12:09] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -61.837459 | E_var:     1.1995 | E_err:   0.017113
[2025-10-22 16:12:22] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -61.814088 | E_var:     0.6147 | E_err:   0.012251
[2025-10-22 16:12:35] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -61.825018 | E_var:     0.5946 | E_err:   0.012049
[2025-10-22 16:12:48] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -61.828600 | E_var:     0.5391 | E_err:   0.011473
[2025-10-22 16:13:01] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -61.840742 | E_var:     0.5427 | E_err:   0.011510
[2025-10-22 16:13:14] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -61.816397 | E_var:     0.4876 | E_err:   0.010911
[2025-10-22 16:13:28] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -61.830512 | E_var:     0.5291 | E_err:   0.011365
[2025-10-22 16:13:41] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -61.841647 | E_var:     0.6542 | E_err:   0.012638
[2025-10-22 16:13:54] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -61.826154 | E_var:     0.6626 | E_err:   0.012719
[2025-10-22 16:14:07] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -61.831110 | E_var:     0.4914 | E_err:   0.010953
[2025-10-22 16:14:20] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -61.817231 | E_var:     0.6069 | E_err:   0.012172
[2025-10-22 16:14:33] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -61.831555 | E_var:     0.6094 | E_err:   0.012198
[2025-10-22 16:14:46] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -61.802189 | E_var:     0.5766 | E_err:   0.011865
[2025-10-22 16:15:00] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -61.830763 | E_var:     0.5937 | E_err:   0.012040
[2025-10-22 16:15:13] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -61.806001 | E_var:     0.5602 | E_err:   0.011695
[2025-10-22 16:15:26] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -61.823199 | E_var:     0.5050 | E_err:   0.011103
[2025-10-22 16:15:39] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -61.837922 | E_var:     0.4889 | E_err:   0.010926
[2025-10-22 16:15:52] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -61.814486 | E_var:     0.9659 | E_err:   0.015356
[2025-10-22 16:16:05] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -61.814424 | E_var:     0.5561 | E_err:   0.011652
[2025-10-22 16:16:18] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -61.805552 | E_var:     0.5863 | E_err:   0.011964
[2025-10-22 16:16:32] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -61.809877 | E_var:     0.5963 | E_err:   0.012066
[2025-10-22 16:16:45] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -61.836338 | E_var:     0.5065 | E_err:   0.011120
[2025-10-22 16:16:58] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -61.808531 | E_var:     0.5500 | E_err:   0.011588
[2025-10-22 16:17:11] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -61.834289 | E_var:     0.5366 | E_err:   0.011446
[2025-10-22 16:17:24] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -61.821189 | E_var:     0.4927 | E_err:   0.010968
[2025-10-22 16:17:37] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -61.817706 | E_var:     0.5848 | E_err:   0.011949
[2025-10-22 16:17:50] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -61.791595 | E_var:     0.5608 | E_err:   0.011701
[2025-10-22 16:18:04] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -61.822662 | E_var:     0.6929 | E_err:   0.013007
[2025-10-22 16:18:17] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -61.836782 | E_var:     0.4945 | E_err:   0.010988
[2025-10-22 16:18:30] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -61.820697 | E_var:     0.5053 | E_err:   0.011106
[2025-10-22 16:18:43] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -61.851082 | E_var:     0.7971 | E_err:   0.013950
[2025-10-22 16:18:56] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -61.808781 | E_var:     0.6007 | E_err:   0.012110
[2025-10-22 16:19:09] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -61.824142 | E_var:     0.5097 | E_err:   0.011155
[2025-10-22 16:19:23] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -61.837338 | E_var:     0.5767 | E_err:   0.011866
[2025-10-22 16:19:36] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -61.803388 | E_var:     0.6016 | E_err:   0.012119
[2025-10-22 16:19:49] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -61.823899 | E_var:     0.5713 | E_err:   0.011810
[2025-10-22 16:20:02] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -61.819875 | E_var:     0.6907 | E_err:   0.012986
[2025-10-22 16:20:15] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -61.824466 | E_var:     0.5715 | E_err:   0.011812
[2025-10-22 16:20:28] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -61.817892 | E_var:     0.5549 | E_err:   0.011639
[2025-10-22 16:20:41] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -61.826162 | E_var:     0.5507 | E_err:   0.011595
[2025-10-22 16:20:55] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -61.820054 | E_var:     0.5328 | E_err:   0.011406
[2025-10-22 16:21:08] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -61.815129 | E_var:     0.6146 | E_err:   0.012249
[2025-10-22 16:21:21] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -61.838918 | E_var:     0.7089 | E_err:   0.013156
[2025-10-22 16:21:34] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -61.830522 | E_var:     0.5007 | E_err:   0.011056
[2025-10-22 16:21:47] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -61.823809 | E_var:     0.5792 | E_err:   0.011891
[2025-10-22 16:22:00] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -61.818961 | E_var:     0.5320 | E_err:   0.011396
[2025-10-22 16:22:13] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -61.823398 | E_var:     0.5232 | E_err:   0.011302
[2025-10-22 16:22:27] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -61.834142 | E_var:     0.7274 | E_err:   0.013327
[2025-10-22 16:22:40] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -61.841621 | E_var:     0.5586 | E_err:   0.011678
[2025-10-22 16:22:53] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -61.822477 | E_var:     0.6443 | E_err:   0.012542
[2025-10-22 16:23:06] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -61.821627 | E_var:     0.6489 | E_err:   0.012586
[2025-10-22 16:23:19] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -61.827306 | E_var:     0.5792 | E_err:   0.011892
[2025-10-22 16:23:32] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -61.811288 | E_var:     0.5114 | E_err:   0.011174
[2025-10-22 16:23:45] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -61.823630 | E_var:     0.5902 | E_err:   0.012004
[2025-10-22 16:23:59] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -61.823367 | E_var:     0.6181 | E_err:   0.012284
[2025-10-22 16:24:12] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -61.808405 | E_var:     0.6088 | E_err:   0.012192
[2025-10-22 16:24:25] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -61.831514 | E_var:     0.4747 | E_err:   0.010766
[2025-10-22 16:24:38] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -61.814757 | E_var:     0.5242 | E_err:   0.011313
[2025-10-22 16:24:51] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -61.832858 | E_var:     0.7096 | E_err:   0.013162
[2025-10-22 16:25:04] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -61.831127 | E_var:     0.5265 | E_err:   0.011338
[2025-10-22 16:25:17] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -61.831136 | E_var:     0.6014 | E_err:   0.012117
[2025-10-22 16:25:31] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -61.827505 | E_var:     0.6399 | E_err:   0.012499
[2025-10-22 16:25:44] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -61.821027 | E_var:     0.5390 | E_err:   0.011472
[2025-10-22 16:25:57] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -61.824057 | E_var:     0.6359 | E_err:   0.012460
[2025-10-22 16:26:10] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -61.818255 | E_var:     0.5673 | E_err:   0.011769
[2025-10-22 16:26:23] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -61.831702 | E_var:     0.5814 | E_err:   0.011914
[2025-10-22 16:26:36] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -61.840144 | E_var:     0.4803 | E_err:   0.010829
[2025-10-22 16:26:49] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -61.829344 | E_var:     0.5041 | E_err:   0.011094
[2025-10-22 16:27:03] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -61.828504 | E_var:     0.5362 | E_err:   0.011442
[2025-10-22 16:27:16] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -61.828258 | E_var:     0.5379 | E_err:   0.011459
[2025-10-22 16:27:29] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -61.827720 | E_var:     0.5392 | E_err:   0.011474
[2025-10-22 16:27:42] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -61.821282 | E_var:     0.6745 | E_err:   0.012832
[2025-10-22 16:27:55] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -61.822091 | E_var:     0.5447 | E_err:   0.011531
[2025-10-22 16:28:08] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -61.808818 | E_var:     0.5419 | E_err:   0.011502
[2025-10-22 16:28:22] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -61.818888 | E_var:     0.6161 | E_err:   0.012264
[2025-10-22 16:28:35] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -61.816830 | E_var:     0.6849 | E_err:   0.012931
[2025-10-22 16:28:48] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -61.830549 | E_var:     0.5786 | E_err:   0.011885
[2025-10-22 16:29:01] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -61.823477 | E_var:     0.5322 | E_err:   0.011399
[2025-10-22 16:29:14] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -61.814201 | E_var:     0.5108 | E_err:   0.011168
[2025-10-22 16:29:27] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -61.822884 | E_var:     0.5160 | E_err:   0.011224
[2025-10-22 16:29:27] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-22 16:29:41] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -61.828439 | E_var:     0.5685 | E_err:   0.011781
[2025-10-22 16:29:54] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -61.821609 | E_var:     0.7517 | E_err:   0.013547
[2025-10-22 16:30:07] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -61.822881 | E_var:     0.5816 | E_err:   0.011916
[2025-10-22 16:30:20] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -61.809543 | E_var:     0.5909 | E_err:   0.012011
[2025-10-22 16:30:33] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -61.824248 | E_var:     0.6638 | E_err:   0.012730
[2025-10-22 16:30:46] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -61.815636 | E_var:     0.8926 | E_err:   0.014762
[2025-10-22 16:30:59] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -61.806842 | E_var:     1.1565 | E_err:   0.016804
[2025-10-22 16:31:13] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -61.820398 | E_var:     0.5740 | E_err:   0.011837
[2025-10-22 16:31:26] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -61.815696 | E_var:     0.6239 | E_err:   0.012342
[2025-10-22 16:31:39] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -61.836430 | E_var:     0.5602 | E_err:   0.011695
[2025-10-22 16:31:52] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -61.825228 | E_var:     0.5893 | E_err:   0.011995
[2025-10-22 16:32:05] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -61.817582 | E_var:     0.5172 | E_err:   0.011236
[2025-10-22 16:32:18] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -61.840536 | E_var:     0.5519 | E_err:   0.011608
[2025-10-22 16:32:32] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -61.864609 | E_var:     7.9625 | E_err:   0.044091
[2025-10-22 16:32:45] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -61.827177 | E_var:     0.6795 | E_err:   0.012880
[2025-10-22 16:32:58] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -61.805349 | E_var:     0.6142 | E_err:   0.012245
[2025-10-22 16:33:11] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -61.805446 | E_var:     0.5662 | E_err:   0.011757
[2025-10-22 16:33:24] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -61.838609 | E_var:     0.4399 | E_err:   0.010363
[2025-10-22 16:33:37] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -61.839213 | E_var:     0.5573 | E_err:   0.011664
[2025-10-22 16:33:50] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -61.805494 | E_var:     0.5912 | E_err:   0.012014
[2025-10-22 16:34:04] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -61.829501 | E_var:     0.5748 | E_err:   0.011846
[2025-10-22 16:34:17] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -61.827368 | E_var:     0.5648 | E_err:   0.011743
[2025-10-22 16:34:30] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -61.822111 | E_var:     0.6235 | E_err:   0.012338
[2025-10-22 16:34:43] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -61.824684 | E_var:     0.6447 | E_err:   0.012546
[2025-10-22 16:34:56] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -61.826926 | E_var:     0.6929 | E_err:   0.013006
[2025-10-22 16:35:09] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -61.829023 | E_var:     0.9149 | E_err:   0.014945
[2025-10-22 16:35:23] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -61.835802 | E_var:     0.5149 | E_err:   0.011212
[2025-10-22 16:35:36] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -61.824435 | E_var:     0.7137 | E_err:   0.013200
[2025-10-22 16:35:49] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -61.831763 | E_var:     0.5760 | E_err:   0.011859
[2025-10-22 16:36:02] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -61.825245 | E_var:     0.5962 | E_err:   0.012065
[2025-10-22 16:36:15] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -61.843854 | E_var:     0.5624 | E_err:   0.011717
[2025-10-22 16:36:28] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -61.827403 | E_var:     0.5790 | E_err:   0.011890
[2025-10-22 16:36:41] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -61.832659 | E_var:     0.5776 | E_err:   0.011875
[2025-10-22 16:36:55] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -61.838907 | E_var:     0.7745 | E_err:   0.013751
[2025-10-22 16:37:08] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -61.841625 | E_var:     0.5473 | E_err:   0.011559
[2025-10-22 16:37:21] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -61.819520 | E_var:     0.5813 | E_err:   0.011913
[2025-10-22 16:37:34] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -61.811762 | E_var:     0.5751 | E_err:   0.011849
[2025-10-22 16:37:47] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -61.817156 | E_var:     0.5712 | E_err:   0.011809
[2025-10-22 16:38:00] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -61.818467 | E_var:     0.5754 | E_err:   0.011852
[2025-10-22 16:38:13] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -61.825692 | E_var:     0.5935 | E_err:   0.012037
[2025-10-22 16:38:27] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -61.817062 | E_var:     0.5821 | E_err:   0.011921
[2025-10-22 16:38:40] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -61.811796 | E_var:     0.4761 | E_err:   0.010781
[2025-10-22 16:38:53] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -61.810122 | E_var:     0.6038 | E_err:   0.012141
[2025-10-22 16:39:06] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -61.813359 | E_var:     0.4635 | E_err:   0.010638
[2025-10-22 16:39:19] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -61.826150 | E_var:     0.5482 | E_err:   0.011569
[2025-10-22 16:39:32] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -61.804666 | E_var:     0.5083 | E_err:   0.011140
[2025-10-22 16:39:45] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -61.813780 | E_var:     0.5522 | E_err:   0.011611
[2025-10-22 16:39:59] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -61.828764 | E_var:     0.5293 | E_err:   0.011368
[2025-10-22 16:40:12] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -61.845995 | E_var:     0.5591 | E_err:   0.011683
[2025-10-22 16:40:25] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -61.822210 | E_var:     0.5892 | E_err:   0.011993
[2025-10-22 16:40:38] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -61.819125 | E_var:     0.5609 | E_err:   0.011702
[2025-10-22 16:40:51] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -61.828330 | E_var:     0.7693 | E_err:   0.013704
[2025-10-22 16:41:04] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -61.824156 | E_var:     0.5698 | E_err:   0.011794
[2025-10-22 16:41:17] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -61.825848 | E_var:     0.5818 | E_err:   0.011919
[2025-10-22 16:41:31] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -61.813181 | E_var:     0.5819 | E_err:   0.011919
[2025-10-22 16:41:44] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -61.818226 | E_var:     0.5297 | E_err:   0.011372
[2025-10-22 16:41:57] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -61.850650 | E_var:     0.4911 | E_err:   0.010950
[2025-10-22 16:42:10] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -61.827054 | E_var:     0.5548 | E_err:   0.011639
[2025-10-22 16:42:23] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -61.826623 | E_var:     0.5661 | E_err:   0.011757
[2025-10-22 16:42:36] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -61.831668 | E_var:     0.4926 | E_err:   0.010966
[2025-10-22 16:42:50] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -61.808771 | E_var:     0.4757 | E_err:   0.010777
[2025-10-22 16:43:03] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -61.804196 | E_var:     0.5873 | E_err:   0.011974
[2025-10-22 16:43:16] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -61.822655 | E_var:     0.5615 | E_err:   0.011708
[2025-10-22 16:43:29] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -61.812910 | E_var:     0.5279 | E_err:   0.011352
[2025-10-22 16:43:42] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -61.820481 | E_var:     0.5307 | E_err:   0.011382
[2025-10-22 16:43:55] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -61.812288 | E_var:     0.6235 | E_err:   0.012338
[2025-10-22 16:44:08] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -61.836207 | E_var:     0.6859 | E_err:   0.012941
[2025-10-22 16:44:22] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -61.820121 | E_var:     0.5089 | E_err:   0.011146
[2025-10-22 16:44:35] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -61.792311 | E_var:     0.5200 | E_err:   0.011268
[2025-10-22 16:44:48] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -61.828893 | E_var:     0.7227 | E_err:   0.013283
[2025-10-22 16:45:01] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -61.822855 | E_var:     0.4808 | E_err:   0.010834
[2025-10-22 16:45:14] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -61.831176 | E_var:     0.5165 | E_err:   0.011230
[2025-10-22 16:45:27] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -61.829290 | E_var:     0.4875 | E_err:   0.010910
[2025-10-22 16:45:41] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -61.819390 | E_var:     0.6497 | E_err:   0.012594
[2025-10-22 16:45:54] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -61.819388 | E_var:     0.5258 | E_err:   0.011330
[2025-10-22 16:46:07] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -61.817059 | E_var:     0.4785 | E_err:   0.010808
[2025-10-22 16:46:20] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -61.828066 | E_var:     0.4871 | E_err:   0.010905
[2025-10-22 16:46:33] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -61.831798 | E_var:     0.5372 | E_err:   0.011453
[2025-10-22 16:46:46] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -61.798420 | E_var:     0.6054 | E_err:   0.012158
[2025-10-22 16:46:59] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -61.831783 | E_var:     0.7390 | E_err:   0.013432
[2025-10-22 16:47:13] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -61.823306 | E_var:     0.6483 | E_err:   0.012581
[2025-10-22 16:47:26] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -61.828090 | E_var:     0.5137 | E_err:   0.011199
[2025-10-22 16:47:39] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -61.820620 | E_var:     0.4540 | E_err:   0.010527
[2025-10-22 16:47:52] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -61.823193 | E_var:     0.5201 | E_err:   0.011269
[2025-10-22 16:48:05] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -61.815992 | E_var:     0.5022 | E_err:   0.011073
[2025-10-22 16:48:18] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -61.823050 | E_var:     0.5345 | E_err:   0.011423
[2025-10-22 16:48:32] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -61.828529 | E_var:     0.4489 | E_err:   0.010469
[2025-10-22 16:48:45] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -61.801246 | E_var:     0.4842 | E_err:   0.010872
[2025-10-22 16:48:58] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -61.826856 | E_var:     0.4957 | E_err:   0.011001
[2025-10-22 16:49:11] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -61.818275 | E_var:     0.5249 | E_err:   0.011321
[2025-10-22 16:49:24] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -61.811755 | E_var:     0.4675 | E_err:   0.010684
[2025-10-22 16:49:37] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -61.821118 | E_var:     0.5339 | E_err:   0.011416
[2025-10-22 16:49:50] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -61.827965 | E_var:     0.5287 | E_err:   0.011362
[2025-10-22 16:50:04] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -61.838883 | E_var:     0.5518 | E_err:   0.011607
[2025-10-22 16:50:17] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -61.825782 | E_var:     0.5123 | E_err:   0.011184
[2025-10-22 16:50:30] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -61.809429 | E_var:     0.5552 | E_err:   0.011642
[2025-10-22 16:50:43] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -61.836665 | E_var:     0.5990 | E_err:   0.012093
[2025-10-22 16:50:56] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -61.846366 | E_var:     0.5079 | E_err:   0.011135
[2025-10-22 16:51:09] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -61.819403 | E_var:     0.5416 | E_err:   0.011499
[2025-10-22 16:51:23] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -61.809503 | E_var:     0.4785 | E_err:   0.010809
[2025-10-22 16:51:36] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -61.819468 | E_var:     0.6951 | E_err:   0.013027
[2025-10-22 16:51:50] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -61.840814 | E_var:     0.6072 | E_err:   0.012176
[2025-10-22 16:52:03] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -61.823065 | E_var:     0.5357 | E_err:   0.011436
[2025-10-22 16:52:16] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -61.834191 | E_var:     0.8310 | E_err:   0.014244
[2025-10-22 16:52:29] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -61.825694 | E_var:     0.5205 | E_err:   0.011273
[2025-10-22 16:52:29] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-22 16:52:43] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -61.820301 | E_var:     0.5469 | E_err:   0.011555
[2025-10-22 16:52:56] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -61.821879 | E_var:     0.5404 | E_err:   0.011486
[2025-10-22 16:53:09] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -61.818326 | E_var:     0.5270 | E_err:   0.011343
[2025-10-22 16:53:23] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -61.828333 | E_var:     0.4743 | E_err:   0.010760
[2025-10-22 16:53:36] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -61.818793 | E_var:     0.4902 | E_err:   0.010940
[2025-10-22 16:53:49] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -61.813790 | E_var:     0.5378 | E_err:   0.011459
[2025-10-22 16:54:02] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -61.838904 | E_var:     0.5261 | E_err:   0.011333
[2025-10-22 16:54:15] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -61.815195 | E_var:     0.6509 | E_err:   0.012606
[2025-10-22 16:54:28] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -61.823892 | E_var:     0.5394 | E_err:   0.011475
[2025-10-22 16:54:41] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -61.820048 | E_var:     0.6543 | E_err:   0.012639
[2025-10-22 16:54:55] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -61.830565 | E_var:     0.5398 | E_err:   0.011480
[2025-10-22 16:55:08] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -61.817166 | E_var:     0.5875 | E_err:   0.011976
[2025-10-22 16:55:21] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -61.816009 | E_var:     0.5462 | E_err:   0.011548
[2025-10-22 16:55:34] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -61.840631 | E_var:     0.5345 | E_err:   0.011423
[2025-10-22 16:55:48] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -61.834912 | E_var:     0.4675 | E_err:   0.010683
[2025-10-22 16:56:01] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -61.817923 | E_var:     0.5322 | E_err:   0.011399
[2025-10-22 16:56:14] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -61.833853 | E_var:     0.5112 | E_err:   0.011172
[2025-10-22 16:56:27] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -61.820030 | E_var:     0.5302 | E_err:   0.011377
[2025-10-22 16:56:40] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -61.827266 | E_var:     0.5324 | E_err:   0.011401
[2025-10-22 16:56:53] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -61.829365 | E_var:     0.6165 | E_err:   0.012268
[2025-10-22 16:57:07] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -61.835884 | E_var:     0.5280 | E_err:   0.011353
[2025-10-22 16:57:20] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -61.812839 | E_var:     0.6208 | E_err:   0.012311
[2025-10-22 16:57:33] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -61.828885 | E_var:     0.6287 | E_err:   0.012389
[2025-10-22 16:57:46] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -61.826396 | E_var:     0.5786 | E_err:   0.011885
[2025-10-22 16:57:59] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -61.819182 | E_var:     0.4893 | E_err:   0.010930
[2025-10-22 16:58:12] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -61.803385 | E_var:     0.7244 | E_err:   0.013299
[2025-10-22 16:58:25] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -61.807955 | E_var:     0.5625 | E_err:   0.011719
[2025-10-22 16:58:39] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -61.834541 | E_var:     0.5472 | E_err:   0.011558
[2025-10-22 16:58:52] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -61.827928 | E_var:     0.4955 | E_err:   0.010999
[2025-10-22 16:59:05] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -61.823756 | E_var:     0.5109 | E_err:   0.011169
[2025-10-22 16:59:20] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -61.798731 | E_var:     0.6571 | E_err:   0.012666
[2025-10-22 16:59:33] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -61.807111 | E_var:     0.7373 | E_err:   0.013417
[2025-10-22 16:59:46] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -61.830205 | E_var:     0.6624 | E_err:   0.012716
[2025-10-22 16:59:59] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -61.828170 | E_var:     0.5269 | E_err:   0.011342
[2025-10-22 17:00:12] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -61.835325 | E_var:     0.6395 | E_err:   0.012495
[2025-10-22 17:00:25] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -61.808185 | E_var:     0.4492 | E_err:   0.010472
[2025-10-22 17:00:38] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -61.811922 | E_var:     0.4931 | E_err:   0.010972
[2025-10-22 17:00:52] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -61.822719 | E_var:     0.5413 | E_err:   0.011495
[2025-10-22 17:01:05] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -61.851327 | E_var:     0.4844 | E_err:   0.010874
[2025-10-22 17:01:18] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -61.822432 | E_var:     0.5831 | E_err:   0.011931
[2025-10-22 17:01:31] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -61.818737 | E_var:     0.5720 | E_err:   0.011818
[2025-10-22 17:01:44] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -61.829037 | E_var:     0.6207 | E_err:   0.012310
[2025-10-22 17:01:57] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -61.795284 | E_var:     0.5569 | E_err:   0.011660
[2025-10-22 17:02:10] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -61.836259 | E_var:     0.5944 | E_err:   0.012046
[2025-10-22 17:02:24] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -61.811394 | E_var:     0.7397 | E_err:   0.013439
[2025-10-22 17:02:37] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -61.831456 | E_var:     0.6855 | E_err:   0.012937
[2025-10-22 17:02:50] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -61.822963 | E_var:     0.6530 | E_err:   0.012627
[2025-10-22 17:03:04] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -61.828218 | E_var:     0.7866 | E_err:   0.013858
[2025-10-22 17:03:17] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -61.838202 | E_var:     0.7941 | E_err:   0.013924
[2025-10-22 17:03:30] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -61.831630 | E_var:     0.5805 | E_err:   0.011904
[2025-10-22 17:03:43] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -61.819745 | E_var:     0.6693 | E_err:   0.012783
[2025-10-22 17:03:56] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -61.812203 | E_var:     0.5539 | E_err:   0.011629
[2025-10-22 17:04:09] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -61.831906 | E_var:     0.4695 | E_err:   0.010707
[2025-10-22 17:04:22] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -61.829199 | E_var:     0.4785 | E_err:   0.010808
[2025-10-22 17:04:36] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -61.829717 | E_var:     0.7406 | E_err:   0.013447
[2025-10-22 17:04:49] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -61.812637 | E_var:     0.5031 | E_err:   0.011082
[2025-10-22 17:05:02] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -61.808484 | E_var:     0.5491 | E_err:   0.011578
[2025-10-22 17:05:15] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -61.820911 | E_var:     0.4629 | E_err:   0.010631
[2025-10-22 17:05:28] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -61.809030 | E_var:     0.5721 | E_err:   0.011819
[2025-10-22 17:05:41] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -61.811554 | E_var:     0.5174 | E_err:   0.011239
[2025-10-22 17:05:54] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -61.815870 | E_var:     0.8494 | E_err:   0.014400
[2025-10-22 17:06:08] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -61.830058 | E_var:     0.5167 | E_err:   0.011231
[2025-10-22 17:06:21] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -61.830260 | E_var:     0.4887 | E_err:   0.010923
[2025-10-22 17:06:34] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -61.804049 | E_var:     0.5322 | E_err:   0.011398
[2025-10-22 17:06:47] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -61.818894 | E_var:     0.5341 | E_err:   0.011419
[2025-10-22 17:07:00] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -61.835210 | E_var:     0.6100 | E_err:   0.012203
[2025-10-22 17:07:13] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -61.807082 | E_var:     0.6670 | E_err:   0.012761
[2025-10-22 17:07:27] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -61.818697 | E_var:     0.7568 | E_err:   0.013593
[2025-10-22 17:07:40] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -61.807473 | E_var:     0.4329 | E_err:   0.010280
[2025-10-22 17:07:53] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -61.813480 | E_var:     0.5304 | E_err:   0.011379
[2025-10-22 17:08:06] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -61.848613 | E_var:     0.6469 | E_err:   0.012568
[2025-10-22 17:08:19] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -61.841459 | E_var:     0.5296 | E_err:   0.011371
[2025-10-22 17:08:32] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -61.830980 | E_var:     0.5201 | E_err:   0.011269
[2025-10-22 17:08:45] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -61.835333 | E_var:     0.5551 | E_err:   0.011642
[2025-10-22 17:08:59] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -61.824129 | E_var:     0.8230 | E_err:   0.014175
[2025-10-22 17:09:12] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -61.822272 | E_var:     0.4720 | E_err:   0.010735
[2025-10-22 17:09:25] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -61.827386 | E_var:     0.6664 | E_err:   0.012755
[2025-10-22 17:09:38] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -61.810013 | E_var:     0.5133 | E_err:   0.011194
[2025-10-22 17:09:51] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -61.834293 | E_var:     0.5073 | E_err:   0.011129
[2025-10-22 17:10:04] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -61.831562 | E_var:     0.5707 | E_err:   0.011804
[2025-10-22 17:10:18] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -61.811759 | E_var:     0.5855 | E_err:   0.011956
[2025-10-22 17:10:31] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -61.831041 | E_var:     0.5324 | E_err:   0.011401
[2025-10-22 17:10:44] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -61.828954 | E_var:     0.5784 | E_err:   0.011883
[2025-10-22 17:10:58] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -61.815187 | E_var:     0.6614 | E_err:   0.012707
[2025-10-22 17:11:11] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -61.832098 | E_var:     0.6266 | E_err:   0.012369
[2025-10-22 17:11:24] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -61.835478 | E_var:     0.4885 | E_err:   0.010921
[2025-10-22 17:11:37] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -61.818475 | E_var:     0.6429 | E_err:   0.012529
[2025-10-22 17:11:50] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -61.801700 | E_var:     0.4945 | E_err:   0.010988
[2025-10-22 17:12:03] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -61.814934 | E_var:     0.4936 | E_err:   0.010978
[2025-10-22 17:12:16] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -61.832781 | E_var:     0.6169 | E_err:   0.012272
[2025-10-22 17:12:30] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -61.825850 | E_var:     0.5368 | E_err:   0.011448
[2025-10-22 17:12:43] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -61.817730 | E_var:     0.5169 | E_err:   0.011234
[2025-10-22 17:12:57] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -61.823620 | E_var:     0.6161 | E_err:   0.012265
[2025-10-22 17:13:10] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -61.825808 | E_var:     0.6926 | E_err:   0.013004
[2025-10-22 17:13:23] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -61.814227 | E_var:     0.4922 | E_err:   0.010962
[2025-10-22 17:13:36] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -61.805639 | E_var:     0.5856 | E_err:   0.011957
[2025-10-22 17:13:49] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -61.801078 | E_var:     0.5270 | E_err:   0.011342
[2025-10-22 17:14:02] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -61.828523 | E_var:     0.5363 | E_err:   0.011443
[2025-10-22 17:14:15] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -61.813275 | E_var:     0.7767 | E_err:   0.013770
[2025-10-22 17:14:29] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -61.833736 | E_var:     0.8885 | E_err:   0.014728
[2025-10-22 17:14:42] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -61.826317 | E_var:     0.5542 | E_err:   0.011632
[2025-10-22 17:14:55] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -61.824755 | E_var:     0.5851 | E_err:   0.011952
[2025-10-22 17:15:08] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -61.828365 | E_var:     0.6610 | E_err:   0.012704
[2025-10-22 17:15:21] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -61.825089 | E_var:     0.5833 | E_err:   0.011933
[2025-10-22 17:15:34] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -61.819004 | E_var:     0.6237 | E_err:   0.012340
[2025-10-22 17:15:34] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-22 17:15:34] ======================================================================================================
[2025-10-22 17:15:34] ✅ Training completed successfully
[2025-10-22 17:15:34] Total restarts: 2
[2025-10-22 17:15:39] Final Energy: -61.81900394 ± 0.01234011
[2025-10-22 17:15:39] Final Variance: 0.623732
[2025-10-22 17:15:39] ======================================================================================================
[2025-10-22 17:15:39] ======================================================================================================
[2025-10-22 17:15:39] Training completed | Runtime: 13875.9s
