[2025-10-22 17:15:59] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.77/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-22 17:15:59]   - 迭代次数: 1050
[2025-10-22 17:15:59]   - 能量: -61.819004-0.001621j ± 0.012340, Var: 0.623732
[2025-10-22 17:15:59]   - 时间戳: 2025-10-22T17:15:34.913974+08:00
[2025-10-22 17:16:24] ✓ 变分状态参数已从checkpoint恢复
[2025-10-22 17:16:24] ======================================================================================================
[2025-10-22 17:16:24] GCNN for Shastry-Sutherland Model
[2025-10-22 17:16:24] ======================================================================================================
[2025-10-22 17:16:24] System parameters:
[2025-10-22 17:16:24]   - System size: L=6, N=144
[2025-10-22 17:16:24]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-10-22 17:16:24] ------------------------------------------------------------------------------------------------------
[2025-10-22 17:16:24] Model parameters:
[2025-10-22 17:16:24]   - Number of layers = 4
[2025-10-22 17:16:24]   - Number of features = 4
[2025-10-22 17:16:24]   - Total parameters = 28252
[2025-10-22 17:16:24] ------------------------------------------------------------------------------------------------------
[2025-10-22 17:16:24] Training parameters:
[2025-10-22 17:16:24]   - Total iterations: 1050
[2025-10-22 17:16:24]   - Annealing cycles: 3
[2025-10-22 17:16:24]   - Initial period: 150
[2025-10-22 17:16:24]   - Period multiplier: 2.0
[2025-10-22 17:16:24]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-22 17:16:24]   - Samples: 4096
[2025-10-22 17:16:24]   - Discarded samples: 0
[2025-10-22 17:16:24]   - Chunk size: 4096
[2025-10-22 17:16:24]   - Diagonal shift: 0.15
[2025-10-22 17:16:24]   - Gradient clipping: 1.0
[2025-10-22 17:16:24]   - Checkpoint enabled: interval=105
[2025-10-22 17:16:24]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.78/training/checkpoints
[2025-10-22 17:16:24]   - Resuming from iteration: 1050
[2025-10-22 17:16:24] ------------------------------------------------------------------------------------------------------
[2025-10-22 17:16:24] Device status:
[2025-10-22 17:16:24]   - Devices model: NVIDIA H200 NVL
[2025-10-22 17:16:24]   - Number of devices: 1
[2025-10-22 17:16:24]   - Sharding: True
[2025-10-22 17:16:25] ======================================================================================================
[2025-10-22 17:17:16] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -62.724948 | E_var:     1.8877 | E_err:   0.021468
[2025-10-22 17:17:49] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -62.745656 | E_var:     1.0451 | E_err:   0.015974
[2025-10-22 17:18:02] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -62.749896 | E_var:     0.9195 | E_err:   0.014983
[2025-10-22 17:18:16] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -62.742346 | E_var:     0.6594 | E_err:   0.012688
[2025-10-22 17:18:29] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -62.745944 | E_var:     0.5967 | E_err:   0.012070
[2025-10-22 17:18:42] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -62.722833 | E_var:     0.6884 | E_err:   0.012964
[2025-10-22 17:18:55] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -62.716664 | E_var:     0.6634 | E_err:   0.012726
[2025-10-22 17:19:08] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -62.744562 | E_var:     0.5299 | E_err:   0.011374
[2025-10-22 17:19:21] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -62.748807 | E_var:     0.7171 | E_err:   0.013231
[2025-10-22 17:19:35] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -62.737335 | E_var:     0.5306 | E_err:   0.011381
[2025-10-22 17:19:48] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -62.729748 | E_var:     0.6067 | E_err:   0.012170
[2025-10-22 17:20:01] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -62.740483 | E_var:     0.5565 | E_err:   0.011656
[2025-10-22 17:20:14] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -62.751721 | E_var:     0.5243 | E_err:   0.011314
[2025-10-22 17:20:27] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -62.740580 | E_var:     0.5737 | E_err:   0.011835
[2025-10-22 17:20:40] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -62.722879 | E_var:     0.5722 | E_err:   0.011820
[2025-10-22 17:20:54] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -62.732481 | E_var:     0.5858 | E_err:   0.011958
[2025-10-22 17:21:07] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -62.733330 | E_var:     0.6708 | E_err:   0.012797
[2025-10-22 17:21:20] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -62.728946 | E_var:     0.8108 | E_err:   0.014070
[2025-10-22 17:21:33] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -62.719128 | E_var:     0.6469 | E_err:   0.012567
[2025-10-22 17:21:46] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -62.738376 | E_var:     0.5086 | E_err:   0.011143
[2025-10-22 17:21:59] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -62.717825 | E_var:     0.5429 | E_err:   0.011513
[2025-10-22 17:22:13] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -62.747362 | E_var:     0.6728 | E_err:   0.012816
[2025-10-22 17:22:26] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -62.752113 | E_var:     0.8117 | E_err:   0.014077
[2025-10-22 17:22:39] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -62.743036 | E_var:     0.4847 | E_err:   0.010878
[2025-10-22 17:22:52] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -62.742252 | E_var:     1.2274 | E_err:   0.017311
[2025-10-22 17:23:05] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -62.725405 | E_var:     0.5248 | E_err:   0.011319
[2025-10-22 17:23:18] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -62.738293 | E_var:     0.5560 | E_err:   0.011651
[2025-10-22 17:23:32] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -62.725255 | E_var:     0.6438 | E_err:   0.012537
[2025-10-22 17:23:45] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -62.753583 | E_var:     0.5690 | E_err:   0.011786
[2025-10-22 17:23:58] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -62.725469 | E_var:     0.6812 | E_err:   0.012896
[2025-10-22 17:24:11] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -62.748944 | E_var:     0.4527 | E_err:   0.010513
[2025-10-22 17:24:24] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -62.742720 | E_var:     0.6643 | E_err:   0.012735
[2025-10-22 17:24:37] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -62.743720 | E_var:     0.5508 | E_err:   0.011597
[2025-10-22 17:24:51] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -62.751543 | E_var:     0.4691 | E_err:   0.010702
[2025-10-22 17:25:04] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -62.726838 | E_var:     0.5684 | E_err:   0.011781
[2025-10-22 17:25:17] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -62.722724 | E_var:     0.5412 | E_err:   0.011495
[2025-10-22 17:25:30] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -62.734399 | E_var:     0.4789 | E_err:   0.010813
[2025-10-22 17:25:44] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -62.754797 | E_var:     0.5908 | E_err:   0.012010
[2025-10-22 17:25:57] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -62.733521 | E_var:     0.7696 | E_err:   0.013707
[2025-10-22 17:26:10] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -62.720361 | E_var:     0.5405 | E_err:   0.011487
[2025-10-22 17:26:23] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -62.746783 | E_var:     0.5110 | E_err:   0.011169
[2025-10-22 17:26:36] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -62.740935 | E_var:     0.4902 | E_err:   0.010939
[2025-10-22 17:26:49] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -62.728988 | E_var:     0.5326 | E_err:   0.011404
[2025-10-22 17:27:03] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -62.714620 | E_var:     0.5757 | E_err:   0.011855
[2025-10-22 17:27:16] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -62.749775 | E_var:     0.4939 | E_err:   0.010981
[2025-10-22 17:27:29] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -62.725491 | E_var:     0.5656 | E_err:   0.011751
[2025-10-22 17:27:42] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -62.724825 | E_var:     0.7679 | E_err:   0.013693
[2025-10-22 17:27:55] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -62.737476 | E_var:     0.6260 | E_err:   0.012363
[2025-10-22 17:28:09] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -62.739433 | E_var:     0.5221 | E_err:   0.011290
[2025-10-22 17:28:23] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -62.728436 | E_var:     0.4643 | E_err:   0.010647
[2025-10-22 17:28:36] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -62.737730 | E_var:     0.5385 | E_err:   0.011466
[2025-10-22 17:28:49] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -62.716125 | E_var:     0.5396 | E_err:   0.011477
[2025-10-22 17:29:02] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -62.736270 | E_var:     0.4877 | E_err:   0.010912
[2025-10-22 17:29:15] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -62.728182 | E_var:     0.5176 | E_err:   0.011241
[2025-10-22 17:29:28] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -62.720681 | E_var:     0.6005 | E_err:   0.012108
[2025-10-22 17:29:42] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -62.735658 | E_var:     0.5651 | E_err:   0.011746
[2025-10-22 17:29:55] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -62.727750 | E_var:     0.5313 | E_err:   0.011389
[2025-10-22 17:30:08] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -62.720385 | E_var:     0.6026 | E_err:   0.012129
[2025-10-22 17:30:21] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -62.759590 | E_var:     0.8579 | E_err:   0.014472
[2025-10-22 17:30:35] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -62.746920 | E_var:     0.5160 | E_err:   0.011224
[2025-10-22 17:30:49] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -62.739313 | E_var:     0.6711 | E_err:   0.012800
[2025-10-22 17:31:02] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -62.759893 | E_var:     0.4755 | E_err:   0.010775
[2025-10-22 17:31:16] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -62.731585 | E_var:     0.4534 | E_err:   0.010521
[2025-10-22 17:31:29] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -62.722364 | E_var:     0.5941 | E_err:   0.012044
[2025-10-22 17:31:42] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -62.748499 | E_var:     0.6117 | E_err:   0.012220
[2025-10-22 17:31:55] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -62.729841 | E_var:     0.4891 | E_err:   0.010928
[2025-10-22 17:32:08] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -62.753963 | E_var:     0.6726 | E_err:   0.012815
[2025-10-22 17:32:21] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -62.751501 | E_var:     0.5342 | E_err:   0.011420
[2025-10-22 17:32:35] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -62.735040 | E_var:     0.5264 | E_err:   0.011337
[2025-10-22 17:32:48] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -62.738263 | E_var:     0.5417 | E_err:   0.011500
[2025-10-22 17:33:01] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -62.737031 | E_var:     0.5973 | E_err:   0.012076
[2025-10-22 17:33:14] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -62.742800 | E_var:     0.5677 | E_err:   0.011773
[2025-10-22 17:33:27] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -62.736390 | E_var:     0.5170 | E_err:   0.011234
[2025-10-22 17:33:41] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -62.734043 | E_var:     0.7660 | E_err:   0.013676
[2025-10-22 17:33:54] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -62.734492 | E_var:     0.4267 | E_err:   0.010207
[2025-10-22 17:34:07] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -62.731985 | E_var:     0.5895 | E_err:   0.011997
[2025-10-22 17:34:20] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -62.741991 | E_var:     0.5138 | E_err:   0.011200
[2025-10-22 17:34:33] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -62.734362 | E_var:     0.4578 | E_err:   0.010572
[2025-10-22 17:34:46] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -62.728860 | E_var:     0.5932 | E_err:   0.012034
[2025-10-22 17:35:00] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -62.749131 | E_var:     0.5452 | E_err:   0.011538
[2025-10-22 17:35:13] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -62.733298 | E_var:     0.5688 | E_err:   0.011784
[2025-10-22 17:35:26] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -62.730991 | E_var:     0.4992 | E_err:   0.011039
[2025-10-22 17:35:39] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -62.733621 | E_var:     0.6460 | E_err:   0.012559
[2025-10-22 17:35:52] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -62.723842 | E_var:     0.5133 | E_err:   0.011195
[2025-10-22 17:36:05] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -62.746467 | E_var:     0.5607 | E_err:   0.011700
[2025-10-22 17:36:19] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -62.734826 | E_var:     0.4639 | E_err:   0.010643
[2025-10-22 17:36:32] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -62.759974 | E_var:     0.4746 | E_err:   0.010764
[2025-10-22 17:36:45] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -62.751964 | E_var:     0.6262 | E_err:   0.012364
[2025-10-22 17:36:58] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -62.730445 | E_var:     0.5541 | E_err:   0.011631
[2025-10-22 17:37:11] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -62.720114 | E_var:     0.5144 | E_err:   0.011206
[2025-10-22 17:37:24] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -62.738443 | E_var:     0.5926 | E_err:   0.012029
[2025-10-22 17:37:38] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -62.728035 | E_var:     0.5012 | E_err:   0.011062
[2025-10-22 17:37:51] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -62.737379 | E_var:     0.4652 | E_err:   0.010657
[2025-10-22 17:38:04] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -62.727177 | E_var:     2.5593 | E_err:   0.024997
[2025-10-22 17:38:17] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -62.745870 | E_var:     0.5788 | E_err:   0.011888
[2025-10-22 17:38:30] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -62.725934 | E_var:     0.5232 | E_err:   0.011302
[2025-10-22 17:38:43] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -62.736028 | E_var:     0.6122 | E_err:   0.012226
[2025-10-22 17:38:57] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -62.749999 | E_var:     0.4045 | E_err:   0.009938
[2025-10-22 17:39:10] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -62.713877 | E_var:     0.5374 | E_err:   0.011455
[2025-10-22 17:39:23] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -62.730929 | E_var:     0.4620 | E_err:   0.010620
[2025-10-22 17:39:36] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -62.727582 | E_var:     0.4817 | E_err:   0.010844
[2025-10-22 17:39:49] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -62.736021 | E_var:     0.6079 | E_err:   0.012183
[2025-10-22 17:40:02] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -62.746826 | E_var:     0.4518 | E_err:   0.010502
[2025-10-22 17:40:16] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -62.724578 | E_var:     0.5350 | E_err:   0.011429
[2025-10-22 17:40:29] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -62.755395 | E_var:     0.4988 | E_err:   0.011035
[2025-10-22 17:40:29] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-22 17:40:42] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -62.748524 | E_var:     0.4409 | E_err:   0.010375
[2025-10-22 17:40:55] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -62.741469 | E_var:     0.5670 | E_err:   0.011765
[2025-10-22 17:41:08] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -62.734079 | E_var:     0.5487 | E_err:   0.011574
[2025-10-22 17:41:21] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -62.729396 | E_var:     0.4821 | E_err:   0.010849
[2025-10-22 17:41:35] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -62.741026 | E_var:     0.4498 | E_err:   0.010479
[2025-10-22 17:41:48] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -62.735325 | E_var:     0.4970 | E_err:   0.011016
[2025-10-22 17:42:01] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -62.739975 | E_var:     0.4769 | E_err:   0.010790
[2025-10-22 17:42:14] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -62.747269 | E_var:     0.4861 | E_err:   0.010894
[2025-10-22 17:42:27] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -62.741464 | E_var:     0.5388 | E_err:   0.011469
[2025-10-22 17:42:40] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -62.734998 | E_var:     0.6054 | E_err:   0.012158
[2025-10-22 17:42:54] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -62.743714 | E_var:     0.5583 | E_err:   0.011675
[2025-10-22 17:43:07] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -62.721393 | E_var:     0.5663 | E_err:   0.011759
[2025-10-22 17:43:20] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -62.739705 | E_var:     0.5824 | E_err:   0.011924
[2025-10-22 17:43:33] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -62.733510 | E_var:     0.4416 | E_err:   0.010383
[2025-10-22 17:43:46] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -62.738862 | E_var:     0.5321 | E_err:   0.011398
[2025-10-22 17:43:59] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -62.725566 | E_var:     0.5747 | E_err:   0.011845
[2025-10-22 17:44:13] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -62.722690 | E_var:     0.6069 | E_err:   0.012172
[2025-10-22 17:44:26] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -62.736898 | E_var:     0.5398 | E_err:   0.011480
[2025-10-22 17:44:39] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -62.720135 | E_var:     0.4809 | E_err:   0.010835
[2025-10-22 17:44:52] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -62.743651 | E_var:     0.7554 | E_err:   0.013581
[2025-10-22 17:45:05] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -62.728635 | E_var:     0.5497 | E_err:   0.011584
[2025-10-22 17:45:19] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -62.748405 | E_var:     0.5683 | E_err:   0.011779
[2025-10-22 17:45:32] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -62.730573 | E_var:     0.4675 | E_err:   0.010683
[2025-10-22 17:45:46] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -62.738479 | E_var:     0.5746 | E_err:   0.011844
[2025-10-22 17:45:59] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -62.736636 | E_var:     0.4847 | E_err:   0.010878
[2025-10-22 17:46:12] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -62.724581 | E_var:     0.4645 | E_err:   0.010649
[2025-10-22 17:46:25] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -62.733979 | E_var:     0.6904 | E_err:   0.012983
[2025-10-22 17:46:38] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -62.752455 | E_var:     0.4567 | E_err:   0.010559
[2025-10-22 17:46:51] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -62.741659 | E_var:     0.4679 | E_err:   0.010688
[2025-10-22 17:47:05] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -62.728099 | E_var:     0.4905 | E_err:   0.010943
[2025-10-22 17:47:18] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -62.730619 | E_var:     0.5321 | E_err:   0.011398
[2025-10-22 17:47:31] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -62.754924 | E_var:     0.5324 | E_err:   0.011401
[2025-10-22 17:47:44] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -62.736797 | E_var:     0.5454 | E_err:   0.011539
[2025-10-22 17:47:57] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -62.717631 | E_var:     0.4615 | E_err:   0.010615
[2025-10-22 17:48:10] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -62.731009 | E_var:     0.5799 | E_err:   0.011899
[2025-10-22 17:48:23] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -62.745475 | E_var:     0.6285 | E_err:   0.012387
[2025-10-22 17:48:37] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -62.727880 | E_var:     0.5700 | E_err:   0.011796
[2025-10-22 17:48:50] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -62.723496 | E_var:     0.6643 | E_err:   0.012735
[2025-10-22 17:49:03] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -62.728679 | E_var:     0.6470 | E_err:   0.012568
[2025-10-22 17:49:16] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -62.771521 | E_var:     0.8821 | E_err:   0.014675
[2025-10-22 17:49:29] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -62.723568 | E_var:     1.3144 | E_err:   0.017914
[2025-10-22 17:49:43] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -62.730643 | E_var:     0.4906 | E_err:   0.010944
[2025-10-22 17:49:56] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -62.726339 | E_var:     0.5865 | E_err:   0.011966
[2025-10-22 17:50:09] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -62.713613 | E_var:     0.5101 | E_err:   0.011159
[2025-10-22 17:50:22] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -62.745717 | E_var:     0.5271 | E_err:   0.011344
[2025-10-22 17:50:22] 🔄 RESTART #1 | Period: 300
[2025-10-22 17:50:35] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -62.738197 | E_var:     0.5393 | E_err:   0.011474
[2025-10-22 17:50:48] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -62.739784 | E_var:     0.5687 | E_err:   0.011783
[2025-10-22 17:51:02] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -62.734926 | E_var:     0.5319 | E_err:   0.011396
[2025-10-22 17:51:15] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -62.742140 | E_var:     0.5186 | E_err:   0.011253
[2025-10-22 17:51:28] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -62.733277 | E_var:     0.5152 | E_err:   0.011215
[2025-10-22 17:51:41] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -62.745341 | E_var:     0.4610 | E_err:   0.010609
[2025-10-22 17:51:54] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -62.737635 | E_var:     0.6186 | E_err:   0.012289
[2025-10-22 17:52:07] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -62.730464 | E_var:     0.4753 | E_err:   0.010772
[2025-10-22 17:52:21] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -62.741491 | E_var:     0.4588 | E_err:   0.010584
[2025-10-22 17:52:34] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -62.730431 | E_var:     0.4978 | E_err:   0.011024
[2025-10-22 17:52:47] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -62.737627 | E_var:     0.5187 | E_err:   0.011254
[2025-10-22 17:53:00] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -62.752361 | E_var:     0.5048 | E_err:   0.011101
[2025-10-22 17:53:13] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -62.750927 | E_var:     0.5613 | E_err:   0.011707
[2025-10-22 17:53:26] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -62.731032 | E_var:     0.4814 | E_err:   0.010841
[2025-10-22 17:53:40] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -62.738268 | E_var:     0.6751 | E_err:   0.012838
[2025-10-22 17:53:54] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -62.741892 | E_var:     0.6188 | E_err:   0.012291
[2025-10-22 17:54:07] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -62.733647 | E_var:     0.6376 | E_err:   0.012476
[2025-10-22 17:54:20] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -62.733738 | E_var:     0.5051 | E_err:   0.011105
[2025-10-22 17:54:33] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -62.748148 | E_var:     0.5863 | E_err:   0.011964
[2025-10-22 17:54:47] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -62.727970 | E_var:     0.5504 | E_err:   0.011592
[2025-10-22 17:55:00] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -62.733050 | E_var:     0.7211 | E_err:   0.013268
[2025-10-22 17:55:13] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -62.736938 | E_var:     0.5513 | E_err:   0.011601
[2025-10-22 17:55:26] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -62.725744 | E_var:     0.5092 | E_err:   0.011149
[2025-10-22 17:55:39] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -62.738228 | E_var:     0.5944 | E_err:   0.012046
[2025-10-22 17:55:52] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -62.740416 | E_var:     0.5593 | E_err:   0.011685
[2025-10-22 17:56:06] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -62.761998 | E_var:     0.4637 | E_err:   0.010639
[2025-10-22 17:56:19] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -62.731171 | E_var:     0.5680 | E_err:   0.011776
[2025-10-22 17:56:32] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -62.751934 | E_var:     0.5127 | E_err:   0.011188
[2025-10-22 17:56:45] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -62.720947 | E_var:     0.5878 | E_err:   0.011979
[2025-10-22 17:56:58] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -62.726793 | E_var:     0.4580 | E_err:   0.010574
[2025-10-22 17:57:12] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -62.726857 | E_var:     0.4513 | E_err:   0.010496
[2025-10-22 17:57:25] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -62.751754 | E_var:     0.5866 | E_err:   0.011967
[2025-10-22 17:57:38] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -62.751192 | E_var:     0.5165 | E_err:   0.011229
[2025-10-22 17:57:51] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -62.738719 | E_var:     0.5552 | E_err:   0.011642
[2025-10-22 17:58:04] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -62.742097 | E_var:     0.4729 | E_err:   0.010745
[2025-10-22 17:58:17] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -62.739794 | E_var:     0.5671 | E_err:   0.011767
[2025-10-22 17:58:31] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -62.751144 | E_var:     0.5326 | E_err:   0.011403
[2025-10-22 17:58:44] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -62.729774 | E_var:     0.4752 | E_err:   0.010771
[2025-10-22 17:58:57] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -62.735691 | E_var:     0.5473 | E_err:   0.011559
[2025-10-22 17:59:10] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -62.719616 | E_var:     0.5399 | E_err:   0.011481
[2025-10-22 17:59:23] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -62.738108 | E_var:     0.5597 | E_err:   0.011690
[2025-10-22 17:59:36] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -62.749464 | E_var:     0.5067 | E_err:   0.011123
[2025-10-22 17:59:50] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -62.736239 | E_var:     0.5109 | E_err:   0.011169
[2025-10-22 18:00:03] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -62.743307 | E_var:     0.4779 | E_err:   0.010801
[2025-10-22 18:00:16] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -62.748414 | E_var:     0.4994 | E_err:   0.011042
[2025-10-22 18:00:29] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -62.750491 | E_var:     0.5143 | E_err:   0.011206
[2025-10-22 18:00:42] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -62.743944 | E_var:     0.5779 | E_err:   0.011878
[2025-10-22 18:00:56] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -62.711720 | E_var:     0.6602 | E_err:   0.012696
[2025-10-22 18:01:09] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -62.733338 | E_var:     0.4125 | E_err:   0.010035
[2025-10-22 18:01:22] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -62.730181 | E_var:     0.5200 | E_err:   0.011268
[2025-10-22 18:01:35] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -62.733155 | E_var:     0.5780 | E_err:   0.011880
[2025-10-22 18:01:48] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -62.741436 | E_var:     0.5548 | E_err:   0.011638
[2025-10-22 18:02:01] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -62.737389 | E_var:     0.5012 | E_err:   0.011062
[2025-10-22 18:02:15] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -62.742301 | E_var:     0.6897 | E_err:   0.012976
[2025-10-22 18:02:28] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -62.728700 | E_var:     0.5963 | E_err:   0.012065
[2025-10-22 18:02:41] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -62.753216 | E_var:     0.5458 | E_err:   0.011544
[2025-10-22 18:02:54] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -62.736510 | E_var:     0.4848 | E_err:   0.010880
[2025-10-22 18:03:07] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -62.746887 | E_var:     0.4788 | E_err:   0.010812
[2025-10-22 18:03:20] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -62.742697 | E_var:     0.6225 | E_err:   0.012328
[2025-10-22 18:03:34] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -62.745040 | E_var:     0.6534 | E_err:   0.012630
[2025-10-22 18:03:34] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-22 18:03:47] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -62.741025 | E_var:     0.5519 | E_err:   0.011608
[2025-10-22 18:04:00] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -62.765984 | E_var:     0.6215 | E_err:   0.012318
[2025-10-22 18:04:13] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -62.719982 | E_var:     0.5848 | E_err:   0.011949
[2025-10-22 18:04:26] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -62.737210 | E_var:     0.5046 | E_err:   0.011099
[2025-10-22 18:04:40] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -62.742032 | E_var:     0.4955 | E_err:   0.010999
[2025-10-22 18:04:53] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -62.746198 | E_var:     0.4858 | E_err:   0.010890
[2025-10-22 18:05:06] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -62.739547 | E_var:     0.5297 | E_err:   0.011372
[2025-10-22 18:05:19] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -62.757232 | E_var:     0.5318 | E_err:   0.011395
[2025-10-22 18:05:32] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -62.750830 | E_var:     0.7383 | E_err:   0.013426
[2025-10-22 18:05:45] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -62.721694 | E_var:     0.6019 | E_err:   0.012123
[2025-10-22 18:05:59] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -62.740477 | E_var:     0.5376 | E_err:   0.011456
[2025-10-22 18:06:12] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -62.732838 | E_var:     0.5817 | E_err:   0.011917
[2025-10-22 18:06:25] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -62.742810 | E_var:     0.5635 | E_err:   0.011729
[2025-10-22 18:06:38] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -62.741419 | E_var:     0.5586 | E_err:   0.011678
[2025-10-22 18:06:51] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -62.719899 | E_var:     0.7213 | E_err:   0.013270
[2025-10-22 18:07:05] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -62.736472 | E_var:     0.5249 | E_err:   0.011320
[2025-10-22 18:07:18] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -62.747315 | E_var:     0.6355 | E_err:   0.012456
[2025-10-22 18:07:31] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -62.723800 | E_var:     0.4969 | E_err:   0.011014
[2025-10-22 18:07:44] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -62.741383 | E_var:     0.8323 | E_err:   0.014255
[2025-10-22 18:07:57] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -62.739008 | E_var:     0.5339 | E_err:   0.011417
[2025-10-22 18:08:10] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -62.742227 | E_var:     0.5079 | E_err:   0.011135
[2025-10-22 18:08:24] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -62.744138 | E_var:     0.4996 | E_err:   0.011044
[2025-10-22 18:08:37] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -62.757939 | E_var:     0.6525 | E_err:   0.012622
[2025-10-22 18:08:50] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -62.738682 | E_var:     0.5824 | E_err:   0.011924
[2025-10-22 18:09:03] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -62.737358 | E_var:     0.5185 | E_err:   0.011251
[2025-10-22 18:09:16] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -62.749473 | E_var:     0.5509 | E_err:   0.011598
[2025-10-22 18:09:29] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -62.750843 | E_var:     0.6265 | E_err:   0.012367
[2025-10-22 18:09:43] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -62.731079 | E_var:     0.5546 | E_err:   0.011636
[2025-10-22 18:09:56] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -62.746248 | E_var:     0.5085 | E_err:   0.011142
[2025-10-22 18:10:09] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -62.754221 | E_var:     0.4580 | E_err:   0.010574
[2025-10-22 18:10:22] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -62.728759 | E_var:     0.7409 | E_err:   0.013449
[2025-10-22 18:10:35] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -62.739215 | E_var:     0.5163 | E_err:   0.011227
[2025-10-22 18:10:48] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -62.722997 | E_var:     0.6228 | E_err:   0.012331
[2025-10-22 18:11:02] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -62.726844 | E_var:     0.4957 | E_err:   0.011001
[2025-10-22 18:11:15] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -62.719265 | E_var:     0.5489 | E_err:   0.011577
[2025-10-22 18:11:28] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -62.742401 | E_var:     0.5225 | E_err:   0.011294
[2025-10-22 18:11:41] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -62.734026 | E_var:     0.5215 | E_err:   0.011283
[2025-10-22 18:11:54] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -62.738992 | E_var:     0.5087 | E_err:   0.011144
[2025-10-22 18:12:07] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -62.731919 | E_var:     0.5427 | E_err:   0.011511
[2025-10-22 18:12:21] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -62.730749 | E_var:     0.4422 | E_err:   0.010390
[2025-10-22 18:12:34] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -62.741444 | E_var:     0.5003 | E_err:   0.011052
[2025-10-22 18:12:47] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -62.720268 | E_var:     0.4504 | E_err:   0.010487
[2025-10-22 18:13:00] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -62.738856 | E_var:     0.5129 | E_err:   0.011191
[2025-10-22 18:13:13] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -62.730918 | E_var:     0.4973 | E_err:   0.011019
[2025-10-22 18:13:27] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -62.734231 | E_var:     0.4846 | E_err:   0.010877
[2025-10-22 18:13:40] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -62.739160 | E_var:     0.6665 | E_err:   0.012756
[2025-10-22 18:13:53] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -62.756828 | E_var:     0.6011 | E_err:   0.012114
[2025-10-22 18:14:06] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -62.724389 | E_var:     0.6612 | E_err:   0.012705
[2025-10-22 18:14:19] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -62.746744 | E_var:     0.5070 | E_err:   0.011126
[2025-10-22 18:14:32] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -62.742122 | E_var:     0.5735 | E_err:   0.011833
[2025-10-22 18:14:46] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -62.747223 | E_var:     0.4750 | E_err:   0.010769
[2025-10-22 18:14:59] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -62.734785 | E_var:     0.5446 | E_err:   0.011531
[2025-10-22 18:15:12] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -62.740856 | E_var:     0.5313 | E_err:   0.011389
[2025-10-22 18:15:25] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -62.720487 | E_var:     0.5893 | E_err:   0.011995
[2025-10-22 18:15:38] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -62.751551 | E_var:     0.6407 | E_err:   0.012506
[2025-10-22 18:15:51] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -62.731461 | E_var:     0.4971 | E_err:   0.011016
[2025-10-22 18:16:05] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -62.737896 | E_var:     0.5799 | E_err:   0.011899
[2025-10-22 18:16:18] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -62.739382 | E_var:     0.4902 | E_err:   0.010940
[2025-10-22 18:16:31] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -62.729418 | E_var:     0.4526 | E_err:   0.010512
[2025-10-22 18:16:44] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -62.740985 | E_var:     0.5194 | E_err:   0.011261
[2025-10-22 18:16:57] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -62.745655 | E_var:     0.5833 | E_err:   0.011934
[2025-10-22 18:17:11] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -62.751475 | E_var:     0.4674 | E_err:   0.010682
[2025-10-22 18:17:24] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -62.727162 | E_var:     0.5206 | E_err:   0.011274
[2025-10-22 18:17:37] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -62.730718 | E_var:     0.5604 | E_err:   0.011697
[2025-10-22 18:17:50] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -62.718828 | E_var:     0.4662 | E_err:   0.010669
[2025-10-22 18:18:03] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -62.730142 | E_var:     0.4820 | E_err:   0.010848
[2025-10-22 18:18:16] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -62.747941 | E_var:     0.5195 | E_err:   0.011261
[2025-10-22 18:18:30] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -62.732370 | E_var:     0.5163 | E_err:   0.011227
[2025-10-22 18:18:43] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -62.712132 | E_var:     0.7391 | E_err:   0.013433
[2025-10-22 18:18:56] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -62.753569 | E_var:     0.5381 | E_err:   0.011462
[2025-10-22 18:19:09] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -62.728508 | E_var:     0.5917 | E_err:   0.012019
[2025-10-22 18:19:22] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -62.721604 | E_var:     0.5747 | E_err:   0.011846
[2025-10-22 18:19:35] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -62.731857 | E_var:     0.4852 | E_err:   0.010884
[2025-10-22 18:19:49] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -62.718241 | E_var:     0.6060 | E_err:   0.012163
[2025-10-22 18:20:02] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -62.739053 | E_var:     0.5419 | E_err:   0.011502
[2025-10-22 18:20:15] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -62.754368 | E_var:     0.6179 | E_err:   0.012282
[2025-10-22 18:20:28] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -62.745748 | E_var:     0.5350 | E_err:   0.011429
[2025-10-22 18:20:41] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -62.736507 | E_var:     0.5767 | E_err:   0.011865
[2025-10-22 18:20:55] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -62.732723 | E_var:     0.5501 | E_err:   0.011589
[2025-10-22 18:21:08] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -62.740597 | E_var:     0.5724 | E_err:   0.011821
[2025-10-22 18:21:21] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -62.736462 | E_var:     0.5465 | E_err:   0.011551
[2025-10-22 18:21:34] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -62.756928 | E_var:     0.5147 | E_err:   0.011210
[2025-10-22 18:21:47] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -62.737867 | E_var:     0.5459 | E_err:   0.011544
[2025-10-22 18:22:00] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -62.723348 | E_var:     0.5660 | E_err:   0.011755
[2025-10-22 18:22:14] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -62.750900 | E_var:     0.4809 | E_err:   0.010836
[2025-10-22 18:22:27] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -62.752032 | E_var:     0.5044 | E_err:   0.011097
[2025-10-22 18:22:40] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -62.743187 | E_var:     0.5430 | E_err:   0.011514
[2025-10-22 18:22:53] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -62.743523 | E_var:     0.4866 | E_err:   0.010900
[2025-10-22 18:23:06] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -62.745949 | E_var:     0.4774 | E_err:   0.010796
[2025-10-22 18:23:20] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -62.737078 | E_var:     0.6274 | E_err:   0.012377
[2025-10-22 18:23:33] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -62.746585 | E_var:     0.4508 | E_err:   0.010491
[2025-10-22 18:23:46] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -62.736234 | E_var:     0.5002 | E_err:   0.011051
[2025-10-22 18:23:59] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -62.740624 | E_var:     0.7407 | E_err:   0.013447
[2025-10-22 18:24:12] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -62.711765 | E_var:     0.7786 | E_err:   0.013787
[2025-10-22 18:24:25] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -62.729287 | E_var:     0.6849 | E_err:   0.012931
[2025-10-22 18:24:39] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -62.748008 | E_var:     0.7528 | E_err:   0.013557
[2025-10-22 18:24:52] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -62.721472 | E_var:     0.5582 | E_err:   0.011673
[2025-10-22 18:25:05] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -62.733312 | E_var:     0.5372 | E_err:   0.011452
[2025-10-22 18:25:18] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -62.731981 | E_var:     0.6217 | E_err:   0.012320
[2025-10-22 18:25:31] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -62.747713 | E_var:     0.4616 | E_err:   0.010616
[2025-10-22 18:25:44] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -62.743846 | E_var:     0.5144 | E_err:   0.011207
[2025-10-22 18:25:58] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -62.744143 | E_var:     0.5032 | E_err:   0.011084
[2025-10-22 18:26:11] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -62.751401 | E_var:     0.5706 | E_err:   0.011803
[2025-10-22 18:26:24] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -62.727074 | E_var:     0.5992 | E_err:   0.012095
[2025-10-22 18:26:37] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -62.739841 | E_var:     0.4510 | E_err:   0.010493
[2025-10-22 18:26:37] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-22 18:26:50] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -62.736032 | E_var:     0.6550 | E_err:   0.012646
[2025-10-22 18:27:04] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -62.736555 | E_var:     0.5382 | E_err:   0.011463
[2025-10-22 18:27:17] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -62.735466 | E_var:     0.5219 | E_err:   0.011288
[2025-10-22 18:27:30] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -62.752024 | E_var:     0.5406 | E_err:   0.011488
[2025-10-22 18:27:44] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -62.739647 | E_var:     0.5651 | E_err:   0.011746
[2025-10-22 18:27:57] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -62.736918 | E_var:     0.4035 | E_err:   0.009925
[2025-10-22 18:28:10] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -62.755787 | E_var:     0.4752 | E_err:   0.010771
[2025-10-22 18:28:23] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -62.765295 | E_var:     0.5652 | E_err:   0.011746
[2025-10-22 18:28:37] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -62.741437 | E_var:     0.5312 | E_err:   0.011388
[2025-10-22 18:28:50] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -62.726893 | E_var:     0.5910 | E_err:   0.012012
[2025-10-22 18:29:03] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -62.728045 | E_var:     0.5132 | E_err:   0.011194
[2025-10-22 18:29:16] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -62.735019 | E_var:     1.1083 | E_err:   0.016449
[2025-10-22 18:29:29] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -62.756119 | E_var:     0.4594 | E_err:   0.010591
[2025-10-22 18:29:43] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -62.746079 | E_var:     0.5980 | E_err:   0.012083
[2025-10-22 18:29:56] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -62.733987 | E_var:     0.5348 | E_err:   0.011426
[2025-10-22 18:30:09] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -62.738552 | E_var:     0.5132 | E_err:   0.011193
[2025-10-22 18:30:22] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -62.733294 | E_var:     0.4760 | E_err:   0.010780
[2025-10-22 18:30:35] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -62.718016 | E_var:     0.6013 | E_err:   0.012116
[2025-10-22 18:30:48] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -62.752793 | E_var:     0.4547 | E_err:   0.010536
[2025-10-22 18:31:02] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -62.728145 | E_var:     0.5305 | E_err:   0.011380
[2025-10-22 18:31:15] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -62.714801 | E_var:     0.6500 | E_err:   0.012597
[2025-10-22 18:31:28] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -62.771764 | E_var:     0.6142 | E_err:   0.012245
[2025-10-22 18:31:41] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -62.747693 | E_var:     0.4028 | E_err:   0.009917
[2025-10-22 18:31:54] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -62.744464 | E_var:     0.5175 | E_err:   0.011240
[2025-10-22 18:32:07] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -62.743750 | E_var:     0.4587 | E_err:   0.010582
[2025-10-22 18:32:21] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -62.746673 | E_var:     0.4785 | E_err:   0.010809
[2025-10-22 18:32:34] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -62.757887 | E_var:     0.5880 | E_err:   0.011981
[2025-10-22 18:32:47] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -62.740765 | E_var:     0.5639 | E_err:   0.011734
[2025-10-22 18:33:00] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -62.733726 | E_var:     0.4934 | E_err:   0.010976
[2025-10-22 18:33:13] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -62.735594 | E_var:     0.4286 | E_err:   0.010229
[2025-10-22 18:33:27] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -62.744577 | E_var:     0.4903 | E_err:   0.010940
[2025-10-22 18:33:40] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -62.742678 | E_var:     0.4910 | E_err:   0.010949
[2025-10-22 18:33:53] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -62.741615 | E_var:     0.5074 | E_err:   0.011131
[2025-10-22 18:34:06] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -62.744309 | E_var:     0.4766 | E_err:   0.010787
[2025-10-22 18:34:19] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -62.746478 | E_var:     0.5217 | E_err:   0.011285
[2025-10-22 18:34:32] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -62.738985 | E_var:     0.4793 | E_err:   0.010818
[2025-10-22 18:34:46] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -62.738665 | E_var:     0.5241 | E_err:   0.011312
[2025-10-22 18:34:59] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -62.749335 | E_var:     0.5785 | E_err:   0.011884
[2025-10-22 18:35:12] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -62.744085 | E_var:     0.4676 | E_err:   0.010685
[2025-10-22 18:35:25] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -62.735831 | E_var:     0.5428 | E_err:   0.011511
[2025-10-22 18:35:38] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -62.744406 | E_var:     0.6125 | E_err:   0.012229
[2025-10-22 18:35:52] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -62.738489 | E_var:     0.5859 | E_err:   0.011960
[2025-10-22 18:36:05] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -62.721943 | E_var:     0.4635 | E_err:   0.010637
[2025-10-22 18:36:18] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -62.758856 | E_var:     0.4968 | E_err:   0.011013
[2025-10-22 18:36:31] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -62.753112 | E_var:     0.5242 | E_err:   0.011313
[2025-10-22 18:36:44] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -62.751615 | E_var:     0.5337 | E_err:   0.011415
[2025-10-22 18:36:57] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -62.748202 | E_var:     0.6011 | E_err:   0.012114
[2025-10-22 18:37:11] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -62.741467 | E_var:     0.4999 | E_err:   0.011047
[2025-10-22 18:37:24] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -62.757477 | E_var:     0.4556 | E_err:   0.010546
[2025-10-22 18:37:37] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -62.742286 | E_var:     0.5302 | E_err:   0.011378
[2025-10-22 18:37:50] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -62.739817 | E_var:     0.5768 | E_err:   0.011867
[2025-10-22 18:38:03] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -62.744728 | E_var:     0.5120 | E_err:   0.011180
[2025-10-22 18:38:16] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -62.753251 | E_var:     0.5457 | E_err:   0.011543
[2025-10-22 18:38:30] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -62.756642 | E_var:     0.4718 | E_err:   0.010733
[2025-10-22 18:38:43] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -62.738928 | E_var:     0.6425 | E_err:   0.012525
[2025-10-22 18:38:56] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -62.753331 | E_var:     0.5144 | E_err:   0.011207
[2025-10-22 18:39:09] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -62.744748 | E_var:     0.5895 | E_err:   0.011996
[2025-10-22 18:39:22] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -62.739844 | E_var:     0.4769 | E_err:   0.010791
[2025-10-22 18:39:36] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -62.740364 | E_var:     0.5198 | E_err:   0.011265
[2025-10-22 18:39:49] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -62.728556 | E_var:     0.5156 | E_err:   0.011220
[2025-10-22 18:40:02] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -62.749270 | E_var:     0.5318 | E_err:   0.011394
[2025-10-22 18:40:15] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -62.728747 | E_var:     0.5376 | E_err:   0.011456
[2025-10-22 18:40:28] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -62.735247 | E_var:     0.5757 | E_err:   0.011856
[2025-10-22 18:40:41] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -62.753253 | E_var:     0.5521 | E_err:   0.011610
[2025-10-22 18:40:55] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -62.719383 | E_var:     0.5578 | E_err:   0.011670
[2025-10-22 18:41:08] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -62.756770 | E_var:     0.5193 | E_err:   0.011260
[2025-10-22 18:41:21] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -62.729864 | E_var:     0.5103 | E_err:   0.011162
[2025-10-22 18:41:34] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -62.735495 | E_var:     0.4546 | E_err:   0.010535
[2025-10-22 18:41:47] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -62.719128 | E_var:     0.9493 | E_err:   0.015224
[2025-10-22 18:42:01] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -62.744212 | E_var:     0.5309 | E_err:   0.011385
[2025-10-22 18:42:14] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -62.742182 | E_var:     0.4842 | E_err:   0.010873
[2025-10-22 18:42:27] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -62.743490 | E_var:     0.5283 | E_err:   0.011357
[2025-10-22 18:42:40] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -62.736913 | E_var:     0.4939 | E_err:   0.010981
[2025-10-22 18:42:53] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -62.735812 | E_var:     0.5759 | E_err:   0.011858
[2025-10-22 18:43:06] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -62.750025 | E_var:     0.4830 | E_err:   0.010859
[2025-10-22 18:43:20] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -62.733625 | E_var:     0.5887 | E_err:   0.011989
[2025-10-22 18:43:33] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -62.742422 | E_var:     0.4805 | E_err:   0.010831
[2025-10-22 18:43:46] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -62.739672 | E_var:     0.5814 | E_err:   0.011914
[2025-10-22 18:43:59] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -62.724897 | E_var:     0.5064 | E_err:   0.011119
[2025-10-22 18:44:12] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -62.730746 | E_var:     0.6382 | E_err:   0.012482
[2025-10-22 18:44:25] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -62.759757 | E_var:     0.5096 | E_err:   0.011154
[2025-10-22 18:44:39] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -62.748408 | E_var:     0.4803 | E_err:   0.010828
[2025-10-22 18:44:52] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -62.723681 | E_var:     0.5312 | E_err:   0.011388
[2025-10-22 18:45:05] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -62.721538 | E_var:     0.5268 | E_err:   0.011340
[2025-10-22 18:45:18] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -62.751430 | E_var:     0.4672 | E_err:   0.010680
[2025-10-22 18:45:31] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -62.739876 | E_var:     0.7204 | E_err:   0.013262
[2025-10-22 18:45:45] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -62.741909 | E_var:     0.5036 | E_err:   0.011088
[2025-10-22 18:45:58] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -62.722491 | E_var:     0.5722 | E_err:   0.011820
[2025-10-22 18:46:11] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -62.755932 | E_var:     0.4708 | E_err:   0.010721
[2025-10-22 18:46:24] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -62.725375 | E_var:     0.5547 | E_err:   0.011637
[2025-10-22 18:46:37] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -62.752532 | E_var:     0.5348 | E_err:   0.011426
[2025-10-22 18:46:50] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -62.758828 | E_var:     0.5365 | E_err:   0.011445
[2025-10-22 18:47:04] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -62.745681 | E_var:     0.4981 | E_err:   0.011027
[2025-10-22 18:47:17] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -62.737784 | E_var:     0.5660 | E_err:   0.011755
[2025-10-22 18:47:30] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -62.743808 | E_var:     0.5782 | E_err:   0.011881
[2025-10-22 18:47:43] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -62.739423 | E_var:     0.5478 | E_err:   0.011564
[2025-10-22 18:47:56] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -62.737760 | E_var:     0.5168 | E_err:   0.011233
[2025-10-22 18:48:09] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -62.736830 | E_var:     0.4936 | E_err:   0.010978
[2025-10-22 18:48:23] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -62.737602 | E_var:     0.5307 | E_err:   0.011382
[2025-10-22 18:48:36] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -62.751904 | E_var:     0.4882 | E_err:   0.010917
[2025-10-22 18:48:49] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -62.728916 | E_var:     0.5247 | E_err:   0.011318
[2025-10-22 18:49:02] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -62.733854 | E_var:     0.4854 | E_err:   0.010886
[2025-10-22 18:49:15] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -62.719215 | E_var:     0.4866 | E_err:   0.010900
[2025-10-22 18:49:29] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -62.738570 | E_var:     0.5174 | E_err:   0.011239
[2025-10-22 18:49:42] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -62.745494 | E_var:     0.5486 | E_err:   0.011573
[2025-10-22 18:49:42] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-22 18:49:55] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -62.734239 | E_var:     0.5291 | E_err:   0.011366
[2025-10-22 18:50:08] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -62.735300 | E_var:     0.4730 | E_err:   0.010746
[2025-10-22 18:50:21] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -62.726202 | E_var:     0.5363 | E_err:   0.011442
[2025-10-22 18:50:35] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -62.711571 | E_var:     0.5165 | E_err:   0.011229
[2025-10-22 18:50:48] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -62.728912 | E_var:     0.5673 | E_err:   0.011769
[2025-10-22 18:51:01] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -62.754540 | E_var:     0.5365 | E_err:   0.011445
[2025-10-22 18:51:14] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -62.739096 | E_var:     0.4557 | E_err:   0.010547
[2025-10-22 18:51:27] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -62.744490 | E_var:     0.5328 | E_err:   0.011405
[2025-10-22 18:51:40] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -62.732646 | E_var:     0.4807 | E_err:   0.010833
[2025-10-22 18:51:54] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -62.740368 | E_var:     0.4703 | E_err:   0.010716
[2025-10-22 18:52:07] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -62.744271 | E_var:     0.6125 | E_err:   0.012228
[2025-10-22 18:52:20] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -62.753847 | E_var:     0.5780 | E_err:   0.011879
[2025-10-22 18:52:33] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -62.750002 | E_var:     0.6062 | E_err:   0.012166
[2025-10-22 18:52:46] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -62.734049 | E_var:     0.5424 | E_err:   0.011507
[2025-10-22 18:52:59] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -62.746584 | E_var:     0.7054 | E_err:   0.013124
[2025-10-22 18:53:13] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -62.753776 | E_var:     0.5692 | E_err:   0.011788
[2025-10-22 18:53:26] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -62.747281 | E_var:     0.4662 | E_err:   0.010668
[2025-10-22 18:53:39] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -62.724938 | E_var:     0.4888 | E_err:   0.010924
[2025-10-22 18:53:52] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -62.752217 | E_var:     0.5576 | E_err:   0.011668
[2025-10-22 18:54:05] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -62.759182 | E_var:     0.4782 | E_err:   0.010805
[2025-10-22 18:54:19] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -62.733989 | E_var:     0.5213 | E_err:   0.011281
[2025-10-22 18:54:32] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -62.729107 | E_var:     0.5237 | E_err:   0.011307
[2025-10-22 18:54:45] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -62.759619 | E_var:     0.5085 | E_err:   0.011142
[2025-10-22 18:54:58] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -62.743986 | E_var:     0.5303 | E_err:   0.011378
[2025-10-22 18:55:11] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -62.750082 | E_var:     0.4982 | E_err:   0.011029
[2025-10-22 18:55:24] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -62.739465 | E_var:     0.4731 | E_err:   0.010747
[2025-10-22 18:55:38] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -62.733599 | E_var:     0.5128 | E_err:   0.011189
[2025-10-22 18:55:51] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -62.734626 | E_var:     0.5450 | E_err:   0.011535
[2025-10-22 18:56:04] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -62.754060 | E_var:     0.7533 | E_err:   0.013561
[2025-10-22 18:56:17] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -62.734449 | E_var:     0.5208 | E_err:   0.011276
[2025-10-22 18:56:17] 🔄 RESTART #2 | Period: 600
[2025-10-22 18:56:30] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -62.742689 | E_var:     0.5704 | E_err:   0.011801
[2025-10-22 18:56:43] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -62.718307 | E_var:     0.6129 | E_err:   0.012232
[2025-10-22 18:56:57] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -62.735267 | E_var:     0.4849 | E_err:   0.010880
[2025-10-22 18:57:10] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -62.730594 | E_var:     0.5188 | E_err:   0.011254
[2025-10-22 18:57:23] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -62.740107 | E_var:     0.6342 | E_err:   0.012443
[2025-10-22 18:57:36] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -62.745032 | E_var:     0.5068 | E_err:   0.011123
[2025-10-22 18:57:49] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -62.750229 | E_var:     0.5634 | E_err:   0.011728
[2025-10-22 18:58:03] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -62.729757 | E_var:     0.5293 | E_err:   0.011368
[2025-10-22 18:58:16] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -62.738347 | E_var:     0.4875 | E_err:   0.010909
[2025-10-22 18:58:29] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -62.713889 | E_var:     0.5159 | E_err:   0.011223
[2025-10-22 18:58:42] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -62.760899 | E_var:     0.5407 | E_err:   0.011490
[2025-10-22 18:58:55] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -62.720472 | E_var:     0.7277 | E_err:   0.013329
[2025-10-22 18:59:08] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -62.725284 | E_var:     0.5243 | E_err:   0.011314
[2025-10-22 18:59:22] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -62.727320 | E_var:     0.6135 | E_err:   0.012238
[2025-10-22 18:59:35] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -62.724640 | E_var:     0.6090 | E_err:   0.012193
[2025-10-22 18:59:48] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -62.738138 | E_var:     0.5632 | E_err:   0.011726
[2025-10-22 19:00:01] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -62.724312 | E_var:     0.4483 | E_err:   0.010462
[2025-10-22 19:00:14] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -62.749549 | E_var:     0.6126 | E_err:   0.012229
[2025-10-22 19:00:28] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -62.742559 | E_var:     0.5123 | E_err:   0.011184
[2025-10-22 19:00:41] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -62.746111 | E_var:     0.5058 | E_err:   0.011113
[2025-10-22 19:00:54] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -62.737015 | E_var:     0.5866 | E_err:   0.011967
[2025-10-22 19:01:07] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -62.752411 | E_var:     0.5646 | E_err:   0.011740
[2025-10-22 19:01:20] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -62.732215 | E_var:     0.4909 | E_err:   0.010947
[2025-10-22 19:01:33] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -62.733498 | E_var:     0.5570 | E_err:   0.011661
[2025-10-22 19:01:47] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -62.736546 | E_var:     0.4576 | E_err:   0.010570
[2025-10-22 19:02:00] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -62.744593 | E_var:     0.5450 | E_err:   0.011535
[2025-10-22 19:02:13] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -62.741459 | E_var:     0.6079 | E_err:   0.012183
[2025-10-22 19:02:26] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -62.727766 | E_var:     0.5623 | E_err:   0.011717
[2025-10-22 19:02:39] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -62.737282 | E_var:     0.5519 | E_err:   0.011608
[2025-10-22 19:02:52] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -62.765197 | E_var:     0.7717 | E_err:   0.013726
[2025-10-22 19:03:06] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -62.744323 | E_var:     0.5388 | E_err:   0.011469
[2025-10-22 19:03:19] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -62.738126 | E_var:     0.4615 | E_err:   0.010614
[2025-10-22 19:03:32] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -62.734135 | E_var:     0.6620 | E_err:   0.012713
[2025-10-22 19:03:45] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -62.736292 | E_var:     0.4539 | E_err:   0.010527
[2025-10-22 19:03:58] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -62.747230 | E_var:     0.4942 | E_err:   0.010984
[2025-10-22 19:04:12] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -62.713471 | E_var:     0.6802 | E_err:   0.012887
[2025-10-22 19:04:25] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -62.735467 | E_var:     0.5450 | E_err:   0.011535
[2025-10-22 19:04:38] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -62.746100 | E_var:     0.4953 | E_err:   0.010997
[2025-10-22 19:04:51] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -62.712762 | E_var:     0.5128 | E_err:   0.011189
[2025-10-22 19:05:04] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -62.746325 | E_var:     0.4498 | E_err:   0.010479
[2025-10-22 19:05:17] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -62.744315 | E_var:     0.5182 | E_err:   0.011248
[2025-10-22 19:05:31] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -62.742640 | E_var:     0.5810 | E_err:   0.011910
[2025-10-22 19:05:44] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -62.721236 | E_var:     0.7521 | E_err:   0.013551
[2025-10-22 19:05:57] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -62.742375 | E_var:     0.5463 | E_err:   0.011549
[2025-10-22 19:06:10] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -62.746195 | E_var:     2.1613 | E_err:   0.022971
[2025-10-22 19:06:23] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -62.747892 | E_var:     0.5452 | E_err:   0.011537
[2025-10-22 19:06:36] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -62.732526 | E_var:     0.5858 | E_err:   0.011959
[2025-10-22 19:06:50] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -62.732172 | E_var:     0.5161 | E_err:   0.011225
[2025-10-22 19:07:03] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -62.742589 | E_var:     0.5854 | E_err:   0.011955
[2025-10-22 19:07:16] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -62.744083 | E_var:     0.4509 | E_err:   0.010492
[2025-10-22 19:07:29] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -62.734592 | E_var:     0.5021 | E_err:   0.011072
[2025-10-22 19:07:42] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -62.750991 | E_var:     0.5257 | E_err:   0.011329
[2025-10-22 19:07:56] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -62.729334 | E_var:     0.5490 | E_err:   0.011578
[2025-10-22 19:08:09] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -62.728358 | E_var:     0.5298 | E_err:   0.011372
[2025-10-22 19:08:22] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -62.744095 | E_var:     0.5796 | E_err:   0.011896
[2025-10-22 19:08:35] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -62.742396 | E_var:     0.4527 | E_err:   0.010513
[2025-10-22 19:08:48] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -62.737897 | E_var:     0.4524 | E_err:   0.010510
[2025-10-22 19:09:01] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -62.722683 | E_var:     0.5043 | E_err:   0.011096
[2025-10-22 19:09:15] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -62.745507 | E_var:     0.4508 | E_err:   0.010490
[2025-10-22 19:09:28] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -62.725955 | E_var:     0.4931 | E_err:   0.010972
[2025-10-22 19:09:41] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -62.746588 | E_var:     0.5178 | E_err:   0.011243
[2025-10-22 19:09:54] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -62.743578 | E_var:     0.5081 | E_err:   0.011138
[2025-10-22 19:10:07] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -62.747752 | E_var:     0.5167 | E_err:   0.011231
[2025-10-22 19:10:20] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -62.741149 | E_var:     0.6256 | E_err:   0.012359
[2025-10-22 19:10:34] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -62.750438 | E_var:     0.5108 | E_err:   0.011167
[2025-10-22 19:10:47] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -62.745640 | E_var:     0.4862 | E_err:   0.010895
[2025-10-22 19:11:00] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -62.725840 | E_var:     0.8944 | E_err:   0.014777
[2025-10-22 19:11:13] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -62.719348 | E_var:     0.5733 | E_err:   0.011831
[2025-10-22 19:11:26] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -62.740764 | E_var:     0.5437 | E_err:   0.011521
[2025-10-22 19:11:39] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -62.754357 | E_var:     0.5032 | E_err:   0.011083
[2025-10-22 19:11:53] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -62.735881 | E_var:     0.4904 | E_err:   0.010942
[2025-10-22 19:12:06] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -62.750417 | E_var:     0.5512 | E_err:   0.011600
[2025-10-22 19:12:19] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -62.747951 | E_var:     0.6238 | E_err:   0.012341
[2025-10-22 19:12:32] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -62.734530 | E_var:     0.7101 | E_err:   0.013166
[2025-10-22 19:12:45] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -62.749570 | E_var:     0.5062 | E_err:   0.011117
[2025-10-22 19:12:45] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-22 19:12:59] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -62.736716 | E_var:     0.6088 | E_err:   0.012192
[2025-10-22 19:13:12] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -62.750902 | E_var:     0.4539 | E_err:   0.010527
[2025-10-22 19:13:25] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -62.741147 | E_var:     0.4703 | E_err:   0.010715
[2025-10-22 19:13:38] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -62.739328 | E_var:     0.5729 | E_err:   0.011827
[2025-10-22 19:13:51] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -62.742700 | E_var:     0.4971 | E_err:   0.011016
[2025-10-22 19:14:04] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -62.728413 | E_var:     0.4687 | E_err:   0.010697
[2025-10-22 19:14:18] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -62.728363 | E_var:     0.7774 | E_err:   0.013777
[2025-10-22 19:14:31] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -62.734049 | E_var:     0.5132 | E_err:   0.011193
[2025-10-22 19:14:44] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -62.746440 | E_var:     0.4709 | E_err:   0.010723
[2025-10-22 19:14:57] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -62.730443 | E_var:     0.5157 | E_err:   0.011221
[2025-10-22 19:15:10] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -62.745123 | E_var:     0.8563 | E_err:   0.014459
[2025-10-22 19:15:24] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -62.743200 | E_var:     0.6773 | E_err:   0.012859
[2025-10-22 19:15:37] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -62.733063 | E_var:     0.5236 | E_err:   0.011307
[2025-10-22 19:15:50] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -62.742825 | E_var:     0.5588 | E_err:   0.011680
[2025-10-22 19:16:03] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -62.711255 | E_var:     1.0801 | E_err:   0.016239
[2025-10-22 19:16:16] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -62.746169 | E_var:     0.4601 | E_err:   0.010599
[2025-10-22 19:16:29] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -62.749302 | E_var:     0.4964 | E_err:   0.011009
[2025-10-22 19:16:43] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -62.736418 | E_var:     0.4395 | E_err:   0.010359
[2025-10-22 19:16:56] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -62.730444 | E_var:     0.5274 | E_err:   0.011347
[2025-10-22 19:17:09] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -62.734981 | E_var:     0.5200 | E_err:   0.011267
[2025-10-22 19:17:22] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -62.743323 | E_var:     0.8078 | E_err:   0.014044
[2025-10-22 19:17:35] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -62.750448 | E_var:     0.4233 | E_err:   0.010166
[2025-10-22 19:17:48] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -62.734111 | E_var:     0.5065 | E_err:   0.011120
[2025-10-22 19:18:02] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -62.756319 | E_var:     0.5022 | E_err:   0.011073
[2025-10-22 19:18:15] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -62.731321 | E_var:     0.4597 | E_err:   0.010594
[2025-10-22 19:18:28] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -62.739797 | E_var:     0.4547 | E_err:   0.010536
[2025-10-22 19:18:41] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -62.757957 | E_var:     0.5028 | E_err:   0.011080
[2025-10-22 19:18:54] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -62.738532 | E_var:     0.5592 | E_err:   0.011684
[2025-10-22 19:19:08] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -62.716957 | E_var:     0.4909 | E_err:   0.010948
[2025-10-22 19:19:21] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -62.741368 | E_var:     0.5046 | E_err:   0.011099
[2025-10-22 19:19:34] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -62.736046 | E_var:     0.5831 | E_err:   0.011931
[2025-10-22 19:19:47] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -62.741609 | E_var:     0.4975 | E_err:   0.011021
[2025-10-22 19:20:00] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -62.755259 | E_var:     0.4584 | E_err:   0.010579
[2025-10-22 19:20:13] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -62.726163 | E_var:     0.7684 | E_err:   0.013696
[2025-10-22 19:20:27] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -62.737540 | E_var:     0.5322 | E_err:   0.011399
[2025-10-22 19:20:40] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -62.758216 | E_var:     0.4874 | E_err:   0.010908
[2025-10-22 19:20:53] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -62.739004 | E_var:     0.6459 | E_err:   0.012558
[2025-10-22 19:21:06] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -62.732998 | E_var:     0.4927 | E_err:   0.010967
[2025-10-22 19:21:19] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -62.724410 | E_var:     0.6149 | E_err:   0.012252
[2025-10-22 19:21:32] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -62.748488 | E_var:     0.5531 | E_err:   0.011620
[2025-10-22 19:21:46] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -62.745383 | E_var:     0.5077 | E_err:   0.011133
[2025-10-22 19:21:59] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -62.708948 | E_var:     0.6122 | E_err:   0.012226
[2025-10-22 19:22:12] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -62.713197 | E_var:     0.5604 | E_err:   0.011697
[2025-10-22 19:22:25] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -62.737268 | E_var:     0.4206 | E_err:   0.010133
[2025-10-22 19:22:38] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -62.724126 | E_var:     0.4343 | E_err:   0.010298
[2025-10-22 19:22:52] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -62.728755 | E_var:     0.5031 | E_err:   0.011083
[2025-10-22 19:23:05] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -62.741635 | E_var:     0.6469 | E_err:   0.012567
[2025-10-22 19:23:18] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -62.735510 | E_var:     0.5775 | E_err:   0.011874
[2025-10-22 19:23:31] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -62.743529 | E_var:     0.5323 | E_err:   0.011400
[2025-10-22 19:23:44] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -62.759397 | E_var:     0.5266 | E_err:   0.011338
[2025-10-22 19:23:57] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -62.779559 | E_var:     0.5713 | E_err:   0.011810
[2025-10-22 19:24:11] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -62.730840 | E_var:     0.6361 | E_err:   0.012462
[2025-10-22 19:24:24] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -62.733685 | E_var:     0.5438 | E_err:   0.011523
[2025-10-22 19:24:37] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -62.761298 | E_var:     0.5692 | E_err:   0.011788
[2025-10-22 19:24:50] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -62.740427 | E_var:     0.5349 | E_err:   0.011428
[2025-10-22 19:25:03] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -62.735182 | E_var:     0.5076 | E_err:   0.011132
[2025-10-22 19:25:16] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -62.721073 | E_var:     0.5340 | E_err:   0.011418
[2025-10-22 19:25:30] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -62.745759 | E_var:     0.4991 | E_err:   0.011039
[2025-10-22 19:25:43] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -62.724958 | E_var:     0.5171 | E_err:   0.011236
[2025-10-22 19:25:56] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -62.753735 | E_var:     0.5911 | E_err:   0.012013
[2025-10-22 19:26:10] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -62.753518 | E_var:     0.5940 | E_err:   0.012042
[2025-10-22 19:26:23] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -62.755167 | E_var:     0.4770 | E_err:   0.010792
[2025-10-22 19:26:36] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -62.733437 | E_var:     0.6134 | E_err:   0.012237
[2025-10-22 19:26:49] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -62.731836 | E_var:     0.7609 | E_err:   0.013630
[2025-10-22 19:27:02] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -62.746989 | E_var:     0.6503 | E_err:   0.012600
[2025-10-22 19:27:15] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -62.749205 | E_var:     0.4505 | E_err:   0.010487
[2025-10-22 19:27:29] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -62.757486 | E_var:     0.5165 | E_err:   0.011229
[2025-10-22 19:27:42] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -62.721937 | E_var:     0.5937 | E_err:   0.012040
[2025-10-22 19:27:55] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -62.746868 | E_var:     0.5053 | E_err:   0.011107
[2025-10-22 19:28:08] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -62.750542 | E_var:     0.4913 | E_err:   0.010953
[2025-10-22 19:28:21] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -62.742926 | E_var:     0.4280 | E_err:   0.010222
[2025-10-22 19:28:34] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -62.728117 | E_var:     0.5506 | E_err:   0.011594
[2025-10-22 19:28:48] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -62.740685 | E_var:     0.4907 | E_err:   0.010945
[2025-10-22 19:29:01] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -62.739797 | E_var:     0.5492 | E_err:   0.011579
[2025-10-22 19:29:14] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -62.749305 | E_var:     0.6270 | E_err:   0.012372
[2025-10-22 19:29:27] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -62.728785 | E_var:     0.4819 | E_err:   0.010846
[2025-10-22 19:29:40] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -62.738527 | E_var:     0.5439 | E_err:   0.011523
[2025-10-22 19:29:54] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -62.735016 | E_var:     0.5211 | E_err:   0.011279
[2025-10-22 19:30:07] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -62.727701 | E_var:     0.6041 | E_err:   0.012145
[2025-10-22 19:30:20] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -62.731354 | E_var:     0.4843 | E_err:   0.010873
[2025-10-22 19:30:33] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -62.735654 | E_var:     0.5716 | E_err:   0.011813
[2025-10-22 19:30:46] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -62.759750 | E_var:     0.4717 | E_err:   0.010731
[2025-10-22 19:30:59] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -62.739720 | E_var:     0.6919 | E_err:   0.012997
[2025-10-22 19:31:13] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -62.708505 | E_var:     0.9310 | E_err:   0.015077
[2025-10-22 19:31:26] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -62.742130 | E_var:     0.5722 | E_err:   0.011820
[2025-10-22 19:31:39] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -62.735292 | E_var:     0.6148 | E_err:   0.012251
[2025-10-22 19:31:52] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -62.745099 | E_var:     0.5340 | E_err:   0.011418
[2025-10-22 19:32:05] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -62.745353 | E_var:     0.6563 | E_err:   0.012659
[2025-10-22 19:32:18] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -62.716649 | E_var:     0.5388 | E_err:   0.011470
[2025-10-22 19:32:32] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -62.763169 | E_var:     0.6948 | E_err:   0.013025
[2025-10-22 19:32:45] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -62.729680 | E_var:     0.6682 | E_err:   0.012773
[2025-10-22 19:32:58] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -62.732480 | E_var:     0.5563 | E_err:   0.011654
[2025-10-22 19:33:11] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -62.736390 | E_var:     0.4845 | E_err:   0.010876
[2025-10-22 19:33:24] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -62.749871 | E_var:     0.5492 | E_err:   0.011579
[2025-10-22 19:33:38] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -62.734263 | E_var:     0.4559 | E_err:   0.010550
[2025-10-22 19:33:51] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -62.755364 | E_var:     0.4347 | E_err:   0.010302
[2025-10-22 19:34:04] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -62.755761 | E_var:     0.4695 | E_err:   0.010706
[2025-10-22 19:34:17] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -62.746465 | E_var:     0.4979 | E_err:   0.011025
[2025-10-22 19:34:30] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -62.748033 | E_var:     0.4418 | E_err:   0.010386
[2025-10-22 19:34:43] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -62.750581 | E_var:     0.4659 | E_err:   0.010665
[2025-10-22 19:34:57] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -62.730247 | E_var:     0.5296 | E_err:   0.011370
[2025-10-22 19:35:10] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -62.732700 | E_var:     0.5213 | E_err:   0.011281
[2025-10-22 19:35:23] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -62.748822 | E_var:     0.6151 | E_err:   0.012255
[2025-10-22 19:35:36] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -62.724716 | E_var:     0.5112 | E_err:   0.011172
[2025-10-22 19:35:49] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -62.753104 | E_var:     0.9183 | E_err:   0.014973
[2025-10-22 19:35:49] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-22 19:36:02] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -62.746967 | E_var:     0.4909 | E_err:   0.010947
[2025-10-22 19:36:16] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -62.743727 | E_var:     0.4643 | E_err:   0.010647
[2025-10-22 19:36:29] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -62.722010 | E_var:     0.5078 | E_err:   0.011135
[2025-10-22 19:36:42] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -62.733094 | E_var:     0.5304 | E_err:   0.011379
[2025-10-22 19:36:55] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -62.746745 | E_var:     0.5501 | E_err:   0.011589
[2025-10-22 19:37:08] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -62.744199 | E_var:     0.5215 | E_err:   0.011284
[2025-10-22 19:37:22] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -62.761956 | E_var:     0.7337 | E_err:   0.013384
[2025-10-22 19:37:35] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -62.722801 | E_var:     0.5709 | E_err:   0.011806
[2025-10-22 19:37:48] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -62.745312 | E_var:     0.4795 | E_err:   0.010820
[2025-10-22 19:38:01] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -62.746022 | E_var:     0.4958 | E_err:   0.011002
[2025-10-22 19:38:14] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -62.759916 | E_var:     0.4265 | E_err:   0.010204
[2025-10-22 19:38:27] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -62.734774 | E_var:     0.4480 | E_err:   0.010459
[2025-10-22 19:38:41] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -62.769137 | E_var:     0.4925 | E_err:   0.010966
[2025-10-22 19:38:54] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -62.744176 | E_var:     0.9564 | E_err:   0.015281
[2025-10-22 19:39:07] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -62.721644 | E_var:     0.4680 | E_err:   0.010689
[2025-10-22 19:39:20] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -62.740060 | E_var:     0.5334 | E_err:   0.011412
[2025-10-22 19:39:33] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -62.730374 | E_var:     0.4648 | E_err:   0.010653
[2025-10-22 19:39:46] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -62.738296 | E_var:     0.4721 | E_err:   0.010736
[2025-10-22 19:40:00] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -62.733432 | E_var:     0.5074 | E_err:   0.011130
[2025-10-22 19:40:13] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -62.748598 | E_var:     0.4492 | E_err:   0.010473
[2025-10-22 19:40:26] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -62.733642 | E_var:     0.5165 | E_err:   0.011230
[2025-10-22 19:40:39] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -62.719557 | E_var:     0.5422 | E_err:   0.011505
[2025-10-22 19:40:52] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -62.736194 | E_var:     0.5487 | E_err:   0.011574
[2025-10-22 19:41:06] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -62.745202 | E_var:     0.5342 | E_err:   0.011421
[2025-10-22 19:41:19] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -62.752022 | E_var:     0.4183 | E_err:   0.010106
[2025-10-22 19:41:32] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -62.738277 | E_var:     0.5698 | E_err:   0.011795
[2025-10-22 19:41:45] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -62.740243 | E_var:     0.4898 | E_err:   0.010935
[2025-10-22 19:41:58] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -62.742676 | E_var:     0.5771 | E_err:   0.011870
[2025-10-22 19:42:11] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -62.732979 | E_var:     0.6206 | E_err:   0.012310
[2025-10-22 19:42:25] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -62.746255 | E_var:     0.4977 | E_err:   0.011023
[2025-10-22 19:42:38] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -62.738756 | E_var:     0.4699 | E_err:   0.010711
[2025-10-22 19:42:51] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -62.718918 | E_var:     0.4611 | E_err:   0.010610
[2025-10-22 19:43:04] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -62.742555 | E_var:     0.4950 | E_err:   0.010993
[2025-10-22 19:43:17] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -62.752756 | E_var:     0.6029 | E_err:   0.012132
[2025-10-22 19:43:30] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -62.747854 | E_var:     0.4478 | E_err:   0.010456
[2025-10-22 19:43:44] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -62.740057 | E_var:     0.4493 | E_err:   0.010474
[2025-10-22 19:43:57] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -62.728050 | E_var:     0.5687 | E_err:   0.011783
[2025-10-22 19:44:10] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -62.749690 | E_var:     0.5248 | E_err:   0.011319
[2025-10-22 19:44:23] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -62.730168 | E_var:     0.5214 | E_err:   0.011283
[2025-10-22 19:44:36] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -62.745265 | E_var:     0.5535 | E_err:   0.011625
[2025-10-22 19:44:49] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -62.741399 | E_var:     0.4659 | E_err:   0.010665
[2025-10-22 19:45:03] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -62.754817 | E_var:     0.5242 | E_err:   0.011312
[2025-10-22 19:45:16] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -62.724517 | E_var:     0.5354 | E_err:   0.011433
[2025-10-22 19:45:29] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -62.746264 | E_var:     0.5854 | E_err:   0.011955
[2025-10-22 19:45:42] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -62.732009 | E_var:     0.5684 | E_err:   0.011780
[2025-10-22 19:45:55] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -62.725444 | E_var:     0.5062 | E_err:   0.011117
[2025-10-22 19:46:09] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -62.744843 | E_var:     0.4778 | E_err:   0.010800
[2025-10-22 19:46:22] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -62.765069 | E_var:     0.6222 | E_err:   0.012324
[2025-10-22 19:46:35] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -62.733529 | E_var:     0.5817 | E_err:   0.011917
[2025-10-22 19:46:48] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -62.731853 | E_var:     0.6101 | E_err:   0.012204
[2025-10-22 19:47:01] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -62.733868 | E_var:     0.5316 | E_err:   0.011392
[2025-10-22 19:47:14] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -62.751286 | E_var:     0.7099 | E_err:   0.013165
[2025-10-22 19:47:28] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -62.729476 | E_var:     0.5516 | E_err:   0.011605
[2025-10-22 19:47:41] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -62.740416 | E_var:     0.6117 | E_err:   0.012221
[2025-10-22 19:47:54] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -62.732253 | E_var:     0.5163 | E_err:   0.011227
[2025-10-22 19:48:07] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -62.745802 | E_var:     0.4756 | E_err:   0.010775
[2025-10-22 19:48:20] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -62.724706 | E_var:     0.6877 | E_err:   0.012957
[2025-10-22 19:48:33] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -62.740667 | E_var:     0.5000 | E_err:   0.011049
[2025-10-22 19:48:47] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -62.755929 | E_var:     0.5214 | E_err:   0.011282
[2025-10-22 19:49:00] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -62.736102 | E_var:     0.4258 | E_err:   0.010196
[2025-10-22 19:49:13] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -62.732896 | E_var:     1.0049 | E_err:   0.015663
[2025-10-22 19:49:26] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -62.724337 | E_var:     0.7125 | E_err:   0.013189
[2025-10-22 19:49:39] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -62.733995 | E_var:     0.5545 | E_err:   0.011635
[2025-10-22 19:49:53] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -62.729405 | E_var:     0.4483 | E_err:   0.010462
[2025-10-22 19:50:06] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -62.731448 | E_var:     0.5504 | E_err:   0.011592
[2025-10-22 19:50:19] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -62.753263 | E_var:     0.5561 | E_err:   0.011652
[2025-10-22 19:50:32] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -62.744947 | E_var:     0.5021 | E_err:   0.011072
[2025-10-22 19:50:45] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -62.742375 | E_var:     0.4925 | E_err:   0.010965
[2025-10-22 19:50:58] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -62.742132 | E_var:     0.5341 | E_err:   0.011420
[2025-10-22 19:51:12] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -62.744958 | E_var:     0.4496 | E_err:   0.010477
[2025-10-22 19:51:25] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -62.738812 | E_var:     0.5842 | E_err:   0.011943
[2025-10-22 19:51:38] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -62.734057 | E_var:     0.5239 | E_err:   0.011310
[2025-10-22 19:51:51] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -62.730050 | E_var:     0.4591 | E_err:   0.010587
[2025-10-22 19:52:04] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -62.741872 | E_var:     0.4426 | E_err:   0.010395
[2025-10-22 19:52:17] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -62.747539 | E_var:     0.4864 | E_err:   0.010898
[2025-10-22 19:52:31] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -62.751449 | E_var:     0.4574 | E_err:   0.010567
[2025-10-22 19:52:44] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -62.731568 | E_var:     0.5244 | E_err:   0.011315
[2025-10-22 19:52:57] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -62.735255 | E_var:     0.4867 | E_err:   0.010901
[2025-10-22 19:53:10] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -62.744146 | E_var:     0.5384 | E_err:   0.011465
[2025-10-22 19:53:23] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -62.747071 | E_var:     0.6152 | E_err:   0.012256
[2025-10-22 19:53:37] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -62.765383 | E_var:     0.5503 | E_err:   0.011591
[2025-10-22 19:53:50] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -62.740999 | E_var:     0.4955 | E_err:   0.010999
[2025-10-22 19:54:03] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -62.747774 | E_var:     0.4733 | E_err:   0.010749
[2025-10-22 19:54:16] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -62.743995 | E_var:     0.6342 | E_err:   0.012443
[2025-10-22 19:54:29] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -62.731391 | E_var:     0.5671 | E_err:   0.011767
[2025-10-22 19:54:42] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -62.730696 | E_var:     0.4828 | E_err:   0.010857
[2025-10-22 19:54:56] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -62.742462 | E_var:     0.4406 | E_err:   0.010371
[2025-10-22 19:55:09] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -62.735041 | E_var:     0.5390 | E_err:   0.011471
[2025-10-22 19:55:22] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -62.765963 | E_var:     0.5703 | E_err:   0.011800
[2025-10-22 19:55:35] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -62.753331 | E_var:     0.4413 | E_err:   0.010380
[2025-10-22 19:55:48] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -62.739680 | E_var:     0.5567 | E_err:   0.011658
[2025-10-22 19:56:01] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -62.752953 | E_var:     0.5439 | E_err:   0.011524
[2025-10-22 19:56:15] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -62.733887 | E_var:     0.7200 | E_err:   0.013258
[2025-10-22 19:56:28] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -62.741872 | E_var:     0.6450 | E_err:   0.012549
[2025-10-22 19:56:41] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -62.734770 | E_var:     0.4806 | E_err:   0.010832
[2025-10-22 19:56:54] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -62.741076 | E_var:     0.5878 | E_err:   0.011979
[2025-10-22 19:57:07] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -62.734090 | E_var:     0.5249 | E_err:   0.011320
[2025-10-22 19:57:21] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -62.744240 | E_var:     0.4561 | E_err:   0.010552
[2025-10-22 19:57:34] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -62.767630 | E_var:     0.6253 | E_err:   0.012356
[2025-10-22 19:57:47] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -62.761211 | E_var:     0.4663 | E_err:   0.010670
[2025-10-22 19:58:00] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -62.729236 | E_var:     0.5273 | E_err:   0.011346
[2025-10-22 19:58:13] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -62.749809 | E_var:     0.5502 | E_err:   0.011589
[2025-10-22 19:58:26] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -62.733730 | E_var:     0.5303 | E_err:   0.011379
[2025-10-22 19:58:40] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -62.742380 | E_var:     0.5365 | E_err:   0.011444
[2025-10-22 19:58:53] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -62.744642 | E_var:     0.7129 | E_err:   0.013192
[2025-10-22 19:58:53] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-22 19:59:06] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -62.729913 | E_var:     0.5110 | E_err:   0.011170
[2025-10-22 19:59:19] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -62.754669 | E_var:     0.4453 | E_err:   0.010426
[2025-10-22 19:59:32] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -62.740924 | E_var:     0.5404 | E_err:   0.011486
[2025-10-22 19:59:45] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -62.753175 | E_var:     0.4867 | E_err:   0.010901
[2025-10-22 19:59:59] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -62.746558 | E_var:     0.4821 | E_err:   0.010849
[2025-10-22 20:00:12] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -62.732789 | E_var:     0.5017 | E_err:   0.011068
[2025-10-22 20:00:25] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -62.754028 | E_var:     0.6156 | E_err:   0.012259
[2025-10-22 20:00:38] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -62.728916 | E_var:     0.5333 | E_err:   0.011411
[2025-10-22 20:00:51] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -62.746095 | E_var:     0.5671 | E_err:   0.011767
[2025-10-22 20:01:04] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -62.731641 | E_var:     0.5425 | E_err:   0.011509
[2025-10-22 20:01:18] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -62.723592 | E_var:     0.5213 | E_err:   0.011281
[2025-10-22 20:01:31] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -62.743696 | E_var:     0.5237 | E_err:   0.011307
[2025-10-22 20:01:44] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -62.744035 | E_var:     0.6029 | E_err:   0.012133
[2025-10-22 20:01:57] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -62.730411 | E_var:     0.5478 | E_err:   0.011565
[2025-10-22 20:02:10] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -62.744128 | E_var:     0.5908 | E_err:   0.012010
[2025-10-22 20:02:24] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -62.732208 | E_var:     0.5027 | E_err:   0.011079
[2025-10-22 20:02:37] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -62.749712 | E_var:     0.5441 | E_err:   0.011525
[2025-10-22 20:02:50] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -62.753638 | E_var:     0.4469 | E_err:   0.010445
[2025-10-22 20:03:03] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -62.732492 | E_var:     0.4964 | E_err:   0.011008
[2025-10-22 20:03:16] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -62.749438 | E_var:     0.4541 | E_err:   0.010529
[2025-10-22 20:03:29] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -62.733251 | E_var:     0.4918 | E_err:   0.010958
[2025-10-22 20:03:43] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -62.748051 | E_var:     0.4515 | E_err:   0.010499
[2025-10-22 20:03:56] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -62.739019 | E_var:     0.5309 | E_err:   0.011385
[2025-10-22 20:04:09] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -62.753676 | E_var:     0.8696 | E_err:   0.014570
[2025-10-22 20:04:22] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -62.747032 | E_var:     0.5381 | E_err:   0.011462
[2025-10-22 20:04:35] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -62.752205 | E_var:     0.4343 | E_err:   0.010297
[2025-10-22 20:04:48] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -62.724426 | E_var:     0.4767 | E_err:   0.010788
[2025-10-22 20:05:02] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -62.736076 | E_var:     0.5318 | E_err:   0.011394
[2025-10-22 20:05:15] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -62.722744 | E_var:     0.5031 | E_err:   0.011083
[2025-10-22 20:05:28] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -62.742511 | E_var:     0.5923 | E_err:   0.012025
[2025-10-22 20:05:41] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -62.745886 | E_var:     0.5328 | E_err:   0.011405
[2025-10-22 20:05:54] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -62.740242 | E_var:     0.5958 | E_err:   0.012061
[2025-10-22 20:06:07] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -62.739238 | E_var:     0.4408 | E_err:   0.010374
[2025-10-22 20:06:21] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -62.730708 | E_var:     0.4568 | E_err:   0.010561
[2025-10-22 20:06:34] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -62.743911 | E_var:     0.4488 | E_err:   0.010468
[2025-10-22 20:06:47] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -62.731650 | E_var:     0.5612 | E_err:   0.011705
[2025-10-22 20:07:00] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -62.720570 | E_var:     0.6642 | E_err:   0.012734
[2025-10-22 20:07:13] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -62.754494 | E_var:     0.5571 | E_err:   0.011662
[2025-10-22 20:07:26] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -62.737504 | E_var:     0.4373 | E_err:   0.010333
[2025-10-22 20:07:40] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -62.739831 | E_var:     0.5556 | E_err:   0.011646
[2025-10-22 20:07:53] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -62.729006 | E_var:     0.6825 | E_err:   0.012908
[2025-10-22 20:08:06] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -62.729138 | E_var:     0.5075 | E_err:   0.011131
[2025-10-22 20:08:19] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -62.739067 | E_var:     0.5126 | E_err:   0.011187
[2025-10-22 20:08:32] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -62.742612 | E_var:     0.5104 | E_err:   0.011163
[2025-10-22 20:08:45] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -62.738886 | E_var:     0.5272 | E_err:   0.011345
[2025-10-22 20:08:59] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -62.752131 | E_var:     0.4672 | E_err:   0.010680
[2025-10-22 20:09:12] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -62.745716 | E_var:     0.5152 | E_err:   0.011216
[2025-10-22 20:09:25] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -62.746939 | E_var:     0.4849 | E_err:   0.010880
[2025-10-22 20:09:38] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -62.736824 | E_var:     0.5219 | E_err:   0.011288
[2025-10-22 20:09:51] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -62.742454 | E_var:     0.4919 | E_err:   0.010959
[2025-10-22 20:10:04] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -62.740030 | E_var:     0.4357 | E_err:   0.010314
[2025-10-22 20:10:18] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -62.726059 | E_var:     0.5240 | E_err:   0.011310
[2025-10-22 20:10:31] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -62.746375 | E_var:     0.5317 | E_err:   0.011394
[2025-10-22 20:10:44] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -62.752306 | E_var:     0.4439 | E_err:   0.010411
[2025-10-22 20:10:57] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -62.744345 | E_var:     0.5284 | E_err:   0.011358
[2025-10-22 20:11:10] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -62.739784 | E_var:     0.5126 | E_err:   0.011187
[2025-10-22 20:11:23] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -62.751519 | E_var:     0.4519 | E_err:   0.010503
[2025-10-22 20:11:37] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -62.737002 | E_var:     0.5101 | E_err:   0.011159
[2025-10-22 20:11:50] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -62.738771 | E_var:     0.4131 | E_err:   0.010042
[2025-10-22 20:12:03] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -62.749219 | E_var:     0.7802 | E_err:   0.013801
[2025-10-22 20:12:16] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -62.730535 | E_var:     0.5107 | E_err:   0.011166
[2025-10-22 20:12:29] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -62.735783 | E_var:     0.4502 | E_err:   0.010484
[2025-10-22 20:12:42] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -62.721511 | E_var:     0.5963 | E_err:   0.012065
[2025-10-22 20:12:56] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -62.749253 | E_var:     0.4586 | E_err:   0.010582
[2025-10-22 20:13:09] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -62.717679 | E_var:     0.5107 | E_err:   0.011166
[2025-10-22 20:13:22] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -62.747924 | E_var:     0.4810 | E_err:   0.010837
[2025-10-22 20:13:35] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -62.743649 | E_var:     0.5786 | E_err:   0.011885
[2025-10-22 20:13:48] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -62.732849 | E_var:     0.6336 | E_err:   0.012437
[2025-10-22 20:14:01] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -62.738136 | E_var:     0.4316 | E_err:   0.010265
[2025-10-22 20:14:15] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -62.747200 | E_var:     0.4380 | E_err:   0.010340
[2025-10-22 20:14:28] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -62.746216 | E_var:     0.6713 | E_err:   0.012802
[2025-10-22 20:14:41] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -62.743475 | E_var:     0.5932 | E_err:   0.012034
[2025-10-22 20:14:54] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -62.737357 | E_var:     0.5246 | E_err:   0.011317
[2025-10-22 20:15:07] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -62.733131 | E_var:     0.6279 | E_err:   0.012381
[2025-10-22 20:15:20] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -62.753572 | E_var:     0.7352 | E_err:   0.013398
[2025-10-22 20:15:34] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -62.762876 | E_var:     0.4723 | E_err:   0.010738
[2025-10-22 20:15:47] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -62.745965 | E_var:     0.8875 | E_err:   0.014720
[2025-10-22 20:16:00] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -62.732002 | E_var:     0.4711 | E_err:   0.010725
[2025-10-22 20:16:13] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -62.744691 | E_var:     0.5696 | E_err:   0.011792
[2025-10-22 20:16:26] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -62.737765 | E_var:     0.5003 | E_err:   0.011052
[2025-10-22 20:16:39] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -62.735094 | E_var:     0.7154 | E_err:   0.013216
[2025-10-22 20:16:53] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -62.752150 | E_var:     0.6202 | E_err:   0.012305
[2025-10-22 20:17:06] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -62.741055 | E_var:     0.5454 | E_err:   0.011539
[2025-10-22 20:17:19] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -62.743441 | E_var:     0.4325 | E_err:   0.010275
[2025-10-22 20:17:32] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -62.760006 | E_var:     0.5210 | E_err:   0.011278
[2025-10-22 20:17:45] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -62.722143 | E_var:     0.4865 | E_err:   0.010898
[2025-10-22 20:17:58] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -62.758379 | E_var:     0.4424 | E_err:   0.010392
[2025-10-22 20:18:12] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -62.738292 | E_var:     0.5833 | E_err:   0.011934
[2025-10-22 20:18:25] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -62.752139 | E_var:     0.4894 | E_err:   0.010931
[2025-10-22 20:18:38] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -62.746152 | E_var:     0.5434 | E_err:   0.011518
[2025-10-22 20:18:51] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -62.755507 | E_var:     0.5260 | E_err:   0.011332
[2025-10-22 20:19:04] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -62.726834 | E_var:     0.5314 | E_err:   0.011390
[2025-10-22 20:19:17] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -62.723528 | E_var:     0.4822 | E_err:   0.010850
[2025-10-22 20:19:31] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -62.751758 | E_var:     0.4831 | E_err:   0.010860
[2025-10-22 20:19:44] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -62.742373 | E_var:     0.5494 | E_err:   0.011582
[2025-10-22 20:19:57] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -62.726441 | E_var:     0.4897 | E_err:   0.010934
[2025-10-22 20:20:10] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -62.730098 | E_var:     0.5424 | E_err:   0.011508
[2025-10-22 20:20:23] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -62.727273 | E_var:     0.5237 | E_err:   0.011307
[2025-10-22 20:20:36] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -62.750062 | E_var:     0.4817 | E_err:   0.010844
[2025-10-22 20:20:50] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -62.737987 | E_var:     0.5319 | E_err:   0.011396
[2025-10-22 20:21:03] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -62.736580 | E_var:     0.5702 | E_err:   0.011798
[2025-10-22 20:21:16] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -62.736635 | E_var:     0.5698 | E_err:   0.011794
[2025-10-22 20:21:29] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -62.756569 | E_var:     0.4780 | E_err:   0.010802
[2025-10-22 20:21:42] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -62.737592 | E_var:     0.5181 | E_err:   0.011247
[2025-10-22 20:21:55] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -62.745829 | E_var:     0.5058 | E_err:   0.011113
[2025-10-22 20:21:56] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-22 20:22:09] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -62.763054 | E_var:     0.4863 | E_err:   0.010897
[2025-10-22 20:22:22] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -62.747183 | E_var:     0.6077 | E_err:   0.012181
[2025-10-22 20:22:35] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -62.731291 | E_var:     0.4875 | E_err:   0.010910
[2025-10-22 20:22:48] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -62.754507 | E_var:     0.5832 | E_err:   0.011932
[2025-10-22 20:23:01] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -62.756274 | E_var:     0.4809 | E_err:   0.010836
[2025-10-22 20:23:15] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -62.749652 | E_var:     0.4524 | E_err:   0.010510
[2025-10-22 20:23:28] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -62.752280 | E_var:     0.9554 | E_err:   0.015273
[2025-10-22 20:23:41] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -62.737066 | E_var:     0.6456 | E_err:   0.012555
[2025-10-22 20:23:54] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -62.747770 | E_var:     0.6501 | E_err:   0.012598
[2025-10-22 20:24:07] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -62.725170 | E_var:     0.5976 | E_err:   0.012079
[2025-10-22 20:24:20] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -62.753570 | E_var:     0.8891 | E_err:   0.014733
[2025-10-22 20:24:34] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -62.715453 | E_var:     9.8485 | E_err:   0.049035
[2025-10-22 20:24:47] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -62.737799 | E_var:     0.3878 | E_err:   0.009730
[2025-10-22 20:25:00] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -62.732907 | E_var:     1.3073 | E_err:   0.017865
[2025-10-22 20:25:13] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -62.738665 | E_var:     0.4379 | E_err:   0.010340
[2025-10-22 20:25:26] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -62.730367 | E_var:     0.5843 | E_err:   0.011943
[2025-10-22 20:25:39] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -62.744698 | E_var:     0.5704 | E_err:   0.011801
[2025-10-22 20:25:53] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -62.737167 | E_var:     0.6753 | E_err:   0.012841
[2025-10-22 20:26:06] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -62.729667 | E_var:     0.5181 | E_err:   0.011247
[2025-10-22 20:26:19] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -62.755542 | E_var:     0.5242 | E_err:   0.011312
[2025-10-22 20:26:32] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -62.718383 | E_var:     0.4547 | E_err:   0.010536
[2025-10-22 20:26:45] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -62.760088 | E_var:     0.4776 | E_err:   0.010799
[2025-10-22 20:26:58] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -62.747390 | E_var:     0.5354 | E_err:   0.011433
[2025-10-22 20:27:12] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -62.740929 | E_var:     0.4527 | E_err:   0.010513
[2025-10-22 20:27:25] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -62.736065 | E_var:     0.6445 | E_err:   0.012544
[2025-10-22 20:27:38] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -62.743519 | E_var:     0.3957 | E_err:   0.009829
[2025-10-22 20:27:51] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -62.734718 | E_var:     0.4839 | E_err:   0.010869
[2025-10-22 20:28:04] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -62.757569 | E_var:     0.4447 | E_err:   0.010419
[2025-10-22 20:28:17] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -62.726654 | E_var:     0.4572 | E_err:   0.010565
[2025-10-22 20:28:31] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -62.735338 | E_var:     0.5993 | E_err:   0.012096
[2025-10-22 20:28:44] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -62.729880 | E_var:     0.4618 | E_err:   0.010619
[2025-10-22 20:28:57] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -62.758074 | E_var:     0.4799 | E_err:   0.010824
[2025-10-22 20:29:10] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -62.737481 | E_var:     0.4815 | E_err:   0.010843
[2025-10-22 20:29:23] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -62.744928 | E_var:     0.4826 | E_err:   0.010855
[2025-10-22 20:29:36] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -62.721855 | E_var:     0.5232 | E_err:   0.011302
[2025-10-22 20:29:50] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -62.724259 | E_var:     0.4660 | E_err:   0.010667
[2025-10-22 20:30:03] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -62.743244 | E_var:     0.6216 | E_err:   0.012319
[2025-10-22 20:30:16] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -62.730880 | E_var:     0.4595 | E_err:   0.010591
[2025-10-22 20:30:29] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -62.729123 | E_var:     0.5798 | E_err:   0.011898
[2025-10-22 20:30:42] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -62.742413 | E_var:     0.6073 | E_err:   0.012176
[2025-10-22 20:30:55] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -62.732986 | E_var:     0.5223 | E_err:   0.011292
[2025-10-22 20:31:09] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -62.732069 | E_var:     0.4927 | E_err:   0.010968
[2025-10-22 20:31:22] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -62.750425 | E_var:     0.5025 | E_err:   0.011076
[2025-10-22 20:31:35] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -62.733217 | E_var:     0.4586 | E_err:   0.010581
[2025-10-22 20:31:48] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -62.753225 | E_var:     0.4729 | E_err:   0.010745
[2025-10-22 20:32:01] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -62.749602 | E_var:     0.5558 | E_err:   0.011649
[2025-10-22 20:32:14] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -62.750840 | E_var:     0.4446 | E_err:   0.010418
[2025-10-22 20:32:27] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -62.754995 | E_var:     0.6041 | E_err:   0.012145
[2025-10-22 20:32:41] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -62.742026 | E_var:     0.5148 | E_err:   0.011210
[2025-10-22 20:32:54] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -62.761516 | E_var:     0.6687 | E_err:   0.012778
[2025-10-22 20:33:07] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -62.740935 | E_var:     0.4447 | E_err:   0.010419
[2025-10-22 20:33:20] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -62.736620 | E_var:     0.4733 | E_err:   0.010750
[2025-10-22 20:33:33] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -62.740572 | E_var:     0.6288 | E_err:   0.012390
[2025-10-22 20:33:46] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -62.747057 | E_var:     0.5594 | E_err:   0.011686
[2025-10-22 20:34:00] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -62.746521 | E_var:     0.5823 | E_err:   0.011924
[2025-10-22 20:34:13] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -62.752747 | E_var:     0.4860 | E_err:   0.010893
[2025-10-22 20:34:26] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -62.748622 | E_var:     0.4608 | E_err:   0.010607
[2025-10-22 20:34:39] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -62.739069 | E_var:     0.5540 | E_err:   0.011630
[2025-10-22 20:34:52] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -62.738237 | E_var:     0.6971 | E_err:   0.013046
[2025-10-22 20:35:05] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -62.740866 | E_var:     0.5002 | E_err:   0.011051
[2025-10-22 20:35:19] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -62.746239 | E_var:     0.5345 | E_err:   0.011423
[2025-10-22 20:35:32] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -62.744996 | E_var:     0.4566 | E_err:   0.010558
[2025-10-22 20:35:45] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -62.761914 | E_var:     0.4484 | E_err:   0.010463
[2025-10-22 20:35:58] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -62.747965 | E_var:     0.4554 | E_err:   0.010545
[2025-10-22 20:36:11] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -62.752792 | E_var:     0.4522 | E_err:   0.010507
[2025-10-22 20:36:24] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -62.717242 | E_var:     0.9588 | E_err:   0.015300
[2025-10-22 20:36:38] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -62.739295 | E_var:     0.5527 | E_err:   0.011616
[2025-10-22 20:36:51] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -62.735967 | E_var:     0.4747 | E_err:   0.010765
[2025-10-22 20:37:04] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -62.747735 | E_var:     0.5070 | E_err:   0.011125
[2025-10-22 20:37:17] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -62.735448 | E_var:     0.4891 | E_err:   0.010927
[2025-10-22 20:37:30] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -62.741419 | E_var:     0.4023 | E_err:   0.009910
[2025-10-22 20:37:43] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -62.740664 | E_var:     0.5350 | E_err:   0.011429
[2025-10-22 20:37:57] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -62.739053 | E_var:     0.5419 | E_err:   0.011502
[2025-10-22 20:38:10] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -62.769444 | E_var:     0.4371 | E_err:   0.010330
[2025-10-22 20:38:23] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -62.719062 | E_var:     0.5549 | E_err:   0.011639
[2025-10-22 20:38:36] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -62.738991 | E_var:     0.4834 | E_err:   0.010863
[2025-10-22 20:38:49] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -62.755812 | E_var:     0.5114 | E_err:   0.011174
[2025-10-22 20:39:02] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -62.746489 | E_var:     0.5119 | E_err:   0.011179
[2025-10-22 20:39:16] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -62.736927 | E_var:     0.5594 | E_err:   0.011686
[2025-10-22 20:39:29] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -62.732673 | E_var:     0.5860 | E_err:   0.011961
[2025-10-22 20:39:42] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -62.735026 | E_var:     0.6055 | E_err:   0.012158
[2025-10-22 20:39:55] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -62.741566 | E_var:     0.5226 | E_err:   0.011295
[2025-10-22 20:40:08] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -62.740334 | E_var:     0.3887 | E_err:   0.009741
[2025-10-22 20:40:21] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -62.741080 | E_var:     0.6729 | E_err:   0.012817
[2025-10-22 20:40:35] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -62.730937 | E_var:     0.5991 | E_err:   0.012094
[2025-10-22 20:40:48] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -62.749714 | E_var:     0.5290 | E_err:   0.011365
[2025-10-22 20:41:01] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -62.736750 | E_var:     0.4024 | E_err:   0.009912
[2025-10-22 20:41:14] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -62.755588 | E_var:     0.5403 | E_err:   0.011485
[2025-10-22 20:41:27] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -62.725269 | E_var:     0.4924 | E_err:   0.010964
[2025-10-22 20:41:40] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -62.743701 | E_var:     0.4643 | E_err:   0.010646
[2025-10-22 20:41:53] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -62.772829 | E_var:     0.4611 | E_err:   0.010610
[2025-10-22 20:42:07] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -62.749129 | E_var:     0.5347 | E_err:   0.011425
[2025-10-22 20:42:20] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -62.768281 | E_var:     0.4949 | E_err:   0.010992
[2025-10-22 20:42:33] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -62.743244 | E_var:     0.7648 | E_err:   0.013664
[2025-10-22 20:42:46] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -62.756251 | E_var:     0.5913 | E_err:   0.012015
[2025-10-22 20:42:59] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -62.749201 | E_var:     0.5021 | E_err:   0.011071
[2025-10-22 20:43:12] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -62.744031 | E_var:     0.5553 | E_err:   0.011643
[2025-10-22 20:43:26] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -62.738295 | E_var:     0.4275 | E_err:   0.010217
[2025-10-22 20:43:39] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -62.728873 | E_var:     0.4752 | E_err:   0.010772
[2025-10-22 20:43:52] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -62.761712 | E_var:     0.4435 | E_err:   0.010405
[2025-10-22 20:44:05] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -62.727254 | E_var:     0.4501 | E_err:   0.010483
[2025-10-22 20:44:18] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -62.750394 | E_var:     0.6068 | E_err:   0.012172
[2025-10-22 20:44:31] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -62.758136 | E_var:     0.4305 | E_err:   0.010252
[2025-10-22 20:44:45] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -62.725484 | E_var:     0.5209 | E_err:   0.011277
[2025-10-22 20:44:58] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -62.735888 | E_var:     0.5350 | E_err:   0.011429
[2025-10-22 20:44:58] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-22 20:45:11] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -62.739675 | E_var:     0.5382 | E_err:   0.011463
[2025-10-22 20:45:24] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -62.745115 | E_var:     0.5607 | E_err:   0.011700
[2025-10-22 20:45:37] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -62.733100 | E_var:     0.6572 | E_err:   0.012667
[2025-10-22 20:45:50] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -62.732355 | E_var:     0.5686 | E_err:   0.011782
[2025-10-22 20:46:04] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -62.748581 | E_var:     0.4305 | E_err:   0.010252
[2025-10-22 20:46:17] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -62.754748 | E_var:     0.5139 | E_err:   0.011201
[2025-10-22 20:46:30] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -62.731394 | E_var:     0.5131 | E_err:   0.011192
[2025-10-22 20:46:43] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -62.731679 | E_var:     0.6003 | E_err:   0.012106
[2025-10-22 20:46:56] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -62.764040 | E_var:     0.5660 | E_err:   0.011755
[2025-10-22 20:47:09] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -62.749218 | E_var:     0.7171 | E_err:   0.013232
[2025-10-22 20:47:23] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -62.756417 | E_var:     0.5779 | E_err:   0.011878
[2025-10-22 20:47:36] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -62.744588 | E_var:     0.4978 | E_err:   0.011024
[2025-10-22 20:47:49] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -62.748349 | E_var:     0.5383 | E_err:   0.011464
[2025-10-22 20:48:02] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -62.734857 | E_var:     0.5301 | E_err:   0.011377
[2025-10-22 20:48:15] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -62.751358 | E_var:     0.4523 | E_err:   0.010509
[2025-10-22 20:48:28] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -62.771152 | E_var:     0.5408 | E_err:   0.011491
[2025-10-22 20:48:42] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -62.739761 | E_var:     0.5399 | E_err:   0.011481
[2025-10-22 20:48:55] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -62.764431 | E_var:     0.7024 | E_err:   0.013095
[2025-10-22 20:49:08] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -62.735010 | E_var:     0.6010 | E_err:   0.012113
[2025-10-22 20:49:21] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -62.749022 | E_var:     0.5114 | E_err:   0.011174
[2025-10-22 20:49:34] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -62.756395 | E_var:     0.4655 | E_err:   0.010661
[2025-10-22 20:49:47] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -62.740128 | E_var:     0.4871 | E_err:   0.010905
[2025-10-22 20:50:01] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -62.735395 | E_var:     0.4570 | E_err:   0.010563
[2025-10-22 20:50:14] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -62.757546 | E_var:     0.4876 | E_err:   0.010910
[2025-10-22 20:50:27] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -62.734487 | E_var:     0.4920 | E_err:   0.010959
[2025-10-22 20:50:40] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -62.742947 | E_var:     0.5826 | E_err:   0.011926
[2025-10-22 20:50:53] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -62.747160 | E_var:     0.5682 | E_err:   0.011778
[2025-10-22 20:51:06] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -62.751428 | E_var:     0.4366 | E_err:   0.010325
[2025-10-22 20:51:20] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -62.741633 | E_var:     0.4452 | E_err:   0.010426
[2025-10-22 20:51:33] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -62.727420 | E_var:     0.5910 | E_err:   0.012012
[2025-10-22 20:51:46] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -62.748445 | E_var:     0.6177 | E_err:   0.012281
[2025-10-22 20:51:59] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -62.741706 | E_var:     0.4747 | E_err:   0.010766
[2025-10-22 20:52:12] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -62.727577 | E_var:     0.5600 | E_err:   0.011692
[2025-10-22 20:52:25] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -62.746533 | E_var:     0.4369 | E_err:   0.010328
[2025-10-22 20:52:39] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -62.731820 | E_var:     0.4569 | E_err:   0.010562
[2025-10-22 20:52:52] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -62.738719 | E_var:     0.4595 | E_err:   0.010591
[2025-10-22 20:53:05] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -62.752609 | E_var:     0.6071 | E_err:   0.012175
[2025-10-22 20:53:18] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -62.753902 | E_var:     0.4832 | E_err:   0.010861
[2025-10-22 20:53:31] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -62.749336 | E_var:     0.4390 | E_err:   0.010353
[2025-10-22 20:53:44] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -62.738212 | E_var:     0.5311 | E_err:   0.011387
[2025-10-22 20:53:57] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -62.744261 | E_var:     0.5193 | E_err:   0.011260
[2025-10-22 20:54:11] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -62.746764 | E_var:     0.4936 | E_err:   0.010978
[2025-10-22 20:54:24] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -62.741736 | E_var:     0.5588 | E_err:   0.011680
[2025-10-22 20:54:37] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -62.735790 | E_var:     0.4708 | E_err:   0.010721
[2025-10-22 20:54:50] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -62.751654 | E_var:     0.4862 | E_err:   0.010895
[2025-10-22 20:55:03] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -62.740174 | E_var:     0.5054 | E_err:   0.011108
[2025-10-22 20:55:16] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -62.742878 | E_var:     0.5461 | E_err:   0.011547
[2025-10-22 20:55:30] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -62.759558 | E_var:     0.5070 | E_err:   0.011126
[2025-10-22 20:55:43] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -62.722951 | E_var:     0.5520 | E_err:   0.011609
[2025-10-22 20:55:56] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -62.720745 | E_var:     0.5922 | E_err:   0.012024
[2025-10-22 20:56:09] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -62.740966 | E_var:     0.4881 | E_err:   0.010916
[2025-10-22 20:56:22] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -62.764495 | E_var:     0.4948 | E_err:   0.010991
[2025-10-22 20:56:35] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -62.727434 | E_var:     0.4993 | E_err:   0.011040
[2025-10-22 20:56:49] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -62.739642 | E_var:     0.4318 | E_err:   0.010267
[2025-10-22 20:57:02] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -62.748002 | E_var:     0.5405 | E_err:   0.011487
[2025-10-22 20:57:15] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -62.744673 | E_var:     0.5080 | E_err:   0.011136
[2025-10-22 20:57:28] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -62.738025 | E_var:     0.5186 | E_err:   0.011252
[2025-10-22 20:57:41] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -62.724115 | E_var:     1.0251 | E_err:   0.015820
[2025-10-22 20:57:54] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -62.745155 | E_var:     0.5118 | E_err:   0.011179
[2025-10-22 20:58:08] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -62.755912 | E_var:     0.4505 | E_err:   0.010487
[2025-10-22 20:58:21] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -62.727586 | E_var:     0.5915 | E_err:   0.012017
[2025-10-22 20:58:34] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -62.749248 | E_var:     0.6220 | E_err:   0.012323
[2025-10-22 20:58:47] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -62.744280 | E_var:     0.6451 | E_err:   0.012550
[2025-10-22 20:59:00] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -62.746510 | E_var:     0.5256 | E_err:   0.011328
[2025-10-22 20:59:13] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -62.729540 | E_var:     0.4588 | E_err:   0.010584
[2025-10-22 20:59:27] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -62.742819 | E_var:     0.4712 | E_err:   0.010726
[2025-10-22 20:59:40] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -62.731126 | E_var:     1.3517 | E_err:   0.018166
[2025-10-22 20:59:53] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -62.725811 | E_var:     0.5505 | E_err:   0.011593
[2025-10-22 21:00:06] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -62.736236 | E_var:     0.5551 | E_err:   0.011641
[2025-10-22 21:00:19] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -62.752161 | E_var:     0.5360 | E_err:   0.011440
[2025-10-22 21:00:32] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -62.740500 | E_var:     0.4466 | E_err:   0.010442
[2025-10-22 21:00:45] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -62.742913 | E_var:     0.4860 | E_err:   0.010893
[2025-10-22 21:00:59] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -62.767652 | E_var:     0.4921 | E_err:   0.010961
[2025-10-22 21:01:12] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -62.722691 | E_var:     0.4228 | E_err:   0.010160
[2025-10-22 21:01:25] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -62.744415 | E_var:     0.4562 | E_err:   0.010553
[2025-10-22 21:01:38] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -62.721967 | E_var:     0.8210 | E_err:   0.014158
[2025-10-22 21:01:51] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -62.744238 | E_var:     0.4813 | E_err:   0.010840
[2025-10-22 21:02:04] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -62.739847 | E_var:     0.4643 | E_err:   0.010647
[2025-10-22 21:02:18] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -62.726682 | E_var:     0.5022 | E_err:   0.011073
[2025-10-22 21:02:31] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -62.748390 | E_var:     0.4681 | E_err:   0.010690
[2025-10-22 21:02:44] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -62.751527 | E_var:     0.5188 | E_err:   0.011254
[2025-10-22 21:02:57] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -62.731885 | E_var:     0.4916 | E_err:   0.010955
[2025-10-22 21:03:10] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -62.740589 | E_var:     0.5238 | E_err:   0.011308
[2025-10-22 21:03:23] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -62.728490 | E_var:     0.4565 | E_err:   0.010557
[2025-10-22 21:03:37] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -62.730868 | E_var:     0.5930 | E_err:   0.012032
[2025-10-22 21:03:50] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -62.734838 | E_var:     0.5012 | E_err:   0.011062
[2025-10-22 21:04:03] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -62.729973 | E_var:     0.4263 | E_err:   0.010202
[2025-10-22 21:04:16] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -62.733541 | E_var:     0.4647 | E_err:   0.010652
[2025-10-22 21:04:29] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -62.758473 | E_var:     0.4497 | E_err:   0.010479
[2025-10-22 21:04:42] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -62.741836 | E_var:     0.3745 | E_err:   0.009561
[2025-10-22 21:04:56] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -62.727750 | E_var:     0.4510 | E_err:   0.010494
[2025-10-22 21:05:09] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -62.734282 | E_var:     0.4841 | E_err:   0.010871
[2025-10-22 21:05:22] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -62.739000 | E_var:     0.5530 | E_err:   0.011619
[2025-10-22 21:05:35] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -62.751773 | E_var:     0.4359 | E_err:   0.010316
[2025-10-22 21:05:48] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -62.738608 | E_var:     0.4608 | E_err:   0.010607
[2025-10-22 21:06:01] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -62.738413 | E_var:     0.4690 | E_err:   0.010701
[2025-10-22 21:06:15] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -62.753402 | E_var:     0.4759 | E_err:   0.010779
[2025-10-22 21:06:28] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -62.742365 | E_var:     0.5025 | E_err:   0.011077
[2025-10-22 21:06:41] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -62.739759 | E_var:     0.4577 | E_err:   0.010571
[2025-10-22 21:06:54] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -62.743219 | E_var:     0.6109 | E_err:   0.012212
[2025-10-22 21:07:07] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -62.730841 | E_var:     0.4661 | E_err:   0.010667
[2025-10-22 21:07:20] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -62.756397 | E_var:     0.5844 | E_err:   0.011945
[2025-10-22 21:07:34] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -62.732477 | E_var:     0.4517 | E_err:   0.010501
[2025-10-22 21:07:47] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -62.734216 | E_var:     0.4550 | E_err:   0.010539
[2025-10-22 21:08:00] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -62.735420 | E_var:     0.5176 | E_err:   0.011241
[2025-10-22 21:08:00] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-22 21:08:00] ======================================================================================================
[2025-10-22 21:08:00] ✅ Training completed successfully
[2025-10-22 21:08:00] Total restarts: 2
[2025-10-22 21:08:04] Final Energy: -62.73542035 ± 0.01124085
[2025-10-22 21:08:04] Final Variance: 0.517557
[2025-10-22 21:08:04] ======================================================================================================
[2025-10-22 21:08:04] ======================================================================================================
[2025-10-22 21:08:04] Training completed | Runtime: 13900.7s
