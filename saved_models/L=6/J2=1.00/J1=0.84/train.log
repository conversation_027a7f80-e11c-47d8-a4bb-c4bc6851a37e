[2025-10-23 16:31:33] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.83/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-23 16:31:33]   - 迭代次数: 1050
[2025-10-23 16:31:33]   - 能量: -67.358687-0.000032j ± 0.010084, Var: 0.416542
[2025-10-23 16:31:33]   - 时间戳: 2025-10-23T16:31:10.077715+08:00
[2025-10-23 16:31:58] ✓ 变分状态参数已从checkpoint恢复
[2025-10-23 16:31:58] ======================================================================================================
[2025-10-23 16:31:58] GCNN for Shastry-Sutherland Model
[2025-10-23 16:31:58] ======================================================================================================
[2025-10-23 16:31:58] System parameters:
[2025-10-23 16:31:58]   - System size: L=6, N=144
[2025-10-23 16:31:58]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-10-23 16:31:58] ------------------------------------------------------------------------------------------------------
[2025-10-23 16:31:58] Model parameters:
[2025-10-23 16:31:58]   - Number of layers = 4
[2025-10-23 16:31:58]   - Number of features = 4
[2025-10-23 16:31:58]   - Total parameters = 28252
[2025-10-23 16:31:58] ------------------------------------------------------------------------------------------------------
[2025-10-23 16:31:58] Training parameters:
[2025-10-23 16:31:58]   - Total iterations: 1050
[2025-10-23 16:31:58]   - Annealing cycles: 3
[2025-10-23 16:31:58]   - Initial period: 150
[2025-10-23 16:31:58]   - Period multiplier: 2.0
[2025-10-23 16:31:58]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-23 16:31:58]   - Samples: 4096
[2025-10-23 16:31:58]   - Discarded samples: 0
[2025-10-23 16:31:58]   - Chunk size: 4096
[2025-10-23 16:31:58]   - Diagonal shift: 0.15
[2025-10-23 16:31:58]   - Gradient clipping: 1.0
[2025-10-23 16:31:58]   - Checkpoint enabled: interval=105
[2025-10-23 16:31:58]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.84/training/checkpoints
[2025-10-23 16:31:58]   - Resuming from iteration: 1050
[2025-10-23 16:31:58] ------------------------------------------------------------------------------------------------------
[2025-10-23 16:31:58] Device status:
[2025-10-23 16:31:58]   - Devices model: NVIDIA H200 NVL
[2025-10-23 16:31:58]   - Number of devices: 1
[2025-10-23 16:31:58]   - Sharding: True
[2025-10-23 16:31:59] ======================================================================================================
[2025-10-23 16:32:50] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -68.263584 | E_var:     1.0106 | E_err:   0.015708
[2025-10-23 16:33:24] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -68.283994 | E_var:     0.5395 | E_err:   0.011476
[2025-10-23 16:33:37] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -68.267829 | E_var:     0.8283 | E_err:   0.014221
[2025-10-23 16:33:50] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -68.290326 | E_var:     0.5140 | E_err:   0.011202
[2025-10-23 16:34:03] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -68.294787 | E_var:     0.6464 | E_err:   0.012563
[2025-10-23 16:34:16] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -68.290245 | E_var:     0.4246 | E_err:   0.010182
[2025-10-23 16:34:29] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -68.292916 | E_var:     0.4254 | E_err:   0.010192
[2025-10-23 16:34:43] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -68.269666 | E_var:     0.4121 | E_err:   0.010030
[2025-10-23 16:34:56] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -68.290435 | E_var:     0.4342 | E_err:   0.010296
[2025-10-23 16:35:09] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -68.288576 | E_var:     0.3477 | E_err:   0.009213
[2025-10-23 16:35:22] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -68.279847 | E_var:     0.4603 | E_err:   0.010601
[2025-10-23 16:35:35] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -68.280213 | E_var:     0.3824 | E_err:   0.009663
[2025-10-23 16:35:48] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -68.284701 | E_var:     0.3299 | E_err:   0.008975
[2025-10-23 16:36:02] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -68.287267 | E_var:     0.7325 | E_err:   0.013373
[2025-10-23 16:36:15] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -68.278453 | E_var:     0.3207 | E_err:   0.008849
[2025-10-23 16:36:28] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -68.284339 | E_var:     0.3439 | E_err:   0.009163
[2025-10-23 16:36:41] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -68.276053 | E_var:     0.3169 | E_err:   0.008795
[2025-10-23 16:36:54] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -68.290307 | E_var:     0.3226 | E_err:   0.008874
[2025-10-23 16:37:08] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -68.271182 | E_var:     0.4210 | E_err:   0.010139
[2025-10-23 16:37:21] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -68.281336 | E_var:     0.3339 | E_err:   0.009029
[2025-10-23 16:37:34] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -68.274869 | E_var:     0.3423 | E_err:   0.009142
[2025-10-23 16:37:47] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -68.292640 | E_var:     0.3357 | E_err:   0.009053
[2025-10-23 16:38:00] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -68.282949 | E_var:     0.3144 | E_err:   0.008761
[2025-10-23 16:38:14] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -68.273975 | E_var:     0.3115 | E_err:   0.008720
[2025-10-23 16:38:27] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -68.288189 | E_var:     0.3889 | E_err:   0.009744
[2025-10-23 16:38:40] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -68.271840 | E_var:     0.3546 | E_err:   0.009305
[2025-10-23 16:38:53] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -68.271261 | E_var:     0.3591 | E_err:   0.009363
[2025-10-23 16:39:06] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -68.287013 | E_var:     0.2930 | E_err:   0.008458
[2025-10-23 16:39:19] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -68.286885 | E_var:     0.3374 | E_err:   0.009076
[2025-10-23 16:39:33] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -68.278819 | E_var:     0.2936 | E_err:   0.008466
[2025-10-23 16:39:46] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -68.279787 | E_var:     0.3505 | E_err:   0.009250
[2025-10-23 16:39:59] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -68.279809 | E_var:     0.3672 | E_err:   0.009468
[2025-10-23 16:40:12] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -68.288325 | E_var:     0.4108 | E_err:   0.010015
[2025-10-23 16:40:25] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -68.277416 | E_var:     0.3120 | E_err:   0.008727
[2025-10-23 16:40:39] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -68.298036 | E_var:     0.4455 | E_err:   0.010429
[2025-10-23 16:40:52] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -68.288822 | E_var:     0.2567 | E_err:   0.007917
[2025-10-23 16:41:05] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -68.296119 | E_var:     0.2996 | E_err:   0.008552
[2025-10-23 16:41:18] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -68.279456 | E_var:     0.3021 | E_err:   0.008588
[2025-10-23 16:41:31] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -68.275637 | E_var:     0.3124 | E_err:   0.008733
[2025-10-23 16:41:45] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -68.282007 | E_var:     0.3171 | E_err:   0.008798
[2025-10-23 16:41:58] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -68.293211 | E_var:     0.2688 | E_err:   0.008101
[2025-10-23 16:42:11] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -68.286498 | E_var:     0.3161 | E_err:   0.008785
[2025-10-23 16:42:24] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -68.283396 | E_var:     0.3276 | E_err:   0.008944
[2025-10-23 16:42:37] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -68.283003 | E_var:     0.3929 | E_err:   0.009794
[2025-10-23 16:42:50] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -68.273474 | E_var:     0.3622 | E_err:   0.009404
[2025-10-23 16:43:04] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -68.263553 | E_var:     0.3127 | E_err:   0.008737
[2025-10-23 16:43:17] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -68.288193 | E_var:     0.3057 | E_err:   0.008639
[2025-10-23 16:43:30] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -68.290885 | E_var:     0.4220 | E_err:   0.010150
[2025-10-23 16:43:43] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -68.265026 | E_var:     0.5391 | E_err:   0.011472
[2025-10-23 16:43:56] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -68.276714 | E_var:     0.3385 | E_err:   0.009091
[2025-10-23 16:44:10] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -68.273968 | E_var:     0.3338 | E_err:   0.009027
[2025-10-23 16:44:23] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -68.289293 | E_var:     0.3008 | E_err:   0.008570
[2025-10-23 16:44:36] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -68.301872 | E_var:     0.3002 | E_err:   0.008561
[2025-10-23 16:44:49] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -68.271118 | E_var:     0.3509 | E_err:   0.009256
[2025-10-23 16:45:02] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -68.286928 | E_var:     0.3167 | E_err:   0.008793
[2025-10-23 16:45:16] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -68.267355 | E_var:     0.5705 | E_err:   0.011802
[2025-10-23 16:45:29] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -68.294806 | E_var:     0.3431 | E_err:   0.009153
[2025-10-23 16:45:42] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -68.275957 | E_var:     0.3790 | E_err:   0.009620
[2025-10-23 16:45:55] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -68.273826 | E_var:     0.3064 | E_err:   0.008650
[2025-10-23 16:46:08] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -68.285111 | E_var:     0.3021 | E_err:   0.008587
[2025-10-23 16:46:22] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -68.272125 | E_var:     0.3069 | E_err:   0.008656
[2025-10-23 16:46:35] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -68.273783 | E_var:     0.3366 | E_err:   0.009066
[2025-10-23 16:46:48] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -68.291216 | E_var:     0.2898 | E_err:   0.008411
[2025-10-23 16:47:01] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -68.279780 | E_var:     0.3486 | E_err:   0.009225
[2025-10-23 16:47:14] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -68.272644 | E_var:     0.3063 | E_err:   0.008647
[2025-10-23 16:47:28] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -68.287507 | E_var:     0.3550 | E_err:   0.009310
[2025-10-23 16:47:41] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -68.292372 | E_var:     0.3113 | E_err:   0.008717
[2025-10-23 16:47:54] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -68.287622 | E_var:     0.3445 | E_err:   0.009171
[2025-10-23 16:48:07] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -68.299884 | E_var:     0.3238 | E_err:   0.008891
[2025-10-23 16:48:20] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -68.289689 | E_var:     0.9953 | E_err:   0.015588
[2025-10-23 16:48:33] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -68.263819 | E_var:     0.5603 | E_err:   0.011696
[2025-10-23 16:48:47] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -68.283076 | E_var:     0.3671 | E_err:   0.009466
[2025-10-23 16:49:00] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -68.272166 | E_var:     0.4425 | E_err:   0.010394
[2025-10-23 16:49:13] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -68.266586 | E_var:     0.3937 | E_err:   0.009804
[2025-10-23 16:49:26] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -68.277615 | E_var:     0.3573 | E_err:   0.009339
[2025-10-23 16:49:39] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -68.277701 | E_var:     0.3481 | E_err:   0.009219
[2025-10-23 16:49:53] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -68.273877 | E_var:     0.3017 | E_err:   0.008583
[2025-10-23 16:50:06] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -68.264354 | E_var:     2.1236 | E_err:   0.022770
[2025-10-23 16:50:19] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -68.288153 | E_var:     0.4080 | E_err:   0.009981
[2025-10-23 16:50:32] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -68.285818 | E_var:     0.3578 | E_err:   0.009346
[2025-10-23 16:50:45] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -68.283717 | E_var:     0.3091 | E_err:   0.008687
[2025-10-23 16:50:59] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -68.276613 | E_var:     0.3542 | E_err:   0.009299
[2025-10-23 16:51:12] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -68.275426 | E_var:     0.3275 | E_err:   0.008942
[2025-10-23 16:51:25] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -68.279594 | E_var:     0.3528 | E_err:   0.009281
[2025-10-23 16:51:38] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -68.278084 | E_var:     0.3647 | E_err:   0.009436
[2025-10-23 16:51:51] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -68.295367 | E_var:     0.3560 | E_err:   0.009322
[2025-10-23 16:52:05] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -68.274113 | E_var:     0.3544 | E_err:   0.009302
[2025-10-23 16:52:18] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -68.280671 | E_var:     0.3713 | E_err:   0.009520
[2025-10-23 16:52:31] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -68.286881 | E_var:     0.3069 | E_err:   0.008656
[2025-10-23 16:52:44] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -68.289788 | E_var:     0.2965 | E_err:   0.008508
[2025-10-23 16:52:57] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -68.298744 | E_var:     0.3335 | E_err:   0.009024
[2025-10-23 16:53:10] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -68.280227 | E_var:     0.4002 | E_err:   0.009884
[2025-10-23 16:53:24] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -68.273916 | E_var:     0.3243 | E_err:   0.008898
[2025-10-23 16:53:37] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -68.278768 | E_var:     0.3696 | E_err:   0.009500
[2025-10-23 16:53:50] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -68.282178 | E_var:     0.3564 | E_err:   0.009328
[2025-10-23 16:54:03] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -68.287172 | E_var:     0.3449 | E_err:   0.009176
[2025-10-23 16:54:16] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -68.288601 | E_var:     0.3421 | E_err:   0.009139
[2025-10-23 16:54:30] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -68.289861 | E_var:     0.3126 | E_err:   0.008736
[2025-10-23 16:54:43] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -68.295159 | E_var:     0.3635 | E_err:   0.009420
[2025-10-23 16:54:56] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -68.290856 | E_var:     0.3188 | E_err:   0.008822
[2025-10-23 16:55:09] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -68.288104 | E_var:     0.3280 | E_err:   0.008949
[2025-10-23 16:55:22] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -68.282155 | E_var:     0.3774 | E_err:   0.009599
[2025-10-23 16:55:36] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -68.284386 | E_var:     0.3241 | E_err:   0.008895
[2025-10-23 16:55:49] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -68.285870 | E_var:     0.4070 | E_err:   0.009968
[2025-10-23 16:56:02] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -68.285251 | E_var:     0.3158 | E_err:   0.008781
[2025-10-23 16:56:02] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-23 16:56:15] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -68.283659 | E_var:     0.3569 | E_err:   0.009334
[2025-10-23 16:56:28] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -68.275748 | E_var:     0.4199 | E_err:   0.010125
[2025-10-23 16:56:42] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -68.267669 | E_var:     0.3435 | E_err:   0.009158
[2025-10-23 16:56:55] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -68.293817 | E_var:     0.2987 | E_err:   0.008539
[2025-10-23 16:57:08] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -68.287149 | E_var:     0.3590 | E_err:   0.009361
[2025-10-23 16:57:21] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -68.281360 | E_var:     0.2906 | E_err:   0.008423
[2025-10-23 16:57:34] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -68.286380 | E_var:     0.3927 | E_err:   0.009792
[2025-10-23 16:57:48] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -68.270460 | E_var:     0.8724 | E_err:   0.014594
[2025-10-23 16:58:01] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -68.298194 | E_var:     0.4446 | E_err:   0.010418
[2025-10-23 16:58:14] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -68.285392 | E_var:     0.3346 | E_err:   0.009039
[2025-10-23 16:58:27] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -68.283410 | E_var:     0.3130 | E_err:   0.008742
[2025-10-23 16:58:40] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -68.289635 | E_var:     0.3489 | E_err:   0.009230
[2025-10-23 16:58:53] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -68.266282 | E_var:     0.2944 | E_err:   0.008478
[2025-10-23 16:59:07] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -68.274300 | E_var:     0.4560 | E_err:   0.010551
[2025-10-23 16:59:20] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -68.280784 | E_var:     0.3184 | E_err:   0.008817
[2025-10-23 16:59:33] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -68.289340 | E_var:     0.3152 | E_err:   0.008772
[2025-10-23 16:59:46] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -68.273737 | E_var:     0.3613 | E_err:   0.009392
[2025-10-23 16:59:59] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -68.281969 | E_var:     0.3874 | E_err:   0.009725
[2025-10-23 17:00:13] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -68.287363 | E_var:     0.3196 | E_err:   0.008833
[2025-10-23 17:00:26] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -68.287105 | E_var:     0.4351 | E_err:   0.010306
[2025-10-23 17:00:39] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -68.284616 | E_var:     0.3392 | E_err:   0.009101
[2025-10-23 17:00:52] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -68.292363 | E_var:     0.3677 | E_err:   0.009474
[2025-10-23 17:01:05] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -68.292982 | E_var:     0.2963 | E_err:   0.008505
[2025-10-23 17:01:19] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -68.288528 | E_var:     0.3571 | E_err:   0.009337
[2025-10-23 17:01:32] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -68.286987 | E_var:     0.3190 | E_err:   0.008825
[2025-10-23 17:01:45] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -68.280987 | E_var:     0.6007 | E_err:   0.012110
[2025-10-23 17:01:58] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -68.281706 | E_var:     0.3618 | E_err:   0.009399
[2025-10-23 17:02:11] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -68.299815 | E_var:     0.3674 | E_err:   0.009470
[2025-10-23 17:02:25] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -68.278301 | E_var:     0.3026 | E_err:   0.008596
[2025-10-23 17:02:38] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -68.293352 | E_var:     0.3287 | E_err:   0.008959
[2025-10-23 17:02:51] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -68.293622 | E_var:     0.3560 | E_err:   0.009323
[2025-10-23 17:03:04] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -68.282888 | E_var:     0.3801 | E_err:   0.009633
[2025-10-23 17:03:17] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -68.294640 | E_var:     0.3408 | E_err:   0.009122
[2025-10-23 17:03:30] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -68.284261 | E_var:     0.3279 | E_err:   0.008947
[2025-10-23 17:03:44] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -68.282459 | E_var:     0.2890 | E_err:   0.008400
[2025-10-23 17:03:57] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -68.274692 | E_var:     0.3410 | E_err:   0.009124
[2025-10-23 17:04:10] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -68.292381 | E_var:     0.4555 | E_err:   0.010546
[2025-10-23 17:04:23] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -68.270887 | E_var:     0.2827 | E_err:   0.008308
[2025-10-23 17:04:36] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -68.277465 | E_var:     0.3341 | E_err:   0.009032
[2025-10-23 17:04:50] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -68.277866 | E_var:     0.3470 | E_err:   0.009204
[2025-10-23 17:05:03] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -68.285575 | E_var:     0.3101 | E_err:   0.008701
[2025-10-23 17:05:16] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -68.284221 | E_var:     0.7568 | E_err:   0.013593
[2025-10-23 17:05:29] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -68.282190 | E_var:     0.3438 | E_err:   0.009161
[2025-10-23 17:05:42] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -68.269866 | E_var:     0.4574 | E_err:   0.010567
[2025-10-23 17:05:56] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -68.285450 | E_var:     0.4117 | E_err:   0.010025
[2025-10-23 17:05:56] 🔄 RESTART #1 | Period: 300
[2025-10-23 17:06:09] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -68.279244 | E_var:     0.3273 | E_err:   0.008939
[2025-10-23 17:06:22] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -68.273439 | E_var:     0.4022 | E_err:   0.009909
[2025-10-23 17:06:35] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -68.285201 | E_var:     0.3634 | E_err:   0.009420
[2025-10-23 17:06:48] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -68.275361 | E_var:     0.3097 | E_err:   0.008696
[2025-10-23 17:07:02] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -68.297392 | E_var:     0.3970 | E_err:   0.009845
[2025-10-23 17:07:15] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -68.273344 | E_var:     0.4249 | E_err:   0.010185
[2025-10-23 17:07:28] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -68.264904 | E_var:     0.3040 | E_err:   0.008615
[2025-10-23 17:07:41] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -68.286946 | E_var:     0.3380 | E_err:   0.009084
[2025-10-23 17:07:54] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -68.288823 | E_var:     0.2832 | E_err:   0.008315
[2025-10-23 17:08:08] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -68.293242 | E_var:     0.2993 | E_err:   0.008548
[2025-10-23 17:08:21] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -68.273034 | E_var:     0.3910 | E_err:   0.009771
[2025-10-23 17:08:34] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -68.280173 | E_var:     0.3227 | E_err:   0.008876
[2025-10-23 17:08:47] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -68.273435 | E_var:     0.3492 | E_err:   0.009234
[2025-10-23 17:09:00] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -68.281824 | E_var:     0.2708 | E_err:   0.008130
[2025-10-23 17:09:13] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -68.290811 | E_var:     0.3148 | E_err:   0.008767
[2025-10-23 17:09:27] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -68.268358 | E_var:     0.3114 | E_err:   0.008720
[2025-10-23 17:09:40] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -68.290435 | E_var:     0.3854 | E_err:   0.009700
[2025-10-23 17:09:53] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -68.290461 | E_var:     0.3087 | E_err:   0.008682
[2025-10-23 17:10:06] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -68.281184 | E_var:     0.4121 | E_err:   0.010031
[2025-10-23 17:10:19] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -68.285265 | E_var:     0.3597 | E_err:   0.009372
[2025-10-23 17:10:33] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -68.298588 | E_var:     0.3591 | E_err:   0.009363
[2025-10-23 17:10:46] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -68.274422 | E_var:     0.2865 | E_err:   0.008364
[2025-10-23 17:10:59] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -68.262667 | E_var:     0.4277 | E_err:   0.010218
[2025-10-23 17:11:12] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -68.292297 | E_var:     0.3716 | E_err:   0.009525
[2025-10-23 17:11:25] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -68.284343 | E_var:     0.3075 | E_err:   0.008664
[2025-10-23 17:11:39] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -68.280875 | E_var:     0.3577 | E_err:   0.009345
[2025-10-23 17:11:52] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -68.275495 | E_var:     0.3104 | E_err:   0.008705
[2025-10-23 17:12:05] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -68.275300 | E_var:     0.2749 | E_err:   0.008192
[2025-10-23 17:12:18] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -68.263675 | E_var:     0.3172 | E_err:   0.008799
[2025-10-23 17:12:31] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -68.299250 | E_var:     0.3358 | E_err:   0.009055
[2025-10-23 17:12:44] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -68.276664 | E_var:     0.3329 | E_err:   0.009015
[2025-10-23 17:12:58] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -68.291038 | E_var:     0.4022 | E_err:   0.009909
[2025-10-23 17:13:11] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -68.275262 | E_var:     0.3783 | E_err:   0.009611
[2025-10-23 17:13:24] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -68.301191 | E_var:     0.3079 | E_err:   0.008670
[2025-10-23 17:13:37] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -68.266350 | E_var:     0.3308 | E_err:   0.008986
[2025-10-23 17:13:50] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -68.276469 | E_var:     0.2937 | E_err:   0.008468
[2025-10-23 17:14:04] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -68.278724 | E_var:     0.2995 | E_err:   0.008551
[2025-10-23 17:14:17] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -68.277249 | E_var:     0.3575 | E_err:   0.009342
[2025-10-23 17:14:30] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -68.285212 | E_var:     0.4392 | E_err:   0.010355
[2025-10-23 17:14:43] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -68.285055 | E_var:     0.3242 | E_err:   0.008897
[2025-10-23 17:14:56] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -68.276433 | E_var:     0.4605 | E_err:   0.010603
[2025-10-23 17:15:09] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -68.276552 | E_var:     0.3051 | E_err:   0.008631
[2025-10-23 17:15:23] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -68.278328 | E_var:     0.3804 | E_err:   0.009636
[2025-10-23 17:15:36] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -68.297704 | E_var:     0.3372 | E_err:   0.009073
[2025-10-23 17:15:49] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -68.288004 | E_var:     0.3172 | E_err:   0.008800
[2025-10-23 17:16:02] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -68.279143 | E_var:     0.3848 | E_err:   0.009693
[2025-10-23 17:16:15] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -68.267520 | E_var:     0.3601 | E_err:   0.009377
[2025-10-23 17:16:29] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -68.261687 | E_var:     0.3276 | E_err:   0.008943
[2025-10-23 17:16:42] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -68.296629 | E_var:     0.3187 | E_err:   0.008821
[2025-10-23 17:16:55] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -68.286149 | E_var:     0.3457 | E_err:   0.009187
[2025-10-23 17:17:08] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -68.279710 | E_var:     0.2941 | E_err:   0.008474
[2025-10-23 17:17:21] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -68.292302 | E_var:     0.3511 | E_err:   0.009259
[2025-10-23 17:17:35] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -68.276645 | E_var:     0.3930 | E_err:   0.009795
[2025-10-23 17:17:48] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -68.275642 | E_var:     0.2842 | E_err:   0.008329
[2025-10-23 17:18:01] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -68.268558 | E_var:     0.4640 | E_err:   0.010643
[2025-10-23 17:18:14] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -68.272100 | E_var:     0.3272 | E_err:   0.008938
[2025-10-23 17:18:27] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -68.288228 | E_var:     0.3276 | E_err:   0.008943
[2025-10-23 17:18:40] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -68.281686 | E_var:     0.3597 | E_err:   0.009371
[2025-10-23 17:18:54] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -68.283069 | E_var:     0.3452 | E_err:   0.009180
[2025-10-23 17:19:07] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -68.301821 | E_var:     0.3330 | E_err:   0.009017
[2025-10-23 17:19:07] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-23 17:19:20] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -68.288792 | E_var:     0.3709 | E_err:   0.009516
[2025-10-23 17:19:33] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -68.287707 | E_var:     0.3659 | E_err:   0.009452
[2025-10-23 17:19:46] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -68.282113 | E_var:     0.3146 | E_err:   0.008763
[2025-10-23 17:20:00] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -68.288462 | E_var:     0.2923 | E_err:   0.008448
[2025-10-23 17:20:13] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -68.291761 | E_var:     0.3587 | E_err:   0.009358
[2025-10-23 17:20:26] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -68.280157 | E_var:     0.3894 | E_err:   0.009750
[2025-10-23 17:20:39] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -68.273165 | E_var:     0.3346 | E_err:   0.009038
[2025-10-23 17:20:52] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -68.277732 | E_var:     0.2950 | E_err:   0.008487
[2025-10-23 17:21:06] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -68.281771 | E_var:     0.3758 | E_err:   0.009579
[2025-10-23 17:21:19] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -68.286421 | E_var:     0.3106 | E_err:   0.008708
[2025-10-23 17:21:32] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -68.269521 | E_var:     0.3398 | E_err:   0.009108
[2025-10-23 17:21:45] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -68.284289 | E_var:     0.3114 | E_err:   0.008720
[2025-10-23 17:21:58] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -68.284996 | E_var:     0.3160 | E_err:   0.008783
[2025-10-23 17:22:11] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -68.264415 | E_var:     0.3345 | E_err:   0.009036
[2025-10-23 17:22:25] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -68.274954 | E_var:     0.2917 | E_err:   0.008440
[2025-10-23 17:22:38] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -68.276216 | E_var:     0.3381 | E_err:   0.009085
[2025-10-23 17:22:51] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -68.283820 | E_var:     0.4099 | E_err:   0.010004
[2025-10-23 17:23:04] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -68.282360 | E_var:     0.3089 | E_err:   0.008685
[2025-10-23 17:23:17] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -68.275840 | E_var:     0.3442 | E_err:   0.009166
[2025-10-23 17:23:30] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -68.269851 | E_var:     0.2726 | E_err:   0.008158
[2025-10-23 17:23:44] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -68.287965 | E_var:     0.3204 | E_err:   0.008844
[2025-10-23 17:23:57] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -68.292113 | E_var:     0.3365 | E_err:   0.009063
[2025-10-23 17:24:10] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -68.267488 | E_var:     0.3524 | E_err:   0.009276
[2025-10-23 17:24:23] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -68.282477 | E_var:     0.3103 | E_err:   0.008704
[2025-10-23 17:24:36] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -68.286403 | E_var:     0.4185 | E_err:   0.010108
[2025-10-23 17:24:50] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -68.289781 | E_var:     0.3368 | E_err:   0.009068
[2025-10-23 17:25:03] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -68.278441 | E_var:     0.3228 | E_err:   0.008877
[2025-10-23 17:25:16] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -68.293288 | E_var:     0.4941 | E_err:   0.010983
[2025-10-23 17:25:29] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -68.283955 | E_var:     0.3787 | E_err:   0.009616
[2025-10-23 17:25:42] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -68.287952 | E_var:     0.2959 | E_err:   0.008500
[2025-10-23 17:25:55] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -68.285871 | E_var:     0.5529 | E_err:   0.011619
[2025-10-23 17:26:09] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -68.278502 | E_var:     0.3510 | E_err:   0.009257
[2025-10-23 17:26:22] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -68.280774 | E_var:     0.3313 | E_err:   0.008994
[2025-10-23 17:26:35] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -68.294854 | E_var:     0.3606 | E_err:   0.009383
[2025-10-23 17:26:48] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -68.295969 | E_var:     0.3883 | E_err:   0.009736
[2025-10-23 17:27:01] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -68.275444 | E_var:     0.3554 | E_err:   0.009314
[2025-10-23 17:27:15] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -68.279575 | E_var:     0.3309 | E_err:   0.008989
[2025-10-23 17:27:28] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -68.272792 | E_var:     0.3198 | E_err:   0.008836
[2025-10-23 17:27:41] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -68.290295 | E_var:     0.5389 | E_err:   0.011471
[2025-10-23 17:27:54] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -68.270818 | E_var:     0.3706 | E_err:   0.009512
[2025-10-23 17:28:07] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -68.280684 | E_var:     0.3441 | E_err:   0.009166
[2025-10-23 17:28:20] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -68.287622 | E_var:     0.3731 | E_err:   0.009544
[2025-10-23 17:28:34] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -68.283605 | E_var:     0.4092 | E_err:   0.009995
[2025-10-23 17:28:47] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -68.291830 | E_var:     0.3499 | E_err:   0.009243
[2025-10-23 17:29:00] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -68.284243 | E_var:     0.3162 | E_err:   0.008787
[2025-10-23 17:29:13] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -68.294665 | E_var:     0.4053 | E_err:   0.009948
[2025-10-23 17:29:26] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -68.282750 | E_var:     0.3706 | E_err:   0.009512
[2025-10-23 17:29:40] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -68.286169 | E_var:     0.3057 | E_err:   0.008638
[2025-10-23 17:29:53] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -68.283917 | E_var:     0.3733 | E_err:   0.009546
[2025-10-23 17:30:06] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -68.290687 | E_var:     0.3881 | E_err:   0.009734
[2025-10-23 17:30:19] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -68.289988 | E_var:     0.3701 | E_err:   0.009506
[2025-10-23 17:30:32] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -68.282463 | E_var:     0.4526 | E_err:   0.010512
[2025-10-23 17:30:45] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -68.289680 | E_var:     0.3691 | E_err:   0.009493
[2025-10-23 17:30:59] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -68.284245 | E_var:     0.3948 | E_err:   0.009818
[2025-10-23 17:31:12] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -68.268344 | E_var:     0.3683 | E_err:   0.009482
[2025-10-23 17:31:25] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -68.277768 | E_var:     0.4548 | E_err:   0.010537
[2025-10-23 17:31:38] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -68.273366 | E_var:     0.3452 | E_err:   0.009180
[2025-10-23 17:31:51] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -68.273571 | E_var:     0.4199 | E_err:   0.010125
[2025-10-23 17:32:05] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -68.285492 | E_var:     0.3354 | E_err:   0.009049
[2025-10-23 17:32:18] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -68.284673 | E_var:     0.3025 | E_err:   0.008594
[2025-10-23 17:32:31] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -68.277997 | E_var:     1.0103 | E_err:   0.015705
[2025-10-23 17:32:44] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -68.279267 | E_var:     0.3541 | E_err:   0.009298
[2025-10-23 17:32:57] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -68.298665 | E_var:     0.3215 | E_err:   0.008860
[2025-10-23 17:33:10] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -68.289392 | E_var:     0.3223 | E_err:   0.008870
[2025-10-23 17:33:24] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -68.272145 | E_var:     0.3343 | E_err:   0.009034
[2025-10-23 17:33:37] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -68.289387 | E_var:     0.4191 | E_err:   0.010115
[2025-10-23 17:33:50] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -68.286122 | E_var:     0.3178 | E_err:   0.008809
[2025-10-23 17:34:03] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -68.276229 | E_var:     0.3197 | E_err:   0.008835
[2025-10-23 17:34:16] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -68.285150 | E_var:     0.3701 | E_err:   0.009505
[2025-10-23 17:34:30] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -68.300925 | E_var:     0.3853 | E_err:   0.009699
[2025-10-23 17:34:43] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -68.290898 | E_var:     0.3438 | E_err:   0.009162
[2025-10-23 17:34:56] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -68.284621 | E_var:     0.2859 | E_err:   0.008355
[2025-10-23 17:35:09] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -68.287990 | E_var:     0.3313 | E_err:   0.008994
[2025-10-23 17:35:22] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -68.283578 | E_var:     0.3115 | E_err:   0.008721
[2025-10-23 17:35:36] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -68.286657 | E_var:     0.3255 | E_err:   0.008915
[2025-10-23 17:35:49] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -68.258544 | E_var:     0.8360 | E_err:   0.014286
[2025-10-23 17:36:02] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -68.285904 | E_var:     0.3450 | E_err:   0.009177
[2025-10-23 17:36:15] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -68.274523 | E_var:     0.3907 | E_err:   0.009766
[2025-10-23 17:36:28] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -68.282292 | E_var:     0.3033 | E_err:   0.008606
[2025-10-23 17:36:41] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -68.285797 | E_var:     0.3666 | E_err:   0.009460
[2025-10-23 17:36:55] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -68.269010 | E_var:     0.3085 | E_err:   0.008679
[2025-10-23 17:37:08] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -68.263921 | E_var:     0.2949 | E_err:   0.008485
[2025-10-23 17:37:21] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -68.266829 | E_var:     0.3419 | E_err:   0.009136
[2025-10-23 17:37:34] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -68.294965 | E_var:     0.3877 | E_err:   0.009729
[2025-10-23 17:37:47] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -68.281054 | E_var:     0.3380 | E_err:   0.009083
[2025-10-23 17:38:01] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -68.282365 | E_var:     0.3675 | E_err:   0.009472
[2025-10-23 17:38:14] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -68.284669 | E_var:     0.3277 | E_err:   0.008944
[2025-10-23 17:38:27] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -68.287663 | E_var:     0.5168 | E_err:   0.011233
[2025-10-23 17:38:40] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -68.269383 | E_var:     0.3259 | E_err:   0.008920
[2025-10-23 17:38:53] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -68.295300 | E_var:     0.2883 | E_err:   0.008390
[2025-10-23 17:39:07] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -68.275153 | E_var:     0.3478 | E_err:   0.009214
[2025-10-23 17:39:20] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -68.277598 | E_var:     0.3524 | E_err:   0.009276
[2025-10-23 17:39:33] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -68.280610 | E_var:     0.3972 | E_err:   0.009847
[2025-10-23 17:39:46] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -68.261461 | E_var:     0.3940 | E_err:   0.009808
[2025-10-23 17:39:59] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -68.279680 | E_var:     0.4058 | E_err:   0.009954
[2025-10-23 17:40:12] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -68.297512 | E_var:     0.3384 | E_err:   0.009090
[2025-10-23 17:40:26] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -68.285711 | E_var:     0.3299 | E_err:   0.008974
[2025-10-23 17:40:39] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -68.281427 | E_var:     0.3154 | E_err:   0.008775
[2025-10-23 17:40:52] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -68.281641 | E_var:     0.3370 | E_err:   0.009070
[2025-10-23 17:41:05] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -68.270196 | E_var:     0.3674 | E_err:   0.009471
[2025-10-23 17:41:19] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -68.286332 | E_var:     0.2894 | E_err:   0.008406
[2025-10-23 17:41:32] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -68.286448 | E_var:     0.3282 | E_err:   0.008951
[2025-10-23 17:41:45] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -68.269734 | E_var:     0.3437 | E_err:   0.009161
[2025-10-23 17:41:58] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -68.268253 | E_var:     0.2928 | E_err:   0.008455
[2025-10-23 17:42:11] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -68.283271 | E_var:     0.3248 | E_err:   0.008905
[2025-10-23 17:42:11] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-23 17:42:24] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -68.270406 | E_var:     0.3296 | E_err:   0.008971
[2025-10-23 17:42:38] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -68.266283 | E_var:     0.3321 | E_err:   0.009004
[2025-10-23 17:42:51] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -68.264576 | E_var:     0.4131 | E_err:   0.010043
[2025-10-23 17:43:04] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -68.265215 | E_var:     0.2980 | E_err:   0.008529
[2025-10-23 17:43:17] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -68.289034 | E_var:     0.3149 | E_err:   0.008769
[2025-10-23 17:43:30] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -68.276407 | E_var:     0.3069 | E_err:   0.008656
[2025-10-23 17:43:44] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -68.284594 | E_var:     0.3022 | E_err:   0.008590
[2025-10-23 17:43:57] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -68.276259 | E_var:     0.4824 | E_err:   0.010852
[2025-10-23 17:44:10] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -68.275102 | E_var:     0.3304 | E_err:   0.008981
[2025-10-23 17:44:23] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -68.275370 | E_var:     0.3541 | E_err:   0.009298
[2025-10-23 17:44:36] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -68.281952 | E_var:     0.3341 | E_err:   0.009031
[2025-10-23 17:44:49] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -68.289563 | E_var:     0.3150 | E_err:   0.008770
[2025-10-23 17:45:03] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -68.288503 | E_var:     0.3389 | E_err:   0.009097
[2025-10-23 17:45:16] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -68.275450 | E_var:     0.3091 | E_err:   0.008687
[2025-10-23 17:45:29] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -68.298098 | E_var:     0.3379 | E_err:   0.009082
[2025-10-23 17:45:42] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -68.279793 | E_var:     0.4358 | E_err:   0.010315
[2025-10-23 17:45:55] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -68.271087 | E_var:     0.3323 | E_err:   0.009006
[2025-10-23 17:46:09] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -68.283236 | E_var:     0.3209 | E_err:   0.008851
[2025-10-23 17:46:22] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -68.281567 | E_var:     0.3499 | E_err:   0.009242
[2025-10-23 17:46:35] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -68.290222 | E_var:     0.4193 | E_err:   0.010118
[2025-10-23 17:46:48] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -68.288046 | E_var:     0.3364 | E_err:   0.009062
[2025-10-23 17:47:01] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -68.295663 | E_var:     0.2809 | E_err:   0.008282
[2025-10-23 17:47:15] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -68.284518 | E_var:     0.3555 | E_err:   0.009316
[2025-10-23 17:47:28] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -68.285354 | E_var:     0.3585 | E_err:   0.009356
[2025-10-23 17:47:41] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -68.286902 | E_var:     0.4094 | E_err:   0.009998
[2025-10-23 17:47:54] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -68.292159 | E_var:     0.3434 | E_err:   0.009157
[2025-10-23 17:48:07] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -68.284723 | E_var:     0.4125 | E_err:   0.010035
[2025-10-23 17:48:20] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -68.290136 | E_var:     0.3144 | E_err:   0.008761
[2025-10-23 17:48:34] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -68.288270 | E_var:     0.3178 | E_err:   0.008809
[2025-10-23 17:48:47] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -68.281250 | E_var:     0.3275 | E_err:   0.008942
[2025-10-23 17:49:00] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -68.283304 | E_var:     0.4985 | E_err:   0.011032
[2025-10-23 17:49:13] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -68.275756 | E_var:     0.4074 | E_err:   0.009973
[2025-10-23 17:49:26] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -68.291250 | E_var:     0.3181 | E_err:   0.008812
[2025-10-23 17:49:40] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -68.288893 | E_var:     0.3516 | E_err:   0.009265
[2025-10-23 17:49:53] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -68.278205 | E_var:     0.4160 | E_err:   0.010077
[2025-10-23 17:50:06] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -68.280970 | E_var:     0.3000 | E_err:   0.008559
[2025-10-23 17:50:19] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -68.260184 | E_var:     0.3595 | E_err:   0.009368
[2025-10-23 17:50:32] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -68.300757 | E_var:     0.3882 | E_err:   0.009735
[2025-10-23 17:50:46] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -68.287128 | E_var:     0.3875 | E_err:   0.009726
[2025-10-23 17:50:59] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -68.266622 | E_var:     0.3534 | E_err:   0.009289
[2025-10-23 17:51:12] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -68.275015 | E_var:     0.3440 | E_err:   0.009164
[2025-10-23 17:51:25] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -68.298118 | E_var:     0.3313 | E_err:   0.008993
[2025-10-23 17:51:38] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -68.281163 | E_var:     0.2831 | E_err:   0.008314
[2025-10-23 17:51:51] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -68.285425 | E_var:     0.3774 | E_err:   0.009599
[2025-10-23 17:52:05] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -68.287944 | E_var:     0.3253 | E_err:   0.008912
[2025-10-23 17:52:18] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -68.297235 | E_var:     0.3239 | E_err:   0.008893
[2025-10-23 17:52:31] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -68.278754 | E_var:     0.3608 | E_err:   0.009385
[2025-10-23 17:52:44] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -68.282429 | E_var:     0.3492 | E_err:   0.009234
[2025-10-23 17:52:57] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -68.281049 | E_var:     0.3408 | E_err:   0.009122
[2025-10-23 17:53:11] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -68.304369 | E_var:     0.3665 | E_err:   0.009460
[2025-10-23 17:53:24] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -68.280072 | E_var:     0.2897 | E_err:   0.008411
[2025-10-23 17:53:37] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -68.269047 | E_var:     0.3554 | E_err:   0.009314
[2025-10-23 17:53:50] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -68.274696 | E_var:     0.4100 | E_err:   0.010005
[2025-10-23 17:54:03] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -68.280966 | E_var:     0.3565 | E_err:   0.009329
[2025-10-23 17:54:17] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -68.282830 | E_var:     0.3184 | E_err:   0.008817
[2025-10-23 17:54:30] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -68.277637 | E_var:     0.3012 | E_err:   0.008575
[2025-10-23 17:54:43] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -68.297969 | E_var:     0.3375 | E_err:   0.009078
[2025-10-23 17:54:56] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -68.292066 | E_var:     0.3221 | E_err:   0.008868
[2025-10-23 17:55:09] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -68.289333 | E_var:     0.2960 | E_err:   0.008501
[2025-10-23 17:55:23] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -68.276307 | E_var:     0.3733 | E_err:   0.009546
[2025-10-23 17:55:36] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -68.299154 | E_var:     0.3453 | E_err:   0.009181
[2025-10-23 17:55:49] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -68.283455 | E_var:     0.3857 | E_err:   0.009704
[2025-10-23 17:56:02] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -68.271197 | E_var:     0.3179 | E_err:   0.008809
[2025-10-23 17:56:15] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -68.300193 | E_var:     0.4026 | E_err:   0.009914
[2025-10-23 17:56:28] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -68.290816 | E_var:     0.3883 | E_err:   0.009737
[2025-10-23 17:56:42] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -68.279966 | E_var:     0.3522 | E_err:   0.009273
[2025-10-23 17:56:55] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -68.287511 | E_var:     0.3145 | E_err:   0.008762
[2025-10-23 17:57:08] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -68.301042 | E_var:     0.4024 | E_err:   0.009911
[2025-10-23 17:57:21] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -68.274654 | E_var:     0.3758 | E_err:   0.009579
[2025-10-23 17:57:34] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -68.290890 | E_var:     0.6485 | E_err:   0.012583
[2025-10-23 17:57:48] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -68.287036 | E_var:     0.3750 | E_err:   0.009568
[2025-10-23 17:58:01] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -68.266838 | E_var:     0.9460 | E_err:   0.015197
[2025-10-23 17:58:14] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -68.290451 | E_var:     0.3134 | E_err:   0.008747
[2025-10-23 17:58:27] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -68.283508 | E_var:     0.2965 | E_err:   0.008507
[2025-10-23 17:58:40] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -68.281510 | E_var:     0.3388 | E_err:   0.009095
[2025-10-23 17:58:53] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -68.273127 | E_var:     0.3293 | E_err:   0.008966
[2025-10-23 17:59:07] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -68.280464 | E_var:     0.2925 | E_err:   0.008451
[2025-10-23 17:59:20] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -68.284131 | E_var:     0.3512 | E_err:   0.009260
[2025-10-23 17:59:33] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -68.277717 | E_var:     0.3424 | E_err:   0.009143
[2025-10-23 17:59:46] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -68.285289 | E_var:     0.2912 | E_err:   0.008432
[2025-10-23 17:59:59] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -68.284333 | E_var:     0.3349 | E_err:   0.009043
[2025-10-23 18:00:13] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -68.278334 | E_var:     0.3258 | E_err:   0.008918
[2025-10-23 18:00:26] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -68.275492 | E_var:     0.3803 | E_err:   0.009636
[2025-10-23 18:00:39] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -68.287338 | E_var:     0.3166 | E_err:   0.008791
[2025-10-23 18:00:52] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -68.283386 | E_var:     0.3218 | E_err:   0.008864
[2025-10-23 18:01:05] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -68.265793 | E_var:     0.3048 | E_err:   0.008627
[2025-10-23 18:01:19] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -68.299790 | E_var:     0.2884 | E_err:   0.008391
[2025-10-23 18:01:32] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -68.279494 | E_var:     0.4211 | E_err:   0.010139
[2025-10-23 18:01:45] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -68.285726 | E_var:     0.7093 | E_err:   0.013160
[2025-10-23 18:01:58] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -68.277672 | E_var:     0.3570 | E_err:   0.009336
[2025-10-23 18:02:11] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -68.274286 | E_var:     0.3641 | E_err:   0.009429
[2025-10-23 18:02:25] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -68.268180 | E_var:     0.3398 | E_err:   0.009108
[2025-10-23 18:02:38] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -68.281846 | E_var:     0.3385 | E_err:   0.009091
[2025-10-23 18:02:51] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -68.298886 | E_var:     0.2955 | E_err:   0.008494
[2025-10-23 18:03:04] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -68.279429 | E_var:     0.2990 | E_err:   0.008543
[2025-10-23 18:03:17] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -68.295440 | E_var:     0.3068 | E_err:   0.008655
[2025-10-23 18:03:30] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -68.276874 | E_var:     0.3817 | E_err:   0.009654
[2025-10-23 18:03:44] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -68.282227 | E_var:     0.3487 | E_err:   0.009227
[2025-10-23 18:03:57] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -68.284982 | E_var:     0.3543 | E_err:   0.009300
[2025-10-23 18:04:10] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -68.287046 | E_var:     0.4036 | E_err:   0.009927
[2025-10-23 18:04:23] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -68.289980 | E_var:     0.3082 | E_err:   0.008674
[2025-10-23 18:04:36] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -68.275335 | E_var:     0.2930 | E_err:   0.008458
[2025-10-23 18:04:50] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -68.278784 | E_var:     0.2984 | E_err:   0.008536
[2025-10-23 18:05:03] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -68.291033 | E_var:     0.3148 | E_err:   0.008766
[2025-10-23 18:05:16] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -68.285318 | E_var:     0.4983 | E_err:   0.011030
[2025-10-23 18:05:16] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-23 18:05:29] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -68.293953 | E_var:     0.3080 | E_err:   0.008671
[2025-10-23 18:05:42] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -68.290088 | E_var:     0.3191 | E_err:   0.008827
[2025-10-23 18:05:56] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -68.295889 | E_var:     0.3545 | E_err:   0.009303
[2025-10-23 18:06:09] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -68.291873 | E_var:     0.4197 | E_err:   0.010123
[2025-10-23 18:06:22] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -68.276459 | E_var:     0.3709 | E_err:   0.009516
[2025-10-23 18:06:35] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -68.279472 | E_var:     0.3175 | E_err:   0.008804
[2025-10-23 18:06:48] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -68.287892 | E_var:     0.3731 | E_err:   0.009545
[2025-10-23 18:07:02] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -68.267909 | E_var:     0.3683 | E_err:   0.009483
[2025-10-23 18:07:15] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -68.306417 | E_var:     0.3513 | E_err:   0.009261
[2025-10-23 18:07:28] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -68.290092 | E_var:     0.3334 | E_err:   0.009022
[2025-10-23 18:07:41] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -68.276170 | E_var:     0.3794 | E_err:   0.009624
[2025-10-23 18:07:54] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -68.285743 | E_var:     0.3665 | E_err:   0.009460
[2025-10-23 18:08:08] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -68.278050 | E_var:     0.3742 | E_err:   0.009559
[2025-10-23 18:08:21] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -68.288693 | E_var:     0.3322 | E_err:   0.009006
[2025-10-23 18:08:34] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -68.279763 | E_var:     0.3238 | E_err:   0.008891
[2025-10-23 18:08:47] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -68.280311 | E_var:     0.3655 | E_err:   0.009447
[2025-10-23 18:09:00] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -68.292172 | E_var:     0.3275 | E_err:   0.008942
[2025-10-23 18:09:13] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -68.278111 | E_var:     0.4202 | E_err:   0.010128
[2025-10-23 18:09:27] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -68.279108 | E_var:     0.6785 | E_err:   0.012870
[2025-10-23 18:09:40] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -68.296214 | E_var:     0.3952 | E_err:   0.009822
[2025-10-23 18:09:53] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -68.285262 | E_var:     0.3330 | E_err:   0.009016
[2025-10-23 18:10:06] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -68.285686 | E_var:     0.3623 | E_err:   0.009405
[2025-10-23 18:10:19] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -68.278255 | E_var:     0.5188 | E_err:   0.011255
[2025-10-23 18:10:33] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -68.291414 | E_var:     0.3660 | E_err:   0.009453
[2025-10-23 18:10:46] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -68.286759 | E_var:     0.3057 | E_err:   0.008639
[2025-10-23 18:10:59] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -68.272721 | E_var:     0.2877 | E_err:   0.008381
[2025-10-23 18:11:12] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -68.279733 | E_var:     0.3752 | E_err:   0.009571
[2025-10-23 18:11:25] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -68.283688 | E_var:     0.3464 | E_err:   0.009197
[2025-10-23 18:11:39] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -68.281189 | E_var:     0.3421 | E_err:   0.009139
[2025-10-23 18:11:52] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -68.297302 | E_var:     0.3417 | E_err:   0.009134
[2025-10-23 18:11:52] 🔄 RESTART #2 | Period: 600
[2025-10-23 18:12:05] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -68.286877 | E_var:     0.4108 | E_err:   0.010015
[2025-10-23 18:12:18] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -68.285650 | E_var:     0.3621 | E_err:   0.009403
[2025-10-23 18:12:31] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -68.296634 | E_var:     0.3553 | E_err:   0.009314
[2025-10-23 18:12:44] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -68.280100 | E_var:     0.3154 | E_err:   0.008776
[2025-10-23 18:12:58] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -68.285834 | E_var:     0.2891 | E_err:   0.008402
[2025-10-23 18:13:11] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -68.288038 | E_var:     0.4468 | E_err:   0.010444
[2025-10-23 18:13:24] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -68.291593 | E_var:     0.3511 | E_err:   0.009259
[2025-10-23 18:13:37] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -68.272538 | E_var:     0.2801 | E_err:   0.008269
[2025-10-23 18:13:50] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -68.286582 | E_var:     0.3552 | E_err:   0.009312
[2025-10-23 18:14:04] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -68.287130 | E_var:     0.3363 | E_err:   0.009061
[2025-10-23 18:14:17] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -68.291211 | E_var:     0.3702 | E_err:   0.009506
[2025-10-23 18:14:30] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -68.274124 | E_var:     0.3331 | E_err:   0.009017
[2025-10-23 18:14:43] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -68.316962 | E_var:     0.3784 | E_err:   0.009611
[2025-10-23 18:14:56] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -68.276429 | E_var:     0.4590 | E_err:   0.010586
[2025-10-23 18:15:10] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -68.287772 | E_var:     0.2969 | E_err:   0.008514
[2025-10-23 18:15:23] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -68.279183 | E_var:     0.2861 | E_err:   0.008358
[2025-10-23 18:15:36] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -68.293266 | E_var:     0.2968 | E_err:   0.008513
[2025-10-23 18:15:49] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -68.272983 | E_var:     0.3827 | E_err:   0.009666
[2025-10-23 18:16:02] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -68.281593 | E_var:     0.3183 | E_err:   0.008816
[2025-10-23 18:16:15] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -68.260405 | E_var:     0.4020 | E_err:   0.009907
[2025-10-23 18:16:29] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -68.278652 | E_var:     0.3757 | E_err:   0.009577
[2025-10-23 18:16:42] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -68.288845 | E_var:     0.4913 | E_err:   0.010952
[2025-10-23 18:16:55] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -68.286255 | E_var:     0.4003 | E_err:   0.009886
[2025-10-23 18:17:08] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -68.282136 | E_var:     0.3096 | E_err:   0.008694
[2025-10-23 18:17:21] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -68.285187 | E_var:     0.3923 | E_err:   0.009786
[2025-10-23 18:17:35] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -68.282977 | E_var:     0.3879 | E_err:   0.009731
[2025-10-23 18:17:48] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -68.287815 | E_var:     0.3075 | E_err:   0.008665
[2025-10-23 18:18:01] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -68.303709 | E_var:     0.3647 | E_err:   0.009437
[2025-10-23 18:18:14] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -68.275345 | E_var:     0.3248 | E_err:   0.008905
[2025-10-23 18:18:27] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -68.286787 | E_var:     0.3729 | E_err:   0.009542
[2025-10-23 18:18:40] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -68.275926 | E_var:     0.4697 | E_err:   0.010708
[2025-10-23 18:18:54] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -68.295459 | E_var:     0.3500 | E_err:   0.009244
[2025-10-23 18:19:07] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -68.288691 | E_var:     1.0670 | E_err:   0.016140
[2025-10-23 18:19:20] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -68.296064 | E_var:     0.3442 | E_err:   0.009166
[2025-10-23 18:19:34] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -68.277419 | E_var:     0.3731 | E_err:   0.009544
[2025-10-23 18:19:47] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -68.280112 | E_var:     0.4250 | E_err:   0.010186
[2025-10-23 18:20:00] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -68.267459 | E_var:     0.3024 | E_err:   0.008592
[2025-10-23 18:20:14] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -68.279293 | E_var:     0.2696 | E_err:   0.008113
[2025-10-23 18:20:27] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -68.298638 | E_var:     0.3697 | E_err:   0.009500
[2025-10-23 18:20:40] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -68.281553 | E_var:     0.3417 | E_err:   0.009133
[2025-10-23 18:20:53] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -68.269711 | E_var:     0.4154 | E_err:   0.010070
[2025-10-23 18:21:06] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -68.289438 | E_var:     0.2937 | E_err:   0.008468
[2025-10-23 18:21:19] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -68.285547 | E_var:     0.3048 | E_err:   0.008626
[2025-10-23 18:21:33] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -68.281910 | E_var:     0.3797 | E_err:   0.009629
[2025-10-23 18:21:46] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -68.262170 | E_var:     0.3778 | E_err:   0.009603
[2025-10-23 18:21:59] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -68.281461 | E_var:     0.5333 | E_err:   0.011410
[2025-10-23 18:22:12] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -68.269004 | E_var:     0.3111 | E_err:   0.008714
[2025-10-23 18:22:25] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -68.282765 | E_var:     0.3689 | E_err:   0.009490
[2025-10-23 18:22:39] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -68.272561 | E_var:     0.5925 | E_err:   0.012027
[2025-10-23 18:22:52] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -68.282290 | E_var:     0.3092 | E_err:   0.008688
[2025-10-23 18:23:05] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -68.269372 | E_var:     0.3269 | E_err:   0.008933
[2025-10-23 18:23:18] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -68.285186 | E_var:     0.3698 | E_err:   0.009502
[2025-10-23 18:23:31] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -68.286711 | E_var:     0.3528 | E_err:   0.009281
[2025-10-23 18:23:45] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -68.272043 | E_var:     0.3155 | E_err:   0.008776
[2025-10-23 18:23:58] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -68.285182 | E_var:     0.3406 | E_err:   0.009119
[2025-10-23 18:24:11] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -68.277847 | E_var:     0.3211 | E_err:   0.008855
[2025-10-23 18:24:24] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -68.273253 | E_var:     0.3585 | E_err:   0.009356
[2025-10-23 18:24:37] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -68.286068 | E_var:     0.2888 | E_err:   0.008397
[2025-10-23 18:24:50] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -68.288749 | E_var:     0.2984 | E_err:   0.008535
[2025-10-23 18:25:04] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -68.278937 | E_var:     0.3381 | E_err:   0.009086
[2025-10-23 18:25:17] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -68.265345 | E_var:     0.3751 | E_err:   0.009570
[2025-10-23 18:25:30] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -68.280787 | E_var:     0.3504 | E_err:   0.009249
[2025-10-23 18:25:43] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -68.265196 | E_var:     0.3273 | E_err:   0.008939
[2025-10-23 18:25:56] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -68.276310 | E_var:     0.4291 | E_err:   0.010236
[2025-10-23 18:26:10] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -68.276975 | E_var:     0.4496 | E_err:   0.010477
[2025-10-23 18:26:23] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -68.276636 | E_var:     0.3135 | E_err:   0.008748
[2025-10-23 18:26:36] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -68.285134 | E_var:     0.4331 | E_err:   0.010283
[2025-10-23 18:26:49] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -68.290641 | E_var:     0.3870 | E_err:   0.009720
[2025-10-23 18:27:02] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -68.269533 | E_var:     0.3349 | E_err:   0.009042
[2025-10-23 18:27:15] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -68.299245 | E_var:     0.2992 | E_err:   0.008547
[2025-10-23 18:27:29] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -68.271003 | E_var:     0.3384 | E_err:   0.009089
[2025-10-23 18:27:42] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -68.281924 | E_var:     0.4357 | E_err:   0.010313
[2025-10-23 18:27:55] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -68.287026 | E_var:     0.3258 | E_err:   0.008919
[2025-10-23 18:28:08] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -68.274010 | E_var:     0.4828 | E_err:   0.010857
[2025-10-23 18:28:21] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -68.283308 | E_var:     0.3646 | E_err:   0.009435
[2025-10-23 18:28:21] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-23 18:28:35] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -68.285234 | E_var:     0.3699 | E_err:   0.009503
[2025-10-23 18:28:48] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -68.278201 | E_var:     0.3344 | E_err:   0.009036
[2025-10-23 18:29:01] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -68.271720 | E_var:     0.3345 | E_err:   0.009037
[2025-10-23 18:29:14] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -68.281414 | E_var:     0.2812 | E_err:   0.008285
[2025-10-23 18:29:27] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -68.289970 | E_var:     0.3158 | E_err:   0.008781
[2025-10-23 18:29:41] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -68.275552 | E_var:     0.3459 | E_err:   0.009189
[2025-10-23 18:29:54] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -68.289130 | E_var:     0.3230 | E_err:   0.008880
[2025-10-23 18:30:07] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -68.282763 | E_var:     0.3479 | E_err:   0.009217
[2025-10-23 18:30:20] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -68.285265 | E_var:     0.3211 | E_err:   0.008854
[2025-10-23 18:30:33] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -68.267080 | E_var:     0.4023 | E_err:   0.009911
[2025-10-23 18:30:46] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -68.284388 | E_var:     0.3359 | E_err:   0.009055
[2025-10-23 18:31:00] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -68.281210 | E_var:     0.3108 | E_err:   0.008711
[2025-10-23 18:31:13] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -68.278316 | E_var:     0.3960 | E_err:   0.009833
[2025-10-23 18:31:26] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -68.284323 | E_var:     0.3379 | E_err:   0.009082
[2025-10-23 18:31:39] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -68.293228 | E_var:     0.3367 | E_err:   0.009066
[2025-10-23 18:31:52] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -68.288571 | E_var:     0.3445 | E_err:   0.009172
[2025-10-23 18:32:06] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -68.282637 | E_var:     0.3801 | E_err:   0.009633
[2025-10-23 18:32:19] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -68.290669 | E_var:     0.4389 | E_err:   0.010352
[2025-10-23 18:32:32] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -68.283575 | E_var:     0.3063 | E_err:   0.008647
[2025-10-23 18:32:45] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -68.291803 | E_var:     0.3094 | E_err:   0.008692
[2025-10-23 18:32:58] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -68.277753 | E_var:     0.3093 | E_err:   0.008689
[2025-10-23 18:33:11] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -68.276151 | E_var:     0.2953 | E_err:   0.008491
[2025-10-23 18:33:25] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -68.288033 | E_var:     0.3322 | E_err:   0.009005
[2025-10-23 18:33:38] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -68.280807 | E_var:     0.3677 | E_err:   0.009475
[2025-10-23 18:33:51] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -68.280198 | E_var:     0.3839 | E_err:   0.009681
[2025-10-23 18:34:04] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -68.267978 | E_var:     0.3354 | E_err:   0.009050
[2025-10-23 18:34:17] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -68.276310 | E_var:     0.4341 | E_err:   0.010295
[2025-10-23 18:34:30] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -68.286698 | E_var:     0.3529 | E_err:   0.009282
[2025-10-23 18:34:44] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -68.279342 | E_var:     0.3771 | E_err:   0.009595
[2025-10-23 18:34:57] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -68.282492 | E_var:     0.3033 | E_err:   0.008605
[2025-10-23 18:35:10] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -68.291817 | E_var:     0.4015 | E_err:   0.009901
[2025-10-23 18:35:23] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -68.288264 | E_var:     0.3893 | E_err:   0.009749
[2025-10-23 18:35:36] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -68.278491 | E_var:     0.2983 | E_err:   0.008534
[2025-10-23 18:35:50] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -68.278847 | E_var:     0.3193 | E_err:   0.008829
[2025-10-23 18:36:03] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -68.271229 | E_var:     0.4053 | E_err:   0.009947
[2025-10-23 18:36:16] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -68.279599 | E_var:     0.3418 | E_err:   0.009136
[2025-10-23 18:36:29] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -68.287696 | E_var:     0.3002 | E_err:   0.008561
[2025-10-23 18:36:42] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -68.291746 | E_var:     0.3079 | E_err:   0.008669
[2025-10-23 18:36:55] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -68.279714 | E_var:     0.4232 | E_err:   0.010164
[2025-10-23 18:37:09] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -68.286067 | E_var:     0.3266 | E_err:   0.008929
[2025-10-23 18:37:22] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -68.274985 | E_var:     0.4057 | E_err:   0.009952
[2025-10-23 18:37:35] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -68.270409 | E_var:     0.3026 | E_err:   0.008594
[2025-10-23 18:37:48] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -68.288572 | E_var:     0.3256 | E_err:   0.008916
[2025-10-23 18:38:01] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -68.299675 | E_var:     0.3146 | E_err:   0.008763
[2025-10-23 18:38:15] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -68.276920 | E_var:     0.3980 | E_err:   0.009857
[2025-10-23 18:38:28] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -68.274495 | E_var:     0.5774 | E_err:   0.011873
[2025-10-23 18:38:41] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -68.290095 | E_var:     0.3086 | E_err:   0.008680
[2025-10-23 18:38:54] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -68.289435 | E_var:     0.4108 | E_err:   0.010014
[2025-10-23 18:39:07] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -68.282744 | E_var:     0.4147 | E_err:   0.010062
[2025-10-23 18:39:20] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -68.281784 | E_var:     0.3998 | E_err:   0.009879
[2025-10-23 18:39:34] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -68.307404 | E_var:     0.2993 | E_err:   0.008549
[2025-10-23 18:39:47] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -68.278456 | E_var:     0.3208 | E_err:   0.008849
[2025-10-23 18:40:00] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -68.275549 | E_var:     0.2931 | E_err:   0.008460
[2025-10-23 18:40:13] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -68.296263 | E_var:     0.2829 | E_err:   0.008311
[2025-10-23 18:40:26] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -68.257238 | E_var:     0.3504 | E_err:   0.009249
[2025-10-23 18:40:40] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -68.282363 | E_var:     0.3073 | E_err:   0.008662
[2025-10-23 18:40:53] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -68.289241 | E_var:     0.2867 | E_err:   0.008367
[2025-10-23 18:41:06] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -68.302400 | E_var:     0.4541 | E_err:   0.010529
[2025-10-23 18:41:19] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -68.280696 | E_var:     0.3800 | E_err:   0.009631
[2025-10-23 18:41:32] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -68.290442 | E_var:     0.3342 | E_err:   0.009033
[2025-10-23 18:41:45] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -68.303543 | E_var:     0.3608 | E_err:   0.009385
[2025-10-23 18:41:59] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -68.290653 | E_var:     0.3351 | E_err:   0.009045
[2025-10-23 18:42:12] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -68.276351 | E_var:     0.2942 | E_err:   0.008474
[2025-10-23 18:42:25] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -68.294362 | E_var:     0.3531 | E_err:   0.009285
[2025-10-23 18:42:38] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -68.279313 | E_var:     0.3371 | E_err:   0.009072
[2025-10-23 18:42:51] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -68.282684 | E_var:     0.3420 | E_err:   0.009137
[2025-10-23 18:43:04] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -68.279939 | E_var:     0.3025 | E_err:   0.008594
[2025-10-23 18:43:18] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -68.273021 | E_var:     0.3076 | E_err:   0.008666
[2025-10-23 18:43:31] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -68.301568 | E_var:     0.3833 | E_err:   0.009674
[2025-10-23 18:43:44] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -68.283433 | E_var:     0.3582 | E_err:   0.009351
[2025-10-23 18:43:57] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -68.287163 | E_var:     0.3161 | E_err:   0.008785
[2025-10-23 18:44:10] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -68.262013 | E_var:     0.4192 | E_err:   0.010117
[2025-10-23 18:44:24] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -68.281311 | E_var:     0.3110 | E_err:   0.008714
[2025-10-23 18:44:37] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -68.277497 | E_var:     0.3596 | E_err:   0.009370
[2025-10-23 18:44:50] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -68.287141 | E_var:     0.3232 | E_err:   0.008883
[2025-10-23 18:45:03] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -68.281935 | E_var:     0.4607 | E_err:   0.010605
[2025-10-23 18:45:16] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -68.295960 | E_var:     0.4092 | E_err:   0.009996
[2025-10-23 18:45:29] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -68.296104 | E_var:     0.3965 | E_err:   0.009838
[2025-10-23 18:45:43] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -68.293889 | E_var:     0.3396 | E_err:   0.009106
[2025-10-23 18:45:56] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -68.275463 | E_var:     0.4913 | E_err:   0.010952
[2025-10-23 18:46:09] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -68.292059 | E_var:     0.2935 | E_err:   0.008465
[2025-10-23 18:46:22] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -68.282700 | E_var:     0.3057 | E_err:   0.008640
[2025-10-23 18:46:35] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -68.272897 | E_var:     0.2978 | E_err:   0.008526
[2025-10-23 18:46:48] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -68.304486 | E_var:     0.3206 | E_err:   0.008848
[2025-10-23 18:47:02] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -68.267724 | E_var:     0.3150 | E_err:   0.008769
[2025-10-23 18:47:15] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -68.277941 | E_var:     0.2756 | E_err:   0.008203
[2025-10-23 18:47:28] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -68.281691 | E_var:     0.2924 | E_err:   0.008449
[2025-10-23 18:47:41] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -68.274554 | E_var:     0.3394 | E_err:   0.009102
[2025-10-23 18:47:54] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -68.294428 | E_var:     0.3428 | E_err:   0.009149
[2025-10-23 18:48:07] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -68.286480 | E_var:     0.4455 | E_err:   0.010429
[2025-10-23 18:48:21] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -68.277533 | E_var:     0.2923 | E_err:   0.008447
[2025-10-23 18:48:34] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -68.278523 | E_var:     0.3403 | E_err:   0.009115
[2025-10-23 18:48:47] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -68.305078 | E_var:     0.3803 | E_err:   0.009635
[2025-10-23 18:49:00] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -68.294462 | E_var:     0.3403 | E_err:   0.009115
[2025-10-23 18:49:13] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -68.287389 | E_var:     0.3316 | E_err:   0.008997
[2025-10-23 18:49:27] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -68.277613 | E_var:     0.2963 | E_err:   0.008506
[2025-10-23 18:49:40] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -68.283866 | E_var:     0.5010 | E_err:   0.011059
[2025-10-23 18:49:53] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -68.274771 | E_var:     0.4462 | E_err:   0.010437
[2025-10-23 18:50:06] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -68.275797 | E_var:     0.3703 | E_err:   0.009509
[2025-10-23 18:50:19] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -68.289053 | E_var:     0.3886 | E_err:   0.009740
[2025-10-23 18:50:32] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -68.278549 | E_var:     0.4646 | E_err:   0.010650
[2025-10-23 18:50:46] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -68.298075 | E_var:     0.2778 | E_err:   0.008235
[2025-10-23 18:50:59] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -68.289037 | E_var:     0.2881 | E_err:   0.008386
[2025-10-23 18:51:12] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -68.283263 | E_var:     0.3185 | E_err:   0.008819
[2025-10-23 18:51:25] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -68.275787 | E_var:     0.3031 | E_err:   0.008603
[2025-10-23 18:51:25] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-23 18:51:38] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -68.285089 | E_var:     0.3054 | E_err:   0.008635
[2025-10-23 18:51:52] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -68.261576 | E_var:     0.3012 | E_err:   0.008576
[2025-10-23 18:52:05] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -68.272238 | E_var:     0.3790 | E_err:   0.009619
[2025-10-23 18:52:18] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -68.263900 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 18:52:31] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -68.283284 | E_var:     0.4229 | E_err:   0.010162
[2025-10-23 18:52:44] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -68.271076 | E_var:     0.3748 | E_err:   0.009566
[2025-10-23 18:52:57] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -68.297764 | E_var:     0.3841 | E_err:   0.009683
[2025-10-23 18:53:11] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -68.286713 | E_var:     0.3114 | E_err:   0.008719
[2025-10-23 18:53:24] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -68.273445 | E_var:     0.3370 | E_err:   0.009071
[2025-10-23 18:53:37] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -68.283757 | E_var:     0.3217 | E_err:   0.008862
[2025-10-23 18:53:50] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -68.281365 | E_var:     0.3082 | E_err:   0.008674
[2025-10-23 18:54:03] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -68.299844 | E_var:     0.3028 | E_err:   0.008598
[2025-10-23 18:54:16] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -68.292176 | E_var:     0.3834 | E_err:   0.009675
[2025-10-23 18:54:30] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -68.278570 | E_var:     0.3383 | E_err:   0.009089
[2025-10-23 18:54:43] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -68.277780 | E_var:     0.3262 | E_err:   0.008925
[2025-10-23 18:54:56] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -68.272885 | E_var:     0.3495 | E_err:   0.009237
[2025-10-23 18:55:09] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -68.295600 | E_var:     0.4262 | E_err:   0.010200
[2025-10-23 18:55:22] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -68.289244 | E_var:     0.2730 | E_err:   0.008164
[2025-10-23 18:55:36] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -68.298481 | E_var:     0.2942 | E_err:   0.008474
[2025-10-23 18:55:49] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -68.290117 | E_var:     0.3204 | E_err:   0.008844
[2025-10-23 18:56:02] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -68.282618 | E_var:     0.3984 | E_err:   0.009863
[2025-10-23 18:56:15] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -68.285850 | E_var:     0.3090 | E_err:   0.008685
[2025-10-23 18:56:28] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -68.282424 | E_var:     0.3354 | E_err:   0.009049
[2025-10-23 18:56:41] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -68.286274 | E_var:     0.5918 | E_err:   0.012020
[2025-10-23 18:56:55] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -68.275444 | E_var:     0.3372 | E_err:   0.009074
[2025-10-23 18:57:08] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -68.294162 | E_var:     0.3303 | E_err:   0.008980
[2025-10-23 18:57:21] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -68.282630 | E_var:     0.3144 | E_err:   0.008761
[2025-10-23 18:57:35] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -68.287590 | E_var:     0.3461 | E_err:   0.009192
[2025-10-23 18:57:48] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -68.286494 | E_var:     0.3556 | E_err:   0.009317
[2025-10-23 18:58:01] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -68.292751 | E_var:     0.3316 | E_err:   0.008998
[2025-10-23 18:58:14] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -68.284510 | E_var:     0.4418 | E_err:   0.010386
[2025-10-23 18:58:27] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -68.290069 | E_var:     0.3509 | E_err:   0.009256
[2025-10-23 18:58:41] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -68.297271 | E_var:     0.5411 | E_err:   0.011494
[2025-10-23 18:58:54] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -68.280104 | E_var:     0.3485 | E_err:   0.009224
[2025-10-23 18:59:07] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -68.275326 | E_var:     0.3493 | E_err:   0.009235
[2025-10-23 18:59:20] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -68.280424 | E_var:     0.3303 | E_err:   0.008981
[2025-10-23 18:59:33] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -68.286399 | E_var:     0.3662 | E_err:   0.009456
[2025-10-23 18:59:47] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -68.284305 | E_var:     0.3429 | E_err:   0.009149
[2025-10-23 19:00:00] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -68.291963 | E_var:     0.3330 | E_err:   0.009017
[2025-10-23 19:00:13] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -68.294261 | E_var:     0.2962 | E_err:   0.008504
[2025-10-23 19:00:26] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -68.282087 | E_var:     0.3058 | E_err:   0.008641
[2025-10-23 19:00:39] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -68.262403 | E_var:     0.3591 | E_err:   0.009363
[2025-10-23 19:00:52] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -68.272746 | E_var:     0.2943 | E_err:   0.008476
[2025-10-23 19:01:06] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -68.276962 | E_var:     0.3509 | E_err:   0.009256
[2025-10-23 19:01:19] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -68.281935 | E_var:     0.3539 | E_err:   0.009295
[2025-10-23 19:01:32] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -68.277417 | E_var:     0.3731 | E_err:   0.009544
[2025-10-23 19:01:45] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -68.281521 | E_var:     0.3080 | E_err:   0.008671
[2025-10-23 19:01:58] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -68.282278 | E_var:     0.3127 | E_err:   0.008738
[2025-10-23 19:02:12] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -68.268153 | E_var:     0.3823 | E_err:   0.009661
[2025-10-23 19:02:25] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -68.277226 | E_var:     0.5500 | E_err:   0.011588
[2025-10-23 19:02:38] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -68.291314 | E_var:     0.6991 | E_err:   0.013064
[2025-10-23 19:02:51] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -68.262581 | E_var:     0.4052 | E_err:   0.009947
[2025-10-23 19:03:04] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -68.278938 | E_var:     0.3491 | E_err:   0.009232
[2025-10-23 19:03:17] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -68.282111 | E_var:     0.3385 | E_err:   0.009090
[2025-10-23 19:03:31] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -68.286788 | E_var:     0.3170 | E_err:   0.008798
[2025-10-23 19:03:44] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -68.284705 | E_var:     0.3176 | E_err:   0.008806
[2025-10-23 19:03:57] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -68.284489 | E_var:     0.2987 | E_err:   0.008539
[2025-10-23 19:04:10] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -68.280466 | E_var:     0.2921 | E_err:   0.008445
[2025-10-23 19:04:23] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -68.277345 | E_var:     0.3680 | E_err:   0.009479
[2025-10-23 19:04:37] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -68.271670 | E_var:     0.3541 | E_err:   0.009298
[2025-10-23 19:04:50] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -68.288469 | E_var:     0.3263 | E_err:   0.008926
[2025-10-23 19:05:03] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -68.277698 | E_var:     0.3619 | E_err:   0.009400
[2025-10-23 19:05:16] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -68.280672 | E_var:     0.3916 | E_err:   0.009778
[2025-10-23 19:05:29] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -68.266724 | E_var:     0.6809 | E_err:   0.012893
[2025-10-23 19:05:42] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -68.286219 | E_var:     0.3200 | E_err:   0.008839
[2025-10-23 19:05:56] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -68.285129 | E_var:     0.3365 | E_err:   0.009063
[2025-10-23 19:06:09] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -68.285483 | E_var:     0.3018 | E_err:   0.008584
[2025-10-23 19:06:22] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -68.282542 | E_var:     0.3313 | E_err:   0.008994
[2025-10-23 19:06:35] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -68.283437 | E_var:     0.3011 | E_err:   0.008574
[2025-10-23 19:06:48] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -68.288894 | E_var:     0.3406 | E_err:   0.009119
[2025-10-23 19:07:02] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -68.286705 | E_var:     0.2644 | E_err:   0.008035
[2025-10-23 19:07:15] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -68.281286 | E_var:     0.5364 | E_err:   0.011444
[2025-10-23 19:07:28] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -68.288625 | E_var:     0.3149 | E_err:   0.008768
[2025-10-23 19:07:41] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -68.277092 | E_var:     0.3167 | E_err:   0.008793
[2025-10-23 19:07:54] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -68.272186 | E_var:     0.3710 | E_err:   0.009518
[2025-10-23 19:08:07] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -68.293288 | E_var:     0.3705 | E_err:   0.009511
[2025-10-23 19:08:21] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -68.283115 | E_var:     0.4234 | E_err:   0.010167
[2025-10-23 19:08:34] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -68.279580 | E_var:     0.2903 | E_err:   0.008419
[2025-10-23 19:08:47] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -68.279189 | E_var:     0.3166 | E_err:   0.008792
[2025-10-23 19:09:00] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -68.286763 | E_var:     0.5253 | E_err:   0.011324
[2025-10-23 19:09:13] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -68.300465 | E_var:     0.3334 | E_err:   0.009021
[2025-10-23 19:09:27] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -68.297911 | E_var:     0.3316 | E_err:   0.008997
[2025-10-23 19:09:40] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -68.272872 | E_var:     0.3844 | E_err:   0.009688
[2025-10-23 19:09:53] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -68.284669 | E_var:     0.3133 | E_err:   0.008745
[2025-10-23 19:10:06] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -68.298395 | E_var:     0.4317 | E_err:   0.010266
[2025-10-23 19:10:19] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -68.278392 | E_var:     0.3873 | E_err:   0.009725
[2025-10-23 19:10:32] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -68.269972 | E_var:     0.3297 | E_err:   0.008971
[2025-10-23 19:10:46] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -68.288078 | E_var:     0.2839 | E_err:   0.008325
[2025-10-23 19:10:59] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -68.277364 | E_var:     0.3426 | E_err:   0.009146
[2025-10-23 19:11:12] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -68.296402 | E_var:     0.3480 | E_err:   0.009217
[2025-10-23 19:11:25] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -68.282577 | E_var:     0.3226 | E_err:   0.008875
[2025-10-23 19:11:38] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -68.275588 | E_var:     0.3729 | E_err:   0.009541
[2025-10-23 19:11:52] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -68.274669 | E_var:     0.3706 | E_err:   0.009512
[2025-10-23 19:12:05] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -68.282174 | E_var:     0.3414 | E_err:   0.009129
[2025-10-23 19:12:18] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -68.288695 | E_var:     0.3060 | E_err:   0.008643
[2025-10-23 19:12:31] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -68.292109 | E_var:     0.4699 | E_err:   0.010710
[2025-10-23 19:12:44] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -68.267274 | E_var:     0.4042 | E_err:   0.009934
[2025-10-23 19:12:57] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -68.292740 | E_var:     0.3150 | E_err:   0.008769
[2025-10-23 19:13:11] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -68.272461 | E_var:     0.3310 | E_err:   0.008990
[2025-10-23 19:13:24] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -68.294059 | E_var:     0.3606 | E_err:   0.009383
[2025-10-23 19:13:37] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -68.288239 | E_var:     0.3501 | E_err:   0.009245
[2025-10-23 19:13:50] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -68.282620 | E_var:     0.3480 | E_err:   0.009217
[2025-10-23 19:14:03] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -68.280436 | E_var:     0.3222 | E_err:   0.008869
[2025-10-23 19:14:17] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -68.291123 | E_var:     0.4381 | E_err:   0.010342
[2025-10-23 19:14:30] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -68.287214 | E_var:     0.2752 | E_err:   0.008197
[2025-10-23 19:14:30] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-23 19:14:43] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -68.284860 | E_var:     0.3046 | E_err:   0.008624
[2025-10-23 19:14:56] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -68.273471 | E_var:     0.3357 | E_err:   0.009053
[2025-10-23 19:15:09] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -68.268058 | E_var:     0.3018 | E_err:   0.008584
[2025-10-23 19:15:22] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -68.287399 | E_var:     0.4503 | E_err:   0.010485
[2025-10-23 19:15:36] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -68.286051 | E_var:     0.3250 | E_err:   0.008907
[2025-10-23 19:15:49] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -68.290970 | E_var:     0.3259 | E_err:   0.008920
[2025-10-23 19:16:02] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -68.292111 | E_var:     0.3117 | E_err:   0.008723
[2025-10-23 19:16:15] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -68.273177 | E_var:     0.3641 | E_err:   0.009429
[2025-10-23 19:16:28] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -68.270327 | E_var:     0.5007 | E_err:   0.011056
[2025-10-23 19:16:42] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -68.292749 | E_var:     0.3070 | E_err:   0.008658
[2025-10-23 19:16:55] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -68.264491 | E_var:     0.3549 | E_err:   0.009309
[2025-10-23 19:17:08] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -68.279140 | E_var:     0.2997 | E_err:   0.008554
[2025-10-23 19:17:21] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -68.277367 | E_var:     0.5055 | E_err:   0.011109
[2025-10-23 19:17:34] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -68.291491 | E_var:     0.3007 | E_err:   0.008569
[2025-10-23 19:17:48] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -68.272946 | E_var:     0.5214 | E_err:   0.011283
[2025-10-23 19:18:01] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -68.298463 | E_var:     0.3496 | E_err:   0.009238
[2025-10-23 19:18:14] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -68.282093 | E_var:     0.3766 | E_err:   0.009588
[2025-10-23 19:18:27] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -68.286313 | E_var:     0.2698 | E_err:   0.008115
[2025-10-23 19:18:40] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -68.276079 | E_var:     0.3014 | E_err:   0.008579
[2025-10-23 19:18:53] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -68.289010 | E_var:     0.3793 | E_err:   0.009623
[2025-10-23 19:19:07] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -68.274806 | E_var:     0.4062 | E_err:   0.009958
[2025-10-23 19:19:20] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -68.280134 | E_var:     0.4211 | E_err:   0.010140
[2025-10-23 19:19:33] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -68.287140 | E_var:     0.3029 | E_err:   0.008599
[2025-10-23 19:19:46] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -68.273112 | E_var:     0.3530 | E_err:   0.009283
[2025-10-23 19:19:59] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -68.278856 | E_var:     0.3245 | E_err:   0.008900
[2025-10-23 19:20:12] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -68.296280 | E_var:     0.3202 | E_err:   0.008841
[2025-10-23 19:20:26] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -68.295727 | E_var:     0.3472 | E_err:   0.009207
[2025-10-23 19:20:39] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -68.276780 | E_var:     0.3437 | E_err:   0.009160
[2025-10-23 19:20:52] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -68.258403 | E_var:     0.3401 | E_err:   0.009113
[2025-10-23 19:21:05] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -68.282709 | E_var:     0.2868 | E_err:   0.008368
[2025-10-23 19:21:18] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -68.284569 | E_var:     0.3494 | E_err:   0.009236
[2025-10-23 19:21:32] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -68.281748 | E_var:     0.3638 | E_err:   0.009424
[2025-10-23 19:21:45] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -68.285343 | E_var:     0.3606 | E_err:   0.009382
[2025-10-23 19:21:58] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -68.297722 | E_var:     0.3505 | E_err:   0.009250
[2025-10-23 19:22:11] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -68.270764 | E_var:     0.4433 | E_err:   0.010403
[2025-10-23 19:22:24] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -68.279423 | E_var:     0.3058 | E_err:   0.008640
[2025-10-23 19:22:38] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -68.283256 | E_var:     0.3617 | E_err:   0.009398
[2025-10-23 19:22:51] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -68.306809 | E_var:     0.3755 | E_err:   0.009575
[2025-10-23 19:23:04] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -68.281020 | E_var:     0.3000 | E_err:   0.008558
[2025-10-23 19:23:17] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -68.270244 | E_var:     0.3497 | E_err:   0.009240
[2025-10-23 19:23:30] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -68.280995 | E_var:     0.3615 | E_err:   0.009394
[2025-10-23 19:23:43] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -68.276147 | E_var:     0.3781 | E_err:   0.009608
[2025-10-23 19:23:57] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -68.302560 | E_var:     0.3011 | E_err:   0.008574
[2025-10-23 19:24:10] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -68.295118 | E_var:     0.2765 | E_err:   0.008216
[2025-10-23 19:24:23] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -68.288452 | E_var:     0.3709 | E_err:   0.009516
[2025-10-23 19:24:36] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -68.280596 | E_var:     0.3010 | E_err:   0.008573
[2025-10-23 19:24:49] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -68.294303 | E_var:     0.3287 | E_err:   0.008959
[2025-10-23 19:25:02] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -68.283562 | E_var:     0.3097 | E_err:   0.008696
[2025-10-23 19:25:16] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -68.274485 | E_var:     0.2834 | E_err:   0.008318
[2025-10-23 19:25:29] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -68.287208 | E_var:     0.3553 | E_err:   0.009314
[2025-10-23 19:25:42] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -68.280769 | E_var:     0.3496 | E_err:   0.009238
[2025-10-23 19:25:55] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -68.269239 | E_var:     0.3337 | E_err:   0.009025
[2025-10-23 19:26:08] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -68.301124 | E_var:     0.2874 | E_err:   0.008376
[2025-10-23 19:26:22] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -68.289541 | E_var:     0.3663 | E_err:   0.009457
[2025-10-23 19:26:35] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -68.287383 | E_var:     0.3919 | E_err:   0.009782
[2025-10-23 19:26:48] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -68.262860 | E_var:     0.3841 | E_err:   0.009684
[2025-10-23 19:27:01] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -68.293021 | E_var:     0.6525 | E_err:   0.012621
[2025-10-23 19:27:14] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -68.280695 | E_var:     0.3072 | E_err:   0.008661
[2025-10-23 19:27:28] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -68.300626 | E_var:     0.3752 | E_err:   0.009570
[2025-10-23 19:27:41] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -68.285450 | E_var:     0.3621 | E_err:   0.009402
[2025-10-23 19:27:54] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -68.281781 | E_var:     0.3228 | E_err:   0.008877
[2025-10-23 19:28:07] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -68.282727 | E_var:     0.3599 | E_err:   0.009373
[2025-10-23 19:28:20] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -68.264227 | E_var:     0.3466 | E_err:   0.009199
[2025-10-23 19:28:33] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -68.293342 | E_var:     0.5076 | E_err:   0.011132
[2025-10-23 19:28:47] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -68.279478 | E_var:     0.3462 | E_err:   0.009193
[2025-10-23 19:29:00] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -68.270724 | E_var:     0.3539 | E_err:   0.009295
[2025-10-23 19:29:13] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -68.284223 | E_var:     0.2958 | E_err:   0.008499
[2025-10-23 19:29:26] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -68.284182 | E_var:     0.3551 | E_err:   0.009311
[2025-10-23 19:29:39] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -68.275137 | E_var:     0.3461 | E_err:   0.009193
[2025-10-23 19:29:53] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -68.285242 | E_var:     0.3422 | E_err:   0.009140
[2025-10-23 19:30:06] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -68.281080 | E_var:     0.2989 | E_err:   0.008543
[2025-10-23 19:30:19] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -68.284681 | E_var:     0.2532 | E_err:   0.007863
[2025-10-23 19:30:32] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -68.293112 | E_var:     0.4063 | E_err:   0.009960
[2025-10-23 19:30:45] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -68.276825 | E_var:     0.3837 | E_err:   0.009678
[2025-10-23 19:30:59] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -68.277441 | E_var:     0.3996 | E_err:   0.009877
[2025-10-23 19:31:12] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -68.276559 | E_var:     0.3089 | E_err:   0.008684
[2025-10-23 19:31:25] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -68.282614 | E_var:     0.3513 | E_err:   0.009261
[2025-10-23 19:31:38] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -68.283627 | E_var:     0.3719 | E_err:   0.009529
[2025-10-23 19:31:51] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -68.283245 | E_var:     0.3946 | E_err:   0.009815
[2025-10-23 19:32:04] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -68.284899 | E_var:     0.3030 | E_err:   0.008601
[2025-10-23 19:32:18] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -68.282141 | E_var:     0.2897 | E_err:   0.008411
[2025-10-23 19:32:31] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -68.268768 | E_var:     0.3007 | E_err:   0.008568
[2025-10-23 19:32:44] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -68.294764 | E_var:     0.3181 | E_err:   0.008813
[2025-10-23 19:32:57] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -68.275982 | E_var:     0.4188 | E_err:   0.010111
[2025-10-23 19:33:10] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -68.283095 | E_var:     0.3499 | E_err:   0.009243
[2025-10-23 19:33:24] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -68.298921 | E_var:     0.3372 | E_err:   0.009073
[2025-10-23 19:33:37] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -68.272680 | E_var:     0.3315 | E_err:   0.008997
[2025-10-23 19:33:50] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -68.276873 | E_var:     0.2857 | E_err:   0.008352
[2025-10-23 19:34:03] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -68.266946 | E_var:     0.6164 | E_err:   0.012267
[2025-10-23 19:34:16] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -68.273873 | E_var:     0.4318 | E_err:   0.010268
[2025-10-23 19:34:29] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -68.290809 | E_var:     0.3381 | E_err:   0.009085
[2025-10-23 19:34:43] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -68.283099 | E_var:     0.3310 | E_err:   0.008990
[2025-10-23 19:34:56] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -68.289377 | E_var:     0.3711 | E_err:   0.009518
[2025-10-23 19:35:09] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -68.281452 | E_var:     0.3082 | E_err:   0.008674
[2025-10-23 19:35:22] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -68.270975 | E_var:     0.4290 | E_err:   0.010234
[2025-10-23 19:35:36] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -68.286510 | E_var:     0.2797 | E_err:   0.008263
[2025-10-23 19:35:49] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -68.279287 | E_var:     0.3008 | E_err:   0.008569
[2025-10-23 19:36:02] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -68.290978 | E_var:     0.3320 | E_err:   0.009002
[2025-10-23 19:36:15] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -68.289509 | E_var:     0.3552 | E_err:   0.009312
[2025-10-23 19:36:28] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -68.276129 | E_var:     0.3532 | E_err:   0.009285
[2025-10-23 19:36:41] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -68.283183 | E_var:     0.3060 | E_err:   0.008643
[2025-10-23 19:36:55] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -68.270402 | E_var:     0.3085 | E_err:   0.008678
[2025-10-23 19:37:08] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -68.290577 | E_var:     0.3618 | E_err:   0.009398
[2025-10-23 19:37:21] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -68.276802 | E_var:     0.3519 | E_err:   0.009269
[2025-10-23 19:37:34] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -68.289213 | E_var:     0.2962 | E_err:   0.008503
[2025-10-23 19:37:34] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-23 19:37:47] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -68.286004 | E_var:     0.3455 | E_err:   0.009185
[2025-10-23 19:38:01] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -68.280062 | E_var:     0.3906 | E_err:   0.009765
[2025-10-23 19:38:14] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -68.285019 | E_var:     0.3655 | E_err:   0.009446
[2025-10-23 19:38:27] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -68.295292 | E_var:     0.2842 | E_err:   0.008329
[2025-10-23 19:38:40] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -68.289651 | E_var:     0.3072 | E_err:   0.008660
[2025-10-23 19:38:53] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -68.279369 | E_var:     0.3086 | E_err:   0.008680
[2025-10-23 19:39:06] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -68.278919 | E_var:     0.3456 | E_err:   0.009185
[2025-10-23 19:39:20] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -68.280905 | E_var:     0.4068 | E_err:   0.009965
[2025-10-23 19:39:33] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -68.278316 | E_var:     0.3375 | E_err:   0.009077
[2025-10-23 19:39:46] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -68.289411 | E_var:     0.3555 | E_err:   0.009316
[2025-10-23 19:39:59] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -68.285922 | E_var:     0.3044 | E_err:   0.008620
[2025-10-23 19:40:12] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -68.279447 | E_var:     0.2958 | E_err:   0.008498
[2025-10-23 19:40:26] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -68.274441 | E_var:     0.3211 | E_err:   0.008854
[2025-10-23 19:40:39] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -68.280511 | E_var:     0.3237 | E_err:   0.008890
[2025-10-23 19:40:52] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -68.288260 | E_var:     0.3602 | E_err:   0.009377
[2025-10-23 19:41:05] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -68.283278 | E_var:     0.3260 | E_err:   0.008921
[2025-10-23 19:41:18] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -68.284065 | E_var:     0.4471 | E_err:   0.010448
[2025-10-23 19:41:31] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -68.294529 | E_var:     0.3188 | E_err:   0.008822
[2025-10-23 19:41:45] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -68.286590 | E_var:     0.5100 | E_err:   0.011158
[2025-10-23 19:41:58] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -68.272210 | E_var:     0.4209 | E_err:   0.010136
[2025-10-23 19:42:11] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -68.265639 | E_var:     0.3710 | E_err:   0.009517
[2025-10-23 19:42:24] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -68.273133 | E_var:     0.3653 | E_err:   0.009444
[2025-10-23 19:42:37] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -68.274031 | E_var:     0.3360 | E_err:   0.009057
[2025-10-23 19:42:50] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -68.279788 | E_var:     0.3048 | E_err:   0.008626
[2025-10-23 19:43:04] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -68.271746 | E_var:     0.2585 | E_err:   0.007945
[2025-10-23 19:43:17] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -68.289029 | E_var:     0.3043 | E_err:   0.008619
[2025-10-23 19:43:30] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -68.284693 | E_var:     0.3548 | E_err:   0.009308
[2025-10-23 19:43:43] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -68.292026 | E_var:     0.3007 | E_err:   0.008568
[2025-10-23 19:43:56] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -68.288517 | E_var:     0.3379 | E_err:   0.009083
[2025-10-23 19:44:10] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -68.275220 | E_var:     0.3788 | E_err:   0.009616
[2025-10-23 19:44:23] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -68.285464 | E_var:     0.3593 | E_err:   0.009366
[2025-10-23 19:44:36] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -68.272555 | E_var:     0.3843 | E_err:   0.009686
[2025-10-23 19:44:49] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -68.293989 | E_var:     0.3299 | E_err:   0.008974
[2025-10-23 19:45:02] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -68.280136 | E_var:     0.3035 | E_err:   0.008608
[2025-10-23 19:45:15] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -68.280449 | E_var:     0.3349 | E_err:   0.009042
[2025-10-23 19:45:29] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -68.282750 | E_var:     0.3925 | E_err:   0.009789
[2025-10-23 19:45:42] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -68.278387 | E_var:     0.3065 | E_err:   0.008651
[2025-10-23 19:45:55] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -68.282691 | E_var:     0.5159 | E_err:   0.011223
[2025-10-23 19:46:08] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -68.281987 | E_var:     0.2887 | E_err:   0.008395
[2025-10-23 19:46:21] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -68.302606 | E_var:     0.4078 | E_err:   0.009978
[2025-10-23 19:46:35] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -68.292745 | E_var:     0.3654 | E_err:   0.009445
[2025-10-23 19:46:48] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -68.261585 | E_var:     0.4587 | E_err:   0.010583
[2025-10-23 19:47:01] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -68.292198 | E_var:     0.3029 | E_err:   0.008599
[2025-10-23 19:47:14] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -68.281269 | E_var:     0.3328 | E_err:   0.009014
[2025-10-23 19:47:27] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -68.280426 | E_var:     0.3521 | E_err:   0.009271
[2025-10-23 19:47:40] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -68.281465 | E_var:     0.3108 | E_err:   0.008712
[2025-10-23 19:47:54] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -68.290994 | E_var:     0.3941 | E_err:   0.009809
[2025-10-23 19:48:07] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -68.279397 | E_var:     0.3019 | E_err:   0.008585
[2025-10-23 19:48:20] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -68.282513 | E_var:     0.2960 | E_err:   0.008501
[2025-10-23 19:48:33] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -68.289548 | E_var:     0.4224 | E_err:   0.010155
[2025-10-23 19:48:46] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -68.275678 | E_var:     0.3310 | E_err:   0.008990
[2025-10-23 19:48:59] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -68.288837 | E_var:     0.3227 | E_err:   0.008877
[2025-10-23 19:49:13] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -68.285420 | E_var:     0.3463 | E_err:   0.009195
[2025-10-23 19:49:26] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -68.288735 | E_var:     0.3051 | E_err:   0.008631
[2025-10-23 19:49:39] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -68.298685 | E_var:     0.2566 | E_err:   0.007916
[2025-10-23 19:49:52] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -68.274601 | E_var:     0.4053 | E_err:   0.009947
[2025-10-23 19:50:05] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -68.288346 | E_var:     1.1218 | E_err:   0.016549
[2025-10-23 19:50:18] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -68.283210 | E_var:     0.3603 | E_err:   0.009379
[2025-10-23 19:50:32] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -68.290157 | E_var:     0.2956 | E_err:   0.008496
[2025-10-23 19:50:45] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -68.290405 | E_var:     0.3812 | E_err:   0.009647
[2025-10-23 19:50:58] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -68.280765 | E_var:     0.3090 | E_err:   0.008686
[2025-10-23 19:51:11] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -68.272614 | E_var:     0.3549 | E_err:   0.009308
[2025-10-23 19:51:24] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -68.274537 | E_var:     0.3892 | E_err:   0.009748
[2025-10-23 19:51:38] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -68.283401 | E_var:     0.3060 | E_err:   0.008644
[2025-10-23 19:51:51] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -68.292324 | E_var:     0.3694 | E_err:   0.009497
[2025-10-23 19:52:04] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -68.275156 | E_var:     0.4034 | E_err:   0.009924
[2025-10-23 19:52:17] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -68.277522 | E_var:     0.3784 | E_err:   0.009612
[2025-10-23 19:52:30] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -68.276815 | E_var:     0.3863 | E_err:   0.009711
[2025-10-23 19:52:43] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -68.288454 | E_var:     0.2918 | E_err:   0.008441
[2025-10-23 19:52:57] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -68.295104 | E_var:     0.2715 | E_err:   0.008141
[2025-10-23 19:53:10] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -68.287967 | E_var:     0.3448 | E_err:   0.009174
[2025-10-23 19:53:23] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -68.288285 | E_var:     0.3174 | E_err:   0.008803
[2025-10-23 19:53:36] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -68.284776 | E_var:     0.3110 | E_err:   0.008713
[2025-10-23 19:53:49] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -68.286252 | E_var:     0.3448 | E_err:   0.009175
[2025-10-23 19:54:02] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -68.296114 | E_var:     0.3436 | E_err:   0.009159
[2025-10-23 19:54:16] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -68.293650 | E_var:     0.3373 | E_err:   0.009075
[2025-10-23 19:54:29] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -68.290252 | E_var:     0.3402 | E_err:   0.009113
[2025-10-23 19:54:42] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -68.270515 | E_var:     0.3204 | E_err:   0.008844
[2025-10-23 19:54:55] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -68.280840 | E_var:     0.3578 | E_err:   0.009346
[2025-10-23 19:55:08] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -68.272201 | E_var:     0.5043 | E_err:   0.011096
[2025-10-23 19:55:22] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -68.283738 | E_var:     0.6995 | E_err:   0.013069
[2025-10-23 19:55:35] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -68.279789 | E_var:     0.3128 | E_err:   0.008739
[2025-10-23 19:55:48] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -68.284164 | E_var:     0.3067 | E_err:   0.008653
[2025-10-23 19:56:01] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -68.284353 | E_var:     0.3882 | E_err:   0.009735
[2025-10-23 19:56:14] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -68.282483 | E_var:     0.3000 | E_err:   0.008559
[2025-10-23 19:56:27] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -68.283943 | E_var:     0.3586 | E_err:   0.009357
[2025-10-23 19:56:41] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -68.288119 | E_var:     0.3863 | E_err:   0.009711
[2025-10-23 19:56:54] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -68.288509 | E_var:     0.3206 | E_err:   0.008846
[2025-10-23 19:57:07] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -68.286196 | E_var:     0.3547 | E_err:   0.009306
[2025-10-23 19:57:20] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -68.278034 | E_var:     0.3320 | E_err:   0.009003
[2025-10-23 19:57:33] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -68.279646 | E_var:     0.4571 | E_err:   0.010564
[2025-10-23 19:57:46] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -68.283575 | E_var:     0.3100 | E_err:   0.008699
[2025-10-23 19:58:00] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -68.271040 | E_var:     0.3075 | E_err:   0.008664
[2025-10-23 19:58:13] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -68.277378 | E_var:     0.3079 | E_err:   0.008671
[2025-10-23 19:58:26] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -68.267364 | E_var:     0.2817 | E_err:   0.008294
[2025-10-23 19:58:39] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -68.284386 | E_var:     0.3774 | E_err:   0.009599
[2025-10-23 19:58:52] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -68.287807 | E_var:     0.3785 | E_err:   0.009612
[2025-10-23 19:59:06] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -68.270877 | E_var:     0.3251 | E_err:   0.008909
[2025-10-23 19:59:19] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -68.294184 | E_var:     0.4404 | E_err:   0.010369
[2025-10-23 19:59:32] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -68.287241 | E_var:     0.3411 | E_err:   0.009126
[2025-10-23 19:59:45] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -68.290862 | E_var:     0.3199 | E_err:   0.008838
[2025-10-23 19:59:58] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -68.294268 | E_var:     0.4633 | E_err:   0.010635
[2025-10-23 20:00:11] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -68.288430 | E_var:     0.3829 | E_err:   0.009668
[2025-10-23 20:00:25] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -68.274225 | E_var:     0.4314 | E_err:   0.010262
[2025-10-23 20:00:38] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -68.278562 | E_var:     0.3175 | E_err:   0.008804
[2025-10-23 20:00:38] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-23 20:00:51] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -68.253473 | E_var:     0.7043 | E_err:   0.013113
[2025-10-23 20:01:04] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -68.277090 | E_var:     0.2992 | E_err:   0.008547
[2025-10-23 20:01:17] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -68.275383 | E_var:     0.3918 | E_err:   0.009780
[2025-10-23 20:01:31] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -68.287990 | E_var:     0.2962 | E_err:   0.008503
[2025-10-23 20:01:44] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -68.276647 | E_var:     0.3123 | E_err:   0.008732
[2025-10-23 20:01:57] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -68.288669 | E_var:     0.3112 | E_err:   0.008716
[2025-10-23 20:02:10] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -68.272248 | E_var:     0.3043 | E_err:   0.008620
[2025-10-23 20:02:23] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -68.281914 | E_var:     0.2711 | E_err:   0.008136
[2025-10-23 20:02:36] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -68.299978 | E_var:     0.3394 | E_err:   0.009102
[2025-10-23 20:02:50] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -68.295449 | E_var:     0.3970 | E_err:   0.009845
[2025-10-23 20:03:03] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -68.287701 | E_var:     0.2696 | E_err:   0.008113
[2025-10-23 20:03:16] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -68.288646 | E_var:     0.3145 | E_err:   0.008763
[2025-10-23 20:03:29] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -68.272517 | E_var:     0.3526 | E_err:   0.009278
[2025-10-23 20:03:42] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -68.282711 | E_var:     0.3348 | E_err:   0.009041
[2025-10-23 20:03:56] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -68.275260 | E_var:     0.3683 | E_err:   0.009482
[2025-10-23 20:04:09] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -68.290476 | E_var:     0.4856 | E_err:   0.010889
[2025-10-23 20:04:22] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -68.282088 | E_var:     0.2893 | E_err:   0.008404
[2025-10-23 20:04:35] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -68.298388 | E_var:     0.3370 | E_err:   0.009071
[2025-10-23 20:04:48] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -68.276006 | E_var:     0.4601 | E_err:   0.010598
[2025-10-23 20:05:01] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -68.283969 | E_var:     0.3172 | E_err:   0.008800
[2025-10-23 20:05:15] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -68.278837 | E_var:     0.3119 | E_err:   0.008727
[2025-10-23 20:05:28] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -68.289809 | E_var:     0.3881 | E_err:   0.009734
[2025-10-23 20:05:41] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -68.282466 | E_var:     0.3172 | E_err:   0.008801
[2025-10-23 20:05:54] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -68.295590 | E_var:     0.3769 | E_err:   0.009593
[2025-10-23 20:06:07] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -68.279397 | E_var:     0.3266 | E_err:   0.008929
[2025-10-23 20:06:20] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -68.281133 | E_var:     0.2796 | E_err:   0.008262
[2025-10-23 20:06:34] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -68.292617 | E_var:     0.3611 | E_err:   0.009390
[2025-10-23 20:06:47] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -68.287340 | E_var:     0.3353 | E_err:   0.009048
[2025-10-23 20:07:00] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -68.302705 | E_var:     0.3305 | E_err:   0.008982
[2025-10-23 20:07:13] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -68.291458 | E_var:     0.3194 | E_err:   0.008830
[2025-10-23 20:07:26] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -68.279546 | E_var:     0.3930 | E_err:   0.009795
[2025-10-23 20:07:40] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -68.289045 | E_var:     0.3830 | E_err:   0.009670
[2025-10-23 20:07:53] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -68.272584 | E_var:     0.4936 | E_err:   0.010978
[2025-10-23 20:08:06] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -68.294420 | E_var:     0.2773 | E_err:   0.008228
[2025-10-23 20:08:19] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -68.280110 | E_var:     0.3085 | E_err:   0.008679
[2025-10-23 20:08:32] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -68.303411 | E_var:     0.3158 | E_err:   0.008781
[2025-10-23 20:08:45] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -68.274637 | E_var:     0.2716 | E_err:   0.008144
[2025-10-23 20:08:59] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -68.276439 | E_var:     0.3360 | E_err:   0.009057
[2025-10-23 20:09:12] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -68.275817 | E_var:     0.3645 | E_err:   0.009434
[2025-10-23 20:09:25] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -68.268554 | E_var:     0.3131 | E_err:   0.008743
[2025-10-23 20:09:38] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -68.269729 | E_var:     0.4500 | E_err:   0.010482
[2025-10-23 20:09:51] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -68.292473 | E_var:     0.3669 | E_err:   0.009464
[2025-10-23 20:10:05] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -68.264243 | E_var:     0.3794 | E_err:   0.009625
[2025-10-23 20:10:18] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -68.275021 | E_var:     0.3001 | E_err:   0.008560
[2025-10-23 20:10:31] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -68.298671 | E_var:     0.4730 | E_err:   0.010746
[2025-10-23 20:10:44] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -68.262474 | E_var:     0.4589 | E_err:   0.010585
[2025-10-23 20:10:57] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -68.279217 | E_var:     0.3395 | E_err:   0.009104
[2025-10-23 20:11:10] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -68.290536 | E_var:     0.3413 | E_err:   0.009128
[2025-10-23 20:11:24] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -68.281921 | E_var:     0.3017 | E_err:   0.008583
[2025-10-23 20:11:37] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -68.290282 | E_var:     0.3617 | E_err:   0.009397
[2025-10-23 20:11:50] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -68.292547 | E_var:     0.3311 | E_err:   0.008991
[2025-10-23 20:12:03] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -68.279951 | E_var:     0.3434 | E_err:   0.009156
[2025-10-23 20:12:16] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -68.284640 | E_var:     0.3928 | E_err:   0.009792
[2025-10-23 20:12:30] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -68.286010 | E_var:     0.3330 | E_err:   0.009017
[2025-10-23 20:12:43] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -68.313732 | E_var:     0.3202 | E_err:   0.008841
[2025-10-23 20:12:56] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -68.297618 | E_var:     0.3073 | E_err:   0.008662
[2025-10-23 20:13:09] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -68.273419 | E_var:     0.3081 | E_err:   0.008674
[2025-10-23 20:13:22] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -68.275687 | E_var:     0.3602 | E_err:   0.009378
[2025-10-23 20:13:36] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -68.289246 | E_var:     0.3298 | E_err:   0.008973
[2025-10-23 20:13:49] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -68.300055 | E_var:     0.3576 | E_err:   0.009343
[2025-10-23 20:14:02] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -68.274076 | E_var:     0.3727 | E_err:   0.009539
[2025-10-23 20:14:15] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -68.273513 | E_var:     0.3120 | E_err:   0.008727
[2025-10-23 20:14:28] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -68.273455 | E_var:     0.3302 | E_err:   0.008979
[2025-10-23 20:14:41] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -68.277049 | E_var:     0.3083 | E_err:   0.008675
[2025-10-23 20:14:55] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -68.295479 | E_var:     0.3210 | E_err:   0.008852
[2025-10-23 20:15:08] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -68.280869 | E_var:     0.3288 | E_err:   0.008959
[2025-10-23 20:15:21] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -68.284911 | E_var:     0.3289 | E_err:   0.008960
[2025-10-23 20:15:34] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -68.278847 | E_var:     0.3361 | E_err:   0.009059
[2025-10-23 20:15:47] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -68.282176 | E_var:     0.3858 | E_err:   0.009706
[2025-10-23 20:16:01] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -68.287426 | E_var:     0.3294 | E_err:   0.008967
[2025-10-23 20:16:14] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -68.279663 | E_var:     0.3638 | E_err:   0.009424
[2025-10-23 20:16:27] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -68.284414 | E_var:     0.3552 | E_err:   0.009312
[2025-10-23 20:16:40] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -68.296362 | E_var:     0.3141 | E_err:   0.008757
[2025-10-23 20:16:53] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -68.275224 | E_var:     0.3308 | E_err:   0.008987
[2025-10-23 20:17:06] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -68.273434 | E_var:     0.3462 | E_err:   0.009194
[2025-10-23 20:17:20] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -68.292148 | E_var:     0.3253 | E_err:   0.008912
[2025-10-23 20:17:33] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -68.281069 | E_var:     0.5604 | E_err:   0.011696
[2025-10-23 20:17:46] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -68.264188 | E_var:     0.3852 | E_err:   0.009698
[2025-10-23 20:17:59] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -68.288176 | E_var:     0.3263 | E_err:   0.008925
[2025-10-23 20:18:12] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -68.267279 | E_var:     0.3709 | E_err:   0.009515
[2025-10-23 20:18:26] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -68.282333 | E_var:     0.3404 | E_err:   0.009117
[2025-10-23 20:18:39] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -68.281723 | E_var:     0.3404 | E_err:   0.009117
[2025-10-23 20:18:52] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -68.285361 | E_var:     0.4119 | E_err:   0.010028
[2025-10-23 20:19:05] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -68.290020 | E_var:     0.3396 | E_err:   0.009105
[2025-10-23 20:19:18] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -68.274177 | E_var:     0.4626 | E_err:   0.010627
[2025-10-23 20:19:31] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -68.275145 | E_var:     0.3542 | E_err:   0.009299
[2025-10-23 20:19:45] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -68.294234 | E_var:     0.3388 | E_err:   0.009095
[2025-10-23 20:19:58] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -68.275930 | E_var:     0.4510 | E_err:   0.010494
[2025-10-23 20:20:11] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -68.271584 | E_var:     0.3324 | E_err:   0.009009
[2025-10-23 20:20:24] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -68.265185 | E_var:     0.3404 | E_err:   0.009116
[2025-10-23 20:20:37] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -68.286642 | E_var:     0.3095 | E_err:   0.008692
[2025-10-23 20:20:50] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -68.277159 | E_var:     0.3204 | E_err:   0.008844
[2025-10-23 20:21:04] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -68.291804 | E_var:     0.3648 | E_err:   0.009437
[2025-10-23 20:21:17] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -68.286540 | E_var:     0.2926 | E_err:   0.008452
[2025-10-23 20:21:30] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -68.279904 | E_var:     0.2930 | E_err:   0.008457
[2025-10-23 20:21:43] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -68.288347 | E_var:     0.3072 | E_err:   0.008661
[2025-10-23 20:21:56] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -68.262731 | E_var:     0.3396 | E_err:   0.009106
[2025-10-23 20:22:10] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -68.293488 | E_var:     0.2854 | E_err:   0.008347
[2025-10-23 20:22:23] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -68.291688 | E_var:     0.3523 | E_err:   0.009274
[2025-10-23 20:22:36] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -68.292308 | E_var:     0.2753 | E_err:   0.008199
[2025-10-23 20:22:49] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -68.290513 | E_var:     0.3827 | E_err:   0.009666
[2025-10-23 20:23:02] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -68.288813 | E_var:     0.3753 | E_err:   0.009572
[2025-10-23 20:23:15] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -68.281670 | E_var:     0.3180 | E_err:   0.008811
[2025-10-23 20:23:29] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -68.264613 | E_var:     0.3857 | E_err:   0.009703
[2025-10-23 20:23:42] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -68.270097 | E_var:     0.4831 | E_err:   0.010860
[2025-10-23 20:23:42] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-23 20:23:42] ======================================================================================================
[2025-10-23 20:23:42] ✅ Training completed successfully
[2025-10-23 20:23:42] Total restarts: 2
[2025-10-23 20:23:46] Final Energy: -68.27009657 ± 0.01086041
[2025-10-23 20:23:46] Final Variance: 0.483117
[2025-10-23 20:23:46] ======================================================================================================
[2025-10-23 20:23:46] ======================================================================================================
[2025-10-23 20:23:46] Training completed | Runtime: 13908.0s
