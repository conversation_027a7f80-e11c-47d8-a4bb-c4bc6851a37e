[2025-10-23 04:54:07] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.80/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-23 04:54:07]   - 迭代次数: 1050
[2025-10-23 04:54:07]   - 能量: -64.595948-0.002222j ± 0.009498, Var: 0.369501
[2025-10-23 04:54:07]   - 时间戳: 2025-10-23T04:53:41.402753+08:00
[2025-10-23 04:54:32] ✓ 变分状态参数已从checkpoint恢复
[2025-10-23 04:54:32] ======================================================================================================
[2025-10-23 04:54:32] GCNN for Shastry-Sutherland Model
[2025-10-23 04:54:32] ======================================================================================================
[2025-10-23 04:54:32] System parameters:
[2025-10-23 04:54:32]   - System size: L=6, N=144
[2025-10-23 04:54:32]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-10-23 04:54:32] ------------------------------------------------------------------------------------------------------
[2025-10-23 04:54:32] Model parameters:
[2025-10-23 04:54:32]   - Number of layers = 4
[2025-10-23 04:54:32]   - Number of features = 4
[2025-10-23 04:54:32]   - Total parameters = 28252
[2025-10-23 04:54:32] ------------------------------------------------------------------------------------------------------
[2025-10-23 04:54:32] Training parameters:
[2025-10-23 04:54:32]   - Total iterations: 1050
[2025-10-23 04:54:32]   - Annealing cycles: 3
[2025-10-23 04:54:32]   - Initial period: 150
[2025-10-23 04:54:32]   - Period multiplier: 2.0
[2025-10-23 04:54:32]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-23 04:54:32]   - Samples: 4096
[2025-10-23 04:54:32]   - Discarded samples: 0
[2025-10-23 04:54:32]   - Chunk size: 4096
[2025-10-23 04:54:32]   - Diagonal shift: 0.15
[2025-10-23 04:54:32]   - Gradient clipping: 1.0
[2025-10-23 04:54:32]   - Checkpoint enabled: interval=105
[2025-10-23 04:54:32]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.81/training/checkpoints
[2025-10-23 04:54:32]   - Resuming from iteration: 1050
[2025-10-23 04:54:32] ------------------------------------------------------------------------------------------------------
[2025-10-23 04:54:32] Device status:
[2025-10-23 04:54:32]   - Devices model: NVIDIA H200 NVL
[2025-10-23 04:54:32]   - Number of devices: 1
[2025-10-23 04:54:32]   - Sharding: True
[2025-10-23 04:54:33] ======================================================================================================
[2025-10-23 04:55:24] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -65.518546 | E_var:     1.4251 | E_err:   0.018653
[2025-10-23 04:55:58] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -65.521105 | E_var:     1.0538 | E_err:   0.016040
[2025-10-23 04:56:11] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -65.495613 | E_var:     0.6684 | E_err:   0.012774
[2025-10-23 04:56:24] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -65.523284 | E_var:     0.6518 | E_err:   0.012615
[2025-10-23 04:56:37] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -65.511169 | E_var:     0.6315 | E_err:   0.012417
[2025-10-23 04:56:50] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -65.476890 | E_var:     0.4895 | E_err:   0.010932
[2025-10-23 04:57:04] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -65.505698 | E_var:     0.4762 | E_err:   0.010782
[2025-10-23 04:57:17] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -65.497877 | E_var:     0.4354 | E_err:   0.010311
[2025-10-23 04:57:30] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -65.504752 | E_var:     0.5968 | E_err:   0.012070
[2025-10-23 04:57:43] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -65.507329 | E_var:     0.6253 | E_err:   0.012355
[2025-10-23 04:57:56] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -65.516285 | E_var:     0.4608 | E_err:   0.010607
[2025-10-23 04:58:10] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -65.506749 | E_var:     0.4427 | E_err:   0.010396
[2025-10-23 04:58:23] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -65.489852 | E_var:     0.4745 | E_err:   0.010764
[2025-10-23 04:58:36] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -65.501012 | E_var:     0.4148 | E_err:   0.010063
[2025-10-23 04:58:49] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -65.506230 | E_var:     0.3633 | E_err:   0.009418
[2025-10-23 04:59:03] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -65.493737 | E_var:     0.4659 | E_err:   0.010665
[2025-10-23 04:59:16] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -65.503009 | E_var:     0.4391 | E_err:   0.010354
[2025-10-23 04:59:29] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -65.491598 | E_var:     0.5756 | E_err:   0.011854
[2025-10-23 04:59:42] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -65.510289 | E_var:     0.4910 | E_err:   0.010949
[2025-10-23 04:59:55] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -65.511328 | E_var:     0.3578 | E_err:   0.009347
[2025-10-23 05:00:09] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -65.494681 | E_var:     0.3735 | E_err:   0.009550
[2025-10-23 05:00:22] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -65.524007 | E_var:     0.4297 | E_err:   0.010243
[2025-10-23 05:00:35] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -65.505770 | E_var:     0.4075 | E_err:   0.009975
[2025-10-23 05:00:48] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -65.523296 | E_var:     0.5386 | E_err:   0.011467
[2025-10-23 05:01:02] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -65.504340 | E_var:     0.4345 | E_err:   0.010300
[2025-10-23 05:01:15] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -65.498731 | E_var:     0.3862 | E_err:   0.009710
[2025-10-23 05:01:28] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -65.504710 | E_var:     0.3683 | E_err:   0.009483
[2025-10-23 05:01:41] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -65.497094 | E_var:     0.3884 | E_err:   0.009738
[2025-10-23 05:01:54] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -65.491476 | E_var:     0.4136 | E_err:   0.010049
[2025-10-23 05:02:08] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -65.511590 | E_var:     0.4229 | E_err:   0.010161
[2025-10-23 05:02:21] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -65.507518 | E_var:     0.4831 | E_err:   0.010860
[2025-10-23 05:02:34] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -65.515874 | E_var:     0.4047 | E_err:   0.009940
[2025-10-23 05:02:47] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -65.514111 | E_var:     0.3977 | E_err:   0.009854
[2025-10-23 05:03:01] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -65.486285 | E_var:     0.4328 | E_err:   0.010280
[2025-10-23 05:03:14] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -65.508684 | E_var:     0.3892 | E_err:   0.009748
[2025-10-23 05:03:27] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -65.499606 | E_var:     0.5734 | E_err:   0.011832
[2025-10-23 05:03:40] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -65.499787 | E_var:     0.4242 | E_err:   0.010176
[2025-10-23 05:03:54] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -65.521499 | E_var:     0.3710 | E_err:   0.009517
[2025-10-23 05:04:07] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -65.512343 | E_var:     0.3909 | E_err:   0.009769
[2025-10-23 05:04:20] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -65.500269 | E_var:     0.4004 | E_err:   0.009887
[2025-10-23 05:04:33] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -65.508644 | E_var:     0.5193 | E_err:   0.011259
[2025-10-23 05:04:46] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -65.504448 | E_var:     0.4043 | E_err:   0.009936
[2025-10-23 05:05:00] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -65.507899 | E_var:     0.4828 | E_err:   0.010857
[2025-10-23 05:05:13] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -65.512001 | E_var:     0.3866 | E_err:   0.009715
[2025-10-23 05:05:26] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -65.505245 | E_var:     0.5168 | E_err:   0.011233
[2025-10-23 05:05:39] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -65.510837 | E_var:     0.3851 | E_err:   0.009696
[2025-10-23 05:05:53] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -65.504685 | E_var:     0.4075 | E_err:   0.009974
[2025-10-23 05:06:06] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -65.520161 | E_var:     0.3992 | E_err:   0.009873
[2025-10-23 05:06:19] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -65.508083 | E_var:     0.4770 | E_err:   0.010792
[2025-10-23 05:06:32] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -65.514964 | E_var:     0.5151 | E_err:   0.011214
[2025-10-23 05:06:46] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -65.486764 | E_var:     0.4250 | E_err:   0.010186
[2025-10-23 05:06:59] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -65.505977 | E_var:     0.3769 | E_err:   0.009593
[2025-10-23 05:07:12] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -65.511597 | E_var:     0.3806 | E_err:   0.009639
[2025-10-23 05:07:25] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -65.493948 | E_var:     0.5077 | E_err:   0.011133
[2025-10-23 05:07:39] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -65.522065 | E_var:     0.4191 | E_err:   0.010115
[2025-10-23 05:07:52] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -65.507604 | E_var:     0.3814 | E_err:   0.009650
[2025-10-23 05:08:05] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -65.510129 | E_var:     0.5014 | E_err:   0.011064
[2025-10-23 05:08:18] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -65.525854 | E_var:     0.4071 | E_err:   0.009969
[2025-10-23 05:08:31] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -65.521596 | E_var:     0.3843 | E_err:   0.009686
[2025-10-23 05:08:45] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -65.507869 | E_var:     0.3818 | E_err:   0.009655
[2025-10-23 05:08:58] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -65.491314 | E_var:     0.4816 | E_err:   0.010844
[2025-10-23 05:09:11] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -65.506821 | E_var:     0.4258 | E_err:   0.010195
[2025-10-23 05:09:24] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -65.512756 | E_var:     0.3621 | E_err:   0.009403
[2025-10-23 05:09:38] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -65.511931 | E_var:     1.0144 | E_err:   0.015737
[2025-10-23 05:09:51] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -65.492240 | E_var:     0.4580 | E_err:   0.010574
[2025-10-23 05:10:04] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -65.497984 | E_var:     0.5149 | E_err:   0.011211
[2025-10-23 05:10:17] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -65.521164 | E_var:     0.5082 | E_err:   0.011139
[2025-10-23 05:10:31] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -65.495421 | E_var:     0.4536 | E_err:   0.010523
[2025-10-23 05:10:44] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -65.512847 | E_var:     0.3877 | E_err:   0.009729
[2025-10-23 05:10:57] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -65.510476 | E_var:     0.4483 | E_err:   0.010462
[2025-10-23 05:11:10] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -65.509858 | E_var:     0.3654 | E_err:   0.009445
[2025-10-23 05:11:24] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -65.517581 | E_var:     0.5466 | E_err:   0.011552
[2025-10-23 05:11:37] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -65.509789 | E_var:     0.4069 | E_err:   0.009967
[2025-10-23 05:11:50] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -65.505760 | E_var:     0.4026 | E_err:   0.009915
[2025-10-23 05:12:03] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -65.523947 | E_var:     0.4572 | E_err:   0.010565
[2025-10-23 05:12:16] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -65.510613 | E_var:     0.3824 | E_err:   0.009662
[2025-10-23 05:12:30] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -65.474773 | E_var:     2.2136 | E_err:   0.023247
[2025-10-23 05:12:43] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -65.489133 | E_var:     0.4731 | E_err:   0.010747
[2025-10-23 05:12:56] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -65.476303 | E_var:     0.6060 | E_err:   0.012163
[2025-10-23 05:13:09] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -65.499681 | E_var:     0.4136 | E_err:   0.010049
[2025-10-23 05:13:23] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -65.511405 | E_var:     0.4019 | E_err:   0.009905
[2025-10-23 05:13:36] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -65.486194 | E_var:     0.3678 | E_err:   0.009475
[2025-10-23 05:13:49] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -65.509774 | E_var:     0.5040 | E_err:   0.011093
[2025-10-23 05:14:02] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -65.520139 | E_var:     0.4805 | E_err:   0.010831
[2025-10-23 05:14:16] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -65.515142 | E_var:     0.4252 | E_err:   0.010189
[2025-10-23 05:14:29] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -65.498791 | E_var:     0.4790 | E_err:   0.010814
[2025-10-23 05:14:42] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -65.514966 | E_var:     0.4570 | E_err:   0.010562
[2025-10-23 05:14:55] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -65.513903 | E_var:     0.3839 | E_err:   0.009681
[2025-10-23 05:15:08] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -65.511719 | E_var:     0.4857 | E_err:   0.010889
[2025-10-23 05:15:22] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -65.529214 | E_var:     0.4261 | E_err:   0.010200
[2025-10-23 05:15:35] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -65.497494 | E_var:     0.3917 | E_err:   0.009778
[2025-10-23 05:15:48] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -65.509328 | E_var:     0.5383 | E_err:   0.011464
[2025-10-23 05:16:01] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -65.511150 | E_var:     0.4245 | E_err:   0.010180
[2025-10-23 05:16:15] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -65.521000 | E_var:     0.4488 | E_err:   0.010468
[2025-10-23 05:16:28] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -65.510549 | E_var:     0.3808 | E_err:   0.009643
[2025-10-23 05:16:41] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -65.521096 | E_var:     0.4622 | E_err:   0.010622
[2025-10-23 05:16:54] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -65.516036 | E_var:     0.3999 | E_err:   0.009881
[2025-10-23 05:17:07] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -65.500074 | E_var:     0.4145 | E_err:   0.010060
[2025-10-23 05:17:21] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -65.516714 | E_var:     0.5977 | E_err:   0.012080
[2025-10-23 05:17:34] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -65.494463 | E_var:     0.3617 | E_err:   0.009397
[2025-10-23 05:17:47] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -65.516310 | E_var:     0.4078 | E_err:   0.009978
[2025-10-23 05:18:00] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -65.473816 | E_var:     0.3916 | E_err:   0.009778
[2025-10-23 05:18:14] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -65.506653 | E_var:     0.4143 | E_err:   0.010057
[2025-10-23 05:18:27] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -65.495252 | E_var:     0.4169 | E_err:   0.010088
[2025-10-23 05:18:40] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -65.513438 | E_var:     0.3871 | E_err:   0.009721
[2025-10-23 05:18:40] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-23 05:18:53] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -65.505621 | E_var:     0.4359 | E_err:   0.010316
[2025-10-23 05:19:07] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -65.504978 | E_var:     0.4251 | E_err:   0.010188
[2025-10-23 05:19:20] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -65.494009 | E_var:     0.4470 | E_err:   0.010447
[2025-10-23 05:19:33] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -65.490300 | E_var:     0.3813 | E_err:   0.009649
[2025-10-23 05:19:46] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -65.506228 | E_var:     0.4936 | E_err:   0.010977
[2025-10-23 05:20:00] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -65.500895 | E_var:     0.3876 | E_err:   0.009727
[2025-10-23 05:20:13] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -65.504353 | E_var:     0.4710 | E_err:   0.010723
[2025-10-23 05:20:26] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -65.502519 | E_var:     0.4522 | E_err:   0.010507
[2025-10-23 05:20:39] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -65.497781 | E_var:     0.4136 | E_err:   0.010049
[2025-10-23 05:20:52] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -65.508919 | E_var:     0.4000 | E_err:   0.009882
[2025-10-23 05:21:06] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -65.505929 | E_var:     0.3589 | E_err:   0.009360
[2025-10-23 05:21:19] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -65.505440 | E_var:     0.3835 | E_err:   0.009676
[2025-10-23 05:21:32] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -65.505758 | E_var:     0.4745 | E_err:   0.010763
[2025-10-23 05:21:45] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -65.498233 | E_var:     0.3961 | E_err:   0.009834
[2025-10-23 05:21:59] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -65.516459 | E_var:     0.3639 | E_err:   0.009426
[2025-10-23 05:22:12] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -65.511875 | E_var:     0.4731 | E_err:   0.010748
[2025-10-23 05:22:25] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -65.506290 | E_var:     0.3810 | E_err:   0.009645
[2025-10-23 05:22:38] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -65.500584 | E_var:     0.4690 | E_err:   0.010701
[2025-10-23 05:22:52] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -65.517566 | E_var:     0.4284 | E_err:   0.010227
[2025-10-23 05:23:05] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -65.513703 | E_var:     0.3949 | E_err:   0.009819
[2025-10-23 05:23:18] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -65.512670 | E_var:     0.3962 | E_err:   0.009834
[2025-10-23 05:23:31] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -65.515193 | E_var:     0.4472 | E_err:   0.010448
[2025-10-23 05:23:44] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -65.502450 | E_var:     0.3976 | E_err:   0.009852
[2025-10-23 05:23:58] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -65.502748 | E_var:     0.4653 | E_err:   0.010658
[2025-10-23 05:24:11] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -65.508483 | E_var:     0.4823 | E_err:   0.010851
[2025-10-23 05:24:24] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -65.507991 | E_var:     0.4231 | E_err:   0.010163
[2025-10-23 05:24:37] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -65.493978 | E_var:     0.4548 | E_err:   0.010538
[2025-10-23 05:24:51] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -65.497825 | E_var:     0.3943 | E_err:   0.009811
[2025-10-23 05:25:04] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -65.480670 | E_var:     0.4977 | E_err:   0.011023
[2025-10-23 05:25:17] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -65.503279 | E_var:     0.4416 | E_err:   0.010383
[2025-10-23 05:25:30] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -65.497593 | E_var:     0.5280 | E_err:   0.011353
[2025-10-23 05:25:44] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -65.512456 | E_var:     0.3621 | E_err:   0.009402
[2025-10-23 05:25:57] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -65.490079 | E_var:     0.5360 | E_err:   0.011439
[2025-10-23 05:26:10] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -65.500208 | E_var:     0.4867 | E_err:   0.010901
[2025-10-23 05:26:23] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -65.494143 | E_var:     0.6403 | E_err:   0.012503
[2025-10-23 05:26:37] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -65.515224 | E_var:     0.4347 | E_err:   0.010302
[2025-10-23 05:26:50] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -65.513455 | E_var:     0.4085 | E_err:   0.009986
[2025-10-23 05:27:03] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -65.512977 | E_var:     0.4440 | E_err:   0.010411
[2025-10-23 05:27:16] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -65.512074 | E_var:     0.3879 | E_err:   0.009731
[2025-10-23 05:27:29] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -65.503847 | E_var:     0.3993 | E_err:   0.009874
[2025-10-23 05:27:43] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -65.519128 | E_var:     0.3565 | E_err:   0.009329
[2025-10-23 05:27:56] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -65.484912 | E_var:     0.4602 | E_err:   0.010600
[2025-10-23 05:28:09] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -65.495826 | E_var:     0.4430 | E_err:   0.010399
[2025-10-23 05:28:22] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -65.502418 | E_var:     0.3852 | E_err:   0.009697
[2025-10-23 05:28:36] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -65.518073 | E_var:     0.4552 | E_err:   0.010542
[2025-10-23 05:28:36] 🔄 RESTART #1 | Period: 300
[2025-10-23 05:28:49] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -65.502883 | E_var:     0.3861 | E_err:   0.009708
[2025-10-23 05:29:02] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -65.515570 | E_var:     0.3945 | E_err:   0.009814
[2025-10-23 05:29:15] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -65.483304 | E_var:     0.4256 | E_err:   0.010194
[2025-10-23 05:29:29] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -65.500918 | E_var:     0.4582 | E_err:   0.010576
[2025-10-23 05:29:42] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -65.514930 | E_var:     0.4375 | E_err:   0.010335
[2025-10-23 05:29:55] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -65.502003 | E_var:     0.3149 | E_err:   0.008768
[2025-10-23 05:30:08] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -65.505311 | E_var:     0.5084 | E_err:   0.011141
[2025-10-23 05:30:21] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -65.521445 | E_var:     0.3966 | E_err:   0.009840
[2025-10-23 05:30:35] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -65.524619 | E_var:     0.4176 | E_err:   0.010097
[2025-10-23 05:30:48] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -65.497392 | E_var:     0.3947 | E_err:   0.009816
[2025-10-23 05:31:01] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -65.499427 | E_var:     0.4668 | E_err:   0.010676
[2025-10-23 05:31:14] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -65.500904 | E_var:     0.3992 | E_err:   0.009873
[2025-10-23 05:31:28] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -65.511060 | E_var:     0.3904 | E_err:   0.009763
[2025-10-23 05:31:41] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -65.501371 | E_var:     0.4002 | E_err:   0.009885
[2025-10-23 05:31:54] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -65.506847 | E_var:     0.4122 | E_err:   0.010032
[2025-10-23 05:32:07] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -65.504520 | E_var:     0.5428 | E_err:   0.011512
[2025-10-23 05:32:20] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -65.507926 | E_var:     0.4015 | E_err:   0.009901
[2025-10-23 05:32:34] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -65.519058 | E_var:     0.3849 | E_err:   0.009693
[2025-10-23 05:32:47] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -65.501342 | E_var:     0.4344 | E_err:   0.010298
[2025-10-23 05:33:00] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -65.516411 | E_var:     0.3222 | E_err:   0.008869
[2025-10-23 05:33:13] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -65.502330 | E_var:     0.4659 | E_err:   0.010665
[2025-10-23 05:33:27] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -65.494916 | E_var:     0.6345 | E_err:   0.012446
[2025-10-23 05:33:40] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -65.494891 | E_var:     0.3827 | E_err:   0.009666
[2025-10-23 05:33:53] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -65.523583 | E_var:     0.5665 | E_err:   0.011760
[2025-10-23 05:34:06] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -65.485107 | E_var:     0.4110 | E_err:   0.010017
[2025-10-23 05:34:20] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -65.511908 | E_var:     0.5927 | E_err:   0.012030
[2025-10-23 05:34:33] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -65.497684 | E_var:     0.3510 | E_err:   0.009257
[2025-10-23 05:34:46] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -65.496167 | E_var:     0.5016 | E_err:   0.011066
[2025-10-23 05:34:59] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -65.512241 | E_var:     0.4129 | E_err:   0.010040
[2025-10-23 05:35:12] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -65.515699 | E_var:     0.4046 | E_err:   0.009939
[2025-10-23 05:35:26] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -65.494717 | E_var:     0.4011 | E_err:   0.009895
[2025-10-23 05:35:39] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -65.513520 | E_var:     0.4230 | E_err:   0.010162
[2025-10-23 05:35:52] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -65.506676 | E_var:     0.5071 | E_err:   0.011127
[2025-10-23 05:36:05] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -65.527203 | E_var:     0.3683 | E_err:   0.009483
[2025-10-23 05:36:19] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -65.496963 | E_var:     0.4542 | E_err:   0.010530
[2025-10-23 05:36:32] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -65.517803 | E_var:     0.3464 | E_err:   0.009197
[2025-10-23 05:36:45] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -65.499172 | E_var:     0.4241 | E_err:   0.010175
[2025-10-23 05:36:58] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -65.514706 | E_var:     0.4513 | E_err:   0.010496
[2025-10-23 05:37:12] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -65.512299 | E_var:     0.5173 | E_err:   0.011238
[2025-10-23 05:37:25] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -65.508750 | E_var:     0.4955 | E_err:   0.010998
[2025-10-23 05:37:38] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -65.508147 | E_var:     0.4605 | E_err:   0.010603
[2025-10-23 05:37:51] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -65.513809 | E_var:     0.5460 | E_err:   0.011546
[2025-10-23 05:38:04] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -65.512813 | E_var:     0.3975 | E_err:   0.009852
[2025-10-23 05:38:18] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -65.499346 | E_var:     0.5278 | E_err:   0.011351
[2025-10-23 05:38:31] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -65.514543 | E_var:     0.3650 | E_err:   0.009440
[2025-10-23 05:38:44] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -65.499765 | E_var:     0.4908 | E_err:   0.010947
[2025-10-23 05:38:57] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -65.504714 | E_var:     0.3703 | E_err:   0.009508
[2025-10-23 05:39:11] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -65.515307 | E_var:     0.3651 | E_err:   0.009441
[2025-10-23 05:39:24] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -65.494668 | E_var:     0.4735 | E_err:   0.010752
[2025-10-23 05:39:37] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -65.505744 | E_var:     0.4404 | E_err:   0.010370
[2025-10-23 05:39:50] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -65.504337 | E_var:     0.3515 | E_err:   0.009264
[2025-10-23 05:40:03] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -65.515926 | E_var:     0.4544 | E_err:   0.010532
[2025-10-23 05:40:17] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -65.488802 | E_var:     0.4456 | E_err:   0.010430
[2025-10-23 05:40:30] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -65.496343 | E_var:     0.4668 | E_err:   0.010675
[2025-10-23 05:40:43] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -65.503025 | E_var:     0.3735 | E_err:   0.009549
[2025-10-23 05:40:56] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -65.503508 | E_var:     0.7143 | E_err:   0.013206
[2025-10-23 05:41:10] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -65.500394 | E_var:     0.3748 | E_err:   0.009566
[2025-10-23 05:41:23] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -65.496068 | E_var:     0.3873 | E_err:   0.009724
[2025-10-23 05:41:36] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -65.517730 | E_var:     0.3455 | E_err:   0.009185
[2025-10-23 05:41:49] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -65.509743 | E_var:     0.4073 | E_err:   0.009971
[2025-10-23 05:41:49] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-23 05:42:03] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -65.502602 | E_var:     0.4396 | E_err:   0.010360
[2025-10-23 05:42:16] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -65.502634 | E_var:     0.8366 | E_err:   0.014292
[2025-10-23 05:42:29] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -65.510808 | E_var:     0.4778 | E_err:   0.010801
[2025-10-23 05:42:42] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -65.497498 | E_var:     0.4573 | E_err:   0.010567
[2025-10-23 05:42:56] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -65.495316 | E_var:     0.3591 | E_err:   0.009363
[2025-10-23 05:43:09] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -65.499120 | E_var:     0.5225 | E_err:   0.011295
[2025-10-23 05:43:22] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -65.504899 | E_var:     0.4274 | E_err:   0.010215
[2025-10-23 05:43:35] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -65.480941 | E_var:     0.4262 | E_err:   0.010200
[2025-10-23 05:43:49] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -65.513475 | E_var:     0.4924 | E_err:   0.010964
[2025-10-23 05:44:02] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -65.504132 | E_var:     0.5017 | E_err:   0.011068
[2025-10-23 05:44:15] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -65.516271 | E_var:     0.4072 | E_err:   0.009971
[2025-10-23 05:44:28] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -65.498511 | E_var:     0.4011 | E_err:   0.009895
[2025-10-23 05:44:41] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -65.494799 | E_var:     0.6010 | E_err:   0.012114
[2025-10-23 05:44:55] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -65.502314 | E_var:     0.4021 | E_err:   0.009908
[2025-10-23 05:45:08] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -65.509319 | E_var:     0.3967 | E_err:   0.009842
[2025-10-23 05:45:21] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -65.498834 | E_var:     0.3740 | E_err:   0.009556
[2025-10-23 05:45:34] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -65.509807 | E_var:     0.4236 | E_err:   0.010170
[2025-10-23 05:45:48] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -65.505591 | E_var:     0.4332 | E_err:   0.010285
[2025-10-23 05:46:01] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -65.499559 | E_var:     0.4041 | E_err:   0.009932
[2025-10-23 05:46:14] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -65.501846 | E_var:     0.4510 | E_err:   0.010494
[2025-10-23 05:46:27] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -65.522663 | E_var:     0.5088 | E_err:   0.011146
[2025-10-23 05:46:40] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -65.519970 | E_var:     0.3941 | E_err:   0.009809
[2025-10-23 05:46:54] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -65.501878 | E_var:     0.3532 | E_err:   0.009286
[2025-10-23 05:47:07] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -65.515105 | E_var:     0.7340 | E_err:   0.013387
[2025-10-23 05:47:20] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -65.490499 | E_var:     0.4342 | E_err:   0.010295
[2025-10-23 05:47:33] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -65.506148 | E_var:     0.4066 | E_err:   0.009963
[2025-10-23 05:47:47] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -65.521234 | E_var:     0.3906 | E_err:   0.009765
[2025-10-23 05:48:00] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -65.496030 | E_var:     0.3485 | E_err:   0.009224
[2025-10-23 05:48:13] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -65.498893 | E_var:     0.3576 | E_err:   0.009344
[2025-10-23 05:48:26] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -65.510300 | E_var:     0.4304 | E_err:   0.010251
[2025-10-23 05:48:39] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -65.511013 | E_var:     0.4596 | E_err:   0.010593
[2025-10-23 05:48:53] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -65.491455 | E_var:     0.4366 | E_err:   0.010324
[2025-10-23 05:49:06] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -65.518207 | E_var:     0.3762 | E_err:   0.009583
[2025-10-23 05:49:19] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -65.515762 | E_var:     0.4983 | E_err:   0.011029
[2025-10-23 05:49:32] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -65.498723 | E_var:     0.4429 | E_err:   0.010398
[2025-10-23 05:49:46] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -65.494418 | E_var:     0.3637 | E_err:   0.009423
[2025-10-23 05:49:59] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -65.500739 | E_var:     0.4134 | E_err:   0.010046
[2025-10-23 05:50:12] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -65.496748 | E_var:     0.3923 | E_err:   0.009787
[2025-10-23 05:50:25] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -65.511986 | E_var:     0.3596 | E_err:   0.009369
[2025-10-23 05:50:38] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -65.511844 | E_var:     0.5005 | E_err:   0.011054
[2025-10-23 05:50:52] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -65.510711 | E_var:     0.3488 | E_err:   0.009228
[2025-10-23 05:51:05] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -65.501667 | E_var:     0.4388 | E_err:   0.010350
[2025-10-23 05:51:18] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -65.515705 | E_var:     0.4691 | E_err:   0.010701
[2025-10-23 05:51:31] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -65.510764 | E_var:     0.3867 | E_err:   0.009716
[2025-10-23 05:51:45] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -65.501240 | E_var:     0.4680 | E_err:   0.010689
[2025-10-23 05:51:58] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -65.527501 | E_var:     0.4122 | E_err:   0.010031
[2025-10-23 05:52:11] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -65.509562 | E_var:     1.1074 | E_err:   0.016443
[2025-10-23 05:52:24] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -65.510871 | E_var:     0.3806 | E_err:   0.009639
[2025-10-23 05:52:37] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -65.502487 | E_var:     0.7779 | E_err:   0.013781
[2025-10-23 05:52:51] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -65.498210 | E_var:     0.3598 | E_err:   0.009373
[2025-10-23 05:53:04] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -65.482900 | E_var:     0.4704 | E_err:   0.010716
[2025-10-23 05:53:17] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -65.516840 | E_var:     0.3674 | E_err:   0.009471
[2025-10-23 05:53:30] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -65.502952 | E_var:     0.5647 | E_err:   0.011742
[2025-10-23 05:53:44] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -65.496324 | E_var:     0.5360 | E_err:   0.011439
[2025-10-23 05:53:57] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -65.497845 | E_var:     0.4701 | E_err:   0.010713
[2025-10-23 05:54:10] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -65.514609 | E_var:     0.3601 | E_err:   0.009377
[2025-10-23 05:54:23] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -65.493877 | E_var:     0.4210 | E_err:   0.010138
[2025-10-23 05:54:36] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -65.510378 | E_var:     0.4440 | E_err:   0.010412
[2025-10-23 05:54:50] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -65.496263 | E_var:     0.4276 | E_err:   0.010218
[2025-10-23 05:55:03] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -65.510317 | E_var:     0.4549 | E_err:   0.010538
[2025-10-23 05:55:16] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -65.500808 | E_var:     0.4059 | E_err:   0.009955
[2025-10-23 05:55:29] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -65.514612 | E_var:     0.4378 | E_err:   0.010339
[2025-10-23 05:55:43] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -65.526473 | E_var:     0.3582 | E_err:   0.009351
[2025-10-23 05:55:56] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -65.518110 | E_var:     0.4141 | E_err:   0.010054
[2025-10-23 05:56:09] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -65.497774 | E_var:     0.3936 | E_err:   0.009802
[2025-10-23 05:56:22] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -65.504937 | E_var:     0.4024 | E_err:   0.009912
[2025-10-23 05:56:35] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -65.506653 | E_var:     0.3553 | E_err:   0.009314
[2025-10-23 05:56:49] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -65.508212 | E_var:     0.3472 | E_err:   0.009207
[2025-10-23 05:57:02] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -65.487339 | E_var:     0.4482 | E_err:   0.010461
[2025-10-23 05:57:15] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -65.501753 | E_var:     0.4807 | E_err:   0.010833
[2025-10-23 05:57:28] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -65.509560 | E_var:     0.4082 | E_err:   0.009983
[2025-10-23 05:57:42] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -65.503328 | E_var:     0.4236 | E_err:   0.010170
[2025-10-23 05:57:55] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -65.518091 | E_var:     0.4426 | E_err:   0.010395
[2025-10-23 05:58:08] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -65.491223 | E_var:     0.4382 | E_err:   0.010343
[2025-10-23 05:58:21] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -65.509406 | E_var:     0.3642 | E_err:   0.009430
[2025-10-23 05:58:35] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -65.496120 | E_var:     0.4478 | E_err:   0.010456
[2025-10-23 05:58:48] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -65.508961 | E_var:     0.3813 | E_err:   0.009649
[2025-10-23 05:59:01] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -65.501152 | E_var:     0.3863 | E_err:   0.009711
[2025-10-23 05:59:14] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -65.500424 | E_var:     0.4253 | E_err:   0.010190
[2025-10-23 05:59:27] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -65.521768 | E_var:     0.3858 | E_err:   0.009705
[2025-10-23 05:59:41] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -65.508637 | E_var:     0.3896 | E_err:   0.009752
[2025-10-23 05:59:54] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -65.510897 | E_var:     0.4947 | E_err:   0.010989
[2025-10-23 06:00:07] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -65.498385 | E_var:     0.4161 | E_err:   0.010079
[2025-10-23 06:00:20] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -65.507726 | E_var:     0.4264 | E_err:   0.010204
[2025-10-23 06:00:34] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -65.519381 | E_var:     0.3767 | E_err:   0.009590
[2025-10-23 06:00:47] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -65.524050 | E_var:     0.5282 | E_err:   0.011356
[2025-10-23 06:01:00] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -65.500299 | E_var:     0.4628 | E_err:   0.010629
[2025-10-23 06:01:13] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -65.493164 | E_var:     0.3492 | E_err:   0.009233
[2025-10-23 06:01:26] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -65.505488 | E_var:     0.4001 | E_err:   0.009883
[2025-10-23 06:01:40] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -65.515678 | E_var:     0.4100 | E_err:   0.010005
[2025-10-23 06:01:53] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -65.500854 | E_var:     0.4584 | E_err:   0.010579
[2025-10-23 06:02:06] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -65.503567 | E_var:     0.4434 | E_err:   0.010404
[2025-10-23 06:02:19] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -65.499784 | E_var:     0.4160 | E_err:   0.010078
[2025-10-23 06:02:33] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -65.489318 | E_var:     0.3525 | E_err:   0.009276
[2025-10-23 06:02:46] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -65.502884 | E_var:     0.3973 | E_err:   0.009848
[2025-10-23 06:03:00] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -65.520872 | E_var:     0.4469 | E_err:   0.010445
[2025-10-23 06:03:13] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -65.508213 | E_var:     0.3911 | E_err:   0.009771
[2025-10-23 06:03:26] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -65.510437 | E_var:     0.4421 | E_err:   0.010389
[2025-10-23 06:03:39] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -65.496818 | E_var:     0.3458 | E_err:   0.009189
[2025-10-23 06:03:52] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -65.512706 | E_var:     0.4023 | E_err:   0.009910
[2025-10-23 06:04:06] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -65.501282 | E_var:     0.3944 | E_err:   0.009813
[2025-10-23 06:04:19] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -65.517203 | E_var:     0.4233 | E_err:   0.010165
[2025-10-23 06:04:32] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -65.515184 | E_var:     0.4447 | E_err:   0.010420
[2025-10-23 06:04:45] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -65.509077 | E_var:     0.3521 | E_err:   0.009272
[2025-10-23 06:04:59] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -65.491246 | E_var:     0.5530 | E_err:   0.011619
[2025-10-23 06:04:59] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-23 06:05:12] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -65.505000 | E_var:     0.3902 | E_err:   0.009760
[2025-10-23 06:05:25] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -65.505683 | E_var:     0.4685 | E_err:   0.010694
[2025-10-23 06:05:38] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -65.492301 | E_var:     0.3753 | E_err:   0.009572
[2025-10-23 06:05:51] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -65.511401 | E_var:     0.3880 | E_err:   0.009733
[2025-10-23 06:06:05] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -65.509168 | E_var:     0.3798 | E_err:   0.009629
[2025-10-23 06:06:18] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -65.494081 | E_var:     0.3690 | E_err:   0.009492
[2025-10-23 06:06:31] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -65.512793 | E_var:     0.3930 | E_err:   0.009795
[2025-10-23 06:06:44] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -65.497068 | E_var:     0.5807 | E_err:   0.011907
[2025-10-23 06:06:58] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -65.486128 | E_var:     0.4329 | E_err:   0.010280
[2025-10-23 06:07:11] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -65.498140 | E_var:     0.8524 | E_err:   0.014426
[2025-10-23 06:07:24] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -65.504452 | E_var:     0.4246 | E_err:   0.010181
[2025-10-23 06:07:37] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -65.500363 | E_var:     0.3491 | E_err:   0.009232
[2025-10-23 06:07:50] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -65.512416 | E_var:     0.3854 | E_err:   0.009700
[2025-10-23 06:08:04] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -65.519162 | E_var:     0.4498 | E_err:   0.010479
[2025-10-23 06:08:17] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -65.515451 | E_var:     0.3204 | E_err:   0.008844
[2025-10-23 06:08:30] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -65.509966 | E_var:     0.4042 | E_err:   0.009933
[2025-10-23 06:08:43] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -65.504579 | E_var:     0.3246 | E_err:   0.008903
[2025-10-23 06:08:57] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -65.527547 | E_var:     0.5742 | E_err:   0.011840
[2025-10-23 06:09:10] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -65.502588 | E_var:     0.4440 | E_err:   0.010412
[2025-10-23 06:09:23] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -65.515435 | E_var:     0.4643 | E_err:   0.010646
[2025-10-23 06:09:36] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -65.508605 | E_var:     0.7125 | E_err:   0.013189
[2025-10-23 06:09:49] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -65.518151 | E_var:     0.3598 | E_err:   0.009373
[2025-10-23 06:10:03] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -65.496541 | E_var:     0.5368 | E_err:   0.011448
[2025-10-23 06:10:16] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -65.499822 | E_var:     0.3907 | E_err:   0.009766
[2025-10-23 06:10:29] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -65.518716 | E_var:     0.3861 | E_err:   0.009708
[2025-10-23 06:10:42] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -65.506643 | E_var:     0.3690 | E_err:   0.009491
[2025-10-23 06:10:56] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -65.482350 | E_var:     0.5602 | E_err:   0.011695
[2025-10-23 06:11:09] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -65.494347 | E_var:     0.4510 | E_err:   0.010493
[2025-10-23 06:11:22] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -65.505515 | E_var:     0.3861 | E_err:   0.009709
[2025-10-23 06:11:35] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -65.501551 | E_var:     0.3774 | E_err:   0.009599
[2025-10-23 06:11:48] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -65.505478 | E_var:     0.3620 | E_err:   0.009401
[2025-10-23 06:12:02] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -65.510782 | E_var:     0.4197 | E_err:   0.010122
[2025-10-23 06:12:15] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -65.497166 | E_var:     0.4338 | E_err:   0.010292
[2025-10-23 06:12:28] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -65.483516 | E_var:     0.5849 | E_err:   0.011949
[2025-10-23 06:12:41] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -65.495260 | E_var:     0.3520 | E_err:   0.009270
[2025-10-23 06:12:55] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -65.482104 | E_var:     0.4039 | E_err:   0.009930
[2025-10-23 06:13:08] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -65.512421 | E_var:     0.4152 | E_err:   0.010068
[2025-10-23 06:13:21] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -65.491987 | E_var:     0.4137 | E_err:   0.010050
[2025-10-23 06:13:34] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -65.501471 | E_var:     0.3796 | E_err:   0.009627
[2025-10-23 06:13:47] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -65.520540 | E_var:     0.3992 | E_err:   0.009872
[2025-10-23 06:14:01] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -65.494772 | E_var:     0.4392 | E_err:   0.010355
[2025-10-23 06:14:14] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -65.506915 | E_var:     0.4578 | E_err:   0.010572
[2025-10-23 06:14:27] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -65.509833 | E_var:     0.5469 | E_err:   0.011556
[2025-10-23 06:14:40] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -65.506668 | E_var:     0.4997 | E_err:   0.011045
[2025-10-23 06:14:54] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -65.491765 | E_var:     0.3975 | E_err:   0.009851
[2025-10-23 06:15:07] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -65.508050 | E_var:     0.3543 | E_err:   0.009300
[2025-10-23 06:15:20] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -65.495962 | E_var:     0.3962 | E_err:   0.009835
[2025-10-23 06:15:33] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -65.510442 | E_var:     0.4082 | E_err:   0.009983
[2025-10-23 06:15:46] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -65.513098 | E_var:     0.3613 | E_err:   0.009392
[2025-10-23 06:16:00] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -65.507661 | E_var:     0.4371 | E_err:   0.010331
[2025-10-23 06:16:13] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -65.493003 | E_var:     0.5871 | E_err:   0.011973
[2025-10-23 06:16:26] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -65.508079 | E_var:     0.3914 | E_err:   0.009775
[2025-10-23 06:16:39] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -65.495321 | E_var:     0.4180 | E_err:   0.010102
[2025-10-23 06:16:53] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -65.512250 | E_var:     0.4596 | E_err:   0.010593
[2025-10-23 06:17:06] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -65.503370 | E_var:     0.4868 | E_err:   0.010902
[2025-10-23 06:17:19] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -65.495151 | E_var:     0.4303 | E_err:   0.010249
[2025-10-23 06:17:32] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -65.498005 | E_var:     0.4595 | E_err:   0.010592
[2025-10-23 06:17:45] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -65.499690 | E_var:     0.3896 | E_err:   0.009752
[2025-10-23 06:17:59] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -65.501558 | E_var:     0.4992 | E_err:   0.011040
[2025-10-23 06:18:12] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -65.525873 | E_var:     0.3901 | E_err:   0.009759
[2025-10-23 06:18:25] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -65.513697 | E_var:     0.4568 | E_err:   0.010561
[2025-10-23 06:18:38] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -65.509389 | E_var:     0.3894 | E_err:   0.009750
[2025-10-23 06:18:52] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -65.520443 | E_var:     0.4191 | E_err:   0.010115
[2025-10-23 06:19:05] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -65.506696 | E_var:     0.5530 | E_err:   0.011620
[2025-10-23 06:19:18] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -65.506546 | E_var:     0.4793 | E_err:   0.010817
[2025-10-23 06:19:31] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -65.507565 | E_var:     0.3953 | E_err:   0.009824
[2025-10-23 06:19:45] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -65.497724 | E_var:     0.4028 | E_err:   0.009917
[2025-10-23 06:19:58] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -65.507291 | E_var:     0.6814 | E_err:   0.012897
[2025-10-23 06:20:11] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -65.498623 | E_var:     0.3709 | E_err:   0.009516
[2025-10-23 06:20:24] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -65.513577 | E_var:     0.5533 | E_err:   0.011622
[2025-10-23 06:20:37] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -65.501124 | E_var:     0.3698 | E_err:   0.009502
[2025-10-23 06:20:51] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -65.491510 | E_var:     0.4186 | E_err:   0.010109
[2025-10-23 06:21:04] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -65.490643 | E_var:     0.4614 | E_err:   0.010613
[2025-10-23 06:21:17] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -65.496373 | E_var:     0.4247 | E_err:   0.010183
[2025-10-23 06:21:30] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -65.506703 | E_var:     0.3754 | E_err:   0.009573
[2025-10-23 06:21:43] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -65.519076 | E_var:     0.5002 | E_err:   0.011051
[2025-10-23 06:21:57] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -65.506807 | E_var:     0.3952 | E_err:   0.009823
[2025-10-23 06:22:10] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -65.507324 | E_var:     0.4224 | E_err:   0.010156
[2025-10-23 06:22:23] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -65.503421 | E_var:     0.5446 | E_err:   0.011531
[2025-10-23 06:22:36] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -65.507251 | E_var:     0.5147 | E_err:   0.011209
[2025-10-23 06:22:50] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -65.506754 | E_var:     0.4209 | E_err:   0.010137
[2025-10-23 06:23:03] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -65.500102 | E_var:     0.3895 | E_err:   0.009751
[2025-10-23 06:23:16] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -65.491405 | E_var:     0.6096 | E_err:   0.012199
[2025-10-23 06:23:29] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -65.525082 | E_var:     0.4056 | E_err:   0.009951
[2025-10-23 06:23:42] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -65.512277 | E_var:     0.4416 | E_err:   0.010383
[2025-10-23 06:23:56] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -65.483608 | E_var:     0.3787 | E_err:   0.009615
[2025-10-23 06:24:09] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -65.499974 | E_var:     0.3782 | E_err:   0.009608
[2025-10-23 06:24:22] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -65.500811 | E_var:     0.3389 | E_err:   0.009096
[2025-10-23 06:24:35] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -65.496611 | E_var:     0.3551 | E_err:   0.009311
[2025-10-23 06:24:49] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -65.517250 | E_var:     0.4375 | E_err:   0.010335
[2025-10-23 06:25:02] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -65.483271 | E_var:     0.4526 | E_err:   0.010511
[2025-10-23 06:25:15] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -65.489374 | E_var:     0.4295 | E_err:   0.010240
[2025-10-23 06:25:28] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -65.518667 | E_var:     0.5129 | E_err:   0.011190
[2025-10-23 06:25:41] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -65.501819 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 06:25:55] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -65.502295 | E_var:     0.6368 | E_err:   0.012469
[2025-10-23 06:26:08] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -65.486823 | E_var:     0.4984 | E_err:   0.011031
[2025-10-23 06:26:21] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -65.505423 | E_var:     0.4008 | E_err:   0.009891
[2025-10-23 06:26:34] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -65.508815 | E_var:     0.4007 | E_err:   0.009891
[2025-10-23 06:26:47] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -65.505533 | E_var:     0.3572 | E_err:   0.009338
[2025-10-23 06:27:01] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -65.503794 | E_var:     0.4197 | E_err:   0.010123
[2025-10-23 06:27:14] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -65.498364 | E_var:     0.4740 | E_err:   0.010758
[2025-10-23 06:27:27] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -65.490506 | E_var:     0.4397 | E_err:   0.010361
[2025-10-23 06:27:40] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -65.496756 | E_var:     0.4364 | E_err:   0.010322
[2025-10-23 06:27:53] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -65.513695 | E_var:     0.4644 | E_err:   0.010648
[2025-10-23 06:28:07] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -65.508592 | E_var:     0.4592 | E_err:   0.010588
[2025-10-23 06:28:07] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-23 06:28:20] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -65.501103 | E_var:     0.3784 | E_err:   0.009611
[2025-10-23 06:28:33] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -65.503538 | E_var:     0.4817 | E_err:   0.010844
[2025-10-23 06:28:46] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -65.513989 | E_var:     0.4585 | E_err:   0.010581
[2025-10-23 06:29:00] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -65.505949 | E_var:     0.4059 | E_err:   0.009955
[2025-10-23 06:29:13] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -65.496367 | E_var:     0.4024 | E_err:   0.009912
[2025-10-23 06:29:26] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -65.519640 | E_var:     0.3484 | E_err:   0.009223
[2025-10-23 06:29:39] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -65.492880 | E_var:     0.4335 | E_err:   0.010287
[2025-10-23 06:29:53] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -65.501367 | E_var:     0.5450 | E_err:   0.011535
[2025-10-23 06:30:06] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -65.498468 | E_var:     0.4540 | E_err:   0.010528
[2025-10-23 06:30:19] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -65.494067 | E_var:     0.5207 | E_err:   0.011274
[2025-10-23 06:30:32] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -65.496993 | E_var:     0.3742 | E_err:   0.009559
[2025-10-23 06:30:45] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -65.511980 | E_var:     0.4628 | E_err:   0.010630
[2025-10-23 06:30:59] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -65.507891 | E_var:     0.3985 | E_err:   0.009864
[2025-10-23 06:31:12] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -65.513065 | E_var:     0.4753 | E_err:   0.010772
[2025-10-23 06:31:25] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -65.498815 | E_var:     0.4799 | E_err:   0.010824
[2025-10-23 06:31:38] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -65.505964 | E_var:     0.4164 | E_err:   0.010083
[2025-10-23 06:31:51] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -65.512593 | E_var:     0.4332 | E_err:   0.010284
[2025-10-23 06:32:05] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -65.498703 | E_var:     0.5075 | E_err:   0.011132
[2025-10-23 06:32:18] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -65.505065 | E_var:     0.3763 | E_err:   0.009585
[2025-10-23 06:32:31] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -65.506458 | E_var:     0.4652 | E_err:   0.010657
[2025-10-23 06:32:44] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -65.493780 | E_var:     0.3712 | E_err:   0.009519
[2025-10-23 06:32:58] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -65.486287 | E_var:     0.4420 | E_err:   0.010387
[2025-10-23 06:33:11] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -65.498668 | E_var:     0.3990 | E_err:   0.009870
[2025-10-23 06:33:24] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -65.492816 | E_var:     0.3814 | E_err:   0.009649
[2025-10-23 06:33:37] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -65.518860 | E_var:     0.4201 | E_err:   0.010127
[2025-10-23 06:33:50] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -65.506949 | E_var:     0.4883 | E_err:   0.010919
[2025-10-23 06:34:04] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -65.497899 | E_var:     0.3786 | E_err:   0.009615
[2025-10-23 06:34:17] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -65.519168 | E_var:     0.3982 | E_err:   0.009860
[2025-10-23 06:34:30] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -65.498280 | E_var:     0.3818 | E_err:   0.009655
[2025-10-23 06:34:43] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -65.493230 | E_var:     0.4887 | E_err:   0.010923
[2025-10-23 06:34:43] 🔄 RESTART #2 | Period: 600
[2025-10-23 06:34:56] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -65.502948 | E_var:     0.3908 | E_err:   0.009767
[2025-10-23 06:35:10] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -65.503465 | E_var:     0.4363 | E_err:   0.010321
[2025-10-23 06:35:23] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -65.500476 | E_var:     0.4075 | E_err:   0.009974
[2025-10-23 06:35:36] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -65.514805 | E_var:     0.4502 | E_err:   0.010484
[2025-10-23 06:35:49] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -65.496263 | E_var:     0.5399 | E_err:   0.011481
[2025-10-23 06:36:03] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -65.503899 | E_var:     0.4175 | E_err:   0.010096
[2025-10-23 06:36:16] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -65.514203 | E_var:     0.3626 | E_err:   0.009409
[2025-10-23 06:36:29] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -65.507340 | E_var:     0.4337 | E_err:   0.010291
[2025-10-23 06:36:42] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -65.497754 | E_var:     0.3927 | E_err:   0.009791
[2025-10-23 06:36:55] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -65.491733 | E_var:     0.7301 | E_err:   0.013351
[2025-10-23 06:37:09] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -65.502425 | E_var:     0.3827 | E_err:   0.009667
[2025-10-23 06:37:22] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -65.500038 | E_var:     0.4028 | E_err:   0.009916
[2025-10-23 06:37:35] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -65.507761 | E_var:     0.5109 | E_err:   0.011169
[2025-10-23 06:37:48] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -65.516349 | E_var:     0.3378 | E_err:   0.009081
[2025-10-23 06:38:01] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -65.501196 | E_var:     0.6303 | E_err:   0.012405
[2025-10-23 06:38:15] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -65.508521 | E_var:     0.5001 | E_err:   0.011050
[2025-10-23 06:38:28] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -65.511985 | E_var:     0.4595 | E_err:   0.010592
[2025-10-23 06:38:41] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -65.514176 | E_var:     0.4465 | E_err:   0.010441
[2025-10-23 06:38:54] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -65.500130 | E_var:     0.3547 | E_err:   0.009306
[2025-10-23 06:39:08] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -65.511197 | E_var:     0.4000 | E_err:   0.009882
[2025-10-23 06:39:21] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -65.514084 | E_var:     0.4400 | E_err:   0.010365
[2025-10-23 06:39:34] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -65.505289 | E_var:     0.4414 | E_err:   0.010381
[2025-10-23 06:39:47] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -65.494465 | E_var:     0.4006 | E_err:   0.009889
[2025-10-23 06:40:00] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -65.500879 | E_var:     0.4195 | E_err:   0.010120
[2025-10-23 06:40:14] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -65.505838 | E_var:     0.6641 | E_err:   0.012733
[2025-10-23 06:40:27] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -65.497675 | E_var:     0.4249 | E_err:   0.010186
[2025-10-23 06:40:40] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -65.504851 | E_var:     0.5000 | E_err:   0.011048
[2025-10-23 06:40:53] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -65.513239 | E_var:     0.3694 | E_err:   0.009496
[2025-10-23 06:41:07] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -65.514250 | E_var:     0.3994 | E_err:   0.009875
[2025-10-23 06:41:20] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -65.497564 | E_var:     0.4027 | E_err:   0.009916
[2025-10-23 06:41:33] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -65.506314 | E_var:     0.3572 | E_err:   0.009339
[2025-10-23 06:41:46] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -65.492852 | E_var:     0.5302 | E_err:   0.011378
[2025-10-23 06:41:59] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -65.497646 | E_var:     0.4310 | E_err:   0.010258
[2025-10-23 06:42:13] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -65.520407 | E_var:     0.3945 | E_err:   0.009814
[2025-10-23 06:42:26] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -65.494084 | E_var:     0.4741 | E_err:   0.010759
[2025-10-23 06:42:39] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -65.512437 | E_var:     0.4293 | E_err:   0.010237
[2025-10-23 06:42:52] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -65.502857 | E_var:     0.3602 | E_err:   0.009378
[2025-10-23 06:43:05] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -65.502362 | E_var:     0.4515 | E_err:   0.010499
[2025-10-23 06:43:19] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -65.483116 | E_var:     0.5989 | E_err:   0.012092
[2025-10-23 06:43:32] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -65.497924 | E_var:     0.3794 | E_err:   0.009625
[2025-10-23 06:43:45] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -65.509964 | E_var:     0.7638 | E_err:   0.013656
[2025-10-23 06:43:58] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -65.510895 | E_var:     0.5146 | E_err:   0.011209
[2025-10-23 06:44:12] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -65.488809 | E_var:     0.4450 | E_err:   0.010423
[2025-10-23 06:44:25] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -65.503830 | E_var:     0.4588 | E_err:   0.010583
[2025-10-23 06:44:38] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -65.511110 | E_var:     0.3448 | E_err:   0.009175
[2025-10-23 06:44:51] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -65.491579 | E_var:     0.3884 | E_err:   0.009738
[2025-10-23 06:45:04] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -65.494204 | E_var:     0.4302 | E_err:   0.010248
[2025-10-23 06:45:18] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -65.517898 | E_var:     0.3706 | E_err:   0.009511
[2025-10-23 06:45:31] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -65.494821 | E_var:     0.3867 | E_err:   0.009717
[2025-10-23 06:45:44] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -65.491126 | E_var:     0.6539 | E_err:   0.012635
[2025-10-23 06:45:57] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -65.511579 | E_var:     0.3568 | E_err:   0.009333
[2025-10-23 06:46:11] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -65.505061 | E_var:     0.4963 | E_err:   0.011008
[2025-10-23 06:46:24] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -65.499414 | E_var:     0.3437 | E_err:   0.009160
[2025-10-23 06:46:37] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -65.509174 | E_var:     0.3998 | E_err:   0.009880
[2025-10-23 06:46:50] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -65.506687 | E_var:     0.4082 | E_err:   0.009982
[2025-10-23 06:47:03] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -65.512016 | E_var:     0.3358 | E_err:   0.009054
[2025-10-23 06:47:17] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -65.513459 | E_var:     0.4274 | E_err:   0.010215
[2025-10-23 06:47:30] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -65.512785 | E_var:     0.3400 | E_err:   0.009111
[2025-10-23 06:47:43] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -65.493839 | E_var:     0.4936 | E_err:   0.010978
[2025-10-23 06:47:56] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -65.491863 | E_var:     0.4192 | E_err:   0.010116
[2025-10-23 06:48:09] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -65.520593 | E_var:     0.4413 | E_err:   0.010380
[2025-10-23 06:48:23] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -65.517948 | E_var:     0.4356 | E_err:   0.010313
[2025-10-23 06:48:36] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -65.514509 | E_var:     0.3923 | E_err:   0.009787
[2025-10-23 06:48:49] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -65.487427 | E_var:     0.5769 | E_err:   0.011868
[2025-10-23 06:49:02] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -65.513169 | E_var:     0.4508 | E_err:   0.010490
[2025-10-23 06:49:16] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -65.515496 | E_var:     0.3420 | E_err:   0.009138
[2025-10-23 06:49:29] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -65.497443 | E_var:     0.3973 | E_err:   0.009849
[2025-10-23 06:49:42] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -65.494035 | E_var:     0.4740 | E_err:   0.010758
[2025-10-23 06:49:55] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -65.494334 | E_var:     0.3745 | E_err:   0.009563
[2025-10-23 06:50:08] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -65.501933 | E_var:     0.4945 | E_err:   0.010988
[2025-10-23 06:50:22] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -65.491179 | E_var:     0.4217 | E_err:   0.010147
[2025-10-23 06:50:35] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -65.497720 | E_var:     0.3746 | E_err:   0.009564
[2025-10-23 06:50:48] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -65.487050 | E_var:     0.4342 | E_err:   0.010296
[2025-10-23 06:51:01] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -65.509800 | E_var:     0.6081 | E_err:   0.012185
[2025-10-23 06:51:15] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -65.512048 | E_var:     0.4135 | E_err:   0.010048
[2025-10-23 06:51:15] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-23 06:51:28] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -65.517641 | E_var:     0.3750 | E_err:   0.009569
[2025-10-23 06:51:41] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -65.524315 | E_var:     0.3723 | E_err:   0.009534
[2025-10-23 06:51:54] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -65.522462 | E_var:     0.4073 | E_err:   0.009972
[2025-10-23 06:52:08] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -65.507569 | E_var:     0.3954 | E_err:   0.009825
[2025-10-23 06:52:21] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -65.529144 | E_var:     0.4215 | E_err:   0.010144
[2025-10-23 06:52:34] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -65.497617 | E_var:     0.4600 | E_err:   0.010597
[2025-10-23 06:52:47] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -65.502916 | E_var:     0.4422 | E_err:   0.010391
[2025-10-23 06:53:00] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -65.503411 | E_var:     0.4032 | E_err:   0.009921
[2025-10-23 06:53:14] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -65.501670 | E_var:     0.4533 | E_err:   0.010520
[2025-10-23 06:53:27] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -65.520102 | E_var:     0.6249 | E_err:   0.012352
[2025-10-23 06:53:40] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -65.510033 | E_var:     0.4005 | E_err:   0.009888
[2025-10-23 06:53:53] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -65.494254 | E_var:     0.4177 | E_err:   0.010099
[2025-10-23 06:54:06] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -65.507995 | E_var:     0.5050 | E_err:   0.011104
[2025-10-23 06:54:20] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -65.523289 | E_var:     0.3670 | E_err:   0.009465
[2025-10-23 06:54:33] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -65.503072 | E_var:     0.4251 | E_err:   0.010188
[2025-10-23 06:54:46] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -65.500353 | E_var:     0.4743 | E_err:   0.010761
[2025-10-23 06:54:59] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -65.494156 | E_var:     0.4681 | E_err:   0.010690
[2025-10-23 06:55:13] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -65.500058 | E_var:     0.4074 | E_err:   0.009973
[2025-10-23 06:55:26] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -65.485094 | E_var:     0.4158 | E_err:   0.010075
[2025-10-23 06:55:39] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -65.521252 | E_var:     0.4066 | E_err:   0.009964
[2025-10-23 06:55:52] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -65.524226 | E_var:     0.6304 | E_err:   0.012406
[2025-10-23 06:56:05] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -65.517445 | E_var:     0.3941 | E_err:   0.009808
[2025-10-23 06:56:19] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -65.517632 | E_var:     0.3760 | E_err:   0.009582
[2025-10-23 06:56:32] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -65.520559 | E_var:     0.4061 | E_err:   0.009957
[2025-10-23 06:56:45] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -65.500609 | E_var:     0.4061 | E_err:   0.009957
[2025-10-23 06:56:58] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -65.503471 | E_var:     0.4503 | E_err:   0.010486
[2025-10-23 06:57:12] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -65.509321 | E_var:     0.3930 | E_err:   0.009795
[2025-10-23 06:57:25] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -65.499029 | E_var:     0.4119 | E_err:   0.010028
[2025-10-23 06:57:38] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -65.495353 | E_var:     0.4768 | E_err:   0.010789
[2025-10-23 06:57:51] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -65.497532 | E_var:     0.3593 | E_err:   0.009365
[2025-10-23 06:58:04] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -65.515545 | E_var:     0.4643 | E_err:   0.010647
[2025-10-23 06:58:18] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -65.518125 | E_var:     0.3799 | E_err:   0.009631
[2025-10-23 06:58:31] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -65.502185 | E_var:     0.3584 | E_err:   0.009354
[2025-10-23 06:58:44] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -65.518885 | E_var:     0.4318 | E_err:   0.010268
[2025-10-23 06:58:57] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -65.514480 | E_var:     0.3962 | E_err:   0.009835
[2025-10-23 06:59:10] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -65.491478 | E_var:     0.3514 | E_err:   0.009263
[2025-10-23 06:59:24] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -65.499105 | E_var:     0.3840 | E_err:   0.009682
[2025-10-23 06:59:37] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -65.506502 | E_var:     0.3948 | E_err:   0.009818
[2025-10-23 06:59:50] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -65.488043 | E_var:     0.4036 | E_err:   0.009926
[2025-10-23 07:00:03] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -65.500460 | E_var:     0.3666 | E_err:   0.009460
[2025-10-23 07:00:17] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -65.515609 | E_var:     0.3584 | E_err:   0.009354
[2025-10-23 07:00:30] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -65.505170 | E_var:     0.4285 | E_err:   0.010228
[2025-10-23 07:00:43] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -65.499561 | E_var:     0.3605 | E_err:   0.009382
[2025-10-23 07:00:56] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -65.495638 | E_var:     0.3541 | E_err:   0.009298
[2025-10-23 07:01:09] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -65.514199 | E_var:     0.3940 | E_err:   0.009808
[2025-10-23 07:01:23] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -65.492688 | E_var:     0.3530 | E_err:   0.009283
[2025-10-23 07:01:36] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -65.496398 | E_var:     0.3305 | E_err:   0.008982
[2025-10-23 07:01:49] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -65.510136 | E_var:     0.3810 | E_err:   0.009644
[2025-10-23 07:02:02] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -65.505779 | E_var:     0.3631 | E_err:   0.009416
[2025-10-23 07:02:16] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -65.505659 | E_var:     0.3928 | E_err:   0.009793
[2025-10-23 07:02:29] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -65.508938 | E_var:     0.4009 | E_err:   0.009893
[2025-10-23 07:02:42] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -65.509103 | E_var:     0.4750 | E_err:   0.010769
[2025-10-23 07:02:55] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -65.517056 | E_var:     0.4705 | E_err:   0.010718
[2025-10-23 07:03:08] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -65.522097 | E_var:     0.3452 | E_err:   0.009181
[2025-10-23 07:03:22] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -65.502261 | E_var:     0.4145 | E_err:   0.010060
[2025-10-23 07:03:35] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -65.514251 | E_var:     0.3584 | E_err:   0.009355
[2025-10-23 07:03:48] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -65.525386 | E_var:     0.4417 | E_err:   0.010384
[2025-10-23 07:04:01] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -65.502472 | E_var:     0.3302 | E_err:   0.008979
[2025-10-23 07:04:14] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -65.499943 | E_var:     0.4347 | E_err:   0.010301
[2025-10-23 07:04:28] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -65.500747 | E_var:     0.4322 | E_err:   0.010272
[2025-10-23 07:04:41] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -65.508482 | E_var:     0.5007 | E_err:   0.011056
[2025-10-23 07:04:54] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -65.500967 | E_var:     0.3672 | E_err:   0.009468
[2025-10-23 07:05:07] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -65.503204 | E_var:     0.4657 | E_err:   0.010663
[2025-10-23 07:05:21] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -65.504454 | E_var:     0.4143 | E_err:   0.010057
[2025-10-23 07:05:34] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -65.496454 | E_var:     0.4288 | E_err:   0.010232
[2025-10-23 07:05:47] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -65.504533 | E_var:     0.3655 | E_err:   0.009447
[2025-10-23 07:06:00] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -65.485490 | E_var:     0.3640 | E_err:   0.009427
[2025-10-23 07:06:13] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -65.516675 | E_var:     0.4019 | E_err:   0.009905
[2025-10-23 07:06:27] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -65.499961 | E_var:     0.4075 | E_err:   0.009974
[2025-10-23 07:06:40] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -65.490186 | E_var:     0.4448 | E_err:   0.010421
[2025-10-23 07:06:53] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -65.505057 | E_var:     0.3872 | E_err:   0.009722
[2025-10-23 07:07:06] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -65.525717 | E_var:     0.3764 | E_err:   0.009586
[2025-10-23 07:07:20] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -65.500670 | E_var:     0.4972 | E_err:   0.011017
[2025-10-23 07:07:33] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -65.509569 | E_var:     0.4421 | E_err:   0.010390
[2025-10-23 07:07:46] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -65.503343 | E_var:     0.5776 | E_err:   0.011875
[2025-10-23 07:07:59] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -65.517545 | E_var:     0.4176 | E_err:   0.010097
[2025-10-23 07:08:12] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -65.499039 | E_var:     0.4150 | E_err:   0.010065
[2025-10-23 07:08:26] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -65.504136 | E_var:     0.4204 | E_err:   0.010131
[2025-10-23 07:08:39] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -65.515010 | E_var:     0.3966 | E_err:   0.009841
[2025-10-23 07:08:52] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -65.508677 | E_var:     0.3320 | E_err:   0.009003
[2025-10-23 07:09:05] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -65.505543 | E_var:     0.4556 | E_err:   0.010546
[2025-10-23 07:09:19] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -65.511011 | E_var:     0.3546 | E_err:   0.009305
[2025-10-23 07:09:32] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -65.508114 | E_var:     0.4488 | E_err:   0.010468
[2025-10-23 07:09:45] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -65.502782 | E_var:     0.4403 | E_err:   0.010367
[2025-10-23 07:09:58] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -65.510585 | E_var:     0.4218 | E_err:   0.010147
[2025-10-23 07:10:11] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -65.504225 | E_var:     0.6084 | E_err:   0.012188
[2025-10-23 07:10:25] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -65.514182 | E_var:     0.3994 | E_err:   0.009875
[2025-10-23 07:10:38] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -65.507050 | E_var:     0.4552 | E_err:   0.010541
[2025-10-23 07:10:51] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -65.509052 | E_var:     0.3616 | E_err:   0.009396
[2025-10-23 07:11:04] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -65.504313 | E_var:     0.4743 | E_err:   0.010760
[2025-10-23 07:11:17] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -65.494692 | E_var:     0.3921 | E_err:   0.009784
[2025-10-23 07:11:31] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -65.506499 | E_var:     0.4127 | E_err:   0.010038
[2025-10-23 07:11:44] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -65.491818 | E_var:     0.3952 | E_err:   0.009823
[2025-10-23 07:11:57] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -65.497150 | E_var:     0.4955 | E_err:   0.010999
[2025-10-23 07:12:10] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -65.477049 | E_var:     0.4336 | E_err:   0.010288
[2025-10-23 07:12:24] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -65.508150 | E_var:     0.4924 | E_err:   0.010965
[2025-10-23 07:12:37] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -65.508865 | E_var:     0.4379 | E_err:   0.010339
[2025-10-23 07:12:50] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -65.487925 | E_var:     0.4532 | E_err:   0.010519
[2025-10-23 07:13:03] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -65.498271 | E_var:     0.3826 | E_err:   0.009665
[2025-10-23 07:13:16] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -65.505305 | E_var:     0.3504 | E_err:   0.009250
[2025-10-23 07:13:30] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -65.502937 | E_var:     0.4062 | E_err:   0.009958
[2025-10-23 07:13:43] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -65.513483 | E_var:     0.3948 | E_err:   0.009818
[2025-10-23 07:13:56] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -65.510563 | E_var:     0.3606 | E_err:   0.009383
[2025-10-23 07:14:09] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -65.501052 | E_var:     0.5090 | E_err:   0.011147
[2025-10-23 07:14:23] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -65.520291 | E_var:     0.4434 | E_err:   0.010404
[2025-10-23 07:14:23] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-23 07:14:36] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -65.513783 | E_var:     0.4291 | E_err:   0.010236
[2025-10-23 07:14:49] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -65.493949 | E_var:     0.3614 | E_err:   0.009393
[2025-10-23 07:15:02] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -65.491564 | E_var:     0.5493 | E_err:   0.011580
[2025-10-23 07:15:15] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -65.502702 | E_var:     0.3771 | E_err:   0.009594
[2025-10-23 07:15:29] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -65.507097 | E_var:     0.4568 | E_err:   0.010560
[2025-10-23 07:15:42] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -65.511286 | E_var:     0.3491 | E_err:   0.009232
[2025-10-23 07:15:55] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -65.495007 | E_var:     0.5682 | E_err:   0.011778
[2025-10-23 07:16:08] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -65.521855 | E_var:     0.4643 | E_err:   0.010647
[2025-10-23 07:16:22] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -65.519713 | E_var:     0.3825 | E_err:   0.009663
[2025-10-23 07:16:35] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -65.494280 | E_var:     0.4652 | E_err:   0.010657
[2025-10-23 07:16:48] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -65.503622 | E_var:     0.4343 | E_err:   0.010298
[2025-10-23 07:17:01] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -65.505573 | E_var:     0.4028 | E_err:   0.009917
[2025-10-23 07:17:14] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -65.509430 | E_var:     0.3828 | E_err:   0.009667
[2025-10-23 07:17:28] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -65.505344 | E_var:     0.4231 | E_err:   0.010164
[2025-10-23 07:17:41] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -65.494698 | E_var:     0.4502 | E_err:   0.010484
[2025-10-23 07:17:54] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -65.518676 | E_var:     0.4791 | E_err:   0.010815
[2025-10-23 07:18:07] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -65.498244 | E_var:     0.4957 | E_err:   0.011001
[2025-10-23 07:18:21] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -65.503271 | E_var:     0.3979 | E_err:   0.009856
[2025-10-23 07:18:34] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -65.523058 | E_var:     0.4054 | E_err:   0.009949
[2025-10-23 07:18:47] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -65.497924 | E_var:     0.3587 | E_err:   0.009358
[2025-10-23 07:19:00] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -65.510676 | E_var:     0.4255 | E_err:   0.010192
[2025-10-23 07:19:13] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -65.509820 | E_var:     0.4872 | E_err:   0.010906
[2025-10-23 07:19:27] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -65.499471 | E_var:     0.5376 | E_err:   0.011457
[2025-10-23 07:19:40] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -65.502389 | E_var:     0.3632 | E_err:   0.009417
[2025-10-23 07:19:53] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -65.498447 | E_var:     0.4624 | E_err:   0.010625
[2025-10-23 07:20:06] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -65.522059 | E_var:     0.4419 | E_err:   0.010387
[2025-10-23 07:20:19] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -65.500166 | E_var:     0.3675 | E_err:   0.009472
[2025-10-23 07:20:33] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -65.500494 | E_var:     0.5001 | E_err:   0.011050
[2025-10-23 07:20:46] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -65.503688 | E_var:     0.3626 | E_err:   0.009409
[2025-10-23 07:20:59] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -65.490760 | E_var:     0.4608 | E_err:   0.010607
[2025-10-23 07:21:12] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -65.487035 | E_var:     0.8028 | E_err:   0.014000
[2025-10-23 07:21:26] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -65.500404 | E_var:     0.3985 | E_err:   0.009864
[2025-10-23 07:21:39] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -65.510856 | E_var:     0.4413 | E_err:   0.010380
[2025-10-23 07:21:52] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -65.520132 | E_var:     0.3594 | E_err:   0.009367
[2025-10-23 07:22:05] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -65.497845 | E_var:     0.3963 | E_err:   0.009836
[2025-10-23 07:22:18] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -65.506463 | E_var:     0.3787 | E_err:   0.009615
[2025-10-23 07:22:32] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -65.503549 | E_var:     0.4108 | E_err:   0.010015
[2025-10-23 07:22:45] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -65.503459 | E_var:     0.3369 | E_err:   0.009069
[2025-10-23 07:22:58] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -65.501343 | E_var:     0.3916 | E_err:   0.009777
[2025-10-23 07:23:11] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -65.504483 | E_var:     0.4103 | E_err:   0.010008
[2025-10-23 07:23:25] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -65.519310 | E_var:     0.3601 | E_err:   0.009377
[2025-10-23 07:23:38] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -65.509294 | E_var:     0.4521 | E_err:   0.010506
[2025-10-23 07:23:51] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -65.494571 | E_var:     0.4188 | E_err:   0.010112
[2025-10-23 07:24:04] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -65.524206 | E_var:     0.4546 | E_err:   0.010535
[2025-10-23 07:24:17] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -65.512753 | E_var:     0.3887 | E_err:   0.009741
[2025-10-23 07:24:31] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -65.497450 | E_var:     0.4103 | E_err:   0.010008
[2025-10-23 07:24:44] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -65.511347 | E_var:     0.4315 | E_err:   0.010264
[2025-10-23 07:24:57] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -65.507616 | E_var:     0.3329 | E_err:   0.009015
[2025-10-23 07:25:10] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -65.486793 | E_var:     0.4196 | E_err:   0.010121
[2025-10-23 07:25:23] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -65.503847 | E_var:     0.4033 | E_err:   0.009922
[2025-10-23 07:25:37] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -65.496318 | E_var:     0.4751 | E_err:   0.010770
[2025-10-23 07:25:50] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -65.502391 | E_var:     0.4004 | E_err:   0.009887
[2025-10-23 07:26:03] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -65.504555 | E_var:     0.4016 | E_err:   0.009901
[2025-10-23 07:26:16] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -65.489787 | E_var:     0.5049 | E_err:   0.011103
[2025-10-23 07:26:30] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -65.509230 | E_var:     0.4314 | E_err:   0.010263
[2025-10-23 07:26:43] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -65.520986 | E_var:     0.4496 | E_err:   0.010477
[2025-10-23 07:26:56] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -65.500188 | E_var:     0.4528 | E_err:   0.010514
[2025-10-23 07:27:09] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -65.525924 | E_var:     0.3383 | E_err:   0.009089
[2025-10-23 07:27:22] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -65.497607 | E_var:     0.4629 | E_err:   0.010630
[2025-10-23 07:27:36] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -65.512384 | E_var:     0.3637 | E_err:   0.009423
[2025-10-23 07:27:49] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -65.503228 | E_var:     0.4093 | E_err:   0.009996
[2025-10-23 07:28:02] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -65.498715 | E_var:     0.4450 | E_err:   0.010423
[2025-10-23 07:28:15] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -65.500343 | E_var:     0.3873 | E_err:   0.009724
[2025-10-23 07:28:29] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -65.505779 | E_var:     0.4436 | E_err:   0.010407
[2025-10-23 07:28:42] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -65.508883 | E_var:     0.3692 | E_err:   0.009494
[2025-10-23 07:28:55] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -65.529530 | E_var:     1.0616 | E_err:   0.016099
[2025-10-23 07:29:08] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -65.490995 | E_var:     0.4581 | E_err:   0.010576
[2025-10-23 07:29:21] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -65.509835 | E_var:     0.4571 | E_err:   0.010564
[2025-10-23 07:29:35] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -65.506128 | E_var:     0.3750 | E_err:   0.009568
[2025-10-23 07:29:48] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -65.504359 | E_var:     0.3826 | E_err:   0.009665
[2025-10-23 07:30:01] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -65.522664 | E_var:     0.4110 | E_err:   0.010017
[2025-10-23 07:30:14] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -65.506358 | E_var:     0.4145 | E_err:   0.010060
[2025-10-23 07:30:27] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -65.507036 | E_var:     0.4933 | E_err:   0.010974
[2025-10-23 07:30:41] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -65.503657 | E_var:     0.3567 | E_err:   0.009332
[2025-10-23 07:30:54] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -65.487806 | E_var:     0.3703 | E_err:   0.009508
[2025-10-23 07:31:07] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -65.505153 | E_var:     0.4437 | E_err:   0.010407
[2025-10-23 07:31:20] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -65.496860 | E_var:     0.3797 | E_err:   0.009627
[2025-10-23 07:31:34] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -65.510650 | E_var:     0.3444 | E_err:   0.009170
[2025-10-23 07:31:47] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -65.503342 | E_var:     0.4905 | E_err:   0.010943
[2025-10-23 07:32:00] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -65.500803 | E_var:     0.4676 | E_err:   0.010684
[2025-10-23 07:32:13] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -65.511241 | E_var:     0.3740 | E_err:   0.009556
[2025-10-23 07:32:26] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -65.508660 | E_var:     0.5890 | E_err:   0.011992
[2025-10-23 07:32:40] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -65.498796 | E_var:     0.4362 | E_err:   0.010320
[2025-10-23 07:32:53] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -65.499967 | E_var:     0.3918 | E_err:   0.009780
[2025-10-23 07:33:06] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -65.520245 | E_var:     0.3629 | E_err:   0.009413
[2025-10-23 07:33:19] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -65.491404 | E_var:     0.3780 | E_err:   0.009607
[2025-10-23 07:33:33] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -65.517976 | E_var:     1.7525 | E_err:   0.020685
[2025-10-23 07:33:46] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -65.511815 | E_var:     0.3657 | E_err:   0.009448
[2025-10-23 07:33:59] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -65.499176 | E_var:     0.5868 | E_err:   0.011970
[2025-10-23 07:34:12] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -65.510062 | E_var:     0.4213 | E_err:   0.010142
[2025-10-23 07:34:25] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -65.524767 | E_var:     0.4430 | E_err:   0.010400
[2025-10-23 07:34:39] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -65.524427 | E_var:     0.5043 | E_err:   0.011096
[2025-10-23 07:34:52] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -65.501117 | E_var:     0.4473 | E_err:   0.010450
[2025-10-23 07:35:05] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -65.513866 | E_var:     0.5065 | E_err:   0.011120
[2025-10-23 07:35:18] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -65.504771 | E_var:     0.4021 | E_err:   0.009908
[2025-10-23 07:35:32] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -65.497990 | E_var:     0.3721 | E_err:   0.009531
[2025-10-23 07:35:45] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -65.507635 | E_var:     0.3967 | E_err:   0.009841
[2025-10-23 07:35:58] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -65.515091 | E_var:     0.3549 | E_err:   0.009308
[2025-10-23 07:36:11] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -65.492575 | E_var:     0.3417 | E_err:   0.009133
[2025-10-23 07:36:24] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -65.503467 | E_var:     0.3536 | E_err:   0.009291
[2025-10-23 07:36:38] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -65.502077 | E_var:     0.4023 | E_err:   0.009911
[2025-10-23 07:36:51] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -65.501815 | E_var:     0.4106 | E_err:   0.010013
[2025-10-23 07:37:04] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -65.520157 | E_var:     0.4246 | E_err:   0.010182
[2025-10-23 07:37:17] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -65.529650 | E_var:     0.3276 | E_err:   0.008943
[2025-10-23 07:37:30] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -65.506567 | E_var:     0.3668 | E_err:   0.009463
[2025-10-23 07:37:30] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-23 07:37:44] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -65.505141 | E_var:     0.4071 | E_err:   0.009970
[2025-10-23 07:37:57] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -65.511705 | E_var:     0.4500 | E_err:   0.010481
[2025-10-23 07:38:10] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -65.505815 | E_var:     0.4000 | E_err:   0.009882
[2025-10-23 07:38:23] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -65.505639 | E_var:     0.4965 | E_err:   0.011010
[2025-10-23 07:38:37] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -65.516830 | E_var:     0.3463 | E_err:   0.009195
[2025-10-23 07:38:50] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -65.506588 | E_var:     0.4433 | E_err:   0.010403
[2025-10-23 07:39:03] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -65.508101 | E_var:     0.3562 | E_err:   0.009326
[2025-10-23 07:39:16] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -65.508025 | E_var:     0.3518 | E_err:   0.009267
[2025-10-23 07:39:29] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -65.496227 | E_var:     0.3843 | E_err:   0.009687
[2025-10-23 07:39:43] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -65.515162 | E_var:     0.3921 | E_err:   0.009785
[2025-10-23 07:39:56] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -65.515859 | E_var:     0.3855 | E_err:   0.009702
[2025-10-23 07:40:09] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -65.527324 | E_var:     0.4262 | E_err:   0.010201
[2025-10-23 07:40:22] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -65.503147 | E_var:     0.3666 | E_err:   0.009460
[2025-10-23 07:40:35] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -65.510254 | E_var:     0.3474 | E_err:   0.009210
[2025-10-23 07:40:49] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -65.498401 | E_var:     0.3927 | E_err:   0.009792
[2025-10-23 07:41:02] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -65.494301 | E_var:     0.3858 | E_err:   0.009705
[2025-10-23 07:41:15] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -65.507738 | E_var:     0.4658 | E_err:   0.010665
[2025-10-23 07:41:28] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -65.508356 | E_var:     0.3456 | E_err:   0.009186
[2025-10-23 07:41:42] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -65.515975 | E_var:     0.5869 | E_err:   0.011970
[2025-10-23 07:41:55] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -65.513927 | E_var:     0.3399 | E_err:   0.009110
[2025-10-23 07:42:08] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -65.502498 | E_var:     0.3914 | E_err:   0.009776
[2025-10-23 07:42:21] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -65.502316 | E_var:     0.6815 | E_err:   0.012899
[2025-10-23 07:42:34] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -65.507091 | E_var:     0.4344 | E_err:   0.010298
[2025-10-23 07:42:48] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -65.513345 | E_var:     0.4759 | E_err:   0.010779
[2025-10-23 07:43:01] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -65.507810 | E_var:     0.3573 | E_err:   0.009340
[2025-10-23 07:43:14] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -65.510109 | E_var:     0.5311 | E_err:   0.011387
[2025-10-23 07:43:27] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -65.493229 | E_var:     0.4627 | E_err:   0.010629
[2025-10-23 07:43:40] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -65.512474 | E_var:     0.3640 | E_err:   0.009426
[2025-10-23 07:43:54] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -65.501047 | E_var:     0.4038 | E_err:   0.009929
[2025-10-23 07:44:07] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -65.507117 | E_var:     0.3943 | E_err:   0.009812
[2025-10-23 07:44:20] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -65.503148 | E_var:     0.3970 | E_err:   0.009845
[2025-10-23 07:44:33] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -65.503615 | E_var:     0.3810 | E_err:   0.009644
[2025-10-23 07:44:47] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -65.506635 | E_var:     0.5385 | E_err:   0.011466
[2025-10-23 07:45:00] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -65.505906 | E_var:     0.5849 | E_err:   0.011950
[2025-10-23 07:45:13] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -65.515833 | E_var:     0.3909 | E_err:   0.009769
[2025-10-23 07:45:26] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -65.513485 | E_var:     0.5246 | E_err:   0.011318
[2025-10-23 07:45:39] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -65.505856 | E_var:     0.3842 | E_err:   0.009685
[2025-10-23 07:45:53] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -65.515537 | E_var:     0.4970 | E_err:   0.011016
[2025-10-23 07:46:06] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -65.520249 | E_var:     0.3872 | E_err:   0.009723
[2025-10-23 07:46:19] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -65.500970 | E_var:     0.4402 | E_err:   0.010367
[2025-10-23 07:46:32] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -65.498804 | E_var:     0.4072 | E_err:   0.009971
[2025-10-23 07:46:46] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -65.489786 | E_var:     0.4257 | E_err:   0.010195
[2025-10-23 07:46:59] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -65.500335 | E_var:     0.3593 | E_err:   0.009366
[2025-10-23 07:47:12] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -65.500665 | E_var:     0.4670 | E_err:   0.010678
[2025-10-23 07:47:25] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -65.521405 | E_var:     0.3661 | E_err:   0.009454
[2025-10-23 07:47:38] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -65.490024 | E_var:     0.4923 | E_err:   0.010964
[2025-10-23 07:47:52] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -65.500916 | E_var:     0.3373 | E_err:   0.009074
[2025-10-23 07:48:05] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -65.517752 | E_var:     0.3848 | E_err:   0.009693
[2025-10-23 07:48:18] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -65.516808 | E_var:     0.4598 | E_err:   0.010595
[2025-10-23 07:48:31] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -65.523669 | E_var:     0.3922 | E_err:   0.009786
[2025-10-23 07:48:44] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -65.520780 | E_var:     0.3902 | E_err:   0.009760
[2025-10-23 07:48:58] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -65.494519 | E_var:     0.3424 | E_err:   0.009144
[2025-10-23 07:49:11] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -65.508523 | E_var:     0.4228 | E_err:   0.010160
[2025-10-23 07:49:24] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -65.509981 | E_var:     0.4584 | E_err:   0.010579
[2025-10-23 07:49:37] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -65.499055 | E_var:     0.4106 | E_err:   0.010012
[2025-10-23 07:49:51] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -65.502484 | E_var:     0.3893 | E_err:   0.009748
[2025-10-23 07:50:04] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -65.507500 | E_var:     0.4633 | E_err:   0.010636
[2025-10-23 07:50:17] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -65.509106 | E_var:     0.4071 | E_err:   0.009969
[2025-10-23 07:50:30] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -65.488964 | E_var:     0.3563 | E_err:   0.009326
[2025-10-23 07:50:43] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -65.504684 | E_var:     0.4000 | E_err:   0.009882
[2025-10-23 07:50:57] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -65.524277 | E_var:     0.3952 | E_err:   0.009823
[2025-10-23 07:51:10] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -65.511737 | E_var:     0.4663 | E_err:   0.010669
[2025-10-23 07:51:23] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -65.488127 | E_var:     0.3765 | E_err:   0.009587
[2025-10-23 07:51:36] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -65.511415 | E_var:     0.4174 | E_err:   0.010095
[2025-10-23 07:51:50] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -65.510466 | E_var:     0.4238 | E_err:   0.010172
[2025-10-23 07:52:03] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -65.493240 | E_var:     0.5106 | E_err:   0.011165
[2025-10-23 07:52:16] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -65.526547 | E_var:     0.3472 | E_err:   0.009206
[2025-10-23 07:52:29] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -65.509875 | E_var:     0.4291 | E_err:   0.010235
[2025-10-23 07:52:42] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -65.522249 | E_var:     0.4120 | E_err:   0.010030
[2025-10-23 07:52:56] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -65.499571 | E_var:     0.3260 | E_err:   0.008921
[2025-10-23 07:53:09] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -65.501132 | E_var:     0.3940 | E_err:   0.009807
[2025-10-23 07:53:22] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -65.501805 | E_var:     0.4449 | E_err:   0.010422
[2025-10-23 07:53:35] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -65.520734 | E_var:     0.4328 | E_err:   0.010280
[2025-10-23 07:53:48] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -65.512183 | E_var:     0.3534 | E_err:   0.009289
[2025-10-23 07:54:02] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -65.498160 | E_var:     0.3572 | E_err:   0.009339
[2025-10-23 07:54:15] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -65.492132 | E_var:     0.4968 | E_err:   0.011013
[2025-10-23 07:54:28] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -65.516544 | E_var:     0.3943 | E_err:   0.009811
[2025-10-23 07:54:41] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -65.504956 | E_var:     0.3756 | E_err:   0.009576
[2025-10-23 07:54:54] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -65.518103 | E_var:     0.5531 | E_err:   0.011620
[2025-10-23 07:55:08] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -65.503820 | E_var:     0.3890 | E_err:   0.009745
[2025-10-23 07:55:21] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -65.509973 | E_var:     0.4568 | E_err:   0.010560
[2025-10-23 07:55:34] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -65.499715 | E_var:     0.3831 | E_err:   0.009671
[2025-10-23 07:55:47] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -65.500998 | E_var:     0.5195 | E_err:   0.011262
[2025-10-23 07:56:01] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -65.494878 | E_var:     0.3700 | E_err:   0.009504
[2025-10-23 07:56:14] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -65.503269 | E_var:     0.4190 | E_err:   0.010114
[2025-10-23 07:56:27] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -65.509923 | E_var:     0.4797 | E_err:   0.010822
[2025-10-23 07:56:40] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -65.510107 | E_var:     0.3593 | E_err:   0.009365
[2025-10-23 07:56:53] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -65.502773 | E_var:     0.3900 | E_err:   0.009758
[2025-10-23 07:57:07] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -65.498276 | E_var:     0.4974 | E_err:   0.011020
[2025-10-23 07:57:20] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -65.503099 | E_var:     0.3812 | E_err:   0.009647
[2025-10-23 07:57:33] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -65.494803 | E_var:     0.3857 | E_err:   0.009704
[2025-10-23 07:57:46] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -65.495669 | E_var:     0.3945 | E_err:   0.009814
[2025-10-23 07:57:59] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -65.512353 | E_var:     0.3804 | E_err:   0.009636
[2025-10-23 07:58:13] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -65.499349 | E_var:     0.5723 | E_err:   0.011820
[2025-10-23 07:58:26] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -65.512518 | E_var:     0.3782 | E_err:   0.009609
[2025-10-23 07:58:39] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -65.504527 | E_var:     0.3744 | E_err:   0.009561
[2025-10-23 07:58:52] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -65.509901 | E_var:     0.4872 | E_err:   0.010907
[2025-10-23 07:59:06] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -65.508304 | E_var:     0.4036 | E_err:   0.009926
[2025-10-23 07:59:19] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -65.516415 | E_var:     0.4166 | E_err:   0.010085
[2025-10-23 07:59:32] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -65.500472 | E_var:     0.4622 | E_err:   0.010623
[2025-10-23 07:59:45] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -65.497974 | E_var:     0.4062 | E_err:   0.009959
[2025-10-23 07:59:58] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -65.514377 | E_var:     0.4177 | E_err:   0.010098
[2025-10-23 08:00:12] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -65.514761 | E_var:     0.4117 | E_err:   0.010026
[2025-10-23 08:00:25] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -65.524041 | E_var:     0.4420 | E_err:   0.010388
[2025-10-23 08:00:38] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -65.497256 | E_var:     0.4266 | E_err:   0.010206
[2025-10-23 08:00:38] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-23 08:00:51] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -65.502133 | E_var:     0.4302 | E_err:   0.010248
[2025-10-23 08:01:05] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -65.500248 | E_var:     0.4091 | E_err:   0.009994
[2025-10-23 08:01:18] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -65.504212 | E_var:     0.5808 | E_err:   0.011908
[2025-10-23 08:01:31] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -65.498785 | E_var:     0.4401 | E_err:   0.010365
[2025-10-23 08:01:44] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -65.498584 | E_var:     0.4562 | E_err:   0.010554
[2025-10-23 08:01:58] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -65.514317 | E_var:     0.3536 | E_err:   0.009291
[2025-10-23 08:02:11] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -65.495859 | E_var:     0.5418 | E_err:   0.011502
[2025-10-23 08:02:24] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -65.496632 | E_var:     0.3962 | E_err:   0.009836
[2025-10-23 08:02:37] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -65.472525 | E_var:     0.5489 | E_err:   0.011576
[2025-10-23 08:02:50] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -65.508058 | E_var:     0.3893 | E_err:   0.009749
[2025-10-23 08:03:04] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -65.511701 | E_var:     0.3681 | E_err:   0.009480
[2025-10-23 08:03:17] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -65.504396 | E_var:     0.4289 | E_err:   0.010233
[2025-10-23 08:03:30] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -65.483989 | E_var:     0.3826 | E_err:   0.009664
[2025-10-23 08:03:43] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -65.509042 | E_var:     0.3411 | E_err:   0.009125
[2025-10-23 08:03:56] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -65.504831 | E_var:     0.4018 | E_err:   0.009904
[2025-10-23 08:04:10] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -65.506980 | E_var:     0.4549 | E_err:   0.010538
[2025-10-23 08:04:23] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -65.509194 | E_var:     0.5584 | E_err:   0.011676
[2025-10-23 08:04:36] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -65.499810 | E_var:     0.6108 | E_err:   0.012212
[2025-10-23 08:04:49] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -65.516998 | E_var:     0.4890 | E_err:   0.010927
[2025-10-23 08:05:02] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -65.508978 | E_var:     0.5375 | E_err:   0.011456
[2025-10-23 08:05:16] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -65.504447 | E_var:     0.3365 | E_err:   0.009064
[2025-10-23 08:05:29] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -65.505694 | E_var:     0.3618 | E_err:   0.009399
[2025-10-23 08:05:42] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -65.501981 | E_var:     0.3771 | E_err:   0.009595
[2025-10-23 08:05:55] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -65.513278 | E_var:     0.4511 | E_err:   0.010494
[2025-10-23 08:06:09] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -65.498560 | E_var:     0.7646 | E_err:   0.013663
[2025-10-23 08:06:22] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -65.502671 | E_var:     0.4063 | E_err:   0.009960
[2025-10-23 08:06:35] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -65.505814 | E_var:     0.4375 | E_err:   0.010334
[2025-10-23 08:06:48] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -65.495472 | E_var:     0.4350 | E_err:   0.010305
[2025-10-23 08:07:01] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -65.495052 | E_var:     0.4747 | E_err:   0.010766
[2025-10-23 08:07:15] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -65.503982 | E_var:     0.4821 | E_err:   0.010849
[2025-10-23 08:07:28] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -65.504441 | E_var:     0.4132 | E_err:   0.010044
[2025-10-23 08:07:41] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -65.510941 | E_var:     0.4376 | E_err:   0.010337
[2025-10-23 08:07:54] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -65.515063 | E_var:     0.4057 | E_err:   0.009952
[2025-10-23 08:08:08] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -65.480752 | E_var:     0.4139 | E_err:   0.010052
[2025-10-23 08:08:21] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -65.491296 | E_var:     0.5477 | E_err:   0.011563
[2025-10-23 08:08:34] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -65.515802 | E_var:     0.8459 | E_err:   0.014371
[2025-10-23 08:08:47] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -65.513901 | E_var:     0.5709 | E_err:   0.011806
[2025-10-23 08:09:00] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -65.507987 | E_var:     0.4442 | E_err:   0.010414
[2025-10-23 08:09:14] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -65.502822 | E_var:     0.5291 | E_err:   0.011365
[2025-10-23 08:09:27] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -65.507295 | E_var:     0.3312 | E_err:   0.008993
[2025-10-23 08:09:40] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -65.504062 | E_var:     0.3778 | E_err:   0.009604
[2025-10-23 08:09:53] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -65.502832 | E_var:     0.4159 | E_err:   0.010077
[2025-10-23 08:10:07] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -65.494378 | E_var:     0.3780 | E_err:   0.009606
[2025-10-23 08:10:20] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -65.521135 | E_var:     0.3719 | E_err:   0.009528
[2025-10-23 08:10:33] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -65.486512 | E_var:     0.6248 | E_err:   0.012351
[2025-10-23 08:10:46] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -65.510832 | E_var:     0.4413 | E_err:   0.010379
[2025-10-23 08:10:59] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -65.500653 | E_var:     0.5887 | E_err:   0.011989
[2025-10-23 08:11:13] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -65.507014 | E_var:     0.4456 | E_err:   0.010430
[2025-10-23 08:11:26] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -65.493392 | E_var:     0.3531 | E_err:   0.009285
[2025-10-23 08:11:39] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -65.512126 | E_var:     0.4196 | E_err:   0.010122
[2025-10-23 08:11:52] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -65.506698 | E_var:     0.4887 | E_err:   0.010923
[2025-10-23 08:12:05] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -65.517744 | E_var:     0.5525 | E_err:   0.011614
[2025-10-23 08:12:19] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -65.482580 | E_var:     0.4373 | E_err:   0.010332
[2025-10-23 08:12:32] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -65.497760 | E_var:     0.3867 | E_err:   0.009717
[2025-10-23 08:12:45] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -65.503137 | E_var:     0.5734 | E_err:   0.011832
[2025-10-23 08:12:58] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -65.510342 | E_var:     0.4158 | E_err:   0.010076
[2025-10-23 08:13:12] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -65.509195 | E_var:     0.4286 | E_err:   0.010229
[2025-10-23 08:13:25] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -65.495896 | E_var:     0.4395 | E_err:   0.010359
[2025-10-23 08:13:38] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -65.510357 | E_var:     0.5215 | E_err:   0.011283
[2025-10-23 08:13:51] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -65.508258 | E_var:     0.4160 | E_err:   0.010078
[2025-10-23 08:14:04] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -65.494262 | E_var:     0.3841 | E_err:   0.009684
[2025-10-23 08:14:18] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -65.493736 | E_var:     0.3770 | E_err:   0.009594
[2025-10-23 08:14:31] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -65.486027 | E_var:     0.4158 | E_err:   0.010075
[2025-10-23 08:14:44] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -65.502220 | E_var:     0.3893 | E_err:   0.009749
[2025-10-23 08:14:57] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -65.499589 | E_var:     0.3899 | E_err:   0.009756
[2025-10-23 08:15:10] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -65.497115 | E_var:     0.4493 | E_err:   0.010473
[2025-10-23 08:15:24] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -65.502266 | E_var:     0.3970 | E_err:   0.009845
[2025-10-23 08:15:37] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -65.506742 | E_var:     0.4676 | E_err:   0.010685
[2025-10-23 08:15:50] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -65.504147 | E_var:     0.3913 | E_err:   0.009774
[2025-10-23 08:16:03] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -65.506610 | E_var:     0.4555 | E_err:   0.010545
[2025-10-23 08:16:17] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -65.511017 | E_var:     0.4462 | E_err:   0.010437
[2025-10-23 08:16:30] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -65.494611 | E_var:     0.4132 | E_err:   0.010044
[2025-10-23 08:16:43] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -65.515474 | E_var:     0.4299 | E_err:   0.010245
[2025-10-23 08:16:56] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -65.506703 | E_var:     0.3497 | E_err:   0.009240
[2025-10-23 08:17:09] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -65.511892 | E_var:     0.4264 | E_err:   0.010203
[2025-10-23 08:17:23] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -65.520650 | E_var:     0.4236 | E_err:   0.010169
[2025-10-23 08:17:36] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -65.484206 | E_var:     0.6141 | E_err:   0.012245
[2025-10-23 08:17:49] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -65.509649 | E_var:     0.3845 | E_err:   0.009689
[2025-10-23 08:18:02] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -65.506641 | E_var:     0.5336 | E_err:   0.011414
[2025-10-23 08:18:16] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -65.516723 | E_var:     0.4824 | E_err:   0.010853
[2025-10-23 08:18:29] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -65.501744 | E_var:     0.3988 | E_err:   0.009868
[2025-10-23 08:18:42] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -65.496288 | E_var:     0.3898 | E_err:   0.009755
[2025-10-23 08:18:55] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -65.506525 | E_var:     0.3712 | E_err:   0.009520
[2025-10-23 08:19:08] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -65.509698 | E_var:     0.4075 | E_err:   0.009975
[2025-10-23 08:19:22] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -65.517705 | E_var:     0.3905 | E_err:   0.009764
[2025-10-23 08:19:35] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -65.509990 | E_var:     0.4290 | E_err:   0.010234
[2025-10-23 08:19:48] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -65.497304 | E_var:     0.4009 | E_err:   0.009893
[2025-10-23 08:20:01] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -65.497520 | E_var:     0.4315 | E_err:   0.010264
[2025-10-23 08:20:14] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -65.520305 | E_var:     0.4055 | E_err:   0.009949
[2025-10-23 08:20:28] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -65.502237 | E_var:     0.4302 | E_err:   0.010248
[2025-10-23 08:20:41] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -65.502305 | E_var:     0.4063 | E_err:   0.009960
[2025-10-23 08:20:54] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -65.495548 | E_var:     0.4414 | E_err:   0.010381
[2025-10-23 08:21:07] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -65.505425 | E_var:     0.3875 | E_err:   0.009727
[2025-10-23 08:21:21] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -65.503416 | E_var:     0.4003 | E_err:   0.009885
[2025-10-23 08:21:34] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -65.510885 | E_var:     0.3783 | E_err:   0.009610
[2025-10-23 08:21:47] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -65.494083 | E_var:     0.4472 | E_err:   0.010448
[2025-10-23 08:22:00] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -65.494750 | E_var:     0.3815 | E_err:   0.009651
[2025-10-23 08:22:13] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -65.490379 | E_var:     0.4482 | E_err:   0.010460
[2025-10-23 08:22:27] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -65.515377 | E_var:     0.4492 | E_err:   0.010472
[2025-10-23 08:22:40] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -65.507279 | E_var:     0.4549 | E_err:   0.010538
[2025-10-23 08:22:53] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -65.505760 | E_var:     0.4041 | E_err:   0.009932
[2025-10-23 08:23:06] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -65.508458 | E_var:     0.6331 | E_err:   0.012433
[2025-10-23 08:23:20] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -65.505254 | E_var:     0.3953 | E_err:   0.009824
[2025-10-23 08:23:33] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -65.505483 | E_var:     0.4942 | E_err:   0.010984
[2025-10-23 08:23:46] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -65.498692 | E_var:     0.3925 | E_err:   0.009789
[2025-10-23 08:23:46] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-23 08:23:59] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -65.519502 | E_var:     0.3775 | E_err:   0.009601
[2025-10-23 08:24:12] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -65.516430 | E_var:     0.4987 | E_err:   0.011034
[2025-10-23 08:24:26] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -65.517909 | E_var:     0.3880 | E_err:   0.009733
[2025-10-23 08:24:39] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -65.506999 | E_var:     0.5087 | E_err:   0.011144
[2025-10-23 08:24:52] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -65.494946 | E_var:     0.4355 | E_err:   0.010311
[2025-10-23 08:25:05] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -65.511056 | E_var:     0.4252 | E_err:   0.010189
[2025-10-23 08:25:19] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -65.498978 | E_var:     0.4115 | E_err:   0.010023
[2025-10-23 08:25:32] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -65.493442 | E_var:     0.3642 | E_err:   0.009430
[2025-10-23 08:25:45] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -65.500866 | E_var:     0.3526 | E_err:   0.009278
[2025-10-23 08:25:58] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -65.520958 | E_var:     0.4323 | E_err:   0.010273
[2025-10-23 08:26:11] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -65.518789 | E_var:     0.3915 | E_err:   0.009777
[2025-10-23 08:26:25] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -65.482092 | E_var:     0.5760 | E_err:   0.011858
[2025-10-23 08:26:38] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -65.497489 | E_var:     0.4328 | E_err:   0.010279
[2025-10-23 08:26:51] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -65.499754 | E_var:     0.3976 | E_err:   0.009853
[2025-10-23 08:27:04] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -65.500894 | E_var:     0.4554 | E_err:   0.010544
[2025-10-23 08:27:17] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -65.507670 | E_var:     0.4018 | E_err:   0.009904
[2025-10-23 08:27:31] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -65.503321 | E_var:     0.3593 | E_err:   0.009366
[2025-10-23 08:27:44] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -65.519394 | E_var:     0.3739 | E_err:   0.009554
[2025-10-23 08:27:57] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -65.499269 | E_var:     0.4182 | E_err:   0.010104
[2025-10-23 08:28:10] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -65.498261 | E_var:     0.3612 | E_err:   0.009391
[2025-10-23 08:28:24] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -65.501224 | E_var:     0.3892 | E_err:   0.009748
[2025-10-23 08:28:37] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -65.504840 | E_var:     0.5264 | E_err:   0.011337
[2025-10-23 08:28:50] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -65.489249 | E_var:     0.4072 | E_err:   0.009971
[2025-10-23 08:29:03] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -65.504481 | E_var:     0.3985 | E_err:   0.009863
[2025-10-23 08:29:16] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -65.511865 | E_var:     0.3614 | E_err:   0.009393
[2025-10-23 08:29:30] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -65.506650 | E_var:     0.4028 | E_err:   0.009917
[2025-10-23 08:29:43] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -65.509278 | E_var:     0.4395 | E_err:   0.010358
[2025-10-23 08:29:56] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -65.510387 | E_var:     0.4064 | E_err:   0.009960
[2025-10-23 08:30:09] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -65.511652 | E_var:     0.4506 | E_err:   0.010489
[2025-10-23 08:30:22] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -65.529677 | E_var:     0.3804 | E_err:   0.009637
[2025-10-23 08:30:36] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -65.511418 | E_var:     0.3480 | E_err:   0.009217
[2025-10-23 08:30:49] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -65.513664 | E_var:     0.3540 | E_err:   0.009297
[2025-10-23 08:31:02] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -65.509517 | E_var:     0.3566 | E_err:   0.009330
[2025-10-23 08:31:15] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -65.506406 | E_var:     0.3681 | E_err:   0.009480
[2025-10-23 08:31:28] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -65.486324 | E_var:     0.5123 | E_err:   0.011184
[2025-10-23 08:31:42] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -65.514353 | E_var:     0.4992 | E_err:   0.011039
[2025-10-23 08:31:55] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -65.526911 | E_var:     0.4522 | E_err:   0.010508
[2025-10-23 08:32:08] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -65.513851 | E_var:     0.3836 | E_err:   0.009677
[2025-10-23 08:32:21] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -65.498409 | E_var:     0.3585 | E_err:   0.009355
[2025-10-23 08:32:35] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -65.504487 | E_var:     0.4003 | E_err:   0.009886
[2025-10-23 08:32:48] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -65.503822 | E_var:     0.5469 | E_err:   0.011555
[2025-10-23 08:33:01] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -65.508638 | E_var:     0.4185 | E_err:   0.010108
[2025-10-23 08:33:14] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -65.511658 | E_var:     0.3743 | E_err:   0.009560
[2025-10-23 08:33:27] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -65.512115 | E_var:     0.3400 | E_err:   0.009111
[2025-10-23 08:33:41] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -65.509945 | E_var:     0.3604 | E_err:   0.009380
[2025-10-23 08:33:54] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -65.503893 | E_var:     0.3908 | E_err:   0.009768
[2025-10-23 08:34:07] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -65.508162 | E_var:     0.4904 | E_err:   0.010942
[2025-10-23 08:34:20] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -65.532332 | E_var:     0.3877 | E_err:   0.009729
[2025-10-23 08:34:34] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -65.524988 | E_var:     0.4088 | E_err:   0.009990
[2025-10-23 08:34:47] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -65.496692 | E_var:     0.3837 | E_err:   0.009678
[2025-10-23 08:35:00] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -65.503229 | E_var:     0.4101 | E_err:   0.010006
[2025-10-23 08:35:13] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -65.509718 | E_var:     0.4846 | E_err:   0.010877
[2025-10-23 08:35:26] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -65.497812 | E_var:     0.3841 | E_err:   0.009684
[2025-10-23 08:35:40] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -65.521847 | E_var:     0.5821 | E_err:   0.011921
[2025-10-23 08:35:53] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -65.510938 | E_var:     0.3532 | E_err:   0.009286
[2025-10-23 08:36:06] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -65.512804 | E_var:     0.4012 | E_err:   0.009897
[2025-10-23 08:36:19] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -65.501807 | E_var:     0.3562 | E_err:   0.009326
[2025-10-23 08:36:32] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -65.502703 | E_var:     0.3992 | E_err:   0.009872
[2025-10-23 08:36:46] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -65.477292 | E_var:     0.4143 | E_err:   0.010057
[2025-10-23 08:36:59] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -65.510172 | E_var:     0.4517 | E_err:   0.010502
[2025-10-23 08:37:12] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -65.489949 | E_var:     0.3596 | E_err:   0.009370
[2025-10-23 08:37:25] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -65.509644 | E_var:     0.4833 | E_err:   0.010862
[2025-10-23 08:37:39] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -65.517832 | E_var:     0.4003 | E_err:   0.009886
[2025-10-23 08:37:52] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -65.500224 | E_var:     0.5338 | E_err:   0.011416
[2025-10-23 08:38:05] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -65.506903 | E_var:     0.4464 | E_err:   0.010440
[2025-10-23 08:38:18] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -65.509300 | E_var:     0.4444 | E_err:   0.010416
[2025-10-23 08:38:31] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -65.510049 | E_var:     0.4192 | E_err:   0.010117
[2025-10-23 08:38:45] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -65.512311 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 08:38:58] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -65.504843 | E_var:     0.3875 | E_err:   0.009727
[2025-10-23 08:39:11] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -65.514827 | E_var:     0.3993 | E_err:   0.009873
[2025-10-23 08:39:24] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -65.512533 | E_var:     0.3638 | E_err:   0.009425
[2025-10-23 08:39:37] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -65.506776 | E_var:     0.4206 | E_err:   0.010134
[2025-10-23 08:39:51] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -65.518658 | E_var:     0.4347 | E_err:   0.010302
[2025-10-23 08:40:04] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -65.505838 | E_var:     0.4758 | E_err:   0.010777
[2025-10-23 08:40:17] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -65.514565 | E_var:     0.4727 | E_err:   0.010743
[2025-10-23 08:40:30] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -65.515426 | E_var:     0.4784 | E_err:   0.010808
[2025-10-23 08:40:44] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -65.505359 | E_var:     0.4017 | E_err:   0.009903
[2025-10-23 08:40:57] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -65.516751 | E_var:     0.3310 | E_err:   0.008989
[2025-10-23 08:41:10] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -65.503129 | E_var:     0.5954 | E_err:   0.012057
[2025-10-23 08:41:23] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -65.485024 | E_var:     0.4431 | E_err:   0.010401
[2025-10-23 08:41:36] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -65.504141 | E_var:     0.4593 | E_err:   0.010589
[2025-10-23 08:41:50] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -65.506785 | E_var:     0.3479 | E_err:   0.009216
[2025-10-23 08:42:03] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -65.525705 | E_var:     0.4058 | E_err:   0.009954
[2025-10-23 08:42:16] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -65.497754 | E_var:     0.4122 | E_err:   0.010032
[2025-10-23 08:42:29] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -65.524063 | E_var:     0.4449 | E_err:   0.010422
[2025-10-23 08:42:42] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -65.512343 | E_var:     0.5475 | E_err:   0.011562
[2025-10-23 08:42:56] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -65.500162 | E_var:     0.4340 | E_err:   0.010293
[2025-10-23 08:43:09] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -65.505290 | E_var:     0.4385 | E_err:   0.010347
[2025-10-23 08:43:22] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -65.504963 | E_var:     0.3839 | E_err:   0.009681
[2025-10-23 08:43:35] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -65.512137 | E_var:     0.4795 | E_err:   0.010820
[2025-10-23 08:43:49] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -65.498811 | E_var:     0.3785 | E_err:   0.009613
[2025-10-23 08:44:02] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -65.506951 | E_var:     0.3762 | E_err:   0.009584
[2025-10-23 08:44:15] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -65.506105 | E_var:     0.4263 | E_err:   0.010202
[2025-10-23 08:44:28] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -65.502333 | E_var:     0.5009 | E_err:   0.011058
[2025-10-23 08:44:41] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -65.504457 | E_var:     0.3867 | E_err:   0.009716
[2025-10-23 08:44:55] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -65.521591 | E_var:     0.3809 | E_err:   0.009643
[2025-10-23 08:45:08] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -65.503882 | E_var:     0.3441 | E_err:   0.009166
[2025-10-23 08:45:21] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -65.504801 | E_var:     0.3592 | E_err:   0.009365
[2025-10-23 08:45:34] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -65.523369 | E_var:     0.3541 | E_err:   0.009298
[2025-10-23 08:45:47] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -65.521052 | E_var:     0.3739 | E_err:   0.009554
[2025-10-23 08:46:01] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -65.501130 | E_var:     0.4334 | E_err:   0.010286
[2025-10-23 08:46:14] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -65.483329 | E_var:     0.4142 | E_err:   0.010056
[2025-10-23 08:46:27] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -65.496194 | E_var:     0.3695 | E_err:   0.009498
[2025-10-23 08:46:40] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -65.506102 | E_var:     0.4460 | E_err:   0.010434
[2025-10-23 08:46:54] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -65.507439 | E_var:     0.3835 | E_err:   0.009676
[2025-10-23 08:46:54] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-23 08:46:54] ======================================================================================================
[2025-10-23 08:46:54] ✅ Training completed successfully
[2025-10-23 08:46:54] Total restarts: 2
[2025-10-23 08:46:58] Final Energy: -65.50743891 ± 0.00967584
[2025-10-23 08:46:58] Final Variance: 0.383475
[2025-10-23 08:46:58] ======================================================================================================
[2025-10-23 08:46:58] ======================================================================================================
[2025-10-23 08:46:58] Training completed | Runtime: 13946.2s
