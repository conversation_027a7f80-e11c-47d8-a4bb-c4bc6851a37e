[2025-10-23 01:00:47] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.79/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-23 01:00:47]   - 迭代次数: 1050
[2025-10-23 01:00:47]   - 能量: -63.660900+0.000285j ± 0.010408, Var: 0.443735
[2025-10-23 01:00:47]   - 时间戳: 2025-10-23T01:00:21.208890+08:00
[2025-10-23 01:01:12] ✓ 变分状态参数已从checkpoint恢复
[2025-10-23 01:01:12] ======================================================================================================
[2025-10-23 01:01:12] GCNN for Shastry-Sutherland Model
[2025-10-23 01:01:12] ======================================================================================================
[2025-10-23 01:01:12] System parameters:
[2025-10-23 01:01:12]   - System size: L=6, N=144
[2025-10-23 01:01:12]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-10-23 01:01:12] ------------------------------------------------------------------------------------------------------
[2025-10-23 01:01:12] Model parameters:
[2025-10-23 01:01:12]   - Number of layers = 4
[2025-10-23 01:01:12]   - Number of features = 4
[2025-10-23 01:01:12]   - Total parameters = 28252
[2025-10-23 01:01:12] ------------------------------------------------------------------------------------------------------
[2025-10-23 01:01:12] Training parameters:
[2025-10-23 01:01:12]   - Total iterations: 1050
[2025-10-23 01:01:12]   - Annealing cycles: 3
[2025-10-23 01:01:12]   - Initial period: 150
[2025-10-23 01:01:12]   - Period multiplier: 2.0
[2025-10-23 01:01:12]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-23 01:01:12]   - Samples: 4096
[2025-10-23 01:01:12]   - Discarded samples: 0
[2025-10-23 01:01:12]   - Chunk size: 4096
[2025-10-23 01:01:12]   - Diagonal shift: 0.15
[2025-10-23 01:01:12]   - Gradient clipping: 1.0
[2025-10-23 01:01:12]   - Checkpoint enabled: interval=105
[2025-10-23 01:01:12]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.80/training/checkpoints
[2025-10-23 01:01:12]   - Resuming from iteration: 1050
[2025-10-23 01:01:12] ------------------------------------------------------------------------------------------------------
[2025-10-23 01:01:12] Device status:
[2025-10-23 01:01:12]   - Devices model: NVIDIA H200 NVL
[2025-10-23 01:01:12]   - Number of devices: 1
[2025-10-23 01:01:12]   - Sharding: True
[2025-10-23 01:01:13] ======================================================================================================
[2025-10-23 01:02:04] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -64.581929 | E_var:     1.8515 | E_err:   0.021261
[2025-10-23 01:02:37] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -64.568346 | E_var:     0.8670 | E_err:   0.014549
[2025-10-23 01:02:51] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -64.587623 | E_var:     0.7261 | E_err:   0.013314
[2025-10-23 01:03:04] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -64.563418 | E_var:     0.5523 | E_err:   0.011612
[2025-10-23 01:03:17] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -64.573648 | E_var:     0.6256 | E_err:   0.012359
[2025-10-23 01:03:30] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -64.592526 | E_var:     0.4601 | E_err:   0.010599
[2025-10-23 01:03:43] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -64.573767 | E_var:     0.4903 | E_err:   0.010941
[2025-10-23 01:03:57] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -64.579678 | E_var:     0.5220 | E_err:   0.011289
[2025-10-23 01:04:10] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -64.576593 | E_var:     0.4604 | E_err:   0.010602
[2025-10-23 01:04:23] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -64.575573 | E_var:     0.5582 | E_err:   0.011674
[2025-10-23 01:04:36] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -64.569695 | E_var:     0.4986 | E_err:   0.011033
[2025-10-23 01:04:50] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -64.579315 | E_var:     0.4430 | E_err:   0.010400
[2025-10-23 01:05:03] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -64.580877 | E_var:     0.5085 | E_err:   0.011143
[2025-10-23 01:05:16] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -64.581923 | E_var:     0.4557 | E_err:   0.010548
[2025-10-23 01:05:29] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -64.580812 | E_var:     0.4625 | E_err:   0.010626
[2025-10-23 01:05:43] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -64.581335 | E_var:     0.4496 | E_err:   0.010477
[2025-10-23 01:05:56] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -64.587451 | E_var:     0.4834 | E_err:   0.010863
[2025-10-23 01:06:09] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -64.578667 | E_var:     0.4463 | E_err:   0.010438
[2025-10-23 01:06:22] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -64.589361 | E_var:     0.4291 | E_err:   0.010235
[2025-10-23 01:06:35] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -64.582956 | E_var:     0.4563 | E_err:   0.010555
[2025-10-23 01:06:49] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -64.587313 | E_var:     0.4578 | E_err:   0.010571
[2025-10-23 01:07:02] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -64.587543 | E_var:     0.4722 | E_err:   0.010737
[2025-10-23 01:07:15] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -64.570710 | E_var:     0.4888 | E_err:   0.010924
[2025-10-23 01:07:28] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -64.590031 | E_var:     0.4395 | E_err:   0.010359
[2025-10-23 01:07:42] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -64.585986 | E_var:     0.4501 | E_err:   0.010483
[2025-10-23 01:07:55] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -64.571469 | E_var:     0.4702 | E_err:   0.010714
[2025-10-23 01:08:08] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -64.577212 | E_var:     0.4173 | E_err:   0.010093
[2025-10-23 01:08:21] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -64.573987 | E_var:     0.6748 | E_err:   0.012836
[2025-10-23 01:08:35] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -64.576402 | E_var:     0.5493 | E_err:   0.011580
[2025-10-23 01:08:48] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -64.582903 | E_var:     0.4497 | E_err:   0.010478
[2025-10-23 01:09:01] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -64.570017 | E_var:     0.4452 | E_err:   0.010425
[2025-10-23 01:09:14] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -64.571208 | E_var:     0.3723 | E_err:   0.009534
[2025-10-23 01:09:28] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -64.598225 | E_var:     0.4120 | E_err:   0.010029
[2025-10-23 01:09:41] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -64.577642 | E_var:     0.5318 | E_err:   0.011395
[2025-10-23 01:09:54] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -64.584229 | E_var:     0.4647 | E_err:   0.010651
[2025-10-23 01:10:07] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -64.576619 | E_var:     0.4776 | E_err:   0.010798
[2025-10-23 01:10:20] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -64.581740 | E_var:     0.5526 | E_err:   0.011615
[2025-10-23 01:10:34] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -64.581650 | E_var:     0.4828 | E_err:   0.010856
[2025-10-23 01:10:47] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -64.584673 | E_var:     0.5599 | E_err:   0.011692
[2025-10-23 01:11:00] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -64.583332 | E_var:     0.4985 | E_err:   0.011032
[2025-10-23 01:11:13] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -64.594772 | E_var:     0.4592 | E_err:   0.010589
[2025-10-23 01:11:27] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -64.587094 | E_var:     0.4132 | E_err:   0.010044
[2025-10-23 01:11:40] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -64.575504 | E_var:     0.4527 | E_err:   0.010513
[2025-10-23 01:11:53] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -64.585962 | E_var:     0.4683 | E_err:   0.010693
[2025-10-23 01:12:06] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -64.589923 | E_var:     0.5131 | E_err:   0.011193
[2025-10-23 01:12:20] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -64.566217 | E_var:     0.4682 | E_err:   0.010692
[2025-10-23 01:12:33] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -64.587022 | E_var:     0.4173 | E_err:   0.010094
[2025-10-23 01:12:46] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -64.582189 | E_var:     0.4853 | E_err:   0.010885
[2025-10-23 01:13:00] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -64.585002 | E_var:     0.4716 | E_err:   0.010730
[2025-10-23 01:13:13] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -64.561684 | E_var:     0.5337 | E_err:   0.011414
[2025-10-23 01:13:26] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -64.579201 | E_var:     0.4450 | E_err:   0.010424
[2025-10-23 01:13:39] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -64.600485 | E_var:     0.4351 | E_err:   0.010306
[2025-10-23 01:13:53] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -64.577246 | E_var:     0.4883 | E_err:   0.010919
[2025-10-23 01:14:06] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -64.563335 | E_var:     0.4504 | E_err:   0.010486
[2025-10-23 01:14:19] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -64.579845 | E_var:     0.4115 | E_err:   0.010023
[2025-10-23 01:14:32] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -64.571013 | E_var:     0.4189 | E_err:   0.010113
[2025-10-23 01:14:46] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -64.581837 | E_var:     0.4151 | E_err:   0.010067
[2025-10-23 01:14:59] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -64.566504 | E_var:     0.5748 | E_err:   0.011846
[2025-10-23 01:15:12] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -64.587426 | E_var:     0.8795 | E_err:   0.014653
[2025-10-23 01:15:25] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -64.581264 | E_var:     0.4631 | E_err:   0.010633
[2025-10-23 01:15:38] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -64.582693 | E_var:     0.4800 | E_err:   0.010825
[2025-10-23 01:15:52] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -64.592819 | E_var:     0.4037 | E_err:   0.009928
[2025-10-23 01:16:05] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -64.579194 | E_var:     0.4640 | E_err:   0.010643
[2025-10-23 01:16:18] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -64.582354 | E_var:     0.7465 | E_err:   0.013500
[2025-10-23 01:16:31] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -64.581879 | E_var:     0.4756 | E_err:   0.010776
[2025-10-23 01:16:45] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -64.596268 | E_var:     0.5296 | E_err:   0.011371
[2025-10-23 01:16:58] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -64.572012 | E_var:     0.5315 | E_err:   0.011391
[2025-10-23 01:17:11] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -64.582239 | E_var:     0.4719 | E_err:   0.010734
[2025-10-23 01:17:24] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -64.572314 | E_var:     0.6278 | E_err:   0.012380
[2025-10-23 01:17:38] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -64.575883 | E_var:     0.5109 | E_err:   0.011169
[2025-10-23 01:17:51] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -64.576722 | E_var:     0.5317 | E_err:   0.011394
[2025-10-23 01:18:04] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -64.594602 | E_var:     0.4560 | E_err:   0.010551
[2025-10-23 01:18:17] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -64.584246 | E_var:     0.4595 | E_err:   0.010592
[2025-10-23 01:18:30] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -64.581989 | E_var:     0.4360 | E_err:   0.010317
[2025-10-23 01:18:44] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -64.600208 | E_var:     0.4807 | E_err:   0.010833
[2025-10-23 01:18:57] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -64.583502 | E_var:     0.3825 | E_err:   0.009663
[2025-10-23 01:19:10] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -64.582476 | E_var:     0.4686 | E_err:   0.010697
[2025-10-23 01:19:23] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -64.561999 | E_var:     0.5027 | E_err:   0.011078
[2025-10-23 01:19:37] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -64.571013 | E_var:     0.5602 | E_err:   0.011694
[2025-10-23 01:19:50] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -64.569001 | E_var:     0.5016 | E_err:   0.011066
[2025-10-23 01:20:03] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -64.593434 | E_var:     0.5047 | E_err:   0.011100
[2025-10-23 01:20:16] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -64.584506 | E_var:     0.4455 | E_err:   0.010429
[2025-10-23 01:20:30] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -64.578096 | E_var:     0.5981 | E_err:   0.012084
[2025-10-23 01:20:43] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -64.579206 | E_var:     0.4637 | E_err:   0.010639
[2025-10-23 01:20:56] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -64.576624 | E_var:     0.4677 | E_err:   0.010685
[2025-10-23 01:21:09] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -64.578194 | E_var:     0.4840 | E_err:   0.010870
[2025-10-23 01:21:22] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -64.579933 | E_var:     0.4253 | E_err:   0.010190
[2025-10-23 01:21:36] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -64.571461 | E_var:     0.4082 | E_err:   0.009983
[2025-10-23 01:21:49] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -64.576707 | E_var:     0.5175 | E_err:   0.011240
[2025-10-23 01:22:02] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -64.582450 | E_var:     0.3984 | E_err:   0.009862
[2025-10-23 01:22:15] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -64.576802 | E_var:     0.5554 | E_err:   0.011644
[2025-10-23 01:22:29] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -64.584774 | E_var:     0.5448 | E_err:   0.011533
[2025-10-23 01:22:42] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -64.595812 | E_var:     0.5168 | E_err:   0.011232
[2025-10-23 01:22:55] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -64.575187 | E_var:     0.4367 | E_err:   0.010325
[2025-10-23 01:23:08] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -64.564931 | E_var:     0.4933 | E_err:   0.010974
[2025-10-23 01:23:22] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -64.578607 | E_var:     0.3736 | E_err:   0.009551
[2025-10-23 01:23:35] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -64.587845 | E_var:     0.5013 | E_err:   0.011063
[2025-10-23 01:23:48] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -64.573050 | E_var:     0.4424 | E_err:   0.010393
[2025-10-23 01:24:01] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -64.571594 | E_var:     0.5172 | E_err:   0.011237
[2025-10-23 01:24:14] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -64.593807 | E_var:     0.4440 | E_err:   0.010411
[2025-10-23 01:24:28] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -64.594435 | E_var:     0.3995 | E_err:   0.009876
[2025-10-23 01:24:41] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -64.565574 | E_var:     0.4692 | E_err:   0.010703
[2025-10-23 01:24:54] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -64.580764 | E_var:     0.4911 | E_err:   0.010950
[2025-10-23 01:25:07] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -64.585535 | E_var:     0.6407 | E_err:   0.012507
[2025-10-23 01:25:21] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -64.568557 | E_var:     0.4619 | E_err:   0.010619
[2025-10-23 01:25:21] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-23 01:25:34] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -64.572531 | E_var:     0.4229 | E_err:   0.010161
[2025-10-23 01:25:47] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -64.567658 | E_var:     0.4506 | E_err:   0.010489
[2025-10-23 01:26:00] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -64.588845 | E_var:     0.4644 | E_err:   0.010648
[2025-10-23 01:26:14] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -64.572524 | E_var:     0.4532 | E_err:   0.010519
[2025-10-23 01:26:27] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -64.585322 | E_var:     0.4469 | E_err:   0.010445
[2025-10-23 01:26:40] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -64.582432 | E_var:     0.6371 | E_err:   0.012472
[2025-10-23 01:26:53] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -64.575584 | E_var:     0.4859 | E_err:   0.010891
[2025-10-23 01:27:07] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -64.578468 | E_var:     0.4363 | E_err:   0.010321
[2025-10-23 01:27:20] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -64.576074 | E_var:     0.4781 | E_err:   0.010804
[2025-10-23 01:27:33] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -64.582483 | E_var:     0.4288 | E_err:   0.010232
[2025-10-23 01:27:46] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -64.598815 | E_var:     0.4553 | E_err:   0.010543
[2025-10-23 01:28:00] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -64.584666 | E_var:     0.5369 | E_err:   0.011449
[2025-10-23 01:28:13] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -64.579299 | E_var:     0.4583 | E_err:   0.010577
[2025-10-23 01:28:26] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -64.601145 | E_var:     0.4786 | E_err:   0.010809
[2025-10-23 01:28:39] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -64.586546 | E_var:     0.3961 | E_err:   0.009834
[2025-10-23 01:28:52] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -64.589968 | E_var:     0.5182 | E_err:   0.011248
[2025-10-23 01:29:06] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -64.574391 | E_var:     0.5023 | E_err:   0.011074
[2025-10-23 01:29:19] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -64.581546 | E_var:     0.3937 | E_err:   0.009804
[2025-10-23 01:29:32] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -64.572368 | E_var:     0.6588 | E_err:   0.012682
[2025-10-23 01:29:45] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -64.576752 | E_var:     0.5030 | E_err:   0.011082
[2025-10-23 01:29:59] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -64.577929 | E_var:     0.4916 | E_err:   0.010955
[2025-10-23 01:30:12] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -64.591848 | E_var:     0.4293 | E_err:   0.010238
[2025-10-23 01:30:25] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -64.587608 | E_var:     0.4168 | E_err:   0.010087
[2025-10-23 01:30:38] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -64.580271 | E_var:     0.4740 | E_err:   0.010758
[2025-10-23 01:30:52] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -64.573043 | E_var:     0.3976 | E_err:   0.009852
[2025-10-23 01:31:05] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -64.550563 | E_var:     0.4672 | E_err:   0.010680
[2025-10-23 01:31:18] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -64.583755 | E_var:     0.5668 | E_err:   0.011764
[2025-10-23 01:31:31] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -64.583701 | E_var:     0.4619 | E_err:   0.010620
[2025-10-23 01:31:44] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -64.576879 | E_var:     0.5448 | E_err:   0.011533
[2025-10-23 01:31:58] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -64.575933 | E_var:     0.4448 | E_err:   0.010420
[2025-10-23 01:32:11] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -64.571989 | E_var:     0.4533 | E_err:   0.010520
[2025-10-23 01:32:25] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -64.587554 | E_var:     0.4238 | E_err:   0.010171
[2025-10-23 01:32:38] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -64.576348 | E_var:     0.4763 | E_err:   0.010783
[2025-10-23 01:32:51] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -64.571609 | E_var:     0.4164 | E_err:   0.010083
[2025-10-23 01:33:04] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -64.574684 | E_var:     0.4033 | E_err:   0.009923
[2025-10-23 01:33:17] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -64.583147 | E_var:     0.4430 | E_err:   0.010399
[2025-10-23 01:33:31] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -64.570337 | E_var:     0.4900 | E_err:   0.010938
[2025-10-23 01:33:44] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -64.559234 | E_var:     0.5369 | E_err:   0.011449
[2025-10-23 01:33:57] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -64.592499 | E_var:     0.5539 | E_err:   0.011629
[2025-10-23 01:34:10] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -64.574257 | E_var:     0.5044 | E_err:   0.011097
[2025-10-23 01:34:24] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -64.601263 | E_var:     0.4127 | E_err:   0.010038
[2025-10-23 01:34:37] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -64.562164 | E_var:     0.4246 | E_err:   0.010182
[2025-10-23 01:34:50] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -64.596896 | E_var:     0.3754 | E_err:   0.009573
[2025-10-23 01:35:03] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -64.577372 | E_var:     0.4847 | E_err:   0.010878
[2025-10-23 01:35:17] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -64.570658 | E_var:     0.5546 | E_err:   0.011636
[2025-10-23 01:35:17] 🔄 RESTART #1 | Period: 300
[2025-10-23 01:35:30] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -64.575524 | E_var:     0.4257 | E_err:   0.010194
[2025-10-23 01:35:43] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -64.582101 | E_var:     0.5176 | E_err:   0.011241
[2025-10-23 01:35:56] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -64.590239 | E_var:     0.6317 | E_err:   0.012419
[2025-10-23 01:36:10] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -64.558644 | E_var:     0.4172 | E_err:   0.010093
[2025-10-23 01:36:23] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -64.576358 | E_var:     0.4003 | E_err:   0.009886
[2025-10-23 01:36:36] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -64.564049 | E_var:     0.5212 | E_err:   0.011280
[2025-10-23 01:36:49] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -64.584156 | E_var:     0.3478 | E_err:   0.009214
[2025-10-23 01:37:02] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -64.569619 | E_var:     0.5244 | E_err:   0.011315
[2025-10-23 01:37:16] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -64.590975 | E_var:     0.4214 | E_err:   0.010143
[2025-10-23 01:37:29] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -64.588515 | E_var:     0.4508 | E_err:   0.010491
[2025-10-23 01:37:42] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -64.582321 | E_var:     0.4458 | E_err:   0.010433
[2025-10-23 01:37:55] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -64.567634 | E_var:     0.4955 | E_err:   0.010999
[2025-10-23 01:38:09] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -64.577367 | E_var:     0.5058 | E_err:   0.011112
[2025-10-23 01:38:22] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -64.586247 | E_var:     0.3960 | E_err:   0.009833
[2025-10-23 01:38:35] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -64.575544 | E_var:     0.3866 | E_err:   0.009715
[2025-10-23 01:38:48] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -64.588890 | E_var:     0.3771 | E_err:   0.009595
[2025-10-23 01:39:01] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -64.601479 | E_var:     0.4056 | E_err:   0.009950
[2025-10-23 01:39:15] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -64.566073 | E_var:     0.6304 | E_err:   0.012405
[2025-10-23 01:39:28] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -64.557462 | E_var:     0.5298 | E_err:   0.011374
[2025-10-23 01:39:41] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -64.588147 | E_var:     0.5330 | E_err:   0.011408
[2025-10-23 01:39:54] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -64.579202 | E_var:     0.5475 | E_err:   0.011562
[2025-10-23 01:40:08] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -64.580962 | E_var:     0.5143 | E_err:   0.011205
[2025-10-23 01:40:21] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -64.583253 | E_var:     0.3998 | E_err:   0.009880
[2025-10-23 01:40:34] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -64.573554 | E_var:     0.5353 | E_err:   0.011432
[2025-10-23 01:40:47] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -64.571677 | E_var:     0.4550 | E_err:   0.010540
[2025-10-23 01:41:01] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -64.584685 | E_var:     0.4149 | E_err:   0.010064
[2025-10-23 01:41:14] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -64.575182 | E_var:     0.5074 | E_err:   0.011130
[2025-10-23 01:41:27] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -64.576458 | E_var:     0.4173 | E_err:   0.010093
[2025-10-23 01:41:40] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -64.574049 | E_var:     0.5877 | E_err:   0.011978
[2025-10-23 01:41:53] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -64.576985 | E_var:     0.4932 | E_err:   0.010973
[2025-10-23 01:42:07] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -64.581054 | E_var:     0.4128 | E_err:   0.010039
[2025-10-23 01:42:20] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -64.596172 | E_var:     0.6044 | E_err:   0.012147
[2025-10-23 01:42:33] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -64.583373 | E_var:     0.4546 | E_err:   0.010535
[2025-10-23 01:42:46] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -64.597094 | E_var:     0.4155 | E_err:   0.010072
[2025-10-23 01:43:00] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -64.575732 | E_var:     0.4223 | E_err:   0.010154
[2025-10-23 01:43:13] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -64.570034 | E_var:     0.4709 | E_err:   0.010723
[2025-10-23 01:43:26] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -64.572599 | E_var:     0.4776 | E_err:   0.010799
[2025-10-23 01:43:39] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -64.576079 | E_var:     0.3942 | E_err:   0.009811
[2025-10-23 01:43:53] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -64.591824 | E_var:     0.4438 | E_err:   0.010409
[2025-10-23 01:44:06] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -64.604266 | E_var:     0.4563 | E_err:   0.010555
[2025-10-23 01:44:19] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -64.586383 | E_var:     0.4147 | E_err:   0.010063
[2025-10-23 01:44:32] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -64.584070 | E_var:     0.4572 | E_err:   0.010565
[2025-10-23 01:44:45] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -64.585135 | E_var:     0.4373 | E_err:   0.010332
[2025-10-23 01:44:59] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -64.610431 | E_var:     0.5627 | E_err:   0.011720
[2025-10-23 01:45:12] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -64.588612 | E_var:     0.5224 | E_err:   0.011293
[2025-10-23 01:45:25] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -64.580332 | E_var:     0.5035 | E_err:   0.011087
[2025-10-23 01:45:38] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -64.582952 | E_var:     0.4931 | E_err:   0.010972
[2025-10-23 01:45:52] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -64.585126 | E_var:     0.4133 | E_err:   0.010045
[2025-10-23 01:46:05] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -64.575619 | E_var:     0.4459 | E_err:   0.010434
[2025-10-23 01:46:18] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -64.577897 | E_var:     0.4995 | E_err:   0.011043
[2025-10-23 01:46:31] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -64.587595 | E_var:     0.4748 | E_err:   0.010767
[2025-10-23 01:46:45] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -64.574291 | E_var:     0.6134 | E_err:   0.012238
[2025-10-23 01:46:58] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -64.586546 | E_var:     0.4737 | E_err:   0.010754
[2025-10-23 01:47:11] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -64.554159 | E_var:     0.4131 | E_err:   0.010043
[2025-10-23 01:47:24] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -64.569645 | E_var:     0.4599 | E_err:   0.010597
[2025-10-23 01:47:37] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -64.593183 | E_var:     0.4775 | E_err:   0.010797
[2025-10-23 01:47:51] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -64.576002 | E_var:     0.4033 | E_err:   0.009923
[2025-10-23 01:48:04] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -64.583193 | E_var:     0.4209 | E_err:   0.010137
[2025-10-23 01:48:17] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -64.570770 | E_var:     0.4125 | E_err:   0.010036
[2025-10-23 01:48:30] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -64.560501 | E_var:     0.4865 | E_err:   0.010898
[2025-10-23 01:48:31] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-23 01:48:44] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -64.588298 | E_var:     0.4670 | E_err:   0.010678
[2025-10-23 01:48:57] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -64.570127 | E_var:     0.4922 | E_err:   0.010962
[2025-10-23 01:49:10] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -64.593325 | E_var:     0.4853 | E_err:   0.010885
[2025-10-23 01:49:23] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -64.577465 | E_var:     0.4473 | E_err:   0.010450
[2025-10-23 01:49:37] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -64.572849 | E_var:     0.4016 | E_err:   0.009901
[2025-10-23 01:49:50] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -64.566319 | E_var:     0.4751 | E_err:   0.010770
[2025-10-23 01:50:03] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -64.600098 | E_var:     0.4684 | E_err:   0.010694
[2025-10-23 01:50:16] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -64.602528 | E_var:     0.5255 | E_err:   0.011327
[2025-10-23 01:50:30] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -64.586588 | E_var:     0.5193 | E_err:   0.011259
[2025-10-23 01:50:43] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -64.589953 | E_var:     0.4234 | E_err:   0.010167
[2025-10-23 01:50:56] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -64.594608 | E_var:     0.4240 | E_err:   0.010174
[2025-10-23 01:51:09] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -64.587131 | E_var:     0.4178 | E_err:   0.010100
[2025-10-23 01:51:22] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -64.591063 | E_var:     0.4998 | E_err:   0.011047
[2025-10-23 01:51:36] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -64.589539 | E_var:     0.6009 | E_err:   0.012112
[2025-10-23 01:51:50] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -64.587913 | E_var:     0.4679 | E_err:   0.010688
[2025-10-23 01:52:03] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -64.577132 | E_var:     0.9038 | E_err:   0.014855
[2025-10-23 01:52:16] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -64.587946 | E_var:     0.4858 | E_err:   0.010891
[2025-10-23 01:52:29] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -64.578164 | E_var:     0.3873 | E_err:   0.009724
[2025-10-23 01:52:42] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -64.573686 | E_var:     0.9566 | E_err:   0.015282
[2025-10-23 01:52:56] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -64.597733 | E_var:     0.4107 | E_err:   0.010014
[2025-10-23 01:53:09] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -64.582754 | E_var:     0.4501 | E_err:   0.010483
[2025-10-23 01:53:22] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -64.578212 | E_var:     0.5109 | E_err:   0.011168
[2025-10-23 01:53:35] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -64.568689 | E_var:     0.5848 | E_err:   0.011949
[2025-10-23 01:53:49] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -64.598503 | E_var:     0.5098 | E_err:   0.011157
[2025-10-23 01:54:02] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -64.582696 | E_var:     0.4071 | E_err:   0.009970
[2025-10-23 01:54:15] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -64.569806 | E_var:     0.3797 | E_err:   0.009629
[2025-10-23 01:54:28] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -64.592485 | E_var:     0.3987 | E_err:   0.009866
[2025-10-23 01:54:42] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -64.559439 | E_var:     0.4341 | E_err:   0.010295
[2025-10-23 01:54:55] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -64.583870 | E_var:     0.4427 | E_err:   0.010397
[2025-10-23 01:55:08] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -64.580698 | E_var:     0.4018 | E_err:   0.009905
[2025-10-23 01:55:21] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -64.582392 | E_var:     0.4206 | E_err:   0.010133
[2025-10-23 01:55:34] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -64.576108 | E_var:     0.4273 | E_err:   0.010214
[2025-10-23 01:55:48] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -64.597555 | E_var:     0.7345 | E_err:   0.013391
[2025-10-23 01:56:01] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -64.597348 | E_var:     0.3832 | E_err:   0.009673
[2025-10-23 01:56:14] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -64.577651 | E_var:     0.4609 | E_err:   0.010608
[2025-10-23 01:56:27] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -64.586810 | E_var:     0.4626 | E_err:   0.010627
[2025-10-23 01:56:41] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -64.578923 | E_var:     0.4743 | E_err:   0.010761
[2025-10-23 01:56:54] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -64.577796 | E_var:     0.4403 | E_err:   0.010368
[2025-10-23 01:57:07] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -64.593403 | E_var:     0.4493 | E_err:   0.010474
[2025-10-23 01:57:20] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -64.594478 | E_var:     0.3999 | E_err:   0.009881
[2025-10-23 01:57:33] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -64.580975 | E_var:     0.3812 | E_err:   0.009647
[2025-10-23 01:57:47] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -64.587448 | E_var:     0.4606 | E_err:   0.010605
[2025-10-23 01:58:00] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -64.580561 | E_var:     0.4097 | E_err:   0.010002
[2025-10-23 01:58:13] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -64.584671 | E_var:     0.4185 | E_err:   0.010108
[2025-10-23 01:58:26] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -64.599228 | E_var:     0.4618 | E_err:   0.010618
[2025-10-23 01:58:40] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -64.567619 | E_var:     0.4117 | E_err:   0.010026
[2025-10-23 01:58:53] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -64.594632 | E_var:     0.4867 | E_err:   0.010901
[2025-10-23 01:59:06] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -64.573952 | E_var:     0.4568 | E_err:   0.010561
[2025-10-23 01:59:19] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -64.571589 | E_var:     0.4594 | E_err:   0.010591
[2025-10-23 01:59:32] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -64.579623 | E_var:     0.4868 | E_err:   0.010902
[2025-10-23 01:59:46] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -64.579642 | E_var:     0.4308 | E_err:   0.010256
[2025-10-23 01:59:59] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -64.578211 | E_var:     0.4361 | E_err:   0.010319
[2025-10-23 02:00:12] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -64.575701 | E_var:     0.4249 | E_err:   0.010185
[2025-10-23 02:00:25] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -64.568877 | E_var:     0.4654 | E_err:   0.010659
[2025-10-23 02:00:39] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -64.567510 | E_var:     0.4335 | E_err:   0.010288
[2025-10-23 02:00:52] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -64.579322 | E_var:     0.5467 | E_err:   0.011553
[2025-10-23 02:01:05] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -64.579769 | E_var:     0.4399 | E_err:   0.010363
[2025-10-23 02:01:18] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -64.588826 | E_var:     0.4604 | E_err:   0.010602
[2025-10-23 02:01:31] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -64.589720 | E_var:     0.6440 | E_err:   0.012539
[2025-10-23 02:01:45] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -64.576109 | E_var:     0.3961 | E_err:   0.009834
[2025-10-23 02:01:58] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -64.585258 | E_var:     0.4223 | E_err:   0.010154
[2025-10-23 02:02:11] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -64.590878 | E_var:     0.4051 | E_err:   0.009945
[2025-10-23 02:02:24] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -64.599804 | E_var:     0.5643 | E_err:   0.011737
[2025-10-23 02:02:38] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -64.561211 | E_var:     0.4584 | E_err:   0.010579
[2025-10-23 02:02:51] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -64.568266 | E_var:     0.4636 | E_err:   0.010639
[2025-10-23 02:03:04] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -64.582829 | E_var:     0.3620 | E_err:   0.009401
[2025-10-23 02:03:17] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -64.573072 | E_var:     0.4135 | E_err:   0.010047
[2025-10-23 02:03:31] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -64.573888 | E_var:     0.4560 | E_err:   0.010551
[2025-10-23 02:03:44] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -64.570547 | E_var:     0.4307 | E_err:   0.010254
[2025-10-23 02:03:57] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -64.566783 | E_var:     0.9987 | E_err:   0.015615
[2025-10-23 02:04:10] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -64.579777 | E_var:     0.6372 | E_err:   0.012472
[2025-10-23 02:04:23] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -64.574446 | E_var:     0.4306 | E_err:   0.010253
[2025-10-23 02:04:37] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -64.588759 | E_var:     0.3630 | E_err:   0.009414
[2025-10-23 02:04:50] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -64.561248 | E_var:     0.9400 | E_err:   0.015149
[2025-10-23 02:05:03] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -64.585798 | E_var:     0.4339 | E_err:   0.010292
[2025-10-23 02:05:16] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -64.584389 | E_var:     0.4921 | E_err:   0.010960
[2025-10-23 02:05:30] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -64.582443 | E_var:     0.4983 | E_err:   0.011030
[2025-10-23 02:05:43] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -64.579474 | E_var:     0.5103 | E_err:   0.011162
[2025-10-23 02:05:56] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -64.579249 | E_var:     0.5255 | E_err:   0.011326
[2025-10-23 02:06:09] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -64.593538 | E_var:     0.4788 | E_err:   0.010812
[2025-10-23 02:06:22] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -64.576123 | E_var:     0.4647 | E_err:   0.010651
[2025-10-23 02:06:36] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -64.575326 | E_var:     0.4875 | E_err:   0.010910
[2025-10-23 02:06:49] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -64.577596 | E_var:     0.4496 | E_err:   0.010477
[2025-10-23 02:07:02] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -64.575042 | E_var:     1.0089 | E_err:   0.015695
[2025-10-23 02:07:15] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -64.558762 | E_var:     0.5170 | E_err:   0.011235
[2025-10-23 02:07:29] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -64.583993 | E_var:     0.4382 | E_err:   0.010344
[2025-10-23 02:07:42] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -64.566240 | E_var:     0.4690 | E_err:   0.010701
[2025-10-23 02:07:55] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -64.579542 | E_var:     0.4629 | E_err:   0.010631
[2025-10-23 02:08:08] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -64.578604 | E_var:     0.4582 | E_err:   0.010576
[2025-10-23 02:08:21] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -64.585571 | E_var:     0.4148 | E_err:   0.010063
[2025-10-23 02:08:35] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -64.576178 | E_var:     0.5721 | E_err:   0.011819
[2025-10-23 02:08:48] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -64.587202 | E_var:     0.4821 | E_err:   0.010849
[2025-10-23 02:09:01] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -64.585213 | E_var:     0.5662 | E_err:   0.011757
[2025-10-23 02:09:14] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -64.578958 | E_var:     0.4347 | E_err:   0.010302
[2025-10-23 02:09:28] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -64.589544 | E_var:     0.4354 | E_err:   0.010310
[2025-10-23 02:09:41] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -64.585002 | E_var:     0.3770 | E_err:   0.009594
[2025-10-23 02:09:54] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -64.580632 | E_var:     0.3661 | E_err:   0.009454
[2025-10-23 02:10:07] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -64.575083 | E_var:     0.6932 | E_err:   0.013009
[2025-10-23 02:10:21] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -64.583276 | E_var:     0.5237 | E_err:   0.011307
[2025-10-23 02:10:34] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -64.578939 | E_var:     0.4311 | E_err:   0.010259
[2025-10-23 02:10:47] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -64.603726 | E_var:     0.4000 | E_err:   0.009882
[2025-10-23 02:11:00] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -64.570285 | E_var:     0.5201 | E_err:   0.011269
[2025-10-23 02:11:13] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -64.561962 | E_var:     0.4422 | E_err:   0.010390
[2025-10-23 02:11:27] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -64.586672 | E_var:     0.4651 | E_err:   0.010656
[2025-10-23 02:11:40] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -64.573481 | E_var:     0.4130 | E_err:   0.010041
[2025-10-23 02:11:40] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-23 02:11:53] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -64.589557 | E_var:     0.3964 | E_err:   0.009838
[2025-10-23 02:12:06] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -64.574147 | E_var:     0.4491 | E_err:   0.010471
[2025-10-23 02:12:20] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -64.575373 | E_var:     0.3787 | E_err:   0.009616
[2025-10-23 02:12:33] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -64.583820 | E_var:     0.4856 | E_err:   0.010888
[2025-10-23 02:12:46] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -64.568542 | E_var:     0.4105 | E_err:   0.010011
[2025-10-23 02:12:59] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -64.574249 | E_var:     0.6038 | E_err:   0.012141
[2025-10-23 02:13:13] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -64.596198 | E_var:     0.5287 | E_err:   0.011361
[2025-10-23 02:13:26] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -64.577916 | E_var:     0.3902 | E_err:   0.009760
[2025-10-23 02:13:39] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -64.592012 | E_var:     0.5570 | E_err:   0.011661
[2025-10-23 02:13:52] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -64.591291 | E_var:     0.4657 | E_err:   0.010663
[2025-10-23 02:14:05] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -64.585436 | E_var:     0.4557 | E_err:   0.010548
[2025-10-23 02:14:19] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -64.571967 | E_var:     0.5149 | E_err:   0.011212
[2025-10-23 02:14:32] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -64.575694 | E_var:     0.4951 | E_err:   0.010994
[2025-10-23 02:14:45] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -64.598917 | E_var:     0.4241 | E_err:   0.010176
[2025-10-23 02:14:58] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -64.571686 | E_var:     0.4619 | E_err:   0.010619
[2025-10-23 02:15:12] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -64.582119 | E_var:     0.5859 | E_err:   0.011960
[2025-10-23 02:15:25] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -64.564365 | E_var:     0.3896 | E_err:   0.009753
[2025-10-23 02:15:38] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -64.593496 | E_var:     0.4309 | E_err:   0.010257
[2025-10-23 02:15:51] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -64.580205 | E_var:     0.4644 | E_err:   0.010647
[2025-10-23 02:16:04] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -64.597793 | E_var:     0.3689 | E_err:   0.009490
[2025-10-23 02:16:18] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -64.568962 | E_var:     0.4662 | E_err:   0.010668
[2025-10-23 02:16:31] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -64.581808 | E_var:     0.9407 | E_err:   0.015154
[2025-10-23 02:16:44] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -64.588377 | E_var:     0.3659 | E_err:   0.009451
[2025-10-23 02:16:57] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -64.580810 | E_var:     0.4272 | E_err:   0.010213
[2025-10-23 02:17:11] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -64.574304 | E_var:     0.4600 | E_err:   0.010597
[2025-10-23 02:17:24] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -64.592833 | E_var:     0.4288 | E_err:   0.010232
[2025-10-23 02:17:37] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -64.585581 | E_var:     0.4075 | E_err:   0.009975
[2025-10-23 02:17:50] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -64.580813 | E_var:     0.4450 | E_err:   0.010423
[2025-10-23 02:18:03] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -64.589347 | E_var:     0.6274 | E_err:   0.012377
[2025-10-23 02:18:17] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -64.582146 | E_var:     0.4911 | E_err:   0.010950
[2025-10-23 02:18:30] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -64.578856 | E_var:     0.4252 | E_err:   0.010188
[2025-10-23 02:18:43] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -64.592821 | E_var:     0.3898 | E_err:   0.009756
[2025-10-23 02:18:56] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -64.573958 | E_var:     0.4266 | E_err:   0.010205
[2025-10-23 02:19:10] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -64.588627 | E_var:     0.5120 | E_err:   0.011180
[2025-10-23 02:19:23] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -64.561966 | E_var:     0.5175 | E_err:   0.011241
[2025-10-23 02:19:36] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -64.586244 | E_var:     0.4440 | E_err:   0.010411
[2025-10-23 02:19:49] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -64.579760 | E_var:     0.4148 | E_err:   0.010063
[2025-10-23 02:20:03] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -64.583107 | E_var:     0.4270 | E_err:   0.010210
[2025-10-23 02:20:16] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -64.578835 | E_var:     0.4023 | E_err:   0.009911
[2025-10-23 02:20:29] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -64.556278 | E_var:     0.4956 | E_err:   0.011000
[2025-10-23 02:20:42] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -64.586347 | E_var:     0.4502 | E_err:   0.010484
[2025-10-23 02:20:55] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -64.597690 | E_var:     0.5362 | E_err:   0.011442
[2025-10-23 02:21:09] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -64.574182 | E_var:     0.5186 | E_err:   0.011252
[2025-10-23 02:21:22] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -64.593763 | E_var:     0.4290 | E_err:   0.010234
[2025-10-23 02:21:35] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -64.584118 | E_var:     0.4107 | E_err:   0.010014
[2025-10-23 02:21:48] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -64.582911 | E_var:     0.4315 | E_err:   0.010264
[2025-10-23 02:22:02] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -64.584610 | E_var:     0.4536 | E_err:   0.010523
[2025-10-23 02:22:15] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -64.599177 | E_var:     1.9272 | E_err:   0.021691
[2025-10-23 02:22:28] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -64.583893 | E_var:     0.4463 | E_err:   0.010438
[2025-10-23 02:22:41] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -64.603621 | E_var:     0.4370 | E_err:   0.010329
[2025-10-23 02:22:54] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -64.588994 | E_var:     0.3579 | E_err:   0.009348
[2025-10-23 02:23:08] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -64.574569 | E_var:     0.5457 | E_err:   0.011542
[2025-10-23 02:23:21] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -64.585216 | E_var:     0.4151 | E_err:   0.010067
[2025-10-23 02:23:34] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -64.587763 | E_var:     0.3978 | E_err:   0.009855
[2025-10-23 02:23:47] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -64.575005 | E_var:     0.4996 | E_err:   0.011044
[2025-10-23 02:24:01] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -64.581481 | E_var:     0.4424 | E_err:   0.010392
[2025-10-23 02:24:14] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -64.587995 | E_var:     0.6118 | E_err:   0.012221
[2025-10-23 02:24:27] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -64.593972 | E_var:     0.4161 | E_err:   0.010079
[2025-10-23 02:24:40] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -64.568063 | E_var:     0.4821 | E_err:   0.010848
[2025-10-23 02:24:54] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -64.595192 | E_var:     0.4274 | E_err:   0.010215
[2025-10-23 02:25:07] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -64.587959 | E_var:     0.4440 | E_err:   0.010412
[2025-10-23 02:25:20] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -64.587984 | E_var:     0.4348 | E_err:   0.010303
[2025-10-23 02:25:33] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -64.570259 | E_var:     0.4046 | E_err:   0.009938
[2025-10-23 02:25:46] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -64.572913 | E_var:     0.4549 | E_err:   0.010539
[2025-10-23 02:26:00] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -64.610616 | E_var:     0.3925 | E_err:   0.009789
[2025-10-23 02:26:13] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -64.590679 | E_var:     0.3953 | E_err:   0.009823
[2025-10-23 02:26:26] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -64.577526 | E_var:     0.6302 | E_err:   0.012404
[2025-10-23 02:26:39] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -64.584587 | E_var:     0.4560 | E_err:   0.010551
[2025-10-23 02:26:53] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -64.576381 | E_var:     0.4250 | E_err:   0.010186
[2025-10-23 02:27:06] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -64.575645 | E_var:     0.4793 | E_err:   0.010817
[2025-10-23 02:27:19] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -64.590011 | E_var:     0.4484 | E_err:   0.010462
[2025-10-23 02:27:32] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -64.571773 | E_var:     0.4656 | E_err:   0.010662
[2025-10-23 02:27:45] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -64.576930 | E_var:     0.3821 | E_err:   0.009659
[2025-10-23 02:27:59] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -64.575166 | E_var:     0.4423 | E_err:   0.010392
[2025-10-23 02:28:12] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -64.583263 | E_var:     0.4342 | E_err:   0.010296
[2025-10-23 02:28:25] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -64.588137 | E_var:     0.4695 | E_err:   0.010706
[2025-10-23 02:28:38] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -64.585751 | E_var:     0.4750 | E_err:   0.010769
[2025-10-23 02:28:52] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -64.571462 | E_var:     0.4225 | E_err:   0.010156
[2025-10-23 02:29:05] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -64.585175 | E_var:     0.5468 | E_err:   0.011554
[2025-10-23 02:29:18] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -64.577490 | E_var:     0.3940 | E_err:   0.009807
[2025-10-23 02:29:31] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -64.569729 | E_var:     0.4661 | E_err:   0.010667
[2025-10-23 02:29:44] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -64.566156 | E_var:     0.5308 | E_err:   0.011384
[2025-10-23 02:29:58] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -64.572049 | E_var:     0.5388 | E_err:   0.011470
[2025-10-23 02:30:11] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -64.597444 | E_var:     0.4678 | E_err:   0.010687
[2025-10-23 02:30:24] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -64.564050 | E_var:     0.4574 | E_err:   0.010568
[2025-10-23 02:30:37] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -64.557692 | E_var:     0.5012 | E_err:   0.011061
[2025-10-23 02:30:51] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -64.564531 | E_var:     0.4741 | E_err:   0.010759
[2025-10-23 02:31:04] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -64.577671 | E_var:     0.3854 | E_err:   0.009701
[2025-10-23 02:31:17] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -64.575538 | E_var:     0.5306 | E_err:   0.011382
[2025-10-23 02:31:30] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -64.587876 | E_var:     0.4568 | E_err:   0.010561
[2025-10-23 02:31:43] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -64.586865 | E_var:     0.4375 | E_err:   0.010335
[2025-10-23 02:31:57] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -64.588847 | E_var:     0.3747 | E_err:   0.009564
[2025-10-23 02:32:10] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -64.582032 | E_var:     0.5256 | E_err:   0.011328
[2025-10-23 02:32:23] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -64.570140 | E_var:     0.4548 | E_err:   0.010537
[2025-10-23 02:32:36] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -64.580139 | E_var:     0.5936 | E_err:   0.012038
[2025-10-23 02:32:50] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -64.547761 | E_var:     0.5583 | E_err:   0.011674
[2025-10-23 02:33:03] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -64.592759 | E_var:     0.3933 | E_err:   0.009799
[2025-10-23 02:33:16] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -64.581295 | E_var:     0.5024 | E_err:   0.011074
[2025-10-23 02:33:29] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -64.587549 | E_var:     0.4402 | E_err:   0.010367
[2025-10-23 02:33:42] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -64.591686 | E_var:     0.3830 | E_err:   0.009670
[2025-10-23 02:33:56] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -64.585540 | E_var:     0.4307 | E_err:   0.010254
[2025-10-23 02:34:09] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -64.581936 | E_var:     0.3814 | E_err:   0.009649
[2025-10-23 02:34:22] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -64.587984 | E_var:     0.4089 | E_err:   0.009992
[2025-10-23 02:34:35] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -64.587604 | E_var:     0.4822 | E_err:   0.010850
[2025-10-23 02:34:49] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -64.597585 | E_var:     0.4365 | E_err:   0.010323
[2025-10-23 02:34:49] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-23 02:35:02] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -64.571768 | E_var:     0.4204 | E_err:   0.010131
[2025-10-23 02:35:15] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -64.597136 | E_var:     0.4146 | E_err:   0.010061
[2025-10-23 02:35:28] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -64.580519 | E_var:     0.4984 | E_err:   0.011031
[2025-10-23 02:35:42] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -64.580261 | E_var:     0.3959 | E_err:   0.009832
[2025-10-23 02:35:55] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -64.574245 | E_var:     0.4289 | E_err:   0.010232
[2025-10-23 02:36:08] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -64.602974 | E_var:     0.4645 | E_err:   0.010649
[2025-10-23 02:36:21] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -64.582759 | E_var:     0.4358 | E_err:   0.010315
[2025-10-23 02:36:34] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -64.576073 | E_var:     0.5559 | E_err:   0.011649
[2025-10-23 02:36:48] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -64.575777 | E_var:     0.4243 | E_err:   0.010178
[2025-10-23 02:37:01] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -64.574822 | E_var:     0.4382 | E_err:   0.010343
[2025-10-23 02:37:14] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -64.572143 | E_var:     0.7527 | E_err:   0.013556
[2025-10-23 02:37:27] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -64.587242 | E_var:     0.4121 | E_err:   0.010031
[2025-10-23 02:37:41] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -64.578675 | E_var:     0.5075 | E_err:   0.011132
[2025-10-23 02:37:54] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -64.569039 | E_var:     0.4477 | E_err:   0.010455
[2025-10-23 02:38:07] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -64.585151 | E_var:     0.5395 | E_err:   0.011477
[2025-10-23 02:38:20] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -64.594398 | E_var:     0.4661 | E_err:   0.010668
[2025-10-23 02:38:33] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -64.582967 | E_var:     0.4726 | E_err:   0.010742
[2025-10-23 02:38:47] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -64.574865 | E_var:     0.6663 | E_err:   0.012755
[2025-10-23 02:39:00] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -64.568519 | E_var:     0.4340 | E_err:   0.010294
[2025-10-23 02:39:13] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -64.584336 | E_var:     0.4460 | E_err:   0.010435
[2025-10-23 02:39:26] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -64.576831 | E_var:     0.4654 | E_err:   0.010660
[2025-10-23 02:39:40] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -64.605331 | E_var:     0.3814 | E_err:   0.009649
[2025-10-23 02:39:53] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -64.578577 | E_var:     0.4570 | E_err:   0.010563
[2025-10-23 02:40:06] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -64.593978 | E_var:     0.4353 | E_err:   0.010309
[2025-10-23 02:40:19] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -64.578813 | E_var:     0.4683 | E_err:   0.010692
[2025-10-23 02:40:33] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -64.601187 | E_var:     0.3705 | E_err:   0.009510
[2025-10-23 02:40:46] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -64.583853 | E_var:     0.4262 | E_err:   0.010200
[2025-10-23 02:40:59] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -64.571448 | E_var:     0.4068 | E_err:   0.009966
[2025-10-23 02:41:12] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -64.577800 | E_var:     0.4996 | E_err:   0.011044
[2025-10-23 02:41:25] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -64.596860 | E_var:     0.4807 | E_err:   0.010833
[2025-10-23 02:41:25] 🔄 RESTART #2 | Period: 600
[2025-10-23 02:41:39] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -64.596240 | E_var:     0.6585 | E_err:   0.012679
[2025-10-23 02:41:52] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -64.571657 | E_var:     0.4274 | E_err:   0.010215
[2025-10-23 02:42:05] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -64.603129 | E_var:     0.4139 | E_err:   0.010053
[2025-10-23 02:42:18] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -64.579657 | E_var:     0.3750 | E_err:   0.009568
[2025-10-23 02:42:32] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -64.597167 | E_var:     0.3521 | E_err:   0.009271
[2025-10-23 02:42:45] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -64.574165 | E_var:     0.4405 | E_err:   0.010371
[2025-10-23 02:42:58] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -64.584437 | E_var:     0.4883 | E_err:   0.010918
[2025-10-23 02:43:11] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -64.564261 | E_var:     0.5732 | E_err:   0.011829
[2025-10-23 02:43:24] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -64.574665 | E_var:     0.4574 | E_err:   0.010567
[2025-10-23 02:43:38] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -64.579843 | E_var:     0.4188 | E_err:   0.010112
[2025-10-23 02:43:51] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -64.597719 | E_var:     0.4391 | E_err:   0.010353
[2025-10-23 02:44:04] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -64.584814 | E_var:     0.4885 | E_err:   0.010921
[2025-10-23 02:44:17] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -64.575636 | E_var:     0.4497 | E_err:   0.010478
[2025-10-23 02:44:31] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -64.566492 | E_var:     0.6733 | E_err:   0.012821
[2025-10-23 02:44:44] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -64.587062 | E_var:     0.4108 | E_err:   0.010015
[2025-10-23 02:44:57] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -64.571770 | E_var:     0.4725 | E_err:   0.010740
[2025-10-23 02:45:10] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -64.593992 | E_var:     0.5007 | E_err:   0.011056
[2025-10-23 02:45:23] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -64.575027 | E_var:     0.4785 | E_err:   0.010809
[2025-10-23 02:45:37] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -64.592098 | E_var:     0.3880 | E_err:   0.009732
[2025-10-23 02:45:50] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -64.599936 | E_var:     0.4324 | E_err:   0.010275
[2025-10-23 02:46:03] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -64.601704 | E_var:     0.4428 | E_err:   0.010397
[2025-10-23 02:46:16] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -64.582491 | E_var:     0.4800 | E_err:   0.010826
[2025-10-23 02:46:30] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -64.572105 | E_var:     0.5964 | E_err:   0.012067
[2025-10-23 02:46:43] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -64.599185 | E_var:     0.3794 | E_err:   0.009624
[2025-10-23 02:46:56] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -64.584618 | E_var:     0.3930 | E_err:   0.009795
[2025-10-23 02:47:09] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -64.579567 | E_var:     0.4917 | E_err:   0.010956
[2025-10-23 02:47:22] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -64.586904 | E_var:     0.4531 | E_err:   0.010518
[2025-10-23 02:47:36] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -64.589197 | E_var:     0.3492 | E_err:   0.009233
[2025-10-23 02:47:49] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -64.584139 | E_var:     0.4818 | E_err:   0.010845
[2025-10-23 02:48:02] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -64.582975 | E_var:     0.4531 | E_err:   0.010518
[2025-10-23 02:48:15] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -64.567819 | E_var:     0.5227 | E_err:   0.011297
[2025-10-23 02:48:29] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -64.580723 | E_var:     0.4196 | E_err:   0.010122
[2025-10-23 02:48:42] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -64.580094 | E_var:     0.4390 | E_err:   0.010353
[2025-10-23 02:48:55] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -64.575380 | E_var:     0.4127 | E_err:   0.010038
[2025-10-23 02:49:08] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -64.579225 | E_var:     0.4551 | E_err:   0.010540
[2025-10-23 02:49:21] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -64.593165 | E_var:     0.4267 | E_err:   0.010206
[2025-10-23 02:49:35] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -64.560977 | E_var:     0.4651 | E_err:   0.010656
[2025-10-23 02:49:48] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -64.576520 | E_var:     0.4619 | E_err:   0.010620
[2025-10-23 02:50:01] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -64.579012 | E_var:     0.4795 | E_err:   0.010819
[2025-10-23 02:50:14] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -64.587443 | E_var:     0.4193 | E_err:   0.010118
[2025-10-23 02:50:28] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -64.594900 | E_var:     0.4599 | E_err:   0.010597
[2025-10-23 02:50:41] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -64.575230 | E_var:     0.4597 | E_err:   0.010594
[2025-10-23 02:50:54] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -64.580586 | E_var:     0.3667 | E_err:   0.009462
[2025-10-23 02:51:07] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -64.578803 | E_var:     0.4245 | E_err:   0.010180
[2025-10-23 02:51:20] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -64.591208 | E_var:     0.4244 | E_err:   0.010179
[2025-10-23 02:51:34] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -64.589159 | E_var:     0.4763 | E_err:   0.010784
[2025-10-23 02:51:47] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -64.575985 | E_var:     0.4892 | E_err:   0.010929
[2025-10-23 02:52:00] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -64.591181 | E_var:     0.4516 | E_err:   0.010500
[2025-10-23 02:52:13] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -64.596092 | E_var:     0.4812 | E_err:   0.010839
[2025-10-23 02:52:27] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -64.585537 | E_var:     0.4266 | E_err:   0.010205
[2025-10-23 02:52:40] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -64.592550 | E_var:     0.3903 | E_err:   0.009761
[2025-10-23 02:52:53] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -64.586702 | E_var:     0.5822 | E_err:   0.011923
[2025-10-23 02:53:06] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -64.575180 | E_var:     0.4650 | E_err:   0.010655
[2025-10-23 02:53:19] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -64.571941 | E_var:     0.4473 | E_err:   0.010449
[2025-10-23 02:53:33] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -64.597796 | E_var:     0.4056 | E_err:   0.009951
[2025-10-23 02:53:46] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -64.573483 | E_var:     1.1289 | E_err:   0.016602
[2025-10-23 02:53:59] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -64.592853 | E_var:     0.4854 | E_err:   0.010886
[2025-10-23 02:54:12] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -64.592848 | E_var:     0.5669 | E_err:   0.011764
[2025-10-23 02:54:26] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -64.577861 | E_var:     0.4594 | E_err:   0.010590
[2025-10-23 02:54:39] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -64.578895 | E_var:     0.3975 | E_err:   0.009852
[2025-10-23 02:54:52] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -64.574857 | E_var:     0.4233 | E_err:   0.010166
[2025-10-23 02:55:05] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -64.579763 | E_var:     0.3874 | E_err:   0.009725
[2025-10-23 02:55:18] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -64.585621 | E_var:     0.3958 | E_err:   0.009830
[2025-10-23 02:55:32] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -64.574672 | E_var:     0.4361 | E_err:   0.010319
[2025-10-23 02:55:45] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -64.596479 | E_var:     0.5331 | E_err:   0.011409
[2025-10-23 02:55:58] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -64.577673 | E_var:     0.3851 | E_err:   0.009696
[2025-10-23 02:56:11] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -64.586048 | E_var:     0.4786 | E_err:   0.010809
[2025-10-23 02:56:25] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -64.582668 | E_var:     0.4779 | E_err:   0.010801
[2025-10-23 02:56:38] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -64.597346 | E_var:     0.3878 | E_err:   0.009731
[2025-10-23 02:56:51] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -64.585023 | E_var:     0.5063 | E_err:   0.011118
[2025-10-23 02:57:04] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -64.582700 | E_var:     0.5692 | E_err:   0.011788
[2025-10-23 02:57:18] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -64.593129 | E_var:     0.7307 | E_err:   0.013357
[2025-10-23 02:57:31] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -64.573597 | E_var:     0.4359 | E_err:   0.010316
[2025-10-23 02:57:44] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -64.584162 | E_var:     0.3822 | E_err:   0.009660
[2025-10-23 02:57:57] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -64.563376 | E_var:     0.5592 | E_err:   0.011685
[2025-10-23 02:57:57] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-23 02:58:10] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -64.581491 | E_var:     0.4142 | E_err:   0.010056
[2025-10-23 02:58:24] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -64.589491 | E_var:     0.3913 | E_err:   0.009774
[2025-10-23 02:58:37] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -64.585297 | E_var:     0.6694 | E_err:   0.012783
[2025-10-23 02:58:50] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -64.586479 | E_var:     0.6320 | E_err:   0.012422
[2025-10-23 02:59:03] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -64.579276 | E_var:     0.4746 | E_err:   0.010764
[2025-10-23 02:59:17] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -64.562867 | E_var:     0.3740 | E_err:   0.009556
[2025-10-23 02:59:30] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -64.571761 | E_var:     0.4736 | E_err:   0.010753
[2025-10-23 02:59:43] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -64.576883 | E_var:     0.4268 | E_err:   0.010208
[2025-10-23 02:59:56] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -64.586609 | E_var:     0.4392 | E_err:   0.010355
[2025-10-23 03:00:10] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -64.577508 | E_var:     0.4142 | E_err:   0.010056
[2025-10-23 03:00:23] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -64.580154 | E_var:     0.4576 | E_err:   0.010569
[2025-10-23 03:00:36] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -64.583225 | E_var:     0.3856 | E_err:   0.009703
[2025-10-23 03:00:49] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -64.596199 | E_var:     0.5093 | E_err:   0.011151
[2025-10-23 03:01:02] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -64.584943 | E_var:     0.4881 | E_err:   0.010917
[2025-10-23 03:01:16] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -64.575282 | E_var:     0.4442 | E_err:   0.010414
[2025-10-23 03:01:29] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -64.563387 | E_var:     0.8664 | E_err:   0.014544
[2025-10-23 03:01:42] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -64.601464 | E_var:     0.4621 | E_err:   0.010621
[2025-10-23 03:01:55] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -64.576452 | E_var:     0.4218 | E_err:   0.010148
[2025-10-23 03:02:09] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -64.584990 | E_var:     0.4087 | E_err:   0.009989
[2025-10-23 03:02:22] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -64.576139 | E_var:     0.5037 | E_err:   0.011089
[2025-10-23 03:02:35] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -64.579905 | E_var:     0.3908 | E_err:   0.009767
[2025-10-23 03:02:48] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -64.588251 | E_var:     0.4874 | E_err:   0.010909
[2025-10-23 03:03:01] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -64.586201 | E_var:     0.3667 | E_err:   0.009462
[2025-10-23 03:03:15] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -64.574356 | E_var:     0.4330 | E_err:   0.010282
[2025-10-23 03:03:28] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -64.575750 | E_var:     0.3844 | E_err:   0.009688
[2025-10-23 03:03:41] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -64.586757 | E_var:     0.5487 | E_err:   0.011575
[2025-10-23 03:03:54] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -64.592293 | E_var:     0.4285 | E_err:   0.010228
[2025-10-23 03:04:08] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -64.595417 | E_var:     0.5887 | E_err:   0.011988
[2025-10-23 03:04:21] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -64.588747 | E_var:     0.4161 | E_err:   0.010079
[2025-10-23 03:04:34] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -64.581484 | E_var:     0.3654 | E_err:   0.009445
[2025-10-23 03:04:47] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -64.588289 | E_var:     0.4049 | E_err:   0.009943
[2025-10-23 03:05:00] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -64.576346 | E_var:     0.4238 | E_err:   0.010172
[2025-10-23 03:05:14] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -64.598501 | E_var:     0.4715 | E_err:   0.010729
[2025-10-23 03:05:27] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -64.585327 | E_var:     0.4194 | E_err:   0.010119
[2025-10-23 03:05:40] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -64.575375 | E_var:     0.4089 | E_err:   0.009991
[2025-10-23 03:05:53] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -64.569814 | E_var:     0.3925 | E_err:   0.009789
[2025-10-23 03:06:07] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -64.573434 | E_var:     0.5038 | E_err:   0.011091
[2025-10-23 03:06:20] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -64.563967 | E_var:     0.4512 | E_err:   0.010495
[2025-10-23 03:06:33] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -64.596854 | E_var:     0.3735 | E_err:   0.009549
[2025-10-23 03:06:46] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -64.598957 | E_var:     0.5197 | E_err:   0.011264
[2025-10-23 03:07:00] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -64.568070 | E_var:     0.4769 | E_err:   0.010791
[2025-10-23 03:07:13] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -64.563090 | E_var:     0.4193 | E_err:   0.010117
[2025-10-23 03:07:26] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -64.583914 | E_var:     0.7050 | E_err:   0.013119
[2025-10-23 03:07:39] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -64.586358 | E_var:     0.4244 | E_err:   0.010180
[2025-10-23 03:07:52] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -64.574261 | E_var:     0.5994 | E_err:   0.012097
[2025-10-23 03:08:06] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -64.573811 | E_var:     0.6696 | E_err:   0.012786
[2025-10-23 03:08:19] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -64.566924 | E_var:     0.4081 | E_err:   0.009982
[2025-10-23 03:08:32] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -64.577308 | E_var:     0.7394 | E_err:   0.013436
[2025-10-23 03:08:45] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -64.579932 | E_var:     0.6386 | E_err:   0.012486
[2025-10-23 03:08:59] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -64.604643 | E_var:     0.4403 | E_err:   0.010368
[2025-10-23 03:09:12] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -64.585470 | E_var:     0.3712 | E_err:   0.009520
[2025-10-23 03:09:25] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -64.575704 | E_var:     0.5330 | E_err:   0.011408
[2025-10-23 03:09:38] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -64.587936 | E_var:     0.4440 | E_err:   0.010412
[2025-10-23 03:09:51] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -64.578544 | E_var:     0.4505 | E_err:   0.010488
[2025-10-23 03:10:05] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -64.579685 | E_var:     0.4161 | E_err:   0.010079
[2025-10-23 03:10:18] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -64.565279 | E_var:     0.4618 | E_err:   0.010618
[2025-10-23 03:10:31] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -64.582642 | E_var:     0.4933 | E_err:   0.010974
[2025-10-23 03:10:44] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -64.589011 | E_var:     0.5016 | E_err:   0.011066
[2025-10-23 03:10:58] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -64.578025 | E_var:     0.4091 | E_err:   0.009994
[2025-10-23 03:11:11] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -64.594395 | E_var:     0.3768 | E_err:   0.009591
[2025-10-23 03:11:24] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -64.568949 | E_var:     0.4135 | E_err:   0.010047
[2025-10-23 03:11:37] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -64.580354 | E_var:     0.4536 | E_err:   0.010524
[2025-10-23 03:11:50] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -64.596637 | E_var:     0.6664 | E_err:   0.012755
[2025-10-23 03:12:04] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -64.582918 | E_var:     0.4079 | E_err:   0.009979
[2025-10-23 03:12:17] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -64.568587 | E_var:     0.4595 | E_err:   0.010591
[2025-10-23 03:12:30] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -64.579422 | E_var:     0.5045 | E_err:   0.011098
[2025-10-23 03:12:43] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -64.597486 | E_var:     0.4418 | E_err:   0.010385
[2025-10-23 03:12:57] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -64.594693 | E_var:     0.3530 | E_err:   0.009284
[2025-10-23 03:13:10] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -64.570328 | E_var:     0.4222 | E_err:   0.010152
[2025-10-23 03:13:23] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -64.585849 | E_var:     0.3976 | E_err:   0.009852
[2025-10-23 03:13:36] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -64.578389 | E_var:     0.4860 | E_err:   0.010893
[2025-10-23 03:13:50] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -64.580844 | E_var:     0.7213 | E_err:   0.013271
[2025-10-23 03:14:03] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -64.587994 | E_var:     0.4523 | E_err:   0.010509
[2025-10-23 03:14:16] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -64.585794 | E_var:     0.3717 | E_err:   0.009526
[2025-10-23 03:14:29] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -64.589439 | E_var:     0.4497 | E_err:   0.010478
[2025-10-23 03:14:42] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -64.586643 | E_var:     0.3948 | E_err:   0.009818
[2025-10-23 03:14:56] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -64.574659 | E_var:     0.4773 | E_err:   0.010795
[2025-10-23 03:15:09] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -64.598020 | E_var:     0.4651 | E_err:   0.010655
[2025-10-23 03:15:22] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -64.575338 | E_var:     0.4407 | E_err:   0.010373
[2025-10-23 03:15:35] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -64.599115 | E_var:     0.4024 | E_err:   0.009912
[2025-10-23 03:15:48] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -64.590602 | E_var:     0.4648 | E_err:   0.010653
[2025-10-23 03:16:02] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -64.586690 | E_var:     0.4286 | E_err:   0.010229
[2025-10-23 03:16:15] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -64.576261 | E_var:     0.6900 | E_err:   0.012979
[2025-10-23 03:16:28] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -64.594520 | E_var:     0.4073 | E_err:   0.009972
[2025-10-23 03:16:41] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -64.580128 | E_var:     0.3556 | E_err:   0.009317
[2025-10-23 03:16:55] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -64.573074 | E_var:     0.4221 | E_err:   0.010151
[2025-10-23 03:17:08] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -64.580136 | E_var:     0.6411 | E_err:   0.012511
[2025-10-23 03:17:21] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -64.574679 | E_var:     0.4725 | E_err:   0.010740
[2025-10-23 03:17:34] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -64.598525 | E_var:     0.4894 | E_err:   0.010931
[2025-10-23 03:17:48] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -64.580762 | E_var:     0.4792 | E_err:   0.010817
[2025-10-23 03:18:01] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -64.571687 | E_var:     0.6501 | E_err:   0.012599
[2025-10-23 03:18:14] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -64.574783 | E_var:     0.3229 | E_err:   0.008879
[2025-10-23 03:18:27] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -64.589958 | E_var:     0.5146 | E_err:   0.011208
[2025-10-23 03:18:40] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -64.588684 | E_var:     0.6050 | E_err:   0.012154
[2025-10-23 03:18:54] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -64.582626 | E_var:     0.5507 | E_err:   0.011595
[2025-10-23 03:19:07] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -64.613114 | E_var:     0.4788 | E_err:   0.010812
[2025-10-23 03:19:20] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -64.594453 | E_var:     0.3985 | E_err:   0.009863
[2025-10-23 03:19:33] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -64.577477 | E_var:     0.3759 | E_err:   0.009579
[2025-10-23 03:19:47] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -64.584744 | E_var:     0.4303 | E_err:   0.010250
[2025-10-23 03:20:00] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -64.586311 | E_var:     0.3774 | E_err:   0.009599
[2025-10-23 03:20:13] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -64.588342 | E_var:     0.4284 | E_err:   0.010227
[2025-10-23 03:20:26] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -64.570339 | E_var:     0.4247 | E_err:   0.010182
[2025-10-23 03:20:39] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -64.585228 | E_var:     0.4173 | E_err:   0.010094
[2025-10-23 03:20:53] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -64.578795 | E_var:     0.5726 | E_err:   0.011823
[2025-10-23 03:21:06] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -64.603359 | E_var:     0.4638 | E_err:   0.010641
[2025-10-23 03:21:06] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-23 03:21:19] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -64.578901 | E_var:     0.4916 | E_err:   0.010955
[2025-10-23 03:21:32] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -64.573931 | E_var:     0.4492 | E_err:   0.010472
[2025-10-23 03:21:46] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -64.567686 | E_var:     0.5250 | E_err:   0.011321
[2025-10-23 03:21:59] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -64.585080 | E_var:     0.4246 | E_err:   0.010181
[2025-10-23 03:22:12] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -64.570943 | E_var:     0.4429 | E_err:   0.010399
[2025-10-23 03:22:25] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -64.557709 | E_var:     0.7531 | E_err:   0.013560
[2025-10-23 03:22:39] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -64.582373 | E_var:     0.4272 | E_err:   0.010213
[2025-10-23 03:22:52] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -64.579203 | E_var:     0.4252 | E_err:   0.010189
[2025-10-23 03:23:05] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -64.581954 | E_var:     0.3979 | E_err:   0.009856
[2025-10-23 03:23:18] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -64.577481 | E_var:     0.4109 | E_err:   0.010015
[2025-10-23 03:23:31] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -64.593578 | E_var:     0.3770 | E_err:   0.009593
[2025-10-23 03:23:45] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -64.595128 | E_var:     0.4117 | E_err:   0.010026
[2025-10-23 03:23:58] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -64.583103 | E_var:     0.4334 | E_err:   0.010287
[2025-10-23 03:24:11] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -64.562673 | E_var:     0.7938 | E_err:   0.013921
[2025-10-23 03:24:24] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -64.584215 | E_var:     0.4271 | E_err:   0.010212
[2025-10-23 03:24:38] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -64.585621 | E_var:     0.4919 | E_err:   0.010959
[2025-10-23 03:24:51] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -64.579980 | E_var:     0.4304 | E_err:   0.010250
[2025-10-23 03:25:04] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -64.590762 | E_var:     0.5561 | E_err:   0.011652
[2025-10-23 03:25:17] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -64.579437 | E_var:     0.4523 | E_err:   0.010509
[2025-10-23 03:25:30] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -64.598333 | E_var:     0.6178 | E_err:   0.012282
[2025-10-23 03:25:44] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -64.590468 | E_var:     0.4518 | E_err:   0.010503
[2025-10-23 03:25:57] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -64.576104 | E_var:     0.4215 | E_err:   0.010144
[2025-10-23 03:26:10] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -64.597466 | E_var:     0.4071 | E_err:   0.009969
[2025-10-23 03:26:23] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -64.580909 | E_var:     0.4040 | E_err:   0.009932
[2025-10-23 03:26:37] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -64.597046 | E_var:     0.6750 | E_err:   0.012838
[2025-10-23 03:26:50] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -64.597689 | E_var:     0.4723 | E_err:   0.010738
[2025-10-23 03:27:03] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -64.584714 | E_var:     0.5556 | E_err:   0.011647
[2025-10-23 03:27:16] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -64.582324 | E_var:     0.4696 | E_err:   0.010707
[2025-10-23 03:27:29] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -64.570557 | E_var:     0.4060 | E_err:   0.009956
[2025-10-23 03:27:43] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -64.588567 | E_var:     0.4622 | E_err:   0.010622
[2025-10-23 03:27:56] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -64.571911 | E_var:     0.4894 | E_err:   0.010930
[2025-10-23 03:28:09] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -64.610346 | E_var:     0.4693 | E_err:   0.010704
[2025-10-23 03:28:22] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -64.585393 | E_var:     0.4261 | E_err:   0.010199
[2025-10-23 03:28:36] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -64.585296 | E_var:     0.5005 | E_err:   0.011054
[2025-10-23 03:28:49] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -64.584396 | E_var:     0.4027 | E_err:   0.009915
[2025-10-23 03:29:02] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -64.576873 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 03:29:15] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -64.578680 | E_var:     0.3619 | E_err:   0.009400
[2025-10-23 03:29:28] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -64.572473 | E_var:     0.4013 | E_err:   0.009898
[2025-10-23 03:29:42] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -64.580240 | E_var:     0.3690 | E_err:   0.009492
[2025-10-23 03:29:55] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -64.571598 | E_var:     0.4310 | E_err:   0.010258
[2025-10-23 03:30:08] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -64.564235 | E_var:     0.4951 | E_err:   0.010994
[2025-10-23 03:30:21] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -64.585212 | E_var:     0.5251 | E_err:   0.011322
[2025-10-23 03:30:35] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -64.593749 | E_var:     0.3968 | E_err:   0.009843
[2025-10-23 03:30:48] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -64.594402 | E_var:     0.4729 | E_err:   0.010745
[2025-10-23 03:31:01] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -64.580442 | E_var:     0.4339 | E_err:   0.010292
[2025-10-23 03:31:14] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -64.584633 | E_var:     0.3363 | E_err:   0.009061
[2025-10-23 03:31:27] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -64.580662 | E_var:     0.3866 | E_err:   0.009715
[2025-10-23 03:31:41] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -64.591098 | E_var:     0.4106 | E_err:   0.010012
[2025-10-23 03:31:54] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -64.606195 | E_var:     0.4090 | E_err:   0.009993
[2025-10-23 03:32:07] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -64.568354 | E_var:     0.3857 | E_err:   0.009704
[2025-10-23 03:32:20] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -64.581822 | E_var:     0.5403 | E_err:   0.011485
[2025-10-23 03:32:34] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -64.591932 | E_var:     0.4050 | E_err:   0.009944
[2025-10-23 03:32:47] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -64.568574 | E_var:     0.4207 | E_err:   0.010135
[2025-10-23 03:33:00] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -64.608249 | E_var:     0.4050 | E_err:   0.009944
[2025-10-23 03:33:13] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -64.582401 | E_var:     0.4340 | E_err:   0.010293
[2025-10-23 03:33:26] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -64.580754 | E_var:     0.4196 | E_err:   0.010121
[2025-10-23 03:33:40] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -64.590702 | E_var:     0.4660 | E_err:   0.010666
[2025-10-23 03:33:53] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -64.588776 | E_var:     0.5091 | E_err:   0.011148
[2025-10-23 03:34:06] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -64.588662 | E_var:     0.4620 | E_err:   0.010621
[2025-10-23 03:34:19] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -64.594276 | E_var:     0.4095 | E_err:   0.009999
[2025-10-23 03:34:33] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -64.592344 | E_var:     0.3809 | E_err:   0.009643
[2025-10-23 03:34:46] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -64.575707 | E_var:     0.4068 | E_err:   0.009965
[2025-10-23 03:34:59] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -64.579337 | E_var:     0.5154 | E_err:   0.011217
[2025-10-23 03:35:12] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -64.559111 | E_var:     0.4921 | E_err:   0.010961
[2025-10-23 03:35:26] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -64.571011 | E_var:     0.4739 | E_err:   0.010756
[2025-10-23 03:35:39] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -64.595000 | E_var:     0.4473 | E_err:   0.010450
[2025-10-23 03:35:52] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -64.577448 | E_var:     0.4349 | E_err:   0.010304
[2025-10-23 03:36:05] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -64.586161 | E_var:     0.5092 | E_err:   0.011150
[2025-10-23 03:36:18] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -64.570842 | E_var:     0.4961 | E_err:   0.011006
[2025-10-23 03:36:32] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -64.570413 | E_var:     0.4412 | E_err:   0.010378
[2025-10-23 03:36:45] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -64.594830 | E_var:     0.3762 | E_err:   0.009583
[2025-10-23 03:36:58] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -64.594302 | E_var:     0.7764 | E_err:   0.013768
[2025-10-23 03:37:11] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -64.587176 | E_var:     0.6166 | E_err:   0.012269
[2025-10-23 03:37:25] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -64.581241 | E_var:     0.3536 | E_err:   0.009291
[2025-10-23 03:37:38] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -64.589287 | E_var:     0.4032 | E_err:   0.009922
[2025-10-23 03:37:51] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -64.577500 | E_var:     0.4419 | E_err:   0.010387
[2025-10-23 03:38:04] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -64.555178 | E_var:     0.5133 | E_err:   0.011195
[2025-10-23 03:38:17] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -64.577083 | E_var:     0.4252 | E_err:   0.010188
[2025-10-23 03:38:31] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -64.607394 | E_var:     0.4072 | E_err:   0.009970
[2025-10-23 03:38:44] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -64.591180 | E_var:     0.4873 | E_err:   0.010908
[2025-10-23 03:38:57] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -64.595874 | E_var:     0.5558 | E_err:   0.011649
[2025-10-23 03:39:10] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -64.584136 | E_var:     0.5466 | E_err:   0.011552
[2025-10-23 03:39:24] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -64.584190 | E_var:     0.5189 | E_err:   0.011256
[2025-10-23 03:39:37] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -64.568339 | E_var:     0.3812 | E_err:   0.009647
[2025-10-23 03:39:50] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -64.581586 | E_var:     0.4271 | E_err:   0.010211
[2025-10-23 03:40:03] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -64.594019 | E_var:     0.4220 | E_err:   0.010150
[2025-10-23 03:40:16] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -64.584229 | E_var:     0.4284 | E_err:   0.010227
[2025-10-23 03:40:30] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -64.555555 | E_var:     0.3905 | E_err:   0.009764
[2025-10-23 03:40:43] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -64.556058 | E_var:     0.4244 | E_err:   0.010179
[2025-10-23 03:40:56] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -64.575084 | E_var:     0.4618 | E_err:   0.010618
[2025-10-23 03:41:09] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -64.582079 | E_var:     0.5535 | E_err:   0.011625
[2025-10-23 03:41:23] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -64.568524 | E_var:     0.4407 | E_err:   0.010373
[2025-10-23 03:41:36] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -64.563520 | E_var:     0.4568 | E_err:   0.010561
[2025-10-23 03:41:49] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -64.574885 | E_var:     0.4311 | E_err:   0.010259
[2025-10-23 03:42:02] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -64.569161 | E_var:     0.3991 | E_err:   0.009870
[2025-10-23 03:42:15] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -64.572592 | E_var:     0.4619 | E_err:   0.010619
[2025-10-23 03:42:29] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -64.588670 | E_var:     0.4633 | E_err:   0.010635
[2025-10-23 03:42:42] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -64.578421 | E_var:     0.4981 | E_err:   0.011028
[2025-10-23 03:42:55] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -64.595905 | E_var:     0.4067 | E_err:   0.009965
[2025-10-23 03:43:08] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -64.578323 | E_var:     0.4514 | E_err:   0.010498
[2025-10-23 03:43:22] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -64.570013 | E_var:     0.3648 | E_err:   0.009437
[2025-10-23 03:43:35] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -64.576648 | E_var:     0.3315 | E_err:   0.008996
[2025-10-23 03:43:48] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -64.587026 | E_var:     0.4572 | E_err:   0.010565
[2025-10-23 03:44:01] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -64.588049 | E_var:     0.4279 | E_err:   0.010221
[2025-10-23 03:44:14] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -64.584289 | E_var:     0.5229 | E_err:   0.011299
[2025-10-23 03:44:15] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-23 03:44:28] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -64.579616 | E_var:     0.4526 | E_err:   0.010512
[2025-10-23 03:44:41] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -64.583886 | E_var:     0.4197 | E_err:   0.010123
[2025-10-23 03:44:54] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -64.567081 | E_var:     0.4096 | E_err:   0.010000
[2025-10-23 03:45:07] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -64.591232 | E_var:     0.3929 | E_err:   0.009794
[2025-10-23 03:45:21] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -64.577062 | E_var:     0.4598 | E_err:   0.010596
[2025-10-23 03:45:34] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -64.581672 | E_var:     0.4167 | E_err:   0.010086
[2025-10-23 03:45:47] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -64.559102 | E_var:     0.4346 | E_err:   0.010301
[2025-10-23 03:46:00] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -64.569045 | E_var:     0.4829 | E_err:   0.010858
[2025-10-23 03:46:14] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -64.588356 | E_var:     0.5120 | E_err:   0.011180
[2025-10-23 03:46:27] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -64.569710 | E_var:     0.4142 | E_err:   0.010056
[2025-10-23 03:46:40] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -64.597316 | E_var:     0.4166 | E_err:   0.010085
[2025-10-23 03:46:53] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -64.604794 | E_var:     0.3772 | E_err:   0.009596
[2025-10-23 03:47:06] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -64.571145 | E_var:     0.4574 | E_err:   0.010568
[2025-10-23 03:47:20] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -64.560183 | E_var:     0.4121 | E_err:   0.010030
[2025-10-23 03:47:33] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -64.595324 | E_var:     0.4528 | E_err:   0.010514
[2025-10-23 03:47:46] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -64.579760 | E_var:     0.4189 | E_err:   0.010113
[2025-10-23 03:47:59] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -64.574145 | E_var:     0.5705 | E_err:   0.011802
[2025-10-23 03:48:13] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -64.571421 | E_var:     0.4368 | E_err:   0.010327
[2025-10-23 03:48:26] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -64.601130 | E_var:     0.4168 | E_err:   0.010088
[2025-10-23 03:48:39] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -64.591964 | E_var:     0.4157 | E_err:   0.010075
[2025-10-23 03:48:52] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -64.579143 | E_var:     0.5826 | E_err:   0.011926
[2025-10-23 03:49:05] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -64.584340 | E_var:     0.3920 | E_err:   0.009782
[2025-10-23 03:49:19] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -64.564698 | E_var:     0.3567 | E_err:   0.009332
[2025-10-23 03:49:32] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -64.575328 | E_var:     0.4024 | E_err:   0.009912
[2025-10-23 03:49:45] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -64.590576 | E_var:     0.4362 | E_err:   0.010319
[2025-10-23 03:49:58] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -64.581721 | E_var:     0.4240 | E_err:   0.010174
[2025-10-23 03:50:12] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -64.589017 | E_var:     0.3981 | E_err:   0.009859
[2025-10-23 03:50:25] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -64.582480 | E_var:     0.4146 | E_err:   0.010061
[2025-10-23 03:50:38] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -64.577285 | E_var:     0.4151 | E_err:   0.010066
[2025-10-23 03:50:51] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -64.582600 | E_var:     0.4805 | E_err:   0.010831
[2025-10-23 03:51:04] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -64.588669 | E_var:     0.4805 | E_err:   0.010831
[2025-10-23 03:51:18] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -64.553987 | E_var:     0.4467 | E_err:   0.010443
[2025-10-23 03:51:31] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -64.591980 | E_var:     0.4253 | E_err:   0.010190
[2025-10-23 03:51:44] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -64.591492 | E_var:     0.4199 | E_err:   0.010125
[2025-10-23 03:51:57] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -64.589215 | E_var:     0.3950 | E_err:   0.009820
[2025-10-23 03:52:11] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -64.596674 | E_var:     0.3888 | E_err:   0.009743
[2025-10-23 03:52:24] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -64.581226 | E_var:     0.3746 | E_err:   0.009563
[2025-10-23 03:52:37] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -64.584622 | E_var:     0.3792 | E_err:   0.009622
[2025-10-23 03:52:50] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -64.592517 | E_var:     0.4216 | E_err:   0.010145
[2025-10-23 03:53:03] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -64.580114 | E_var:     0.3919 | E_err:   0.009781
[2025-10-23 03:53:17] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -64.593121 | E_var:     0.4235 | E_err:   0.010168
[2025-10-23 03:53:30] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -64.577072 | E_var:     0.3845 | E_err:   0.009688
[2025-10-23 03:53:43] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -64.599764 | E_var:     0.5758 | E_err:   0.011856
[2025-10-23 03:53:56] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -64.565959 | E_var:     0.4824 | E_err:   0.010852
[2025-10-23 03:54:10] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -64.557134 | E_var:     0.4755 | E_err:   0.010775
[2025-10-23 03:54:23] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -64.602038 | E_var:     0.4368 | E_err:   0.010326
[2025-10-23 03:54:36] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -64.576779 | E_var:     0.4009 | E_err:   0.009894
[2025-10-23 03:54:49] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -64.560823 | E_var:     0.4732 | E_err:   0.010749
[2025-10-23 03:55:02] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -64.589317 | E_var:     0.4585 | E_err:   0.010580
[2025-10-23 03:55:16] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -64.588089 | E_var:     0.4495 | E_err:   0.010476
[2025-10-23 03:55:29] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -64.593470 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 03:55:42] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -64.609079 | E_var:     0.4439 | E_err:   0.010410
[2025-10-23 03:55:55] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -64.579056 | E_var:     0.3771 | E_err:   0.009595
[2025-10-23 03:56:09] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -64.585680 | E_var:     0.3624 | E_err:   0.009406
[2025-10-23 03:56:22] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -64.571562 | E_var:     0.3933 | E_err:   0.009799
[2025-10-23 03:56:35] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -64.581159 | E_var:     0.4427 | E_err:   0.010396
[2025-10-23 03:56:48] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -64.582412 | E_var:     0.3749 | E_err:   0.009567
[2025-10-23 03:57:01] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -64.574390 | E_var:     0.4169 | E_err:   0.010088
[2025-10-23 03:57:15] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -64.566461 | E_var:     0.4017 | E_err:   0.009903
[2025-10-23 03:57:28] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -64.580704 | E_var:     0.3910 | E_err:   0.009771
[2025-10-23 03:57:41] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -64.573742 | E_var:     0.4612 | E_err:   0.010611
[2025-10-23 03:57:54] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -64.569009 | E_var:     0.4791 | E_err:   0.010816
[2025-10-23 03:58:08] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -64.579426 | E_var:     0.3853 | E_err:   0.009699
[2025-10-23 03:58:21] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -64.592586 | E_var:     0.3913 | E_err:   0.009774
[2025-10-23 03:58:34] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -64.580164 | E_var:     0.4103 | E_err:   0.010008
[2025-10-23 03:58:47] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -64.587206 | E_var:     0.6871 | E_err:   0.012952
[2025-10-23 03:59:00] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -64.573831 | E_var:     0.4119 | E_err:   0.010028
[2025-10-23 03:59:14] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -64.574580 | E_var:     0.3909 | E_err:   0.009769
[2025-10-23 03:59:27] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -64.593661 | E_var:     0.5054 | E_err:   0.011108
[2025-10-23 03:59:40] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -64.578582 | E_var:     0.4104 | E_err:   0.010010
[2025-10-23 03:59:53] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -64.598076 | E_var:     0.4620 | E_err:   0.010620
[2025-10-23 04:00:07] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -64.580317 | E_var:     0.3385 | E_err:   0.009091
[2025-10-23 04:00:20] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -64.586010 | E_var:     0.4204 | E_err:   0.010131
[2025-10-23 04:00:33] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -64.589801 | E_var:     0.4315 | E_err:   0.010263
[2025-10-23 04:00:46] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -64.603400 | E_var:     0.4448 | E_err:   0.010420
[2025-10-23 04:00:59] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -64.585571 | E_var:     0.6558 | E_err:   0.012653
[2025-10-23 04:01:13] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -64.575748 | E_var:     0.3856 | E_err:   0.009703
[2025-10-23 04:01:26] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -64.577267 | E_var:     0.4191 | E_err:   0.010116
[2025-10-23 04:01:39] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -64.589076 | E_var:     0.4500 | E_err:   0.010482
[2025-10-23 04:01:52] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -64.587837 | E_var:     0.5316 | E_err:   0.011393
[2025-10-23 04:02:06] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -64.586506 | E_var:     0.4499 | E_err:   0.010481
[2025-10-23 04:02:19] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -64.589850 | E_var:     0.4256 | E_err:   0.010194
[2025-10-23 04:02:32] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -64.575127 | E_var:     0.4583 | E_err:   0.010578
[2025-10-23 04:02:45] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -64.588046 | E_var:     0.5736 | E_err:   0.011834
[2025-10-23 04:02:58] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -64.601162 | E_var:     0.4726 | E_err:   0.010742
[2025-10-23 04:03:12] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -64.586902 | E_var:     0.4146 | E_err:   0.010061
[2025-10-23 04:03:25] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -64.580543 | E_var:     0.3804 | E_err:   0.009637
[2025-10-23 04:03:38] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -64.590875 | E_var:     0.5287 | E_err:   0.011361
[2025-10-23 04:03:51] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -64.572996 | E_var:     0.4229 | E_err:   0.010161
[2025-10-23 04:04:05] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -64.593046 | E_var:     0.4243 | E_err:   0.010178
[2025-10-23 04:04:18] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -64.580259 | E_var:     0.4173 | E_err:   0.010093
[2025-10-23 04:04:31] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -64.580728 | E_var:     0.4286 | E_err:   0.010230
[2025-10-23 04:04:44] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -64.577540 | E_var:     0.4428 | E_err:   0.010398
[2025-10-23 04:04:57] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -64.585800 | E_var:     0.5179 | E_err:   0.011244
[2025-10-23 04:05:11] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -64.597987 | E_var:     0.5152 | E_err:   0.011215
[2025-10-23 04:05:24] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -64.574304 | E_var:     0.4162 | E_err:   0.010080
[2025-10-23 04:05:37] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -64.570378 | E_var:     0.4636 | E_err:   0.010639
[2025-10-23 04:05:50] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -64.576905 | E_var:     0.4474 | E_err:   0.010451
[2025-10-23 04:06:04] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -64.576346 | E_var:     0.4110 | E_err:   0.010017
[2025-10-23 04:06:17] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -64.609442 | E_var:     0.4526 | E_err:   0.010512
[2025-10-23 04:06:30] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -64.580032 | E_var:     0.4534 | E_err:   0.010522
[2025-10-23 04:06:43] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -64.580837 | E_var:     0.6008 | E_err:   0.012111
[2025-10-23 04:06:57] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -64.598126 | E_var:     0.4358 | E_err:   0.010315
[2025-10-23 04:07:10] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -64.596468 | E_var:     0.4702 | E_err:   0.010714
[2025-10-23 04:07:24] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -64.598397 | E_var:     0.4517 | E_err:   0.010501
[2025-10-23 04:07:24] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-23 04:07:37] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -64.579897 | E_var:     0.4338 | E_err:   0.010291
[2025-10-23 04:07:50] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -64.576538 | E_var:     0.4798 | E_err:   0.010823
[2025-10-23 04:08:03] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -64.591238 | E_var:     0.5626 | E_err:   0.011719
[2025-10-23 04:08:17] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -64.577412 | E_var:     0.4176 | E_err:   0.010098
[2025-10-23 04:08:30] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -64.580386 | E_var:     0.4350 | E_err:   0.010305
[2025-10-23 04:08:43] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -64.587295 | E_var:     0.4403 | E_err:   0.010368
[2025-10-23 04:08:56] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -64.580010 | E_var:     0.4008 | E_err:   0.009891
[2025-10-23 04:09:10] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -64.573176 | E_var:     0.4285 | E_err:   0.010228
[2025-10-23 04:09:23] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -64.578614 | E_var:     0.4781 | E_err:   0.010804
[2025-10-23 04:09:36] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -64.599359 | E_var:     0.4414 | E_err:   0.010381
[2025-10-23 04:09:49] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -64.595440 | E_var:     0.5002 | E_err:   0.011051
[2025-10-23 04:10:02] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -64.577421 | E_var:     0.3872 | E_err:   0.009722
[2025-10-23 04:10:16] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -64.594015 | E_var:     0.4713 | E_err:   0.010727
[2025-10-23 04:10:29] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -64.585991 | E_var:     0.4216 | E_err:   0.010146
[2025-10-23 04:10:42] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -64.575764 | E_var:     0.5685 | E_err:   0.011781
[2025-10-23 04:10:55] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -64.570131 | E_var:     0.4566 | E_err:   0.010558
[2025-10-23 04:11:09] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -64.572337 | E_var:     0.4195 | E_err:   0.010120
[2025-10-23 04:11:22] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -64.573260 | E_var:     0.4923 | E_err:   0.010963
[2025-10-23 04:11:35] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -64.577842 | E_var:     0.5713 | E_err:   0.011810
[2025-10-23 04:11:48] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -64.560892 | E_var:     0.4003 | E_err:   0.009885
[2025-10-23 04:12:01] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -64.582919 | E_var:     0.4059 | E_err:   0.009954
[2025-10-23 04:12:15] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -64.594115 | E_var:     0.4365 | E_err:   0.010324
[2025-10-23 04:12:28] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -64.591835 | E_var:     0.4947 | E_err:   0.010990
[2025-10-23 04:12:41] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -64.582854 | E_var:     0.3894 | E_err:   0.009750
[2025-10-23 04:12:54] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -64.578830 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 04:13:08] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -64.583264 | E_var:     0.4157 | E_err:   0.010074
[2025-10-23 04:13:21] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -64.584691 | E_var:     0.4057 | E_err:   0.009953
[2025-10-23 04:13:34] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -64.587068 | E_var:     0.3979 | E_err:   0.009856
[2025-10-23 04:13:47] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -64.564085 | E_var:     0.4883 | E_err:   0.010918
[2025-10-23 04:14:00] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -64.572626 | E_var:     0.4105 | E_err:   0.010011
[2025-10-23 04:14:14] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -64.594358 | E_var:     0.4800 | E_err:   0.010825
[2025-10-23 04:14:27] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -64.581319 | E_var:     0.4521 | E_err:   0.010506
[2025-10-23 04:14:40] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -64.584280 | E_var:     0.3911 | E_err:   0.009772
[2025-10-23 04:14:53] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -64.590974 | E_var:     0.3453 | E_err:   0.009182
[2025-10-23 04:15:07] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -64.588082 | E_var:     0.4118 | E_err:   0.010026
[2025-10-23 04:15:20] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -64.588629 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 04:15:33] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -64.584616 | E_var:     0.3443 | E_err:   0.009168
[2025-10-23 04:15:46] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -64.572325 | E_var:     0.4566 | E_err:   0.010558
[2025-10-23 04:16:00] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -64.573052 | E_var:     0.7351 | E_err:   0.013396
[2025-10-23 04:16:13] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -64.586551 | E_var:     0.3814 | E_err:   0.009650
[2025-10-23 04:16:26] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -64.583310 | E_var:     0.4312 | E_err:   0.010260
[2025-10-23 04:16:39] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -64.588768 | E_var:     0.4108 | E_err:   0.010014
[2025-10-23 04:16:52] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -64.572328 | E_var:     0.5098 | E_err:   0.011157
[2025-10-23 04:17:06] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -64.583132 | E_var:     0.4126 | E_err:   0.010037
[2025-10-23 04:17:19] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -64.575676 | E_var:     0.4279 | E_err:   0.010221
[2025-10-23 04:17:32] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -64.566750 | E_var:     0.4105 | E_err:   0.010011
[2025-10-23 04:17:45] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -64.577583 | E_var:     0.6848 | E_err:   0.012930
[2025-10-23 04:17:59] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -64.564630 | E_var:     0.4462 | E_err:   0.010437
[2025-10-23 04:18:12] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -64.581551 | E_var:     0.5039 | E_err:   0.011091
[2025-10-23 04:18:25] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -64.588179 | E_var:     0.5368 | E_err:   0.011448
[2025-10-23 04:18:38] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -64.583743 | E_var:     0.4455 | E_err:   0.010429
[2025-10-23 04:18:51] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -64.606039 | E_var:     0.7075 | E_err:   0.013143
[2025-10-23 04:19:05] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -64.585252 | E_var:     0.4269 | E_err:   0.010209
[2025-10-23 04:19:18] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -64.572523 | E_var:     0.4330 | E_err:   0.010282
[2025-10-23 04:19:31] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -64.584146 | E_var:     0.4836 | E_err:   0.010866
[2025-10-23 04:19:44] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -64.572293 | E_var:     0.4153 | E_err:   0.010069
[2025-10-23 04:19:58] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -64.586175 | E_var:     0.4232 | E_err:   0.010165
[2025-10-23 04:20:11] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -64.552590 | E_var:     0.4264 | E_err:   0.010203
[2025-10-23 04:20:24] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -64.590847 | E_var:     0.4687 | E_err:   0.010697
[2025-10-23 04:20:37] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -64.585996 | E_var:     0.4608 | E_err:   0.010607
[2025-10-23 04:20:50] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -64.591627 | E_var:     0.5381 | E_err:   0.011462
[2025-10-23 04:21:04] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -64.571635 | E_var:     0.4545 | E_err:   0.010534
[2025-10-23 04:21:17] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -64.582458 | E_var:     0.4966 | E_err:   0.011011
[2025-10-23 04:21:30] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -64.594361 | E_var:     0.4344 | E_err:   0.010299
[2025-10-23 04:21:43] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -64.589573 | E_var:     0.4120 | E_err:   0.010030
[2025-10-23 04:21:57] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -64.566931 | E_var:     0.8104 | E_err:   0.014066
[2025-10-23 04:22:10] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -64.567025 | E_var:     0.4467 | E_err:   0.010443
[2025-10-23 04:22:23] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -64.575810 | E_var:     0.4309 | E_err:   0.010257
[2025-10-23 04:22:36] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -64.579719 | E_var:     0.5229 | E_err:   0.011298
[2025-10-23 04:22:49] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -64.582342 | E_var:     0.3801 | E_err:   0.009633
[2025-10-23 04:23:03] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -64.582071 | E_var:     0.4372 | E_err:   0.010332
[2025-10-23 04:23:16] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -64.588753 | E_var:     0.3831 | E_err:   0.009671
[2025-10-23 04:23:29] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -64.586089 | E_var:     0.4108 | E_err:   0.010014
[2025-10-23 04:23:42] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -64.580707 | E_var:     0.4051 | E_err:   0.009945
[2025-10-23 04:23:56] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -64.579126 | E_var:     0.4115 | E_err:   0.010023
[2025-10-23 04:24:09] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -64.567072 | E_var:     0.5091 | E_err:   0.011148
[2025-10-23 04:24:22] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -64.574697 | E_var:     0.4228 | E_err:   0.010160
[2025-10-23 04:24:35] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -64.580270 | E_var:     0.3755 | E_err:   0.009574
[2025-10-23 04:24:48] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -64.597190 | E_var:     0.5074 | E_err:   0.011130
[2025-10-23 04:25:02] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -64.586381 | E_var:     0.5009 | E_err:   0.011058
[2025-10-23 04:25:15] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -64.582294 | E_var:     0.4467 | E_err:   0.010443
[2025-10-23 04:25:28] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -64.585266 | E_var:     0.4858 | E_err:   0.010891
[2025-10-23 04:25:41] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -64.578524 | E_var:     0.4100 | E_err:   0.010004
[2025-10-23 04:25:55] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -64.588642 | E_var:     0.3928 | E_err:   0.009792
[2025-10-23 04:26:08] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -64.598709 | E_var:     0.3859 | E_err:   0.009706
[2025-10-23 04:26:21] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -64.586768 | E_var:     0.3968 | E_err:   0.009842
[2025-10-23 04:26:34] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -64.582330 | E_var:     0.4291 | E_err:   0.010235
[2025-10-23 04:26:48] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -64.589000 | E_var:     0.4491 | E_err:   0.010471
[2025-10-23 04:27:01] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -64.587366 | E_var:     0.4981 | E_err:   0.011028
[2025-10-23 04:27:14] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -64.597683 | E_var:     0.4294 | E_err:   0.010239
[2025-10-23 04:27:27] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -64.590745 | E_var:     0.4040 | E_err:   0.009931
[2025-10-23 04:27:40] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -64.583171 | E_var:     0.4279 | E_err:   0.010221
[2025-10-23 04:27:54] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -64.574816 | E_var:     0.4253 | E_err:   0.010190
[2025-10-23 04:28:07] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -64.583774 | E_var:     0.5108 | E_err:   0.011167
[2025-10-23 04:28:20] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -64.586263 | E_var:     0.4842 | E_err:   0.010873
[2025-10-23 04:28:33] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -64.576537 | E_var:     0.5644 | E_err:   0.011739
[2025-10-23 04:28:46] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -64.580465 | E_var:     0.4563 | E_err:   0.010554
[2025-10-23 04:29:00] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -64.576298 | E_var:     0.5760 | E_err:   0.011858
[2025-10-23 04:29:13] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -64.581443 | E_var:     0.4018 | E_err:   0.009904
[2025-10-23 04:29:26] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -64.584723 | E_var:     0.4324 | E_err:   0.010274
[2025-10-23 04:29:39] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -64.573192 | E_var:     0.4579 | E_err:   0.010573
[2025-10-23 04:29:53] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -64.592378 | E_var:     0.4703 | E_err:   0.010716
[2025-10-23 04:30:06] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -64.585377 | E_var:     0.4493 | E_err:   0.010473
[2025-10-23 04:30:19] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -64.603891 | E_var:     0.4330 | E_err:   0.010282
[2025-10-23 04:30:32] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -64.593615 | E_var:     0.4560 | E_err:   0.010552
[2025-10-23 04:30:32] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-23 04:30:46] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -64.575310 | E_var:     0.4247 | E_err:   0.010182
[2025-10-23 04:30:59] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -64.595701 | E_var:     0.4710 | E_err:   0.010723
[2025-10-23 04:31:12] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -64.559868 | E_var:     0.4469 | E_err:   0.010446
[2025-10-23 04:31:25] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -64.591087 | E_var:     0.4186 | E_err:   0.010109
[2025-10-23 04:31:38] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -64.587027 | E_var:     0.5569 | E_err:   0.011660
[2025-10-23 04:31:52] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -64.565005 | E_var:     0.4749 | E_err:   0.010768
[2025-10-23 04:32:05] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -64.597704 | E_var:     0.4142 | E_err:   0.010056
[2025-10-23 04:32:18] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -64.581192 | E_var:     0.5202 | E_err:   0.011270
[2025-10-23 04:32:31] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -64.578446 | E_var:     0.4581 | E_err:   0.010575
[2025-10-23 04:32:45] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -64.592824 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 04:32:58] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -64.584744 | E_var:     0.5264 | E_err:   0.011337
[2025-10-23 04:33:11] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -64.595665 | E_var:     0.4276 | E_err:   0.010217
[2025-10-23 04:33:24] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -64.574623 | E_var:     0.4810 | E_err:   0.010837
[2025-10-23 04:33:37] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -64.584280 | E_var:     0.4483 | E_err:   0.010462
[2025-10-23 04:33:51] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -64.587780 | E_var:     0.4136 | E_err:   0.010049
[2025-10-23 04:34:04] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -64.581949 | E_var:     0.4418 | E_err:   0.010386
[2025-10-23 04:34:17] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -64.591374 | E_var:     0.3609 | E_err:   0.009387
[2025-10-23 04:34:30] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -64.562776 | E_var:     0.8244 | E_err:   0.014187
[2025-10-23 04:34:44] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -64.598332 | E_var:     0.5383 | E_err:   0.011464
[2025-10-23 04:34:57] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -64.579102 | E_var:     0.3908 | E_err:   0.009768
[2025-10-23 04:35:10] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -64.571956 | E_var:     0.4691 | E_err:   0.010702
[2025-10-23 04:35:23] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -64.573243 | E_var:     0.4645 | E_err:   0.010649
[2025-10-23 04:35:36] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -64.598196 | E_var:     0.4257 | E_err:   0.010195
[2025-10-23 04:35:50] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -64.580195 | E_var:     0.3864 | E_err:   0.009713
[2025-10-23 04:36:03] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -64.597299 | E_var:     0.4470 | E_err:   0.010446
[2025-10-23 04:36:16] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -64.589347 | E_var:     0.4569 | E_err:   0.010562
[2025-10-23 04:36:29] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -64.584471 | E_var:     0.4359 | E_err:   0.010316
[2025-10-23 04:36:43] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -64.589875 | E_var:     0.4336 | E_err:   0.010289
[2025-10-23 04:36:56] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -64.575226 | E_var:     0.4641 | E_err:   0.010645
[2025-10-23 04:37:09] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -64.567454 | E_var:     0.4382 | E_err:   0.010344
[2025-10-23 04:37:22] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -64.574272 | E_var:     0.4513 | E_err:   0.010497
[2025-10-23 04:37:35] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -64.586874 | E_var:     0.4584 | E_err:   0.010579
[2025-10-23 04:37:49] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -64.595935 | E_var:     0.4533 | E_err:   0.010519
[2025-10-23 04:38:02] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -64.601469 | E_var:     0.3899 | E_err:   0.009757
[2025-10-23 04:38:15] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -64.587725 | E_var:     0.4022 | E_err:   0.009910
[2025-10-23 04:38:28] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -64.578484 | E_var:     0.4677 | E_err:   0.010685
[2025-10-23 04:38:42] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -64.575626 | E_var:     0.3938 | E_err:   0.009806
[2025-10-23 04:38:55] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -64.602476 | E_var:     0.4431 | E_err:   0.010401
[2025-10-23 04:39:08] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -64.575967 | E_var:     0.4126 | E_err:   0.010037
[2025-10-23 04:39:21] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -64.572700 | E_var:     0.4267 | E_err:   0.010207
[2025-10-23 04:39:34] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -64.575729 | E_var:     0.4262 | E_err:   0.010200
[2025-10-23 04:39:48] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -64.589876 | E_var:     0.5199 | E_err:   0.011266
[2025-10-23 04:40:01] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -64.586595 | E_var:     0.4650 | E_err:   0.010655
[2025-10-23 04:40:14] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -64.579476 | E_var:     0.4476 | E_err:   0.010454
[2025-10-23 04:40:27] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -64.587719 | E_var:     0.5118 | E_err:   0.011178
[2025-10-23 04:40:41] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -64.578387 | E_var:     0.4311 | E_err:   0.010259
[2025-10-23 04:40:54] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -64.578306 | E_var:     0.4495 | E_err:   0.010476
[2025-10-23 04:41:07] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -64.594847 | E_var:     0.5123 | E_err:   0.011183
[2025-10-23 04:41:20] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -64.578747 | E_var:     0.4387 | E_err:   0.010349
[2025-10-23 04:41:33] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -64.580146 | E_var:     0.4024 | E_err:   0.009912
[2025-10-23 04:41:47] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -64.572653 | E_var:     0.4607 | E_err:   0.010605
[2025-10-23 04:42:00] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -64.569974 | E_var:     0.5547 | E_err:   0.011637
[2025-10-23 04:42:13] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -64.570673 | E_var:     0.4874 | E_err:   0.010909
[2025-10-23 04:42:26] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -64.580339 | E_var:     0.5822 | E_err:   0.011922
[2025-10-23 04:42:40] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -64.589169 | E_var:     0.4558 | E_err:   0.010549
[2025-10-23 04:42:53] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -64.580894 | E_var:     0.4060 | E_err:   0.009956
[2025-10-23 04:43:06] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -64.595860 | E_var:     0.4237 | E_err:   0.010171
[2025-10-23 04:43:19] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -64.582606 | E_var:     0.5216 | E_err:   0.011285
[2025-10-23 04:43:32] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -64.576859 | E_var:     0.4550 | E_err:   0.010539
[2025-10-23 04:43:46] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -64.581488 | E_var:     0.3972 | E_err:   0.009848
[2025-10-23 04:43:59] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -64.573447 | E_var:     0.3694 | E_err:   0.009497
[2025-10-23 04:44:12] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -64.583092 | E_var:     0.3926 | E_err:   0.009790
[2025-10-23 04:44:25] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -64.589084 | E_var:     0.4485 | E_err:   0.010464
[2025-10-23 04:44:39] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -64.580222 | E_var:     0.4301 | E_err:   0.010247
[2025-10-23 04:44:52] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -64.559130 | E_var:     1.1040 | E_err:   0.016417
[2025-10-23 04:45:05] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -64.565540 | E_var:     0.7090 | E_err:   0.013157
[2025-10-23 04:45:18] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -64.582477 | E_var:     0.4025 | E_err:   0.009913
[2025-10-23 04:45:32] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -64.584665 | E_var:     0.5526 | E_err:   0.011615
[2025-10-23 04:45:45] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -64.592975 | E_var:     0.4078 | E_err:   0.009978
[2025-10-23 04:45:58] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -64.582712 | E_var:     0.3835 | E_err:   0.009676
[2025-10-23 04:46:11] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -64.587310 | E_var:     0.5826 | E_err:   0.011926
[2025-10-23 04:46:24] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -64.577448 | E_var:     0.4073 | E_err:   0.009972
[2025-10-23 04:46:38] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -64.584045 | E_var:     0.4012 | E_err:   0.009897
[2025-10-23 04:46:51] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -64.578391 | E_var:     0.4275 | E_err:   0.010217
[2025-10-23 04:47:04] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -64.581339 | E_var:     0.4285 | E_err:   0.010228
[2025-10-23 04:47:17] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -64.585309 | E_var:     0.4180 | E_err:   0.010102
[2025-10-23 04:47:31] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -64.580035 | E_var:     0.3995 | E_err:   0.009876
[2025-10-23 04:47:44] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -64.574477 | E_var:     0.3962 | E_err:   0.009835
[2025-10-23 04:47:57] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -64.577559 | E_var:     0.4017 | E_err:   0.009903
[2025-10-23 04:48:10] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -64.581158 | E_var:     0.5876 | E_err:   0.011977
[2025-10-23 04:48:23] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -64.580130 | E_var:     0.4436 | E_err:   0.010407
[2025-10-23 04:48:37] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -64.589009 | E_var:     0.4793 | E_err:   0.010818
[2025-10-23 04:48:50] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -64.592213 | E_var:     0.4796 | E_err:   0.010821
[2025-10-23 04:49:03] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -64.596987 | E_var:     0.4246 | E_err:   0.010181
[2025-10-23 04:49:16] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -64.581939 | E_var:     0.4459 | E_err:   0.010434
[2025-10-23 04:49:30] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -64.566312 | E_var:     0.4539 | E_err:   0.010527
[2025-10-23 04:49:43] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -64.568438 | E_var:     0.3618 | E_err:   0.009399
[2025-10-23 04:49:56] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -64.572000 | E_var:     0.3658 | E_err:   0.009450
[2025-10-23 04:50:09] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -64.591607 | E_var:     0.4002 | E_err:   0.009885
[2025-10-23 04:50:22] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -64.592062 | E_var:     0.4595 | E_err:   0.010592
[2025-10-23 04:50:36] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -64.583004 | E_var:     0.4879 | E_err:   0.010914
[2025-10-23 04:50:49] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -64.570893 | E_var:     0.4043 | E_err:   0.009936
[2025-10-23 04:51:02] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -64.571267 | E_var:     0.4914 | E_err:   0.010953
[2025-10-23 04:51:15] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -64.578391 | E_var:     0.4192 | E_err:   0.010116
[2025-10-23 04:51:29] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -64.573434 | E_var:     0.4396 | E_err:   0.010359
[2025-10-23 04:51:42] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -64.582178 | E_var:     0.4450 | E_err:   0.010423
[2025-10-23 04:51:55] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -64.579749 | E_var:     0.4037 | E_err:   0.009928
[2025-10-23 04:52:08] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -64.603450 | E_var:     0.4478 | E_err:   0.010456
[2025-10-23 04:52:22] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -64.576248 | E_var:     0.3427 | E_err:   0.009147
[2025-10-23 04:52:35] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -64.587215 | E_var:     0.4324 | E_err:   0.010275
[2025-10-23 04:52:48] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -64.588168 | E_var:     0.5897 | E_err:   0.011999
[2025-10-23 04:53:01] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -64.579276 | E_var:     0.5437 | E_err:   0.011521
[2025-10-23 04:53:14] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -64.580414 | E_var:     0.4104 | E_err:   0.010009
[2025-10-23 04:53:28] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -64.575174 | E_var:     0.4411 | E_err:   0.010378
[2025-10-23 04:53:41] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -64.595948 | E_var:     0.3695 | E_err:   0.009498
[2025-10-23 04:53:41] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-23 04:53:41] ======================================================================================================
[2025-10-23 04:53:41] ✅ Training completed successfully
[2025-10-23 04:53:41] Total restarts: 2
[2025-10-23 04:53:45] Final Energy: -64.59594819 ± 0.00949790
[2025-10-23 04:53:45] Final Variance: 0.369501
[2025-10-23 04:53:45] ======================================================================================================
[2025-10-23 04:53:45] ======================================================================================================
[2025-10-23 04:53:45] Training completed | Runtime: 13953.6s
