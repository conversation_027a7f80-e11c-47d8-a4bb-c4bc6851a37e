[2025-10-22 21:08:24] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-22 21:08:24]   - 迭代次数: 1050
[2025-10-22 21:08:24]   - 能量: -62.735420-0.003897j ± 0.011241, Var: 0.517557
[2025-10-22 21:08:24]   - 时间戳: 2025-10-22T21:08:00.399045+08:00
[2025-10-22 21:08:49] ✓ 变分状态参数已从checkpoint恢复
[2025-10-22 21:08:49] ======================================================================================================
[2025-10-22 21:08:49] GCNN for Shastry-Sutherland Model
[2025-10-22 21:08:49] ======================================================================================================
[2025-10-22 21:08:49] System parameters:
[2025-10-22 21:08:49]   - System size: L=6, N=144
[2025-10-22 21:08:49]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-10-22 21:08:49] ------------------------------------------------------------------------------------------------------
[2025-10-22 21:08:49] Model parameters:
[2025-10-22 21:08:49]   - Number of layers = 4
[2025-10-22 21:08:49]   - Number of features = 4
[2025-10-22 21:08:49]   - Total parameters = 28252
[2025-10-22 21:08:49] ------------------------------------------------------------------------------------------------------
[2025-10-22 21:08:49] Training parameters:
[2025-10-22 21:08:49]   - Total iterations: 1050
[2025-10-22 21:08:49]   - Annealing cycles: 3
[2025-10-22 21:08:49]   - Initial period: 150
[2025-10-22 21:08:49]   - Period multiplier: 2.0
[2025-10-22 21:08:49]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-22 21:08:49]   - Samples: 4096
[2025-10-22 21:08:49]   - Discarded samples: 0
[2025-10-22 21:08:49]   - Chunk size: 4096
[2025-10-22 21:08:49]   - Diagonal shift: 0.15
[2025-10-22 21:08:49]   - Gradient clipping: 1.0
[2025-10-22 21:08:49]   - Checkpoint enabled: interval=105
[2025-10-22 21:08:49]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.79/training/checkpoints
[2025-10-22 21:08:49]   - Resuming from iteration: 1050
[2025-10-22 21:08:49] ------------------------------------------------------------------------------------------------------
[2025-10-22 21:08:49] Device status:
[2025-10-22 21:08:49]   - Devices model: NVIDIA H200 NVL
[2025-10-22 21:08:49]   - Number of devices: 1
[2025-10-22 21:08:49]   - Sharding: True
[2025-10-22 21:08:49] ======================================================================================================
[2025-10-22 21:09:40] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -63.680433 | E_var:     1.5554 | E_err:   0.019487
[2025-10-22 21:10:14] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -63.662031 | E_var:     0.8893 | E_err:   0.014735
[2025-10-22 21:10:27] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -63.640215 | E_var:     0.7171 | E_err:   0.013232
[2025-10-22 21:10:40] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -63.661187 | E_var:     0.5926 | E_err:   0.012029
[2025-10-22 21:10:53] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -63.646359 | E_var:     0.6607 | E_err:   0.012700
[2025-10-22 21:11:06] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -63.657642 | E_var:     0.5755 | E_err:   0.011854
[2025-10-22 21:11:20] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -63.669039 | E_var:     0.6794 | E_err:   0.012879
[2025-10-22 21:11:33] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -63.666584 | E_var:     0.5935 | E_err:   0.012037
[2025-10-22 21:11:46] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -63.654904 | E_var:     0.5365 | E_err:   0.011445
[2025-10-22 21:11:59] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -63.652012 | E_var:     0.6410 | E_err:   0.012510
[2025-10-22 21:12:12] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -63.660750 | E_var:     0.5512 | E_err:   0.011601
[2025-10-22 21:12:25] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -63.678793 | E_var:     0.4858 | E_err:   0.010891
[2025-10-22 21:12:39] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -63.651175 | E_var:     0.5177 | E_err:   0.011242
[2025-10-22 21:12:52] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -63.643185 | E_var:     0.5852 | E_err:   0.011953
[2025-10-22 21:13:05] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -63.648717 | E_var:     0.4952 | E_err:   0.010996
[2025-10-22 21:13:18] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -63.654305 | E_var:     0.4863 | E_err:   0.010896
[2025-10-22 21:13:31] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -63.672235 | E_var:     0.5912 | E_err:   0.012014
[2025-10-22 21:13:44] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -63.669245 | E_var:     0.5505 | E_err:   0.011593
[2025-10-22 21:13:58] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -63.668210 | E_var:     0.4216 | E_err:   0.010146
[2025-10-22 21:14:11] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -63.646610 | E_var:     0.7317 | E_err:   0.013365
[2025-10-22 21:14:24] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -63.671700 | E_var:     0.4585 | E_err:   0.010580
[2025-10-22 21:14:37] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -63.659877 | E_var:     0.5721 | E_err:   0.011818
[2025-10-22 21:14:50] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -63.656049 | E_var:     0.5709 | E_err:   0.011805
[2025-10-22 21:15:03] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -63.638453 | E_var:     0.5164 | E_err:   0.011229
[2025-10-22 21:15:17] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -63.667336 | E_var:     0.5070 | E_err:   0.011126
[2025-10-22 21:15:30] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -63.660039 | E_var:     0.4405 | E_err:   0.010370
[2025-10-22 21:15:43] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -63.665143 | E_var:     0.4733 | E_err:   0.010750
[2025-10-22 21:15:56] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -63.651931 | E_var:     0.5169 | E_err:   0.011233
[2025-10-22 21:16:09] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -63.646698 | E_var:     0.5208 | E_err:   0.011277
[2025-10-22 21:16:22] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -63.656598 | E_var:     0.5390 | E_err:   0.011471
[2025-10-22 21:16:36] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -63.660194 | E_var:     0.8130 | E_err:   0.014089
[2025-10-22 21:16:49] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -63.655134 | E_var:     0.4791 | E_err:   0.010815
[2025-10-22 21:17:02] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -63.661838 | E_var:     0.5119 | E_err:   0.011179
[2025-10-22 21:17:15] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -63.651142 | E_var:     0.5387 | E_err:   0.011468
[2025-10-22 21:17:28] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -63.660488 | E_var:     0.4857 | E_err:   0.010890
[2025-10-22 21:17:41] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -63.655349 | E_var:     0.4609 | E_err:   0.010607
[2025-10-22 21:17:55] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -63.640192 | E_var:     0.5459 | E_err:   0.011544
[2025-10-22 21:18:08] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -63.668240 | E_var:     0.6492 | E_err:   0.012589
[2025-10-22 21:18:21] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -63.664954 | E_var:     0.4609 | E_err:   0.010608
[2025-10-22 21:18:34] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -63.664409 | E_var:     0.6137 | E_err:   0.012240
[2025-10-22 21:18:47] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -63.633637 | E_var:     0.4913 | E_err:   0.010952
[2025-10-22 21:19:00] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -63.679494 | E_var:     0.5030 | E_err:   0.011081
[2025-10-22 21:19:14] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -63.653504 | E_var:     0.4862 | E_err:   0.010895
[2025-10-22 21:19:27] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -63.649920 | E_var:     0.5195 | E_err:   0.011262
[2025-10-22 21:19:40] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -63.662626 | E_var:     0.5177 | E_err:   0.011243
[2025-10-22 21:19:53] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -63.661396 | E_var:     0.5380 | E_err:   0.011461
[2025-10-22 21:20:06] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -63.663245 | E_var:     0.4777 | E_err:   0.010799
[2025-10-22 21:20:19] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -63.665736 | E_var:     0.4707 | E_err:   0.010719
[2025-10-22 21:20:33] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -63.654228 | E_var:     0.4906 | E_err:   0.010944
[2025-10-22 21:20:46] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -63.656101 | E_var:     0.4476 | E_err:   0.010453
[2025-10-22 21:20:59] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -63.668418 | E_var:     0.4364 | E_err:   0.010322
[2025-10-22 21:21:12] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -63.645275 | E_var:     0.6256 | E_err:   0.012358
[2025-10-22 21:21:25] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -63.661247 | E_var:     0.4568 | E_err:   0.010561
[2025-10-22 21:21:39] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -63.660006 | E_var:     0.4972 | E_err:   0.011018
[2025-10-22 21:21:52] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -63.652361 | E_var:     0.4453 | E_err:   0.010427
[2025-10-22 21:22:05] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -63.656183 | E_var:     0.4468 | E_err:   0.010444
[2025-10-22 21:22:18] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -63.657835 | E_var:     0.5184 | E_err:   0.011250
[2025-10-22 21:22:31] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -63.651206 | E_var:     0.5796 | E_err:   0.011896
[2025-10-22 21:22:44] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -63.631572 | E_var:     0.4723 | E_err:   0.010738
[2025-10-22 21:22:58] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -63.644101 | E_var:     0.7097 | E_err:   0.013163
[2025-10-22 21:23:11] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -63.651242 | E_var:     0.4173 | E_err:   0.010093
[2025-10-22 21:23:24] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -63.678431 | E_var:     0.5137 | E_err:   0.011199
[2025-10-22 21:23:37] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -63.646607 | E_var:     0.4913 | E_err:   0.010953
[2025-10-22 21:23:50] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -63.650481 | E_var:     0.5909 | E_err:   0.012011
[2025-10-22 21:24:03] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -63.662368 | E_var:     0.4860 | E_err:   0.010893
[2025-10-22 21:24:17] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -63.669404 | E_var:     0.5608 | E_err:   0.011701
[2025-10-22 21:24:30] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -63.656935 | E_var:     0.4792 | E_err:   0.010816
[2025-10-22 21:24:43] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -63.663258 | E_var:     0.4629 | E_err:   0.010631
[2025-10-22 21:24:56] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -63.655148 | E_var:     0.5049 | E_err:   0.011102
[2025-10-22 21:25:09] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -63.640479 | E_var:     0.4143 | E_err:   0.010057
[2025-10-22 21:25:22] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -63.656301 | E_var:     0.5455 | E_err:   0.011540
[2025-10-22 21:25:36] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -63.638454 | E_var:     0.6300 | E_err:   0.012402
[2025-10-22 21:25:49] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -63.663087 | E_var:     0.4723 | E_err:   0.010739
[2025-10-22 21:26:02] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -63.659523 | E_var:     0.4663 | E_err:   0.010670
[2025-10-22 21:26:15] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -63.670042 | E_var:     0.6811 | E_err:   0.012895
[2025-10-22 21:26:28] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -63.673042 | E_var:     0.6170 | E_err:   0.012274
[2025-10-22 21:26:41] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -63.656715 | E_var:     0.5221 | E_err:   0.011290
[2025-10-22 21:26:55] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -63.664281 | E_var:     0.4788 | E_err:   0.010812
[2025-10-22 21:27:08] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -63.664220 | E_var:     0.4906 | E_err:   0.010944
[2025-10-22 21:27:21] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -63.660328 | E_var:     0.4740 | E_err:   0.010758
[2025-10-22 21:27:34] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -63.669320 | E_var:     0.5298 | E_err:   0.011373
[2025-10-22 21:27:47] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -63.651053 | E_var:     0.5126 | E_err:   0.011187
[2025-10-22 21:28:00] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -63.657762 | E_var:     0.4908 | E_err:   0.010946
[2025-10-22 21:28:14] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -63.660552 | E_var:     0.4054 | E_err:   0.009949
[2025-10-22 21:28:27] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -63.647776 | E_var:     0.4907 | E_err:   0.010945
[2025-10-22 21:28:40] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -63.652054 | E_var:     0.4814 | E_err:   0.010842
[2025-10-22 21:28:53] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -63.649722 | E_var:     0.4319 | E_err:   0.010268
[2025-10-22 21:29:06] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -63.665270 | E_var:     0.4413 | E_err:   0.010380
[2025-10-22 21:29:19] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -63.653803 | E_var:     0.5673 | E_err:   0.011769
[2025-10-22 21:29:33] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -63.664718 | E_var:     0.4216 | E_err:   0.010146
[2025-10-22 21:29:46] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -63.645922 | E_var:     0.5006 | E_err:   0.011055
[2025-10-22 21:29:59] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -63.669747 | E_var:     0.5174 | E_err:   0.011239
[2025-10-22 21:30:12] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -63.638432 | E_var:     0.5506 | E_err:   0.011594
[2025-10-22 21:30:25] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -63.653063 | E_var:     0.4915 | E_err:   0.010954
[2025-10-22 21:30:38] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -63.662558 | E_var:     0.4136 | E_err:   0.010049
[2025-10-22 21:30:52] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -63.666108 | E_var:     0.5129 | E_err:   0.011190
[2025-10-22 21:31:05] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -63.657605 | E_var:     0.4589 | E_err:   0.010585
[2025-10-22 21:31:18] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -63.639588 | E_var:     0.5390 | E_err:   0.011471
[2025-10-22 21:31:31] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -63.654989 | E_var:     0.5298 | E_err:   0.011373
[2025-10-22 21:31:44] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -63.645856 | E_var:     0.4822 | E_err:   0.010850
[2025-10-22 21:31:57] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -63.641412 | E_var:     0.7636 | E_err:   0.013654
[2025-10-22 21:32:11] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -63.662556 | E_var:     0.4133 | E_err:   0.010045
[2025-10-22 21:32:24] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -63.672138 | E_var:     0.4249 | E_err:   0.010185
[2025-10-22 21:32:37] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -63.660509 | E_var:     0.4606 | E_err:   0.010604
[2025-10-22 21:32:50] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -63.669815 | E_var:     0.4767 | E_err:   0.010788
[2025-10-22 21:32:50] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-22 21:33:03] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -63.657690 | E_var:     0.5205 | E_err:   0.011273
[2025-10-22 21:33:16] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -63.658131 | E_var:     0.4964 | E_err:   0.011008
[2025-10-22 21:33:30] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -63.671046 | E_var:     0.4452 | E_err:   0.010426
[2025-10-22 21:33:43] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -63.664590 | E_var:     0.5395 | E_err:   0.011477
[2025-10-22 21:33:56] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -63.660381 | E_var:     0.4838 | E_err:   0.010868
[2025-10-22 21:34:09] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -63.683453 | E_var:     0.5211 | E_err:   0.011279
[2025-10-22 21:34:22] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -63.640275 | E_var:     0.5242 | E_err:   0.011313
[2025-10-22 21:34:35] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -63.676892 | E_var:     1.9475 | E_err:   0.021805
[2025-10-22 21:34:49] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -63.669716 | E_var:     0.5831 | E_err:   0.011931
[2025-10-22 21:35:02] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -63.659633 | E_var:     0.5531 | E_err:   0.011620
[2025-10-22 21:35:15] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -63.669858 | E_var:     0.3860 | E_err:   0.009708
[2025-10-22 21:35:28] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -63.651640 | E_var:     0.4864 | E_err:   0.010897
[2025-10-22 21:35:41] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -63.660257 | E_var:     0.4990 | E_err:   0.011037
[2025-10-22 21:35:55] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -63.649115 | E_var:     0.4701 | E_err:   0.010713
[2025-10-22 21:36:08] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -63.662733 | E_var:     0.6151 | E_err:   0.012254
[2025-10-22 21:36:21] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -63.666759 | E_var:     0.5194 | E_err:   0.011261
[2025-10-22 21:36:34] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -63.687073 | E_var:     0.4961 | E_err:   0.011005
[2025-10-22 21:36:47] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -63.664067 | E_var:     0.4113 | E_err:   0.010021
[2025-10-22 21:37:00] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -63.652612 | E_var:     0.4191 | E_err:   0.010115
[2025-10-22 21:37:14] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -63.655188 | E_var:     0.4552 | E_err:   0.010541
[2025-10-22 21:37:27] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -63.665943 | E_var:     0.4378 | E_err:   0.010338
[2025-10-22 21:37:40] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -63.668547 | E_var:     0.5335 | E_err:   0.011412
[2025-10-22 21:37:53] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -63.658739 | E_var:     1.0944 | E_err:   0.016346
[2025-10-22 21:38:06] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -63.641207 | E_var:     0.5273 | E_err:   0.011346
[2025-10-22 21:38:19] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -63.665703 | E_var:     0.4444 | E_err:   0.010417
[2025-10-22 21:38:33] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -63.676114 | E_var:     0.4628 | E_err:   0.010630
[2025-10-22 21:38:46] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -63.663652 | E_var:     0.5404 | E_err:   0.011486
[2025-10-22 21:38:59] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -63.645815 | E_var:     0.6266 | E_err:   0.012368
[2025-10-22 21:39:12] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -63.662816 | E_var:     0.6402 | E_err:   0.012502
[2025-10-22 21:39:25] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -63.656922 | E_var:     0.5494 | E_err:   0.011582
[2025-10-22 21:39:38] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -63.655450 | E_var:     0.4330 | E_err:   0.010282
[2025-10-22 21:39:52] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -63.660031 | E_var:     0.5018 | E_err:   0.011068
[2025-10-22 21:40:05] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -63.665634 | E_var:     0.5862 | E_err:   0.011963
[2025-10-22 21:40:18] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -63.651877 | E_var:     0.4231 | E_err:   0.010164
[2025-10-22 21:40:31] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -63.668835 | E_var:     0.4876 | E_err:   0.010911
[2025-10-22 21:40:44] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -63.672725 | E_var:     0.5168 | E_err:   0.011232
[2025-10-22 21:40:57] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -63.663118 | E_var:     0.4402 | E_err:   0.010367
[2025-10-22 21:41:11] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -63.675301 | E_var:     0.4859 | E_err:   0.010891
[2025-10-22 21:41:24] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -63.665682 | E_var:     0.5031 | E_err:   0.011082
[2025-10-22 21:41:37] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -63.659820 | E_var:     0.4859 | E_err:   0.010892
[2025-10-22 21:41:50] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -63.660224 | E_var:     0.5133 | E_err:   0.011194
[2025-10-22 21:42:03] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -63.662128 | E_var:     0.6363 | E_err:   0.012464
[2025-10-22 21:42:16] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -63.666379 | E_var:     0.4112 | E_err:   0.010020
[2025-10-22 21:42:30] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -63.654610 | E_var:     0.5008 | E_err:   0.011057
[2025-10-22 21:42:43] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -63.664651 | E_var:     0.5165 | E_err:   0.011229
[2025-10-22 21:42:43] 🔄 RESTART #1 | Period: 300
[2025-10-22 21:42:56] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -63.662120 | E_var:     0.4666 | E_err:   0.010673
[2025-10-22 21:43:09] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -63.672211 | E_var:     0.5699 | E_err:   0.011795
[2025-10-22 21:43:22] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -63.632351 | E_var:     0.7198 | E_err:   0.013256
[2025-10-22 21:43:35] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -63.648734 | E_var:     0.4901 | E_err:   0.010938
[2025-10-22 21:43:49] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -63.645201 | E_var:     0.4655 | E_err:   0.010660
[2025-10-22 21:44:02] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -63.640608 | E_var:     0.5377 | E_err:   0.011458
[2025-10-22 21:44:15] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -63.677159 | E_var:     0.5064 | E_err:   0.011119
[2025-10-22 21:44:28] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -63.646919 | E_var:     0.4383 | E_err:   0.010344
[2025-10-22 21:44:41] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -63.652527 | E_var:     0.5549 | E_err:   0.011639
[2025-10-22 21:44:54] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -63.658899 | E_var:     0.5778 | E_err:   0.011877
[2025-10-22 21:45:08] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -63.641529 | E_var:     0.5225 | E_err:   0.011295
[2025-10-22 21:45:21] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -63.659581 | E_var:     0.6009 | E_err:   0.012112
[2025-10-22 21:45:34] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -63.662311 | E_var:     0.5069 | E_err:   0.011125
[2025-10-22 21:45:47] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -63.658487 | E_var:     0.6104 | E_err:   0.012207
[2025-10-22 21:46:00] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -63.660188 | E_var:     0.5921 | E_err:   0.012023
[2025-10-22 21:46:13] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -63.678647 | E_var:     0.4786 | E_err:   0.010810
[2025-10-22 21:46:27] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -63.645309 | E_var:     0.5189 | E_err:   0.011255
[2025-10-22 21:46:40] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -63.653859 | E_var:     0.4933 | E_err:   0.010975
[2025-10-22 21:46:53] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -63.664633 | E_var:     0.4133 | E_err:   0.010045
[2025-10-22 21:47:06] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -63.653674 | E_var:     0.5196 | E_err:   0.011263
[2025-10-22 21:47:20] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -63.654153 | E_var:     0.4376 | E_err:   0.010336
[2025-10-22 21:47:33] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -63.653857 | E_var:     0.4034 | E_err:   0.009924
[2025-10-22 21:47:46] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -63.632475 | E_var:     0.4880 | E_err:   0.010915
[2025-10-22 21:47:59] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -63.654412 | E_var:     0.4728 | E_err:   0.010744
[2025-10-22 21:48:12] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -63.644339 | E_var:     0.4934 | E_err:   0.010976
[2025-10-22 21:48:25] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -63.674290 | E_var:     0.4399 | E_err:   0.010363
[2025-10-22 21:48:39] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -63.653217 | E_var:     0.5037 | E_err:   0.011089
[2025-10-22 21:48:52] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -63.667673 | E_var:     0.4255 | E_err:   0.010192
[2025-10-22 21:49:05] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -63.652141 | E_var:     0.3967 | E_err:   0.009841
[2025-10-22 21:49:18] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -63.661063 | E_var:     0.5904 | E_err:   0.012006
[2025-10-22 21:49:31] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -63.659220 | E_var:     0.4394 | E_err:   0.010357
[2025-10-22 21:49:45] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -63.660574 | E_var:     0.4146 | E_err:   0.010061
[2025-10-22 21:49:58] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -63.666404 | E_var:     0.4100 | E_err:   0.010005
[2025-10-22 21:50:11] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -63.677255 | E_var:     0.4944 | E_err:   0.010986
[2025-10-22 21:50:24] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -63.650547 | E_var:     0.4552 | E_err:   0.010542
[2025-10-22 21:50:37] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -63.664363 | E_var:     0.5231 | E_err:   0.011301
[2025-10-22 21:50:50] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -63.648867 | E_var:     0.4797 | E_err:   0.010821
[2025-10-22 21:51:04] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -63.677626 | E_var:     0.4287 | E_err:   0.010230
[2025-10-22 21:51:17] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -63.667379 | E_var:     0.7694 | E_err:   0.013706
[2025-10-22 21:51:30] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -63.674020 | E_var:     0.4767 | E_err:   0.010789
[2025-10-22 21:51:43] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -63.676638 | E_var:     0.6189 | E_err:   0.012292
[2025-10-22 21:51:56] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -63.664601 | E_var:     0.4926 | E_err:   0.010966
[2025-10-22 21:52:10] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -63.650063 | E_var:     0.5340 | E_err:   0.011418
[2025-10-22 21:52:23] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -63.651325 | E_var:     0.6515 | E_err:   0.012612
[2025-10-22 21:52:36] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -63.672389 | E_var:     0.5059 | E_err:   0.011114
[2025-10-22 21:52:49] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -63.644373 | E_var:     0.4819 | E_err:   0.010846
[2025-10-22 21:53:03] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -63.680532 | E_var:     0.4735 | E_err:   0.010752
[2025-10-22 21:53:16] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -63.665415 | E_var:     0.4692 | E_err:   0.010703
[2025-10-22 21:53:29] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -63.654689 | E_var:     0.4488 | E_err:   0.010467
[2025-10-22 21:53:42] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -63.645802 | E_var:     0.5323 | E_err:   0.011400
[2025-10-22 21:53:55] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -63.642637 | E_var:     0.4365 | E_err:   0.010323
[2025-10-22 21:54:08] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -63.655127 | E_var:     0.4791 | E_err:   0.010815
[2025-10-22 21:54:22] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -63.647548 | E_var:     0.5793 | E_err:   0.011893
[2025-10-22 21:54:35] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -63.663871 | E_var:     0.4960 | E_err:   0.011004
[2025-10-22 21:54:48] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -63.658712 | E_var:     0.4644 | E_err:   0.010648
[2025-10-22 21:55:01] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -63.672230 | E_var:     0.5103 | E_err:   0.011162
[2025-10-22 21:55:14] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -63.640175 | E_var:     0.4605 | E_err:   0.010604
[2025-10-22 21:55:27] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -63.659229 | E_var:     0.4197 | E_err:   0.010123
[2025-10-22 21:55:41] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -63.669674 | E_var:     0.5633 | E_err:   0.011727
[2025-10-22 21:55:54] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -63.670400 | E_var:     0.4717 | E_err:   0.010731
[2025-10-22 21:55:54] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-22 21:56:07] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -63.666495 | E_var:     1.0309 | E_err:   0.015865
[2025-10-22 21:56:20] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -63.659714 | E_var:     0.5046 | E_err:   0.011099
[2025-10-22 21:56:33] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -63.641425 | E_var:     0.5354 | E_err:   0.011433
[2025-10-22 21:56:47] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -63.661679 | E_var:     0.4462 | E_err:   0.010437
[2025-10-22 21:57:00] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -63.656848 | E_var:     0.5363 | E_err:   0.011442
[2025-10-22 21:57:13] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -63.645131 | E_var:     0.5427 | E_err:   0.011510
[2025-10-22 21:57:26] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -63.662045 | E_var:     0.5126 | E_err:   0.011187
[2025-10-22 21:57:39] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -63.649919 | E_var:     0.5160 | E_err:   0.011224
[2025-10-22 21:57:52] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -63.662631 | E_var:     0.5423 | E_err:   0.011506
[2025-10-22 21:58:06] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -63.657615 | E_var:     0.5311 | E_err:   0.011387
[2025-10-22 21:58:19] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -63.669732 | E_var:     1.0072 | E_err:   0.015681
[2025-10-22 21:58:32] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -63.648535 | E_var:     0.5044 | E_err:   0.011097
[2025-10-22 21:58:45] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -63.673565 | E_var:     0.4552 | E_err:   0.010542
[2025-10-22 21:58:58] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -63.680106 | E_var:     2.2551 | E_err:   0.023464
[2025-10-22 21:59:11] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -63.662006 | E_var:     0.4100 | E_err:   0.010005
[2025-10-22 21:59:25] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -63.664566 | E_var:     0.4565 | E_err:   0.010557
[2025-10-22 21:59:38] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -63.649444 | E_var:     0.6901 | E_err:   0.012980
[2025-10-22 21:59:51] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -63.667567 | E_var:     0.5438 | E_err:   0.011522
[2025-10-22 22:00:04] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -63.684667 | E_var:     0.4051 | E_err:   0.009945
[2025-10-22 22:00:17] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -63.657594 | E_var:     0.4251 | E_err:   0.010188
[2025-10-22 22:00:30] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -63.649447 | E_var:     0.4468 | E_err:   0.010444
[2025-10-22 22:00:44] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -63.645789 | E_var:     0.4114 | E_err:   0.010021
[2025-10-22 22:00:57] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -63.655384 | E_var:     0.6354 | E_err:   0.012455
[2025-10-22 22:01:10] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -63.672944 | E_var:     0.5854 | E_err:   0.011955
[2025-10-22 22:01:23] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -63.653186 | E_var:     0.5299 | E_err:   0.011374
[2025-10-22 22:01:36] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -63.663155 | E_var:     0.4915 | E_err:   0.010954
[2025-10-22 22:01:49] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -63.656693 | E_var:     0.4818 | E_err:   0.010846
[2025-10-22 22:02:03] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -63.662927 | E_var:     0.5193 | E_err:   0.011260
[2025-10-22 22:02:16] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -63.662365 | E_var:     0.4419 | E_err:   0.010387
[2025-10-22 22:02:29] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -63.666675 | E_var:     0.4432 | E_err:   0.010402
[2025-10-22 22:02:42] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -63.661351 | E_var:     0.4655 | E_err:   0.010660
[2025-10-22 22:02:55] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -63.660964 | E_var:     0.4975 | E_err:   0.011020
[2025-10-22 22:03:08] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -63.667831 | E_var:     0.4996 | E_err:   0.011044
[2025-10-22 22:03:22] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -63.662797 | E_var:     0.5128 | E_err:   0.011189
[2025-10-22 22:03:35] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -63.643357 | E_var:     0.6465 | E_err:   0.012563
[2025-10-22 22:03:48] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -63.666441 | E_var:     0.5819 | E_err:   0.011920
[2025-10-22 22:04:01] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -63.691391 | E_var:     0.7882 | E_err:   0.013872
[2025-10-22 22:04:14] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -63.659643 | E_var:     0.4321 | E_err:   0.010271
[2025-10-22 22:04:27] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -63.656628 | E_var:     0.4596 | E_err:   0.010592
[2025-10-22 22:04:41] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -63.666000 | E_var:     0.4290 | E_err:   0.010234
[2025-10-22 22:04:54] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -63.660255 | E_var:     0.5942 | E_err:   0.012044
[2025-10-22 22:05:07] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -63.660103 | E_var:     0.4801 | E_err:   0.010827
[2025-10-22 22:05:20] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -63.667734 | E_var:     0.4585 | E_err:   0.010580
[2025-10-22 22:05:33] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -63.649248 | E_var:     0.4152 | E_err:   0.010068
[2025-10-22 22:05:47] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -63.659196 | E_var:     0.4456 | E_err:   0.010430
[2025-10-22 22:06:00] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -63.662719 | E_var:     0.4605 | E_err:   0.010603
[2025-10-22 22:06:13] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -63.646257 | E_var:     0.4996 | E_err:   0.011045
[2025-10-22 22:06:26] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -63.634999 | E_var:     0.4548 | E_err:   0.010537
[2025-10-22 22:06:40] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -63.667303 | E_var:     0.4920 | E_err:   0.010960
[2025-10-22 22:06:53] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -63.663299 | E_var:     0.4748 | E_err:   0.010767
[2025-10-22 22:07:06] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -63.643902 | E_var:     0.5957 | E_err:   0.012059
[2025-10-22 22:07:19] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -63.655114 | E_var:     0.5610 | E_err:   0.011703
[2025-10-22 22:07:32] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -63.649396 | E_var:     0.5086 | E_err:   0.011143
[2025-10-22 22:07:45] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -63.672791 | E_var:     0.4866 | E_err:   0.010899
[2025-10-22 22:07:59] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -63.659160 | E_var:     0.5240 | E_err:   0.011311
[2025-10-22 22:08:12] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -63.660747 | E_var:     0.4686 | E_err:   0.010696
[2025-10-22 22:08:25] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -63.678447 | E_var:     0.4692 | E_err:   0.010703
[2025-10-22 22:08:38] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -63.669918 | E_var:     0.6623 | E_err:   0.012716
[2025-10-22 22:08:51] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -63.669765 | E_var:     0.5459 | E_err:   0.011544
[2025-10-22 22:09:05] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -63.650901 | E_var:     0.5658 | E_err:   0.011753
[2025-10-22 22:09:18] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -63.665381 | E_var:     0.7299 | E_err:   0.013349
[2025-10-22 22:09:31] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -63.652979 | E_var:     0.4593 | E_err:   0.010589
[2025-10-22 22:09:44] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -63.672486 | E_var:     0.5164 | E_err:   0.011229
[2025-10-22 22:09:57] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -63.664175 | E_var:     0.5266 | E_err:   0.011339
[2025-10-22 22:10:10] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -63.668522 | E_var:     0.4462 | E_err:   0.010437
[2025-10-22 22:10:24] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -63.662503 | E_var:     0.5680 | E_err:   0.011776
[2025-10-22 22:10:37] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -63.671826 | E_var:     0.4875 | E_err:   0.010910
[2025-10-22 22:10:50] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -63.645691 | E_var:     0.5391 | E_err:   0.011473
[2025-10-22 22:11:03] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -63.664696 | E_var:     0.4941 | E_err:   0.010984
[2025-10-22 22:11:16] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -63.642099 | E_var:     0.5613 | E_err:   0.011706
[2025-10-22 22:11:30] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -63.668482 | E_var:     0.4780 | E_err:   0.010803
[2025-10-22 22:11:43] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -63.663954 | E_var:     0.4333 | E_err:   0.010285
[2025-10-22 22:11:56] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -63.659872 | E_var:     0.5049 | E_err:   0.011103
[2025-10-22 22:12:09] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -63.643047 | E_var:     0.4525 | E_err:   0.010511
[2025-10-22 22:12:22] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -63.654776 | E_var:     0.4991 | E_err:   0.011039
[2025-10-22 22:12:35] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -63.650834 | E_var:     0.4660 | E_err:   0.010666
[2025-10-22 22:12:49] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -63.658988 | E_var:     0.4347 | E_err:   0.010302
[2025-10-22 22:13:02] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -63.672535 | E_var:     0.4107 | E_err:   0.010013
[2025-10-22 22:13:15] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -63.674151 | E_var:     0.4852 | E_err:   0.010884
[2025-10-22 22:13:29] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -63.682553 | E_var:     0.4809 | E_err:   0.010836
[2025-10-22 22:13:42] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -63.651032 | E_var:     0.5536 | E_err:   0.011626
[2025-10-22 22:13:55] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -63.664434 | E_var:     0.4715 | E_err:   0.010729
[2025-10-22 22:14:08] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -63.655865 | E_var:     0.4728 | E_err:   0.010744
[2025-10-22 22:14:21] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -63.666929 | E_var:     0.6766 | E_err:   0.012852
[2025-10-22 22:14:34] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -63.640313 | E_var:     0.4625 | E_err:   0.010626
[2025-10-22 22:14:48] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -63.656029 | E_var:     0.4668 | E_err:   0.010676
[2025-10-22 22:15:01] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -63.680127 | E_var:     0.8072 | E_err:   0.014038
[2025-10-22 22:15:14] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -63.663355 | E_var:     0.5537 | E_err:   0.011627
[2025-10-22 22:15:27] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -63.668197 | E_var:     0.4742 | E_err:   0.010760
[2025-10-22 22:15:40] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -63.662656 | E_var:     0.4719 | E_err:   0.010734
[2025-10-22 22:15:53] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -63.648394 | E_var:     0.7118 | E_err:   0.013182
[2025-10-22 22:16:07] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -63.662618 | E_var:     0.4869 | E_err:   0.010903
[2025-10-22 22:16:20] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -63.667994 | E_var:     0.5720 | E_err:   0.011818
[2025-10-22 22:16:33] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -63.668093 | E_var:     0.6865 | E_err:   0.012947
[2025-10-22 22:16:46] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -63.663669 | E_var:     0.4216 | E_err:   0.010145
[2025-10-22 22:16:59] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -63.680479 | E_var:     0.4202 | E_err:   0.010129
[2025-10-22 22:17:12] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -63.663451 | E_var:     0.4955 | E_err:   0.010999
[2025-10-22 22:17:26] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -63.659547 | E_var:     0.6354 | E_err:   0.012455
[2025-10-22 22:17:39] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -63.676346 | E_var:     0.5009 | E_err:   0.011059
[2025-10-22 22:17:52] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -63.666876 | E_var:     0.4707 | E_err:   0.010720
[2025-10-22 22:18:05] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -63.668694 | E_var:     0.4247 | E_err:   0.010183
[2025-10-22 22:18:18] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -63.663578 | E_var:     0.5295 | E_err:   0.011370
[2025-10-22 22:18:31] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -63.660622 | E_var:     0.5314 | E_err:   0.011390
[2025-10-22 22:18:45] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -63.640193 | E_var:     0.5666 | E_err:   0.011761
[2025-10-22 22:18:58] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -63.652987 | E_var:     0.5127 | E_err:   0.011188
[2025-10-22 22:18:58] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-22 22:19:11] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -63.671171 | E_var:     0.4470 | E_err:   0.010447
[2025-10-22 22:19:24] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -63.655518 | E_var:     0.6374 | E_err:   0.012475
[2025-10-22 22:19:37] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -63.640191 | E_var:     0.4599 | E_err:   0.010597
[2025-10-22 22:19:50] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -63.654553 | E_var:     0.4578 | E_err:   0.010573
[2025-10-22 22:20:04] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -63.667036 | E_var:     0.4200 | E_err:   0.010126
[2025-10-22 22:20:17] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -63.650947 | E_var:     0.4457 | E_err:   0.010431
[2025-10-22 22:20:30] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -63.671259 | E_var:     0.5087 | E_err:   0.011144
[2025-10-22 22:20:43] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -63.666403 | E_var:     0.5035 | E_err:   0.011087
[2025-10-22 22:20:56] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -63.670126 | E_var:     0.4883 | E_err:   0.010919
[2025-10-22 22:21:09] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -63.674829 | E_var:     0.4634 | E_err:   0.010636
[2025-10-22 22:21:23] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -63.665934 | E_var:     0.4915 | E_err:   0.010954
[2025-10-22 22:21:36] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -63.654139 | E_var:     0.4905 | E_err:   0.010943
[2025-10-22 22:21:49] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -63.653478 | E_var:     0.5882 | E_err:   0.011984
[2025-10-22 22:22:02] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -63.683381 | E_var:     0.5774 | E_err:   0.011873
[2025-10-22 22:22:15] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -63.669966 | E_var:     0.4412 | E_err:   0.010379
[2025-10-22 22:22:28] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -63.679078 | E_var:     0.4531 | E_err:   0.010517
[2025-10-22 22:22:42] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -63.660510 | E_var:     0.4958 | E_err:   0.011002
[2025-10-22 22:22:55] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -63.665309 | E_var:     0.5207 | E_err:   0.011275
[2025-10-22 22:23:08] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -63.652930 | E_var:     0.4092 | E_err:   0.009996
[2025-10-22 22:23:21] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -63.673055 | E_var:     0.4677 | E_err:   0.010685
[2025-10-22 22:23:34] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -63.663598 | E_var:     0.5399 | E_err:   0.011480
[2025-10-22 22:23:47] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -63.657117 | E_var:     0.4544 | E_err:   0.010532
[2025-10-22 22:24:01] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -63.662747 | E_var:     0.4788 | E_err:   0.010812
[2025-10-22 22:24:14] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -63.654558 | E_var:     0.4532 | E_err:   0.010519
[2025-10-22 22:24:27] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -63.655496 | E_var:     0.5523 | E_err:   0.011612
[2025-10-22 22:24:40] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -63.676929 | E_var:     0.4948 | E_err:   0.010991
[2025-10-22 22:24:53] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -63.659337 | E_var:     0.4904 | E_err:   0.010942
[2025-10-22 22:25:06] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -63.669133 | E_var:     0.5817 | E_err:   0.011917
[2025-10-22 22:25:20] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -63.664237 | E_var:     0.5546 | E_err:   0.011636
[2025-10-22 22:25:33] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -63.649440 | E_var:     2.1589 | E_err:   0.022958
[2025-10-22 22:25:46] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -63.650660 | E_var:     0.4867 | E_err:   0.010900
[2025-10-22 22:25:59] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -63.668638 | E_var:     0.4466 | E_err:   0.010441
[2025-10-22 22:26:12] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -63.657976 | E_var:     0.5225 | E_err:   0.011294
[2025-10-22 22:26:26] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -63.645356 | E_var:     0.4846 | E_err:   0.010877
[2025-10-22 22:26:39] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -63.661660 | E_var:     0.4641 | E_err:   0.010645
[2025-10-22 22:26:52] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -63.643999 | E_var:     0.5187 | E_err:   0.011253
[2025-10-22 22:27:05] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -63.647753 | E_var:     0.4611 | E_err:   0.010610
[2025-10-22 22:27:18] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -63.655259 | E_var:     0.4638 | E_err:   0.010641
[2025-10-22 22:27:31] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -63.664397 | E_var:     0.5559 | E_err:   0.011649
[2025-10-22 22:27:45] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -63.661198 | E_var:     0.5291 | E_err:   0.011365
[2025-10-22 22:27:58] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -63.655607 | E_var:     0.5027 | E_err:   0.011079
[2025-10-22 22:28:11] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -63.661052 | E_var:     0.5080 | E_err:   0.011136
[2025-10-22 22:28:24] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -63.659130 | E_var:     0.5036 | E_err:   0.011089
[2025-10-22 22:28:37] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -63.663369 | E_var:     0.4775 | E_err:   0.010798
[2025-10-22 22:28:50] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -63.675546 | E_var:     0.5387 | E_err:   0.011468
[2025-10-22 22:29:04] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -63.667592 | E_var:     0.4660 | E_err:   0.010666
[2025-10-22 22:29:17] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -63.654854 | E_var:     0.5162 | E_err:   0.011226
[2025-10-22 22:29:30] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -63.657709 | E_var:     0.4238 | E_err:   0.010172
[2025-10-22 22:29:43] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -63.650503 | E_var:     0.4315 | E_err:   0.010264
[2025-10-22 22:29:56] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -63.679776 | E_var:     0.5214 | E_err:   0.011283
[2025-10-22 22:30:09] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -63.663170 | E_var:     0.5434 | E_err:   0.011518
[2025-10-22 22:30:23] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -63.651050 | E_var:     0.5426 | E_err:   0.011509
[2025-10-22 22:30:36] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -63.649163 | E_var:     0.5048 | E_err:   0.011101
[2025-10-22 22:30:49] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -63.668982 | E_var:     0.4256 | E_err:   0.010193
[2025-10-22 22:31:02] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -63.663893 | E_var:     0.4296 | E_err:   0.010241
[2025-10-22 22:31:15] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -63.677132 | E_var:     0.4646 | E_err:   0.010651
[2025-10-22 22:31:29] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -63.667139 | E_var:     0.4985 | E_err:   0.011032
[2025-10-22 22:31:42] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -63.662919 | E_var:     0.4670 | E_err:   0.010678
[2025-10-22 22:31:55] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -63.672318 | E_var:     0.5417 | E_err:   0.011500
[2025-10-22 22:32:08] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -63.659710 | E_var:     0.4949 | E_err:   0.010992
[2025-10-22 22:32:21] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -63.658040 | E_var:     0.4422 | E_err:   0.010391
[2025-10-22 22:32:34] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -63.679793 | E_var:     0.4213 | E_err:   0.010142
[2025-10-22 22:32:48] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -63.657824 | E_var:     0.4218 | E_err:   0.010148
[2025-10-22 22:33:01] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -63.662737 | E_var:     0.4519 | E_err:   0.010504
[2025-10-22 22:33:14] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -63.659699 | E_var:     0.4130 | E_err:   0.010041
[2025-10-22 22:33:27] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -63.656347 | E_var:     0.5442 | E_err:   0.011527
[2025-10-22 22:33:40] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -63.662302 | E_var:     0.4598 | E_err:   0.010595
[2025-10-22 22:33:53] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -63.665725 | E_var:     0.4500 | E_err:   0.010481
[2025-10-22 22:34:07] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -63.665382 | E_var:     0.4971 | E_err:   0.011017
[2025-10-22 22:34:20] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -63.670086 | E_var:     0.5350 | E_err:   0.011428
[2025-10-22 22:34:33] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -63.660174 | E_var:     0.5712 | E_err:   0.011809
[2025-10-22 22:34:46] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -63.663230 | E_var:     0.5023 | E_err:   0.011074
[2025-10-22 22:34:59] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -63.652075 | E_var:     0.4901 | E_err:   0.010939
[2025-10-22 22:35:12] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -63.684283 | E_var:     0.5264 | E_err:   0.011337
[2025-10-22 22:35:26] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -63.669357 | E_var:     0.4309 | E_err:   0.010257
[2025-10-22 22:35:39] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -63.650230 | E_var:     0.4467 | E_err:   0.010443
[2025-10-22 22:35:52] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -63.670020 | E_var:     0.5204 | E_err:   0.011272
[2025-10-22 22:36:05] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -63.674794 | E_var:     0.5394 | E_err:   0.011476
[2025-10-22 22:36:18] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -63.682261 | E_var:     0.4496 | E_err:   0.010477
[2025-10-22 22:36:32] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -63.657183 | E_var:     0.5267 | E_err:   0.011340
[2025-10-22 22:36:45] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -63.672921 | E_var:     0.4973 | E_err:   0.011019
[2025-10-22 22:36:58] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -63.660952 | E_var:     0.5443 | E_err:   0.011528
[2025-10-22 22:37:11] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -63.658754 | E_var:     0.5587 | E_err:   0.011679
[2025-10-22 22:37:24] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -63.671808 | E_var:     2.2206 | E_err:   0.023284
[2025-10-22 22:37:37] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -63.669458 | E_var:     0.5647 | E_err:   0.011742
[2025-10-22 22:37:51] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -63.653659 | E_var:     0.6640 | E_err:   0.012732
[2025-10-22 22:38:04] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -63.664152 | E_var:     0.5248 | E_err:   0.011319
[2025-10-22 22:38:17] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -63.666004 | E_var:     0.5357 | E_err:   0.011436
[2025-10-22 22:38:30] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -63.665675 | E_var:     0.4297 | E_err:   0.010243
[2025-10-22 22:38:43] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -63.668447 | E_var:     0.5064 | E_err:   0.011119
[2025-10-22 22:38:56] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -63.631930 | E_var:     0.5991 | E_err:   0.012094
[2025-10-22 22:39:10] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -63.643179 | E_var:     0.5504 | E_err:   0.011592
[2025-10-22 22:39:23] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -63.660422 | E_var:     0.5177 | E_err:   0.011243
[2025-10-22 22:39:36] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -63.666946 | E_var:     0.5592 | E_err:   0.011685
[2025-10-22 22:39:49] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -63.661758 | E_var:     0.4482 | E_err:   0.010461
[2025-10-22 22:40:02] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -63.673946 | E_var:     0.5016 | E_err:   0.011066
[2025-10-22 22:40:15] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -63.674302 | E_var:     0.4215 | E_err:   0.010145
[2025-10-22 22:40:29] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -63.645580 | E_var:     0.5571 | E_err:   0.011662
[2025-10-22 22:40:42] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -63.652917 | E_var:     0.4928 | E_err:   0.010969
[2025-10-22 22:40:55] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -63.650537 | E_var:     0.6289 | E_err:   0.012391
[2025-10-22 22:41:08] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -63.678879 | E_var:     0.4839 | E_err:   0.010870
[2025-10-22 22:41:21] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -63.652181 | E_var:     0.8887 | E_err:   0.014730
[2025-10-22 22:41:34] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -63.662622 | E_var:     0.5347 | E_err:   0.011426
[2025-10-22 22:41:48] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -63.666280 | E_var:     0.4635 | E_err:   0.010638
[2025-10-22 22:42:01] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -63.657868 | E_var:     0.5913 | E_err:   0.012015
[2025-10-22 22:42:01] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-22 22:42:14] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -63.651691 | E_var:     0.4602 | E_err:   0.010600
[2025-10-22 22:42:27] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -63.654073 | E_var:     0.5002 | E_err:   0.011051
[2025-10-22 22:42:40] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -63.656756 | E_var:     0.4352 | E_err:   0.010308
[2025-10-22 22:42:54] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -63.662977 | E_var:     0.4351 | E_err:   0.010306
[2025-10-22 22:43:07] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -63.668994 | E_var:     0.5058 | E_err:   0.011113
[2025-10-22 22:43:20] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -63.659436 | E_var:     0.5132 | E_err:   0.011193
[2025-10-22 22:43:33] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -63.665949 | E_var:     0.4678 | E_err:   0.010687
[2025-10-22 22:43:46] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -63.659524 | E_var:     0.4427 | E_err:   0.010396
[2025-10-22 22:43:59] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -63.667487 | E_var:     0.4617 | E_err:   0.010617
[2025-10-22 22:44:13] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -63.671121 | E_var:     0.5730 | E_err:   0.011828
[2025-10-22 22:44:26] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -63.658596 | E_var:     0.8910 | E_err:   0.014749
[2025-10-22 22:44:39] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -63.674535 | E_var:     0.4899 | E_err:   0.010936
[2025-10-22 22:44:52] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -63.664193 | E_var:     0.5794 | E_err:   0.011894
[2025-10-22 22:45:05] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -63.678167 | E_var:     0.4775 | E_err:   0.010798
[2025-10-22 22:45:18] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -63.672803 | E_var:     0.5647 | E_err:   0.011741
[2025-10-22 22:45:32] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -63.640097 | E_var:     0.4215 | E_err:   0.010144
[2025-10-22 22:45:45] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -63.649864 | E_var:     0.4601 | E_err:   0.010599
[2025-10-22 22:45:58] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -63.655953 | E_var:     0.5132 | E_err:   0.011193
[2025-10-22 22:46:11] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -63.658274 | E_var:     0.4805 | E_err:   0.010831
[2025-10-22 22:46:24] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -63.664510 | E_var:     0.4635 | E_err:   0.010638
[2025-10-22 22:46:38] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -63.678670 | E_var:     0.4098 | E_err:   0.010003
[2025-10-22 22:46:51] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -63.670859 | E_var:     0.5517 | E_err:   0.011606
[2025-10-22 22:47:04] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -63.668320 | E_var:     0.4892 | E_err:   0.010928
[2025-10-22 22:47:17] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -63.665910 | E_var:     0.5577 | E_err:   0.011668
[2025-10-22 22:47:30] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -63.640948 | E_var:     0.4784 | E_err:   0.010807
[2025-10-22 22:47:43] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -63.674751 | E_var:     0.4987 | E_err:   0.011034
[2025-10-22 22:47:57] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -63.649839 | E_var:     0.5495 | E_err:   0.011582
[2025-10-22 22:48:10] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -63.643047 | E_var:     0.4726 | E_err:   0.010742
[2025-10-22 22:48:23] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -63.647825 | E_var:     0.5668 | E_err:   0.011764
[2025-10-22 22:48:36] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -63.655577 | E_var:     0.4735 | E_err:   0.010752
[2025-10-22 22:48:36] 🔄 RESTART #2 | Period: 600
[2025-10-22 22:48:49] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -63.636075 | E_var:     0.4361 | E_err:   0.010319
[2025-10-22 22:49:02] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -63.666396 | E_var:     0.4584 | E_err:   0.010579
[2025-10-22 22:49:16] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -63.658331 | E_var:     0.4148 | E_err:   0.010063
[2025-10-22 22:49:29] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -63.639608 | E_var:     0.4579 | E_err:   0.010573
[2025-10-22 22:49:42] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -63.649401 | E_var:     0.4392 | E_err:   0.010355
[2025-10-22 22:49:55] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -63.655901 | E_var:     0.4772 | E_err:   0.010794
[2025-10-22 22:50:08] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -63.656659 | E_var:     0.4222 | E_err:   0.010152
[2025-10-22 22:50:21] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -63.653850 | E_var:     0.4337 | E_err:   0.010291
[2025-10-22 22:50:35] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -63.642914 | E_var:     0.4733 | E_err:   0.010750
[2025-10-22 22:50:48] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -63.660910 | E_var:     0.4174 | E_err:   0.010095
[2025-10-22 22:51:01] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -63.666642 | E_var:     0.4615 | E_err:   0.010615
[2025-10-22 22:51:14] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -63.655763 | E_var:     0.4809 | E_err:   0.010836
[2025-10-22 22:51:27] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -63.665880 | E_var:     0.4029 | E_err:   0.009918
[2025-10-22 22:51:40] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -63.647567 | E_var:     0.4146 | E_err:   0.010061
[2025-10-22 22:51:54] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -63.649567 | E_var:     0.4685 | E_err:   0.010695
[2025-10-22 22:52:07] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -63.668843 | E_var:     0.5245 | E_err:   0.011316
[2025-10-22 22:52:20] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -63.687629 | E_var:     0.4458 | E_err:   0.010433
[2025-10-22 22:52:33] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -63.651444 | E_var:     0.4010 | E_err:   0.009894
[2025-10-22 22:52:46] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -63.665316 | E_var:     0.4461 | E_err:   0.010437
[2025-10-22 22:53:00] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -63.652508 | E_var:     0.5040 | E_err:   0.011093
[2025-10-22 22:53:13] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -63.653260 | E_var:     0.4296 | E_err:   0.010241
[2025-10-22 22:53:26] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -63.646023 | E_var:     0.5460 | E_err:   0.011546
[2025-10-22 22:53:39] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -63.664615 | E_var:     1.0476 | E_err:   0.015993
[2025-10-22 22:53:52] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -63.659867 | E_var:     0.5815 | E_err:   0.011916
[2025-10-22 22:54:05] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -63.659982 | E_var:     0.4041 | E_err:   0.009932
[2025-10-22 22:54:19] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -63.659424 | E_var:     0.4608 | E_err:   0.010606
[2025-10-22 22:54:32] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -63.647760 | E_var:     0.6195 | E_err:   0.012299
[2025-10-22 22:54:45] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -63.661556 | E_var:     0.4417 | E_err:   0.010384
[2025-10-22 22:54:58] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -63.652676 | E_var:     0.4328 | E_err:   0.010279
[2025-10-22 22:55:11] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -63.666976 | E_var:     0.6541 | E_err:   0.012637
[2025-10-22 22:55:24] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -63.661187 | E_var:     0.6749 | E_err:   0.012836
[2025-10-22 22:55:38] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -63.672188 | E_var:     0.4598 | E_err:   0.010596
[2025-10-22 22:55:51] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -63.652615 | E_var:     0.5046 | E_err:   0.011099
[2025-10-22 22:56:04] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -63.659763 | E_var:     0.4495 | E_err:   0.010476
[2025-10-22 22:56:17] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -63.656893 | E_var:     0.4859 | E_err:   0.010892
[2025-10-22 22:56:30] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -63.648908 | E_var:     0.5067 | E_err:   0.011122
[2025-10-22 22:56:43] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -63.668231 | E_var:     0.4552 | E_err:   0.010542
[2025-10-22 22:56:57] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -63.667281 | E_var:     0.5010 | E_err:   0.011060
[2025-10-22 22:57:10] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -63.650295 | E_var:     0.4716 | E_err:   0.010730
[2025-10-22 22:57:23] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -63.655590 | E_var:     0.4032 | E_err:   0.009922
[2025-10-22 22:57:36] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -63.664973 | E_var:     0.4017 | E_err:   0.009904
[2025-10-22 22:57:49] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -63.649804 | E_var:     0.4841 | E_err:   0.010871
[2025-10-22 22:58:02] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -63.664026 | E_var:     0.4371 | E_err:   0.010331
[2025-10-22 22:58:16] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -63.652861 | E_var:     0.5680 | E_err:   0.011776
[2025-10-22 22:58:29] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -63.660421 | E_var:     0.5566 | E_err:   0.011657
[2025-10-22 22:58:42] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -63.650768 | E_var:     0.4170 | E_err:   0.010090
[2025-10-22 22:58:55] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -63.652459 | E_var:     0.4522 | E_err:   0.010507
[2025-10-22 22:59:08] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -63.657037 | E_var:     0.4844 | E_err:   0.010875
[2025-10-22 22:59:21] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -63.681314 | E_var:     0.4834 | E_err:   0.010863
[2025-10-22 22:59:35] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -63.657264 | E_var:     0.4705 | E_err:   0.010718
[2025-10-22 22:59:48] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -63.663990 | E_var:     0.4422 | E_err:   0.010390
[2025-10-22 23:00:01] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -63.656458 | E_var:     0.4407 | E_err:   0.010372
[2025-10-22 23:00:14] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -63.634663 | E_var:     0.5846 | E_err:   0.011947
[2025-10-22 23:00:27] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -63.669383 | E_var:     0.4331 | E_err:   0.010283
[2025-10-22 23:00:40] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -63.656569 | E_var:     0.5033 | E_err:   0.011085
[2025-10-22 23:00:54] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -63.663418 | E_var:     0.3928 | E_err:   0.009792
[2025-10-22 23:01:07] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -63.648129 | E_var:     0.4797 | E_err:   0.010822
[2025-10-22 23:01:20] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -63.654398 | E_var:     0.4589 | E_err:   0.010584
[2025-10-22 23:01:33] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -63.659830 | E_var:     0.4236 | E_err:   0.010169
[2025-10-22 23:01:46] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -63.663220 | E_var:     0.5227 | E_err:   0.011297
[2025-10-22 23:01:59] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -63.671559 | E_var:     0.4646 | E_err:   0.010650
[2025-10-22 23:02:13] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -63.666110 | E_var:     0.4086 | E_err:   0.009988
[2025-10-22 23:02:26] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -63.659648 | E_var:     0.5898 | E_err:   0.012000
[2025-10-22 23:02:39] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -63.659682 | E_var:     0.4638 | E_err:   0.010641
[2025-10-22 23:02:52] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -63.643787 | E_var:     0.5096 | E_err:   0.011155
[2025-10-22 23:03:05] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -63.636465 | E_var:     0.4427 | E_err:   0.010396
[2025-10-22 23:03:19] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -63.669314 | E_var:     0.5095 | E_err:   0.011153
[2025-10-22 23:03:32] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -63.670600 | E_var:     0.4691 | E_err:   0.010701
[2025-10-22 23:03:45] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -63.652706 | E_var:     0.4446 | E_err:   0.010418
[2025-10-22 23:03:58] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -63.659164 | E_var:     0.3987 | E_err:   0.009866
[2025-10-22 23:04:11] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -63.658700 | E_var:     0.4144 | E_err:   0.010058
[2025-10-22 23:04:24] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -63.663230 | E_var:     0.4483 | E_err:   0.010462
[2025-10-22 23:04:38] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -63.667124 | E_var:     0.4423 | E_err:   0.010392
[2025-10-22 23:04:51] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -63.660300 | E_var:     0.4625 | E_err:   0.010626
[2025-10-22 23:05:04] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -63.667597 | E_var:     0.4401 | E_err:   0.010365
[2025-10-22 23:05:04] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-22 23:05:17] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -63.673920 | E_var:     0.4531 | E_err:   0.010517
[2025-10-22 23:05:30] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -63.691919 | E_var:     0.4601 | E_err:   0.010599
[2025-10-22 23:05:44] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -63.651198 | E_var:     0.5176 | E_err:   0.011241
[2025-10-22 23:05:57] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -63.680742 | E_var:     0.4985 | E_err:   0.011031
[2025-10-22 23:06:10] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -63.670324 | E_var:     0.5030 | E_err:   0.011082
[2025-10-22 23:06:23] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -63.671163 | E_var:     0.4781 | E_err:   0.010804
[2025-10-22 23:06:36] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -63.657913 | E_var:     0.5198 | E_err:   0.011265
[2025-10-22 23:06:49] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -63.652189 | E_var:     0.5519 | E_err:   0.011608
[2025-10-22 23:07:03] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -63.661422 | E_var:     0.4783 | E_err:   0.010806
[2025-10-22 23:07:16] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -63.660733 | E_var:     0.6212 | E_err:   0.012315
[2025-10-22 23:07:29] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -63.661914 | E_var:     0.4131 | E_err:   0.010042
[2025-10-22 23:07:42] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -63.653889 | E_var:     0.4457 | E_err:   0.010432
[2025-10-22 23:07:55] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -63.657937 | E_var:     0.5691 | E_err:   0.011787
[2025-10-22 23:08:08] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -63.664379 | E_var:     0.4916 | E_err:   0.010955
[2025-10-22 23:08:22] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -63.661143 | E_var:     0.5725 | E_err:   0.011822
[2025-10-22 23:08:35] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -63.688845 | E_var:     0.5235 | E_err:   0.011306
[2025-10-22 23:08:48] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -63.653546 | E_var:     0.5885 | E_err:   0.011986
[2025-10-22 23:09:01] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -63.649346 | E_var:     0.4513 | E_err:   0.010497
[2025-10-22 23:09:14] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -63.652411 | E_var:     0.4228 | E_err:   0.010160
[2025-10-22 23:09:27] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -63.649107 | E_var:     0.5338 | E_err:   0.011416
[2025-10-22 23:09:41] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -63.662395 | E_var:     0.5470 | E_err:   0.011556
[2025-10-22 23:09:54] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -63.651521 | E_var:     0.3979 | E_err:   0.009857
[2025-10-22 23:10:07] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -63.678554 | E_var:     0.4563 | E_err:   0.010554
[2025-10-22 23:10:20] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -63.650913 | E_var:     0.5649 | E_err:   0.011743
[2025-10-22 23:10:33] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -63.647047 | E_var:     0.5012 | E_err:   0.011062
[2025-10-22 23:10:47] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -63.662904 | E_var:     0.4788 | E_err:   0.010811
[2025-10-22 23:11:00] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -63.661302 | E_var:     0.4744 | E_err:   0.010762
[2025-10-22 23:11:13] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -63.660035 | E_var:     0.5389 | E_err:   0.011470
[2025-10-22 23:11:26] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -63.652125 | E_var:     0.5278 | E_err:   0.011352
[2025-10-22 23:11:39] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -63.651519 | E_var:     0.3840 | E_err:   0.009683
[2025-10-22 23:11:52] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -63.660900 | E_var:     0.6117 | E_err:   0.012221
[2025-10-22 23:12:06] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -63.666350 | E_var:     0.4233 | E_err:   0.010165
[2025-10-22 23:12:19] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -63.658985 | E_var:     0.4759 | E_err:   0.010779
[2025-10-22 23:12:32] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -63.627235 | E_var:     0.9386 | E_err:   0.015138
[2025-10-22 23:12:45] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -63.644888 | E_var:     0.6727 | E_err:   0.012815
[2025-10-22 23:12:58] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -63.655337 | E_var:     0.4368 | E_err:   0.010327
[2025-10-22 23:13:11] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -63.647340 | E_var:     0.6095 | E_err:   0.012199
[2025-10-22 23:13:25] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -63.678688 | E_var:     0.4745 | E_err:   0.010763
[2025-10-22 23:13:38] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -63.669153 | E_var:     0.4667 | E_err:   0.010674
[2025-10-22 23:13:51] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -63.667443 | E_var:     0.4529 | E_err:   0.010515
[2025-10-22 23:14:04] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -63.651861 | E_var:     0.4956 | E_err:   0.011000
[2025-10-22 23:14:17] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -63.649616 | E_var:     0.5108 | E_err:   0.011167
[2025-10-22 23:14:31] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -63.644804 | E_var:     0.4180 | E_err:   0.010103
[2025-10-22 23:14:44] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -63.674647 | E_var:     0.5962 | E_err:   0.012065
[2025-10-22 23:14:57] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -63.669679 | E_var:     0.5053 | E_err:   0.011107
[2025-10-22 23:15:10] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -63.658704 | E_var:     0.4699 | E_err:   0.010711
[2025-10-22 23:15:23] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -63.668666 | E_var:     0.4507 | E_err:   0.010489
[2025-10-22 23:15:36] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -63.673238 | E_var:     0.4334 | E_err:   0.010286
[2025-10-22 23:15:50] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -63.665777 | E_var:     0.4271 | E_err:   0.010212
[2025-10-22 23:16:03] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -63.661274 | E_var:     0.4705 | E_err:   0.010718
[2025-10-22 23:16:16] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -63.659647 | E_var:     0.5605 | E_err:   0.011697
[2025-10-22 23:16:29] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -63.662603 | E_var:     0.4663 | E_err:   0.010670
[2025-10-22 23:16:42] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -63.667031 | E_var:     0.4682 | E_err:   0.010692
[2025-10-22 23:16:55] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -63.655837 | E_var:     0.5549 | E_err:   0.011640
[2025-10-22 23:17:09] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -63.650064 | E_var:     0.6442 | E_err:   0.012541
[2025-10-22 23:17:22] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -63.668783 | E_var:     0.4098 | E_err:   0.010003
[2025-10-22 23:17:35] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -63.663695 | E_var:     0.3965 | E_err:   0.009838
[2025-10-22 23:17:48] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -63.670438 | E_var:     0.4897 | E_err:   0.010935
[2025-10-22 23:18:01] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -63.664506 | E_var:     0.4118 | E_err:   0.010026
[2025-10-22 23:18:14] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -63.658516 | E_var:     0.3905 | E_err:   0.009764
[2025-10-22 23:18:28] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -63.665535 | E_var:     0.4614 | E_err:   0.010613
[2025-10-22 23:18:41] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -63.653646 | E_var:     0.5042 | E_err:   0.011095
[2025-10-22 23:18:54] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -63.659637 | E_var:     0.4748 | E_err:   0.010766
[2025-10-22 23:19:07] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -63.664087 | E_var:     0.4365 | E_err:   0.010324
[2025-10-22 23:19:20] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -63.663011 | E_var:     0.4616 | E_err:   0.010616
[2025-10-22 23:19:34] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -63.653997 | E_var:     0.4238 | E_err:   0.010172
[2025-10-22 23:19:47] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -63.667099 | E_var:     0.4771 | E_err:   0.010792
[2025-10-22 23:20:00] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -63.651074 | E_var:     0.4312 | E_err:   0.010260
[2025-10-22 23:20:13] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -63.652848 | E_var:     0.4409 | E_err:   0.010375
[2025-10-22 23:20:26] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -63.662791 | E_var:     0.4560 | E_err:   0.010551
[2025-10-22 23:20:39] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -63.664161 | E_var:     0.5493 | E_err:   0.011581
[2025-10-22 23:20:53] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -63.674054 | E_var:     0.3772 | E_err:   0.009596
[2025-10-22 23:21:06] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -63.673134 | E_var:     0.3990 | E_err:   0.009869
[2025-10-22 23:21:19] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -63.665690 | E_var:     0.4121 | E_err:   0.010030
[2025-10-22 23:21:32] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -63.647216 | E_var:     0.4062 | E_err:   0.009958
[2025-10-22 23:21:45] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -63.670035 | E_var:     0.4484 | E_err:   0.010463
[2025-10-22 23:21:58] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -63.663756 | E_var:     0.3891 | E_err:   0.009746
[2025-10-22 23:22:12] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -63.649198 | E_var:     0.4360 | E_err:   0.010317
[2025-10-22 23:22:25] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -63.663803 | E_var:     0.4241 | E_err:   0.010175
[2025-10-22 23:22:38] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -63.676306 | E_var:     0.5361 | E_err:   0.011441
[2025-10-22 23:22:51] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -63.663633 | E_var:     0.4917 | E_err:   0.010957
[2025-10-22 23:23:04] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -63.662412 | E_var:     0.4360 | E_err:   0.010318
[2025-10-22 23:23:17] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -63.659966 | E_var:     0.4486 | E_err:   0.010465
[2025-10-22 23:23:31] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -63.669165 | E_var:     0.4924 | E_err:   0.010964
[2025-10-22 23:23:44] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -63.684940 | E_var:     0.4442 | E_err:   0.010413
[2025-10-22 23:23:57] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -63.660317 | E_var:     0.4472 | E_err:   0.010449
[2025-10-22 23:24:10] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -63.648578 | E_var:     0.5785 | E_err:   0.011884
[2025-10-22 23:24:23] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -63.661197 | E_var:     0.4999 | E_err:   0.011048
[2025-10-22 23:24:37] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -63.651592 | E_var:     0.4643 | E_err:   0.010646
[2025-10-22 23:24:50] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -63.680093 | E_var:     0.4601 | E_err:   0.010599
[2025-10-22 23:25:03] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -63.660806 | E_var:     0.5105 | E_err:   0.011164
[2025-10-22 23:25:16] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -63.670795 | E_var:     0.4494 | E_err:   0.010475
[2025-10-22 23:25:29] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -63.674693 | E_var:     0.4022 | E_err:   0.009909
[2025-10-22 23:25:42] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -63.660246 | E_var:     0.3801 | E_err:   0.009633
[2025-10-22 23:25:56] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -63.654045 | E_var:     0.5105 | E_err:   0.011164
[2025-10-22 23:26:09] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -63.659768 | E_var:     0.3851 | E_err:   0.009696
[2025-10-22 23:26:22] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -63.683808 | E_var:     0.5786 | E_err:   0.011885
[2025-10-22 23:26:35] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -63.662983 | E_var:     0.4254 | E_err:   0.010192
[2025-10-22 23:26:48] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -63.654010 | E_var:     0.4710 | E_err:   0.010724
[2025-10-22 23:27:01] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -63.665828 | E_var:     0.4539 | E_err:   0.010527
[2025-10-22 23:27:15] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -63.653127 | E_var:     0.6879 | E_err:   0.012960
[2025-10-22 23:27:28] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -63.668807 | E_var:     0.4843 | E_err:   0.010873
[2025-10-22 23:27:41] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -63.657350 | E_var:     0.6040 | E_err:   0.012143
[2025-10-22 23:27:54] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -63.675659 | E_var:     0.4816 | E_err:   0.010843
[2025-10-22 23:28:07] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -63.666771 | E_var:     0.5880 | E_err:   0.011981
[2025-10-22 23:28:07] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-22 23:28:21] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -63.664291 | E_var:     0.5218 | E_err:   0.011287
[2025-10-22 23:28:34] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -63.680270 | E_var:     0.5289 | E_err:   0.011363
[2025-10-22 23:28:47] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -63.665959 | E_var:     0.5159 | E_err:   0.011223
[2025-10-22 23:29:00] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -63.662793 | E_var:     0.4478 | E_err:   0.010455
[2025-10-22 23:29:13] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -63.656486 | E_var:     0.5044 | E_err:   0.011097
[2025-10-22 23:29:27] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -63.660055 | E_var:     0.4305 | E_err:   0.010251
[2025-10-22 23:29:40] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -63.670078 | E_var:     0.5057 | E_err:   0.011111
[2025-10-22 23:29:53] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -63.659792 | E_var:     2.2503 | E_err:   0.023439
[2025-10-22 23:30:06] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -63.655104 | E_var:     0.5100 | E_err:   0.011159
[2025-10-22 23:30:19] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -63.664310 | E_var:     0.4472 | E_err:   0.010449
[2025-10-22 23:30:32] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -63.661034 | E_var:     0.4988 | E_err:   0.011035
[2025-10-22 23:30:46] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -63.665328 | E_var:     0.4532 | E_err:   0.010518
[2025-10-22 23:30:59] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -63.660283 | E_var:     0.5197 | E_err:   0.011264
[2025-10-22 23:31:12] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -63.661708 | E_var:     0.4846 | E_err:   0.010877
[2025-10-22 23:31:25] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -63.638773 | E_var:     0.4603 | E_err:   0.010601
[2025-10-22 23:31:38] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -63.676697 | E_var:     0.4595 | E_err:   0.010592
[2025-10-22 23:31:51] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -63.651095 | E_var:     0.4256 | E_err:   0.010194
[2025-10-22 23:32:05] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -63.673672 | E_var:     0.4825 | E_err:   0.010854
[2025-10-22 23:32:18] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -63.665056 | E_var:     0.4615 | E_err:   0.010615
[2025-10-22 23:32:31] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -63.659144 | E_var:     0.6328 | E_err:   0.012429
[2025-10-22 23:32:44] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -63.679416 | E_var:     0.4588 | E_err:   0.010583
[2025-10-22 23:32:57] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -63.647060 | E_var:     0.4789 | E_err:   0.010812
[2025-10-22 23:33:10] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -63.667712 | E_var:     0.4611 | E_err:   0.010610
[2025-10-22 23:33:24] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -63.692400 | E_var:     0.4895 | E_err:   0.010932
[2025-10-22 23:33:37] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -63.670417 | E_var:     0.4202 | E_err:   0.010129
[2025-10-22 23:33:50] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -63.667407 | E_var:     0.4987 | E_err:   0.011034
[2025-10-22 23:34:03] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -63.652667 | E_var:     0.4820 | E_err:   0.010848
[2025-10-22 23:34:16] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -63.671474 | E_var:     0.3946 | E_err:   0.009815
[2025-10-22 23:34:29] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -63.666160 | E_var:     0.4549 | E_err:   0.010538
[2025-10-22 23:34:43] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -63.669340 | E_var:     0.4708 | E_err:   0.010721
[2025-10-22 23:34:56] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -63.662636 | E_var:     0.4391 | E_err:   0.010354
[2025-10-22 23:35:09] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -63.652724 | E_var:     0.5219 | E_err:   0.011287
[2025-10-22 23:35:22] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -63.678082 | E_var:     0.4510 | E_err:   0.010494
[2025-10-22 23:35:35] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -63.656590 | E_var:     0.4592 | E_err:   0.010588
[2025-10-22 23:35:49] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -63.657042 | E_var:     0.4368 | E_err:   0.010326
[2025-10-22 23:36:02] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -63.662181 | E_var:     0.5372 | E_err:   0.011453
[2025-10-22 23:36:15] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -63.661995 | E_var:     0.5093 | E_err:   0.011151
[2025-10-22 23:36:28] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -63.672083 | E_var:     0.4595 | E_err:   0.010591
[2025-10-22 23:36:42] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -63.648413 | E_var:     0.5175 | E_err:   0.011240
[2025-10-22 23:36:55] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -63.640078 | E_var:     0.5873 | E_err:   0.011975
[2025-10-22 23:37:08] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -63.662616 | E_var:     0.4846 | E_err:   0.010877
[2025-10-22 23:37:21] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -63.659825 | E_var:     0.4440 | E_err:   0.010411
[2025-10-22 23:37:34] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -63.660315 | E_var:     0.4810 | E_err:   0.010836
[2025-10-22 23:37:47] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -63.672588 | E_var:     0.5297 | E_err:   0.011372
[2025-10-22 23:38:01] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -63.665230 | E_var:     0.4797 | E_err:   0.010822
[2025-10-22 23:38:14] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -63.674588 | E_var:     0.4323 | E_err:   0.010274
[2025-10-22 23:38:27] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -63.647574 | E_var:     0.5896 | E_err:   0.011998
[2025-10-22 23:38:40] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -63.657816 | E_var:     0.4994 | E_err:   0.011041
[2025-10-22 23:38:53] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -63.656794 | E_var:     0.4683 | E_err:   0.010693
[2025-10-22 23:39:06] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -63.652784 | E_var:     0.4537 | E_err:   0.010524
[2025-10-22 23:39:20] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -63.657230 | E_var:     0.5060 | E_err:   0.011114
[2025-10-22 23:39:33] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -63.661017 | E_var:     0.4466 | E_err:   0.010442
[2025-10-22 23:39:46] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -63.658446 | E_var:     0.4765 | E_err:   0.010786
[2025-10-22 23:39:59] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -63.666517 | E_var:     0.4208 | E_err:   0.010136
[2025-10-22 23:40:12] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -63.656014 | E_var:     0.4595 | E_err:   0.010591
[2025-10-22 23:40:25] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -63.670891 | E_var:     0.4704 | E_err:   0.010717
[2025-10-22 23:40:39] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -63.660097 | E_var:     0.7142 | E_err:   0.013205
[2025-10-22 23:40:52] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -63.659279 | E_var:     0.5997 | E_err:   0.012100
[2025-10-22 23:41:05] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -63.665542 | E_var:     0.4650 | E_err:   0.010654
[2025-10-22 23:41:18] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -63.668269 | E_var:     0.4965 | E_err:   0.011010
[2025-10-22 23:41:31] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -63.661746 | E_var:     0.4426 | E_err:   0.010395
[2025-10-22 23:41:45] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -63.654354 | E_var:     0.6368 | E_err:   0.012469
[2025-10-22 23:41:58] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -63.652084 | E_var:     0.5854 | E_err:   0.011955
[2025-10-22 23:42:11] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -63.662088 | E_var:     0.5875 | E_err:   0.011977
[2025-10-22 23:42:24] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -63.656937 | E_var:     0.4320 | E_err:   0.010269
[2025-10-22 23:42:37] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -63.650640 | E_var:     0.4230 | E_err:   0.010162
[2025-10-22 23:42:50] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -63.660542 | E_var:     0.3836 | E_err:   0.009677
[2025-10-22 23:43:04] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -63.656615 | E_var:     0.4680 | E_err:   0.010689
[2025-10-22 23:43:17] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -63.668170 | E_var:     0.6131 | E_err:   0.012235
[2025-10-22 23:43:30] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -63.666861 | E_var:     0.4150 | E_err:   0.010065
[2025-10-22 23:43:43] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -63.657068 | E_var:     0.4874 | E_err:   0.010908
[2025-10-22 23:43:56] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -63.657286 | E_var:     0.4770 | E_err:   0.010791
[2025-10-22 23:44:09] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -63.648567 | E_var:     0.5709 | E_err:   0.011806
[2025-10-22 23:44:23] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -63.663609 | E_var:     0.4728 | E_err:   0.010743
[2025-10-22 23:44:36] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -63.661356 | E_var:     0.4793 | E_err:   0.010818
[2025-10-22 23:44:49] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -63.647794 | E_var:     0.5042 | E_err:   0.011095
[2025-10-22 23:45:02] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -63.653802 | E_var:     0.5259 | E_err:   0.011331
[2025-10-22 23:45:15] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -63.659707 | E_var:     0.5123 | E_err:   0.011184
[2025-10-22 23:45:29] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -63.665561 | E_var:     0.4798 | E_err:   0.010823
[2025-10-22 23:45:42] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -63.670579 | E_var:     0.6003 | E_err:   0.012106
[2025-10-22 23:45:55] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -63.670828 | E_var:     0.6723 | E_err:   0.012811
[2025-10-22 23:46:08] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -63.663637 | E_var:     0.5263 | E_err:   0.011335
[2025-10-22 23:46:21] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -63.645435 | E_var:     0.4567 | E_err:   0.010559
[2025-10-22 23:46:34] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -63.672090 | E_var:     0.4667 | E_err:   0.010675
[2025-10-22 23:46:48] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -63.644125 | E_var:     0.7450 | E_err:   0.013487
[2025-10-22 23:47:01] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -63.676711 | E_var:     0.4808 | E_err:   0.010834
[2025-10-22 23:47:14] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -63.656499 | E_var:     0.4198 | E_err:   0.010124
[2025-10-22 23:47:27] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -63.682300 | E_var:     0.4099 | E_err:   0.010004
[2025-10-22 23:47:40] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -63.674214 | E_var:     0.4309 | E_err:   0.010257
[2025-10-22 23:47:53] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -63.677185 | E_var:     0.5162 | E_err:   0.011226
[2025-10-22 23:48:07] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -63.656682 | E_var:     0.4789 | E_err:   0.010813
[2025-10-22 23:48:20] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -63.658713 | E_var:     0.4192 | E_err:   0.010116
[2025-10-22 23:48:33] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -63.663157 | E_var:     0.4787 | E_err:   0.010811
[2025-10-22 23:48:46] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -63.647245 | E_var:     0.4647 | E_err:   0.010652
[2025-10-22 23:48:59] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -63.659962 | E_var:     0.4623 | E_err:   0.010624
[2025-10-22 23:49:12] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -63.652259 | E_var:     0.4180 | E_err:   0.010102
[2025-10-22 23:49:26] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -63.665788 | E_var:     0.4618 | E_err:   0.010618
[2025-10-22 23:49:39] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -63.670164 | E_var:     0.4779 | E_err:   0.010801
[2025-10-22 23:49:52] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -63.647526 | E_var:     0.6494 | E_err:   0.012591
[2025-10-22 23:50:05] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -63.680567 | E_var:     0.5710 | E_err:   0.011807
[2025-10-22 23:50:18] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -63.664433 | E_var:     0.4874 | E_err:   0.010908
[2025-10-22 23:50:31] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -63.651972 | E_var:     0.5219 | E_err:   0.011288
[2025-10-22 23:50:45] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -63.660468 | E_var:     0.4416 | E_err:   0.010383
[2025-10-22 23:50:58] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -63.660221 | E_var:     0.5283 | E_err:   0.011357
[2025-10-22 23:51:11] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -63.670818 | E_var:     0.4873 | E_err:   0.010908
[2025-10-22 23:51:11] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-22 23:51:24] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -63.661847 | E_var:     0.4536 | E_err:   0.010524
[2025-10-22 23:51:37] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -63.648570 | E_var:     0.4428 | E_err:   0.010397
[2025-10-22 23:51:51] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -63.679589 | E_var:     0.5519 | E_err:   0.011608
[2025-10-22 23:52:04] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -63.665637 | E_var:     0.4499 | E_err:   0.010480
[2025-10-22 23:52:17] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -63.667876 | E_var:     0.5298 | E_err:   0.011374
[2025-10-22 23:52:30] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -63.656673 | E_var:     0.4791 | E_err:   0.010816
[2025-10-22 23:52:43] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -63.667126 | E_var:     0.4878 | E_err:   0.010913
[2025-10-22 23:52:56] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -63.645696 | E_var:     0.4056 | E_err:   0.009951
[2025-10-22 23:53:10] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -63.655406 | E_var:     0.5790 | E_err:   0.011890
[2025-10-22 23:53:23] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -63.659302 | E_var:     0.5287 | E_err:   0.011362
[2025-10-22 23:53:36] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -63.672497 | E_var:     0.5244 | E_err:   0.011315
[2025-10-22 23:53:49] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -63.654343 | E_var:     0.5074 | E_err:   0.011130
[2025-10-22 23:54:02] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -63.678179 | E_var:     0.4381 | E_err:   0.010342
[2025-10-22 23:54:15] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -63.645676 | E_var:     0.6279 | E_err:   0.012381
[2025-10-22 23:54:29] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -63.651325 | E_var:     0.4881 | E_err:   0.010917
[2025-10-22 23:54:42] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -63.679566 | E_var:     0.4781 | E_err:   0.010804
[2025-10-22 23:54:55] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -63.649333 | E_var:     0.6524 | E_err:   0.012621
[2025-10-22 23:55:08] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -63.683993 | E_var:     0.5096 | E_err:   0.011154
[2025-10-22 23:55:21] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -63.654541 | E_var:     0.4208 | E_err:   0.010136
[2025-10-22 23:55:35] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -63.664292 | E_var:     0.5210 | E_err:   0.011278
[2025-10-22 23:55:48] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -63.665623 | E_var:     0.5105 | E_err:   0.011164
[2025-10-22 23:56:01] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -63.676036 | E_var:     0.5647 | E_err:   0.011741
[2025-10-22 23:56:14] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -63.652302 | E_var:     0.5068 | E_err:   0.011123
[2025-10-22 23:56:27] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -63.654511 | E_var:     0.9638 | E_err:   0.015339
[2025-10-22 23:56:40] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -63.650094 | E_var:     0.8587 | E_err:   0.014479
[2025-10-22 23:56:54] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -63.674751 | E_var:     0.4897 | E_err:   0.010934
[2025-10-22 23:57:07] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -63.662528 | E_var:     0.4996 | E_err:   0.011044
[2025-10-22 23:57:20] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -63.670798 | E_var:     0.5516 | E_err:   0.011605
[2025-10-22 23:57:33] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -63.645106 | E_var:     0.6364 | E_err:   0.012465
[2025-10-22 23:57:46] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -63.647588 | E_var:     0.4999 | E_err:   0.011048
[2025-10-22 23:57:59] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -63.662848 | E_var:     0.5411 | E_err:   0.011494
[2025-10-22 23:58:13] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -63.666947 | E_var:     0.4163 | E_err:   0.010082
[2025-10-22 23:58:26] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -63.664221 | E_var:     0.4269 | E_err:   0.010209
[2025-10-22 23:58:39] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -63.658298 | E_var:     0.4540 | E_err:   0.010528
[2025-10-22 23:58:52] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -63.665737 | E_var:     0.4430 | E_err:   0.010400
[2025-10-22 23:59:05] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -63.655456 | E_var:     0.4909 | E_err:   0.010947
[2025-10-22 23:59:19] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -63.655324 | E_var:     0.4850 | E_err:   0.010882
[2025-10-22 23:59:32] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -63.678154 | E_var:     0.4615 | E_err:   0.010614
[2025-10-22 23:59:45] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -63.658994 | E_var:     0.4017 | E_err:   0.009904
[2025-10-22 23:59:58] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -63.658882 | E_var:     0.5018 | E_err:   0.011069
[2025-10-23 00:00:11] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -63.659435 | E_var:     0.5051 | E_err:   0.011104
[2025-10-23 00:00:24] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -63.657035 | E_var:     0.4278 | E_err:   0.010219
[2025-10-23 00:00:38] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -63.672140 | E_var:     0.4111 | E_err:   0.010018
[2025-10-23 00:00:51] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -63.652530 | E_var:     0.5083 | E_err:   0.011140
[2025-10-23 00:01:04] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -63.663635 | E_var:     0.4125 | E_err:   0.010036
[2025-10-23 00:01:17] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -63.661582 | E_var:     0.4504 | E_err:   0.010486
[2025-10-23 00:01:30] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -63.670745 | E_var:     0.5920 | E_err:   0.012022
[2025-10-23 00:01:43] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -63.650805 | E_var:     0.4842 | E_err:   0.010872
[2025-10-23 00:01:57] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -63.675365 | E_var:     0.4934 | E_err:   0.010976
[2025-10-23 00:02:10] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -63.657871 | E_var:     0.7478 | E_err:   0.013512
[2025-10-23 00:02:23] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -63.682821 | E_var:     0.4657 | E_err:   0.010663
[2025-10-23 00:02:36] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -63.670308 | E_var:     0.5176 | E_err:   0.011242
[2025-10-23 00:02:49] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -63.639233 | E_var:     0.4625 | E_err:   0.010626
[2025-10-23 00:03:03] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -63.670815 | E_var:     0.4180 | E_err:   0.010101
[2025-10-23 00:03:16] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -63.653410 | E_var:     0.4355 | E_err:   0.010311
[2025-10-23 00:03:29] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -63.677370 | E_var:     0.5568 | E_err:   0.011659
[2025-10-23 00:03:42] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -63.675159 | E_var:     0.4358 | E_err:   0.010314
[2025-10-23 00:03:55] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -63.660500 | E_var:     0.4701 | E_err:   0.010713
[2025-10-23 00:04:08] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -63.655311 | E_var:     0.5180 | E_err:   0.011245
[2025-10-23 00:04:22] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -63.651251 | E_var:     0.4095 | E_err:   0.009998
[2025-10-23 00:04:35] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -63.660745 | E_var:     0.4291 | E_err:   0.010235
[2025-10-23 00:04:48] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -63.651488 | E_var:     0.4175 | E_err:   0.010096
[2025-10-23 00:05:01] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -63.686897 | E_var:     0.4690 | E_err:   0.010700
[2025-10-23 00:05:14] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -63.658623 | E_var:     0.4450 | E_err:   0.010423
[2025-10-23 00:05:27] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -63.682872 | E_var:     0.3693 | E_err:   0.009495
[2025-10-23 00:05:41] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -63.666202 | E_var:     0.6758 | E_err:   0.012845
[2025-10-23 00:05:54] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -63.634058 | E_var:     0.5013 | E_err:   0.011063
[2025-10-23 00:06:07] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -63.659862 | E_var:     0.4779 | E_err:   0.010802
[2025-10-23 00:06:20] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -63.653846 | E_var:     0.4003 | E_err:   0.009886
[2025-10-23 00:06:33] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -63.674402 | E_var:     0.4180 | E_err:   0.010102
[2025-10-23 00:06:47] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -63.669857 | E_var:     0.5101 | E_err:   0.011159
[2025-10-23 00:07:00] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -63.652986 | E_var:     0.5121 | E_err:   0.011181
[2025-10-23 00:07:13] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -63.682163 | E_var:     0.5919 | E_err:   0.012021
[2025-10-23 00:07:26] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -63.669825 | E_var:     0.5329 | E_err:   0.011407
[2025-10-23 00:07:39] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -63.687080 | E_var:     0.5251 | E_err:   0.011322
[2025-10-23 00:07:52] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -63.672286 | E_var:     0.4564 | E_err:   0.010556
[2025-10-23 00:08:06] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -63.660113 | E_var:     0.5949 | E_err:   0.012052
[2025-10-23 00:08:19] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -63.659429 | E_var:     0.4439 | E_err:   0.010410
[2025-10-23 00:08:32] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -63.637890 | E_var:     0.5200 | E_err:   0.011267
[2025-10-23 00:08:45] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -63.661528 | E_var:     0.5649 | E_err:   0.011743
[2025-10-23 00:08:58] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -63.661130 | E_var:     0.4424 | E_err:   0.010392
[2025-10-23 00:09:11] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -63.652074 | E_var:     0.4874 | E_err:   0.010908
[2025-10-23 00:09:25] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -63.664836 | E_var:     0.5819 | E_err:   0.011919
[2025-10-23 00:09:38] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -63.669529 | E_var:     0.4917 | E_err:   0.010956
[2025-10-23 00:09:51] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -63.662299 | E_var:     0.5519 | E_err:   0.011608
[2025-10-23 00:10:04] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -63.662902 | E_var:     0.5642 | E_err:   0.011736
[2025-10-23 00:10:17] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -63.666275 | E_var:     0.5634 | E_err:   0.011728
[2025-10-23 00:10:30] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -63.660001 | E_var:     0.4463 | E_err:   0.010438
[2025-10-23 00:10:44] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -63.663231 | E_var:     0.4183 | E_err:   0.010105
[2025-10-23 00:10:57] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -63.675686 | E_var:     0.4798 | E_err:   0.010823
[2025-10-23 00:11:10] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -63.653523 | E_var:     0.4603 | E_err:   0.010601
[2025-10-23 00:11:23] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -63.653675 | E_var:     0.4250 | E_err:   0.010186
[2025-10-23 00:11:36] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -63.647681 | E_var:     0.4404 | E_err:   0.010370
[2025-10-23 00:11:50] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -63.666600 | E_var:     0.3970 | E_err:   0.009846
[2025-10-23 00:12:03] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -63.678098 | E_var:     0.5170 | E_err:   0.011235
[2025-10-23 00:12:16] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -63.638253 | E_var:     0.4353 | E_err:   0.010309
[2025-10-23 00:12:29] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -63.644299 | E_var:     0.5168 | E_err:   0.011232
[2025-10-23 00:12:42] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -63.664859 | E_var:     0.4946 | E_err:   0.010988
[2025-10-23 00:12:55] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -63.665000 | E_var:     0.5006 | E_err:   0.011055
[2025-10-23 00:13:09] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -63.670319 | E_var:     0.4632 | E_err:   0.010635
[2025-10-23 00:13:22] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -63.660616 | E_var:     0.5686 | E_err:   0.011782
[2025-10-23 00:13:35] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -63.649503 | E_var:     0.5592 | E_err:   0.011684
[2025-10-23 00:13:48] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -63.663551 | E_var:     0.5158 | E_err:   0.011222
[2025-10-23 00:14:01] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -63.640191 | E_var:     0.4933 | E_err:   0.010975
[2025-10-23 00:14:14] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -63.662956 | E_var:     0.4491 | E_err:   0.010471
[2025-10-23 00:14:15] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-23 00:14:28] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -63.672272 | E_var:     0.4283 | E_err:   0.010226
[2025-10-23 00:14:41] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -63.659521 | E_var:     0.6081 | E_err:   0.012184
[2025-10-23 00:14:54] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -63.673203 | E_var:     0.4824 | E_err:   0.010852
[2025-10-23 00:15:07] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -63.653242 | E_var:     0.5079 | E_err:   0.011136
[2025-10-23 00:15:20] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -63.674081 | E_var:     0.4585 | E_err:   0.010580
[2025-10-23 00:15:34] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -63.678795 | E_var:     0.5008 | E_err:   0.011057
[2025-10-23 00:15:47] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -63.657545 | E_var:     0.5374 | E_err:   0.011454
[2025-10-23 00:16:00] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -63.663717 | E_var:     0.5738 | E_err:   0.011836
[2025-10-23 00:16:13] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -63.663239 | E_var:     0.4372 | E_err:   0.010332
[2025-10-23 00:16:26] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -63.643812 | E_var:     0.4573 | E_err:   0.010567
[2025-10-23 00:16:39] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -63.657907 | E_var:     0.4872 | E_err:   0.010906
[2025-10-23 00:16:53] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -63.664316 | E_var:     0.4799 | E_err:   0.010824
[2025-10-23 00:17:06] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -63.659533 | E_var:     0.4338 | E_err:   0.010291
[2025-10-23 00:17:19] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -63.651568 | E_var:     0.5355 | E_err:   0.011434
[2025-10-23 00:17:32] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -63.701392 | E_var:     1.7335 | E_err:   0.020572
[2025-10-23 00:17:45] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -63.655147 | E_var:     0.4175 | E_err:   0.010096
[2025-10-23 00:17:58] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -63.642984 | E_var:     0.4629 | E_err:   0.010631
[2025-10-23 00:18:12] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -63.659236 | E_var:     0.4266 | E_err:   0.010206
[2025-10-23 00:18:25] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -63.664429 | E_var:     0.4071 | E_err:   0.009969
[2025-10-23 00:18:38] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -63.664535 | E_var:     0.5135 | E_err:   0.011196
[2025-10-23 00:18:51] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -63.663022 | E_var:     0.4422 | E_err:   0.010390
[2025-10-23 00:19:04] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -63.674521 | E_var:     0.5464 | E_err:   0.011549
[2025-10-23 00:19:17] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -63.655101 | E_var:     0.5217 | E_err:   0.011286
[2025-10-23 00:19:31] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -63.661279 | E_var:     0.4765 | E_err:   0.010785
[2025-10-23 00:19:44] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -63.668249 | E_var:     0.4420 | E_err:   0.010387
[2025-10-23 00:19:57] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -63.650814 | E_var:     0.4727 | E_err:   0.010742
[2025-10-23 00:20:10] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -63.682543 | E_var:     0.5855 | E_err:   0.011956
[2025-10-23 00:20:23] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -63.658612 | E_var:     0.4190 | E_err:   0.010114
[2025-10-23 00:20:37] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -63.661158 | E_var:     0.4637 | E_err:   0.010640
[2025-10-23 00:20:50] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -63.661828 | E_var:     0.4956 | E_err:   0.011000
[2025-10-23 00:21:03] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -63.657472 | E_var:     0.4422 | E_err:   0.010390
[2025-10-23 00:21:16] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -63.657011 | E_var:     0.5069 | E_err:   0.011124
[2025-10-23 00:21:29] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -63.674345 | E_var:     0.5210 | E_err:   0.011278
[2025-10-23 00:21:42] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -63.662898 | E_var:     0.6040 | E_err:   0.012144
[2025-10-23 00:21:56] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -63.676168 | E_var:     0.4325 | E_err:   0.010276
[2025-10-23 00:22:09] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -63.678576 | E_var:     0.7857 | E_err:   0.013850
[2025-10-23 00:22:22] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -63.660607 | E_var:     0.5479 | E_err:   0.011566
[2025-10-23 00:22:35] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -63.651544 | E_var:     0.4758 | E_err:   0.010778
[2025-10-23 00:22:48] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -63.683570 | E_var:     0.4580 | E_err:   0.010575
[2025-10-23 00:23:01] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -63.652943 | E_var:     0.4932 | E_err:   0.010974
[2025-10-23 00:23:15] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -63.646597 | E_var:     0.4906 | E_err:   0.010944
[2025-10-23 00:23:28] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -63.655872 | E_var:     0.3973 | E_err:   0.009849
[2025-10-23 00:23:41] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -63.656099 | E_var:     0.5056 | E_err:   0.011110
[2025-10-23 00:23:54] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -63.665483 | E_var:     0.4358 | E_err:   0.010314
[2025-10-23 00:24:07] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -63.654752 | E_var:     0.4072 | E_err:   0.009971
[2025-10-23 00:24:20] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -63.644977 | E_var:     0.4783 | E_err:   0.010806
[2025-10-23 00:24:34] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -63.654741 | E_var:     0.4664 | E_err:   0.010671
[2025-10-23 00:24:47] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -63.667935 | E_var:     0.5046 | E_err:   0.011099
[2025-10-23 00:25:00] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -63.658602 | E_var:     0.5891 | E_err:   0.011993
[2025-10-23 00:25:13] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -63.656088 | E_var:     0.4983 | E_err:   0.011030
[2025-10-23 00:25:26] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -63.657472 | E_var:     0.6918 | E_err:   0.012996
[2025-10-23 00:25:39] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -63.669469 | E_var:     0.4392 | E_err:   0.010355
[2025-10-23 00:25:53] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -63.662918 | E_var:     0.4752 | E_err:   0.010772
[2025-10-23 00:26:06] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -63.666029 | E_var:     0.4040 | E_err:   0.009931
[2025-10-23 00:26:19] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -63.671920 | E_var:     0.5507 | E_err:   0.011595
[2025-10-23 00:26:32] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -63.665531 | E_var:     0.4572 | E_err:   0.010565
[2025-10-23 00:26:45] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -63.632656 | E_var:     0.4991 | E_err:   0.011038
[2025-10-23 00:26:58] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -63.671724 | E_var:     0.4632 | E_err:   0.010634
[2025-10-23 00:27:12] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -63.663403 | E_var:     0.4360 | E_err:   0.010317
[2025-10-23 00:27:25] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -63.671331 | E_var:     0.4934 | E_err:   0.010975
[2025-10-23 00:27:38] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -63.664159 | E_var:     0.4720 | E_err:   0.010735
[2025-10-23 00:27:51] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -63.661281 | E_var:     0.5030 | E_err:   0.011082
[2025-10-23 00:28:04] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -63.647874 | E_var:     0.4304 | E_err:   0.010251
[2025-10-23 00:28:18] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -63.674451 | E_var:     0.4475 | E_err:   0.010453
[2025-10-23 00:28:31] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -63.657885 | E_var:     0.4723 | E_err:   0.010739
[2025-10-23 00:28:44] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -63.657022 | E_var:     0.4828 | E_err:   0.010857
[2025-10-23 00:28:57] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -63.676441 | E_var:     0.5408 | E_err:   0.011491
[2025-10-23 00:29:10] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -63.659003 | E_var:     0.5478 | E_err:   0.011565
[2025-10-23 00:29:23] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -63.676336 | E_var:     0.5060 | E_err:   0.011115
[2025-10-23 00:29:37] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -63.669501 | E_var:     0.4743 | E_err:   0.010760
[2025-10-23 00:29:50] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -63.654741 | E_var:     0.4379 | E_err:   0.010340
[2025-10-23 00:30:03] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -63.673862 | E_var:     0.4912 | E_err:   0.010950
[2025-10-23 00:30:16] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -63.666686 | E_var:     0.4471 | E_err:   0.010448
[2025-10-23 00:30:29] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -63.664449 | E_var:     0.4820 | E_err:   0.010848
[2025-10-23 00:30:42] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -63.683896 | E_var:     0.4301 | E_err:   0.010247
[2025-10-23 00:30:56] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -63.683403 | E_var:     0.5683 | E_err:   0.011779
[2025-10-23 00:31:09] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -63.690870 | E_var:     0.5282 | E_err:   0.011356
[2025-10-23 00:31:22] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -63.670305 | E_var:     0.3565 | E_err:   0.009329
[2025-10-23 00:31:35] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -63.673537 | E_var:     0.5229 | E_err:   0.011299
[2025-10-23 00:31:48] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -63.684200 | E_var:     0.4083 | E_err:   0.009984
[2025-10-23 00:32:01] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -63.672468 | E_var:     0.5297 | E_err:   0.011372
[2025-10-23 00:32:15] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -63.665593 | E_var:     0.6114 | E_err:   0.012218
[2025-10-23 00:32:28] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -63.655493 | E_var:     0.4100 | E_err:   0.010004
[2025-10-23 00:32:41] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -63.645052 | E_var:     0.5468 | E_err:   0.011554
[2025-10-23 00:32:54] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -63.659778 | E_var:     0.5042 | E_err:   0.011095
[2025-10-23 00:33:07] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -63.656290 | E_var:     0.4943 | E_err:   0.010985
[2025-10-23 00:33:20] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -63.668218 | E_var:     0.5159 | E_err:   0.011223
[2025-10-23 00:33:34] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -63.673762 | E_var:     0.4084 | E_err:   0.009986
[2025-10-23 00:33:47] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -63.671786 | E_var:     0.4371 | E_err:   0.010330
[2025-10-23 00:34:00] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -63.662136 | E_var:     0.3913 | E_err:   0.009774
[2025-10-23 00:34:13] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -63.659301 | E_var:     0.4125 | E_err:   0.010035
[2025-10-23 00:34:26] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -63.663210 | E_var:     0.4537 | E_err:   0.010524
[2025-10-23 00:34:40] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -63.665833 | E_var:     0.4308 | E_err:   0.010256
[2025-10-23 00:34:53] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -63.656957 | E_var:     0.4732 | E_err:   0.010749
[2025-10-23 00:35:06] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -63.658746 | E_var:     0.4624 | E_err:   0.010625
[2025-10-23 00:35:19] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -63.659635 | E_var:     0.6229 | E_err:   0.012332
[2025-10-23 00:35:32] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -63.653132 | E_var:     0.4950 | E_err:   0.010993
[2025-10-23 00:35:45] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -63.655463 | E_var:     0.4853 | E_err:   0.010885
[2025-10-23 00:35:59] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -63.659780 | E_var:     0.5874 | E_err:   0.011975
[2025-10-23 00:36:12] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -63.678837 | E_var:     0.5305 | E_err:   0.011380
[2025-10-23 00:36:25] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -63.666966 | E_var:     0.4017 | E_err:   0.009903
[2025-10-23 00:36:38] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -63.654429 | E_var:     0.5087 | E_err:   0.011144
[2025-10-23 00:36:51] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -63.669958 | E_var:     0.4453 | E_err:   0.010426
[2025-10-23 00:37:04] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -63.664370 | E_var:     0.5349 | E_err:   0.011428
[2025-10-23 00:37:18] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -63.633814 | E_var:     0.5160 | E_err:   0.011224
[2025-10-23 00:37:18] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-23 00:37:31] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -63.658573 | E_var:     0.4932 | E_err:   0.010973
[2025-10-23 00:37:44] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -63.672724 | E_var:     0.4792 | E_err:   0.010816
[2025-10-23 00:37:57] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -63.654571 | E_var:     0.4714 | E_err:   0.010728
[2025-10-23 00:38:10] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -63.674045 | E_var:     0.8377 | E_err:   0.014301
[2025-10-23 00:38:23] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -63.660840 | E_var:     0.4259 | E_err:   0.010198
[2025-10-23 00:38:37] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -63.637586 | E_var:     0.4172 | E_err:   0.010093
[2025-10-23 00:38:50] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -63.644770 | E_var:     0.3948 | E_err:   0.009817
[2025-10-23 00:39:03] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -63.651449 | E_var:     0.5208 | E_err:   0.011276
[2025-10-23 00:39:16] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -63.662941 | E_var:     0.4444 | E_err:   0.010416
[2025-10-23 00:39:29] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -63.657137 | E_var:     0.4513 | E_err:   0.010497
[2025-10-23 00:39:42] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -63.667458 | E_var:     0.4315 | E_err:   0.010264
[2025-10-23 00:39:56] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -63.660772 | E_var:     0.3717 | E_err:   0.009527
[2025-10-23 00:40:09] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -63.671613 | E_var:     0.5968 | E_err:   0.012071
[2025-10-23 00:40:22] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -63.650405 | E_var:     0.4736 | E_err:   0.010753
[2025-10-23 00:40:35] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -63.657786 | E_var:     0.4579 | E_err:   0.010573
[2025-10-23 00:40:48] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -63.643744 | E_var:     0.4331 | E_err:   0.010283
[2025-10-23 00:41:02] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -63.632630 | E_var:     2.4530 | E_err:   0.024472
[2025-10-23 00:41:15] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -63.672415 | E_var:     0.4050 | E_err:   0.009943
[2025-10-23 00:41:28] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -63.657223 | E_var:     0.4636 | E_err:   0.010639
[2025-10-23 00:41:41] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -63.670812 | E_var:     0.6248 | E_err:   0.012350
[2025-10-23 00:41:54] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -63.657398 | E_var:     0.4297 | E_err:   0.010243
[2025-10-23 00:42:07] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -63.660896 | E_var:     0.4293 | E_err:   0.010237
[2025-10-23 00:42:21] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -63.674495 | E_var:     0.4095 | E_err:   0.009999
[2025-10-23 00:42:34] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -63.642131 | E_var:     0.5312 | E_err:   0.011388
[2025-10-23 00:42:47] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -63.675648 | E_var:     0.4564 | E_err:   0.010556
[2025-10-23 00:43:00] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -63.658052 | E_var:     0.3673 | E_err:   0.009470
[2025-10-23 00:43:13] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -63.663089 | E_var:     0.4790 | E_err:   0.010814
[2025-10-23 00:43:26] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -63.652842 | E_var:     0.4336 | E_err:   0.010289
[2025-10-23 00:43:40] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -63.659256 | E_var:     0.4599 | E_err:   0.010596
[2025-10-23 00:43:53] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -63.665205 | E_var:     0.4018 | E_err:   0.009905
[2025-10-23 00:44:06] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -63.673729 | E_var:     0.4514 | E_err:   0.010498
[2025-10-23 00:44:19] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -63.656171 | E_var:     0.4230 | E_err:   0.010162
[2025-10-23 00:44:32] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -63.659132 | E_var:     0.5089 | E_err:   0.011146
[2025-10-23 00:44:45] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -63.646365 | E_var:     0.4072 | E_err:   0.009971
[2025-10-23 00:44:59] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -63.656208 | E_var:     0.4837 | E_err:   0.010867
[2025-10-23 00:45:12] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -63.647432 | E_var:     0.4572 | E_err:   0.010565
[2025-10-23 00:45:25] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -63.667761 | E_var:     0.5128 | E_err:   0.011190
[2025-10-23 00:45:38] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -63.661558 | E_var:     0.5047 | E_err:   0.011100
[2025-10-23 00:45:51] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -63.659495 | E_var:     0.4508 | E_err:   0.010491
[2025-10-23 00:46:04] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -63.656349 | E_var:     0.5380 | E_err:   0.011460
[2025-10-23 00:46:18] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -63.655365 | E_var:     0.4611 | E_err:   0.010610
[2025-10-23 00:46:31] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -63.666922 | E_var:     0.5126 | E_err:   0.011186
[2025-10-23 00:46:44] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -63.664896 | E_var:     0.4694 | E_err:   0.010705
[2025-10-23 00:46:57] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -63.658897 | E_var:     0.4356 | E_err:   0.010313
[2025-10-23 00:47:10] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -63.678461 | E_var:     0.4779 | E_err:   0.010801
[2025-10-23 00:47:24] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -63.645072 | E_var:     0.4688 | E_err:   0.010698
[2025-10-23 00:47:37] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -63.647983 | E_var:     0.4550 | E_err:   0.010540
[2025-10-23 00:47:50] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -63.652332 | E_var:     0.4213 | E_err:   0.010141
[2025-10-23 00:48:03] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -63.678329 | E_var:     0.5696 | E_err:   0.011793
[2025-10-23 00:48:16] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -63.671101 | E_var:     0.4524 | E_err:   0.010509
[2025-10-23 00:48:29] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -63.678304 | E_var:     0.4927 | E_err:   0.010967
[2025-10-23 00:48:43] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -63.668505 | E_var:     0.4626 | E_err:   0.010627
[2025-10-23 00:48:56] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -63.670601 | E_var:     0.4671 | E_err:   0.010679
[2025-10-23 00:49:09] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -63.654897 | E_var:     0.4040 | E_err:   0.009931
[2025-10-23 00:49:22] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -63.677163 | E_var:     0.4735 | E_err:   0.010752
[2025-10-23 00:49:35] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -63.664157 | E_var:     0.4406 | E_err:   0.010371
[2025-10-23 00:49:48] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -63.649160 | E_var:     0.4683 | E_err:   0.010693
[2025-10-23 00:50:02] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -63.661445 | E_var:     0.5647 | E_err:   0.011742
[2025-10-23 00:50:15] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -63.648125 | E_var:     0.4169 | E_err:   0.010088
[2025-10-23 00:50:28] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -63.642301 | E_var:     0.4626 | E_err:   0.010627
[2025-10-23 00:50:41] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -63.667862 | E_var:     0.3755 | E_err:   0.009575
[2025-10-23 00:50:54] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -63.672759 | E_var:     0.4198 | E_err:   0.010124
[2025-10-23 00:51:07] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -63.686617 | E_var:     0.6611 | E_err:   0.012705
[2025-10-23 00:51:21] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -63.667470 | E_var:     0.4465 | E_err:   0.010440
[2025-10-23 00:51:34] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -63.663339 | E_var:     0.3880 | E_err:   0.009733
[2025-10-23 00:51:47] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -63.646749 | E_var:     0.4781 | E_err:   0.010804
[2025-10-23 00:52:00] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -63.672238 | E_var:     0.4834 | E_err:   0.010864
[2025-10-23 00:52:13] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -63.663014 | E_var:     0.5212 | E_err:   0.011281
[2025-10-23 00:52:26] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -63.659422 | E_var:     0.4367 | E_err:   0.010325
[2025-10-23 00:52:40] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -63.674395 | E_var:     0.4391 | E_err:   0.010354
[2025-10-23 00:52:53] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -63.671721 | E_var:     0.4625 | E_err:   0.010626
[2025-10-23 00:53:06] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -63.667059 | E_var:     0.5437 | E_err:   0.011521
[2025-10-23 00:53:19] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -63.671554 | E_var:     0.4836 | E_err:   0.010866
[2025-10-23 00:53:32] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -63.673797 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 00:53:45] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -63.667581 | E_var:     0.5024 | E_err:   0.011075
[2025-10-23 00:53:59] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -63.656157 | E_var:     0.4743 | E_err:   0.010761
[2025-10-23 00:54:12] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -63.660942 | E_var:     0.5182 | E_err:   0.011248
[2025-10-23 00:54:25] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -63.651577 | E_var:     0.4431 | E_err:   0.010401
[2025-10-23 00:54:38] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -63.668355 | E_var:     0.4169 | E_err:   0.010089
[2025-10-23 00:54:51] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -63.659282 | E_var:     0.4612 | E_err:   0.010611
[2025-10-23 00:55:05] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -63.671381 | E_var:     0.4712 | E_err:   0.010726
[2025-10-23 00:55:18] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -63.668152 | E_var:     0.3987 | E_err:   0.009866
[2025-10-23 00:55:31] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -63.667934 | E_var:     0.4304 | E_err:   0.010251
[2025-10-23 00:55:44] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -63.676740 | E_var:     0.3886 | E_err:   0.009740
[2025-10-23 00:55:57] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -63.657426 | E_var:     0.4321 | E_err:   0.010271
[2025-10-23 00:56:10] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -63.662900 | E_var:     0.4412 | E_err:   0.010378
[2025-10-23 00:56:24] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -63.657040 | E_var:     0.4848 | E_err:   0.010879
[2025-10-23 00:56:37] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -63.665268 | E_var:     0.8632 | E_err:   0.014517
[2025-10-23 00:56:50] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -63.659645 | E_var:     0.4244 | E_err:   0.010179
[2025-10-23 00:57:03] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -63.640842 | E_var:     0.4932 | E_err:   0.010974
[2025-10-23 00:57:16] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -63.659262 | E_var:     0.4868 | E_err:   0.010902
[2025-10-23 00:57:29] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -63.673759 | E_var:     0.3957 | E_err:   0.009828
[2025-10-23 00:57:43] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -63.656420 | E_var:     0.4238 | E_err:   0.010172
[2025-10-23 00:57:56] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -63.663673 | E_var:     0.5306 | E_err:   0.011381
[2025-10-23 00:58:09] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -63.660469 | E_var:     0.5492 | E_err:   0.011579
[2025-10-23 00:58:22] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -63.653023 | E_var:     0.4566 | E_err:   0.010558
[2025-10-23 00:58:35] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -63.660800 | E_var:     0.4587 | E_err:   0.010583
[2025-10-23 00:58:48] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -63.662241 | E_var:     0.4430 | E_err:   0.010400
[2025-10-23 00:59:02] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -63.672214 | E_var:     0.4344 | E_err:   0.010298
[2025-10-23 00:59:15] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -63.649816 | E_var:     0.4688 | E_err:   0.010698
[2025-10-23 00:59:28] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -63.664745 | E_var:     0.4163 | E_err:   0.010082
[2025-10-23 00:59:41] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -63.681023 | E_var:     0.3837 | E_err:   0.009679
[2025-10-23 00:59:54] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -63.655017 | E_var:     0.5120 | E_err:   0.011181
[2025-10-23 01:00:08] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -63.649769 | E_var:     0.4635 | E_err:   0.010638
[2025-10-23 01:00:21] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -63.660900 | E_var:     0.4437 | E_err:   0.010408
[2025-10-23 01:00:21] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-23 01:00:21] ======================================================================================================
[2025-10-23 01:00:21] ✅ Training completed successfully
[2025-10-23 01:00:21] Total restarts: 2
[2025-10-23 01:00:25] Final Energy: -63.66090009 ± 0.01040835
[2025-10-23 01:00:25] Final Variance: 0.443735
[2025-10-23 01:00:25] ======================================================================================================
[2025-10-23 01:00:25] ======================================================================================================
[2025-10-23 01:00:25] Training completed | Runtime: 13896.5s
