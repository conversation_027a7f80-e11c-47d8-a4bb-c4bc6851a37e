[2025-10-23 12:39:20] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-23 12:39:20]   - 迭代次数: 1050
[2025-10-23 12:39:20]   - 能量: -66.444141-0.001337j ± 0.008877, Var: 0.322757
[2025-10-23 12:39:20]   - 时间戳: 2025-10-23T12:38:56.529000+08:00
[2025-10-23 12:39:45] ✓ 变分状态参数已从checkpoint恢复
[2025-10-23 12:39:45] ======================================================================================================
[2025-10-23 12:39:45] GCNN for Shastry-Sutherland Model
[2025-10-23 12:39:45] ======================================================================================================
[2025-10-23 12:39:45] System parameters:
[2025-10-23 12:39:45]   - System size: L=6, N=144
[2025-10-23 12:39:45]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-10-23 12:39:45] ------------------------------------------------------------------------------------------------------
[2025-10-23 12:39:45] Model parameters:
[2025-10-23 12:39:45]   - Number of layers = 4
[2025-10-23 12:39:45]   - Number of features = 4
[2025-10-23 12:39:45]   - Total parameters = 28252
[2025-10-23 12:39:45] ------------------------------------------------------------------------------------------------------
[2025-10-23 12:39:45] Training parameters:
[2025-10-23 12:39:45]   - Total iterations: 1050
[2025-10-23 12:39:45]   - Annealing cycles: 3
[2025-10-23 12:39:45]   - Initial period: 150
[2025-10-23 12:39:45]   - Period multiplier: 2.0
[2025-10-23 12:39:45]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-23 12:39:45]   - Samples: 4096
[2025-10-23 12:39:45]   - Discarded samples: 0
[2025-10-23 12:39:45]   - Chunk size: 4096
[2025-10-23 12:39:45]   - Diagonal shift: 0.15
[2025-10-23 12:39:45]   - Gradient clipping: 1.0
[2025-10-23 12:39:45]   - Checkpoint enabled: interval=105
[2025-10-23 12:39:45]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.83/training/checkpoints
[2025-10-23 12:39:45]   - Resuming from iteration: 1050
[2025-10-23 12:39:45] ------------------------------------------------------------------------------------------------------
[2025-10-23 12:39:45] Device status:
[2025-10-23 12:39:45]   - Devices model: NVIDIA H200 NVL
[2025-10-23 12:39:45]   - Number of devices: 1
[2025-10-23 12:39:45]   - Sharding: True
[2025-10-23 12:39:46] ======================================================================================================
[2025-10-23 12:40:37] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -67.380093 | E_var:     1.0164 | E_err:   0.015753
[2025-10-23 12:41:10] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -67.331436 | E_var:     0.6535 | E_err:   0.012631
[2025-10-23 12:41:23] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -67.347012 | E_var:     0.5046 | E_err:   0.011100
[2025-10-23 12:41:36] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -67.329561 | E_var:     0.4253 | E_err:   0.010190
[2025-10-23 12:41:49] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -67.336542 | E_var:     0.5156 | E_err:   0.011220
[2025-10-23 12:42:03] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -67.340415 | E_var:     0.4554 | E_err:   0.010544
[2025-10-23 12:42:16] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -67.345711 | E_var:     0.3585 | E_err:   0.009355
[2025-10-23 12:42:29] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -67.353457 | E_var:     0.5476 | E_err:   0.011562
[2025-10-23 12:42:42] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -67.334724 | E_var:     0.3942 | E_err:   0.009810
[2025-10-23 12:42:55] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -67.351990 | E_var:     0.3864 | E_err:   0.009713
[2025-10-23 12:43:08] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -67.352869 | E_var:     0.4482 | E_err:   0.010461
[2025-10-23 12:43:22] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -67.343206 | E_var:     0.3637 | E_err:   0.009423
[2025-10-23 12:43:35] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -67.338801 | E_var:     0.4049 | E_err:   0.009942
[2025-10-23 12:43:48] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -67.368467 | E_var:     0.3512 | E_err:   0.009259
[2025-10-23 12:44:01] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -67.352141 | E_var:     0.3367 | E_err:   0.009066
[2025-10-23 12:44:14] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -67.336532 | E_var:     0.3228 | E_err:   0.008878
[2025-10-23 12:44:27] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -67.346014 | E_var:     0.3717 | E_err:   0.009526
[2025-10-23 12:44:40] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -67.346433 | E_var:     0.3453 | E_err:   0.009181
[2025-10-23 12:44:53] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -67.352267 | E_var:     0.3257 | E_err:   0.008917
[2025-10-23 12:45:07] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -67.344201 | E_var:     0.3721 | E_err:   0.009531
[2025-10-23 12:45:20] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -67.353135 | E_var:     0.3551 | E_err:   0.009311
[2025-10-23 12:45:33] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -67.356421 | E_var:     0.3837 | E_err:   0.009679
[2025-10-23 12:45:46] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -67.362754 | E_var:     0.6338 | E_err:   0.012439
[2025-10-23 12:45:59] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -67.363239 | E_var:     0.3303 | E_err:   0.008980
[2025-10-23 12:46:12] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -67.355272 | E_var:     0.3547 | E_err:   0.009306
[2025-10-23 12:46:25] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -67.350657 | E_var:     0.3251 | E_err:   0.008909
[2025-10-23 12:46:39] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -67.360248 | E_var:     0.3443 | E_err:   0.009169
[2025-10-23 12:46:52] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -67.355075 | E_var:     0.4020 | E_err:   0.009907
[2025-10-23 12:47:05] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -67.366742 | E_var:     0.3527 | E_err:   0.009279
[2025-10-23 12:47:18] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -67.347950 | E_var:     0.3913 | E_err:   0.009774
[2025-10-23 12:47:31] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -67.355886 | E_var:     0.3167 | E_err:   0.008793
[2025-10-23 12:47:44] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -67.352687 | E_var:     0.3678 | E_err:   0.009476
[2025-10-23 12:47:57] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -67.367386 | E_var:     0.3757 | E_err:   0.009577
[2025-10-23 12:48:11] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -67.367373 | E_var:     0.3528 | E_err:   0.009281
[2025-10-23 12:48:24] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -67.356642 | E_var:     0.4585 | E_err:   0.010580
[2025-10-23 12:48:37] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -67.350518 | E_var:     0.3688 | E_err:   0.009489
[2025-10-23 12:48:50] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -67.350855 | E_var:     0.4124 | E_err:   0.010034
[2025-10-23 12:49:03] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -67.353625 | E_var:     0.4545 | E_err:   0.010534
[2025-10-23 12:49:16] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -67.344149 | E_var:     0.3723 | E_err:   0.009534
[2025-10-23 12:49:29] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -67.366486 | E_var:     0.5655 | E_err:   0.011750
[2025-10-23 12:49:43] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -67.357532 | E_var:     0.3121 | E_err:   0.008730
[2025-10-23 12:49:56] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -67.367004 | E_var:     0.3660 | E_err:   0.009453
[2025-10-23 12:50:09] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -67.363509 | E_var:     0.5900 | E_err:   0.012002
[2025-10-23 12:50:22] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -67.354862 | E_var:     0.5584 | E_err:   0.011676
[2025-10-23 12:50:35] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -67.355282 | E_var:     0.4474 | E_err:   0.010451
[2025-10-23 12:50:48] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -67.346019 | E_var:     0.3474 | E_err:   0.009209
[2025-10-23 12:51:01] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -67.359449 | E_var:     0.4546 | E_err:   0.010535
[2025-10-23 12:51:14] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -67.336109 | E_var:     0.3408 | E_err:   0.009121
[2025-10-23 12:51:28] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -67.353579 | E_var:     0.4145 | E_err:   0.010059
[2025-10-23 12:51:41] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -67.344318 | E_var:     0.3702 | E_err:   0.009507
[2025-10-23 12:51:54] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -67.345149 | E_var:     0.3042 | E_err:   0.008618
[2025-10-23 12:52:07] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -67.356180 | E_var:     0.3708 | E_err:   0.009515
[2025-10-23 12:52:20] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -67.364603 | E_var:     0.3644 | E_err:   0.009432
[2025-10-23 12:52:33] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -67.345194 | E_var:     0.3394 | E_err:   0.009103
[2025-10-23 12:52:46] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -67.349724 | E_var:     0.3326 | E_err:   0.009011
[2025-10-23 12:53:00] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -67.349519 | E_var:     0.4478 | E_err:   0.010455
[2025-10-23 12:53:13] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -67.370223 | E_var:     0.3599 | E_err:   0.009373
[2025-10-23 12:53:26] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -67.348590 | E_var:     0.2973 | E_err:   0.008519
[2025-10-23 12:53:39] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -67.354035 | E_var:     0.4571 | E_err:   0.010564
[2025-10-23 12:53:52] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -67.341354 | E_var:     0.3702 | E_err:   0.009506
[2025-10-23 12:54:05] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -67.336416 | E_var:     0.4001 | E_err:   0.009883
[2025-10-23 12:54:18] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -67.344504 | E_var:     0.4295 | E_err:   0.010239
[2025-10-23 12:54:32] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -67.361588 | E_var:     0.3181 | E_err:   0.008812
[2025-10-23 12:54:45] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -67.352507 | E_var:     0.4162 | E_err:   0.010080
[2025-10-23 12:54:58] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -67.354918 | E_var:     0.4107 | E_err:   0.010013
[2025-10-23 12:55:11] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -67.358242 | E_var:     0.3597 | E_err:   0.009371
[2025-10-23 12:55:24] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -67.372392 | E_var:     1.8548 | E_err:   0.021280
[2025-10-23 12:55:37] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -67.365595 | E_var:     0.3234 | E_err:   0.008886
[2025-10-23 12:55:50] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -67.341121 | E_var:     0.3336 | E_err:   0.009024
[2025-10-23 12:56:03] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -67.370041 | E_var:     0.3438 | E_err:   0.009162
[2025-10-23 12:56:17] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -67.358987 | E_var:     0.3471 | E_err:   0.009205
[2025-10-23 12:56:30] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -67.356368 | E_var:     0.3160 | E_err:   0.008784
[2025-10-23 12:56:43] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -67.359847 | E_var:     0.4424 | E_err:   0.010393
[2025-10-23 12:56:56] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -67.354758 | E_var:     0.5446 | E_err:   0.011531
[2025-10-23 12:57:09] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -67.346691 | E_var:     0.3441 | E_err:   0.009166
[2025-10-23 12:57:22] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -67.358753 | E_var:     0.3893 | E_err:   0.009749
[2025-10-23 12:57:35] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -67.358706 | E_var:     0.3717 | E_err:   0.009526
[2025-10-23 12:57:49] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -67.348423 | E_var:     0.3661 | E_err:   0.009454
[2025-10-23 12:58:02] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -67.341471 | E_var:     0.3290 | E_err:   0.008962
[2025-10-23 12:58:15] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -67.348975 | E_var:     0.3295 | E_err:   0.008969
[2025-10-23 12:58:28] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -67.363301 | E_var:     0.4184 | E_err:   0.010107
[2025-10-23 12:58:41] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -67.363746 | E_var:     0.4270 | E_err:   0.010210
[2025-10-23 12:58:54] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -67.361648 | E_var:     0.3327 | E_err:   0.009012
[2025-10-23 12:59:07] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -67.351543 | E_var:     0.6333 | E_err:   0.012434
[2025-10-23 12:59:21] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -67.371336 | E_var:     0.4299 | E_err:   0.010245
[2025-10-23 12:59:34] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -67.354375 | E_var:     0.3305 | E_err:   0.008983
[2025-10-23 12:59:47] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -67.363495 | E_var:     0.3621 | E_err:   0.009402
[2025-10-23 13:00:00] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -67.372178 | E_var:     0.3283 | E_err:   0.008953
[2025-10-23 13:00:13] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -67.349958 | E_var:     0.3841 | E_err:   0.009684
[2025-10-23 13:00:26] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -67.362731 | E_var:     0.3500 | E_err:   0.009244
[2025-10-23 13:00:39] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -67.348836 | E_var:     0.3749 | E_err:   0.009567
[2025-10-23 13:00:52] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -67.367164 | E_var:     0.3983 | E_err:   0.009861
[2025-10-23 13:01:06] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -67.354530 | E_var:     0.3314 | E_err:   0.008995
[2025-10-23 13:01:19] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -67.361424 | E_var:     0.3534 | E_err:   0.009289
[2025-10-23 13:01:32] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -67.337525 | E_var:     0.3509 | E_err:   0.009256
[2025-10-23 13:01:45] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -67.365145 | E_var:     0.3975 | E_err:   0.009851
[2025-10-23 13:01:58] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -67.351791 | E_var:     0.3883 | E_err:   0.009737
[2025-10-23 13:02:11] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -67.342320 | E_var:     0.4025 | E_err:   0.009913
[2025-10-23 13:02:24] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -67.359550 | E_var:     0.3732 | E_err:   0.009545
[2025-10-23 13:02:37] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -67.355859 | E_var:     0.3291 | E_err:   0.008964
[2025-10-23 13:02:51] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -67.369780 | E_var:     0.3635 | E_err:   0.009420
[2025-10-23 13:03:04] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -67.345967 | E_var:     0.3300 | E_err:   0.008975
[2025-10-23 13:03:17] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -67.338720 | E_var:     0.3601 | E_err:   0.009377
[2025-10-23 13:03:30] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -67.346630 | E_var:     0.3714 | E_err:   0.009522
[2025-10-23 13:03:43] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -67.345143 | E_var:     0.3371 | E_err:   0.009072
[2025-10-23 13:03:43] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-23 13:03:56] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -67.369489 | E_var:     0.3159 | E_err:   0.008783
[2025-10-23 13:04:09] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -67.341694 | E_var:     0.3340 | E_err:   0.009030
[2025-10-23 13:04:23] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -67.350601 | E_var:     0.3465 | E_err:   0.009198
[2025-10-23 13:04:36] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -67.370829 | E_var:     0.4854 | E_err:   0.010887
[2025-10-23 13:04:49] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -67.344212 | E_var:     0.6090 | E_err:   0.012194
[2025-10-23 13:05:02] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -67.346846 | E_var:     0.3210 | E_err:   0.008853
[2025-10-23 13:05:15] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -67.356145 | E_var:     0.3241 | E_err:   0.008895
[2025-10-23 13:05:28] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -67.355755 | E_var:     0.4217 | E_err:   0.010147
[2025-10-23 13:05:41] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -67.340889 | E_var:     0.5342 | E_err:   0.011420
[2025-10-23 13:05:55] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -67.357849 | E_var:     0.3537 | E_err:   0.009293
[2025-10-23 13:06:08] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -67.351064 | E_var:     0.3278 | E_err:   0.008946
[2025-10-23 13:06:21] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -67.353672 | E_var:     0.3743 | E_err:   0.009559
[2025-10-23 13:06:34] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -67.346881 | E_var:     0.5752 | E_err:   0.011850
[2025-10-23 13:06:47] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -67.359275 | E_var:     0.3594 | E_err:   0.009367
[2025-10-23 13:07:00] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -67.364227 | E_var:     0.3980 | E_err:   0.009857
[2025-10-23 13:07:13] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -67.357497 | E_var:     0.3357 | E_err:   0.009053
[2025-10-23 13:07:27] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -67.351909 | E_var:     0.3399 | E_err:   0.009109
[2025-10-23 13:07:40] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -67.341645 | E_var:     0.3812 | E_err:   0.009648
[2025-10-23 13:07:53] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -67.344152 | E_var:     0.5691 | E_err:   0.011788
[2025-10-23 13:08:06] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -67.349964 | E_var:     0.3741 | E_err:   0.009557
[2025-10-23 13:08:19] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -67.353105 | E_var:     0.3428 | E_err:   0.009148
[2025-10-23 13:08:32] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -67.351649 | E_var:     0.4492 | E_err:   0.010472
[2025-10-23 13:08:45] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -67.344672 | E_var:     0.4346 | E_err:   0.010300
[2025-10-23 13:08:58] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -67.350982 | E_var:     0.3107 | E_err:   0.008710
[2025-10-23 13:09:12] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -67.358601 | E_var:     0.3640 | E_err:   0.009427
[2025-10-23 13:09:25] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -67.355829 | E_var:     0.3224 | E_err:   0.008872
[2025-10-23 13:09:38] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -67.347399 | E_var:     0.3014 | E_err:   0.008579
[2025-10-23 13:09:51] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -67.359225 | E_var:     0.5627 | E_err:   0.011721
[2025-10-23 13:10:04] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -67.343605 | E_var:     0.3665 | E_err:   0.009459
[2025-10-23 13:10:17] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -67.355687 | E_var:     0.3310 | E_err:   0.008990
[2025-10-23 13:10:30] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -67.346128 | E_var:     0.3629 | E_err:   0.009413
[2025-10-23 13:10:44] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -67.363929 | E_var:     0.3476 | E_err:   0.009212
[2025-10-23 13:10:57] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -67.360189 | E_var:     0.3940 | E_err:   0.009808
[2025-10-23 13:11:10] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -67.353962 | E_var:     0.3299 | E_err:   0.008975
[2025-10-23 13:11:23] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -67.352037 | E_var:     0.3456 | E_err:   0.009185
[2025-10-23 13:11:36] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -67.358837 | E_var:     0.3129 | E_err:   0.008741
[2025-10-23 13:11:49] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -67.352770 | E_var:     0.3393 | E_err:   0.009102
[2025-10-23 13:12:02] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -67.352809 | E_var:     0.3201 | E_err:   0.008840
[2025-10-23 13:12:16] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -67.350800 | E_var:     0.4092 | E_err:   0.009995
[2025-10-23 13:12:29] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -67.355677 | E_var:     0.3290 | E_err:   0.008962
[2025-10-23 13:12:42] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -67.352494 | E_var:     0.3585 | E_err:   0.009355
[2025-10-23 13:12:55] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -67.349300 | E_var:     0.3305 | E_err:   0.008982
[2025-10-23 13:13:08] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -67.356168 | E_var:     0.3544 | E_err:   0.009302
[2025-10-23 13:13:21] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -67.345440 | E_var:     0.3268 | E_err:   0.008932
[2025-10-23 13:13:34] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -67.363906 | E_var:     0.3376 | E_err:   0.009079
[2025-10-23 13:13:34] 🔄 RESTART #1 | Period: 300
[2025-10-23 13:13:47] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -67.363557 | E_var:     0.3593 | E_err:   0.009366
[2025-10-23 13:14:01] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -67.356256 | E_var:     0.3643 | E_err:   0.009430
[2025-10-23 13:14:14] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -67.356075 | E_var:     0.3733 | E_err:   0.009547
[2025-10-23 13:14:27] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -67.374934 | E_var:     0.3923 | E_err:   0.009787
[2025-10-23 13:14:40] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -67.343363 | E_var:     0.4129 | E_err:   0.010040
[2025-10-23 13:14:53] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -67.349354 | E_var:     0.4576 | E_err:   0.010570
[2025-10-23 13:15:06] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -67.357522 | E_var:     0.4054 | E_err:   0.009949
[2025-10-23 13:15:19] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -67.349215 | E_var:     0.3964 | E_err:   0.009837
[2025-10-23 13:15:33] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -67.334983 | E_var:     0.4263 | E_err:   0.010202
[2025-10-23 13:15:46] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -67.356445 | E_var:     0.3716 | E_err:   0.009525
[2025-10-23 13:15:59] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -67.356071 | E_var:     0.3144 | E_err:   0.008761
[2025-10-23 13:16:12] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -67.353941 | E_var:     0.3414 | E_err:   0.009129
[2025-10-23 13:16:25] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -67.358824 | E_var:     0.4349 | E_err:   0.010305
[2025-10-23 13:16:38] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -67.337287 | E_var:     0.3490 | E_err:   0.009231
[2025-10-23 13:16:51] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -67.338214 | E_var:     0.3488 | E_err:   0.009227
[2025-10-23 13:17:05] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -67.345173 | E_var:     0.3356 | E_err:   0.009052
[2025-10-23 13:17:19] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -67.360251 | E_var:     0.6727 | E_err:   0.012815
[2025-10-23 13:17:32] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -67.371058 | E_var:     0.4262 | E_err:   0.010201
[2025-10-23 13:17:45] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -67.361294 | E_var:     0.3328 | E_err:   0.009014
[2025-10-23 13:17:58] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -67.344165 | E_var:     0.4085 | E_err:   0.009987
[2025-10-23 13:18:11] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -67.364236 | E_var:     0.3251 | E_err:   0.008910
[2025-10-23 13:18:24] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -67.363844 | E_var:     0.4053 | E_err:   0.009947
[2025-10-23 13:18:38] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -67.363758 | E_var:     0.3755 | E_err:   0.009575
[2025-10-23 13:18:51] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -67.362689 | E_var:     0.4133 | E_err:   0.010045
[2025-10-23 13:19:04] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -67.360997 | E_var:     0.5151 | E_err:   0.011214
[2025-10-23 13:19:17] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -67.369392 | E_var:     0.3485 | E_err:   0.009224
[2025-10-23 13:19:30] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -67.361803 | E_var:     0.3398 | E_err:   0.009108
[2025-10-23 13:19:43] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -67.351548 | E_var:     0.3351 | E_err:   0.009045
[2025-10-23 13:19:57] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -67.369929 | E_var:     0.3498 | E_err:   0.009241
[2025-10-23 13:20:10] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -67.366220 | E_var:     0.3679 | E_err:   0.009477
[2025-10-23 13:20:23] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -67.346175 | E_var:     0.3391 | E_err:   0.009098
[2025-10-23 13:20:36] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -67.343689 | E_var:     0.3584 | E_err:   0.009354
[2025-10-23 13:20:49] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -67.368178 | E_var:     0.3442 | E_err:   0.009167
[2025-10-23 13:21:02] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -67.353733 | E_var:     0.3966 | E_err:   0.009840
[2025-10-23 13:21:15] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -67.361944 | E_var:     0.3196 | E_err:   0.008833
[2025-10-23 13:21:29] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -67.345807 | E_var:     0.3777 | E_err:   0.009603
[2025-10-23 13:21:42] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -67.364435 | E_var:     0.3379 | E_err:   0.009083
[2025-10-23 13:21:55] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -67.353625 | E_var:     0.4206 | E_err:   0.010133
[2025-10-23 13:22:08] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -67.352754 | E_var:     0.3476 | E_err:   0.009212
[2025-10-23 13:22:21] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -67.362651 | E_var:     0.3556 | E_err:   0.009318
[2025-10-23 13:22:34] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -67.351626 | E_var:     0.3678 | E_err:   0.009477
[2025-10-23 13:22:48] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -67.373981 | E_var:     0.3083 | E_err:   0.008676
[2025-10-23 13:23:01] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -67.331029 | E_var:     0.3790 | E_err:   0.009620
[2025-10-23 13:23:14] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -67.349509 | E_var:     0.4897 | E_err:   0.010934
[2025-10-23 13:23:27] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -67.347695 | E_var:     0.3577 | E_err:   0.009344
[2025-10-23 13:23:40] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -67.335684 | E_var:     0.3779 | E_err:   0.009605
[2025-10-23 13:23:53] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -67.360029 | E_var:     0.3706 | E_err:   0.009512
[2025-10-23 13:24:07] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -67.354559 | E_var:     0.3499 | E_err:   0.009242
[2025-10-23 13:24:20] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -67.360768 | E_var:     0.3261 | E_err:   0.008923
[2025-10-23 13:24:33] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -67.351990 | E_var:     0.3463 | E_err:   0.009195
[2025-10-23 13:24:46] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -67.357101 | E_var:     0.3251 | E_err:   0.008910
[2025-10-23 13:24:59] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -67.355510 | E_var:     0.3947 | E_err:   0.009816
[2025-10-23 13:25:12] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -67.342541 | E_var:     0.4105 | E_err:   0.010012
[2025-10-23 13:25:25] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -67.368864 | E_var:     0.3957 | E_err:   0.009829
[2025-10-23 13:25:39] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -67.356641 | E_var:     0.3186 | E_err:   0.008820
[2025-10-23 13:25:52] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -67.346593 | E_var:     0.4189 | E_err:   0.010113
[2025-10-23 13:26:05] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -67.358164 | E_var:     0.3466 | E_err:   0.009199
[2025-10-23 13:26:18] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -67.365422 | E_var:     0.4280 | E_err:   0.010222
[2025-10-23 13:26:31] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -67.351700 | E_var:     0.3694 | E_err:   0.009497
[2025-10-23 13:26:44] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -67.361844 | E_var:     0.4200 | E_err:   0.010126
[2025-10-23 13:26:45] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-23 13:26:58] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -67.352509 | E_var:     0.5960 | E_err:   0.012062
[2025-10-23 13:27:11] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -67.336936 | E_var:     0.4013 | E_err:   0.009898
[2025-10-23 13:27:24] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -67.354962 | E_var:     0.2825 | E_err:   0.008304
[2025-10-23 13:27:37] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -67.378258 | E_var:     0.3474 | E_err:   0.009210
[2025-10-23 13:27:50] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -67.360601 | E_var:     0.3654 | E_err:   0.009445
[2025-10-23 13:28:03] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -67.359925 | E_var:     0.2919 | E_err:   0.008442
[2025-10-23 13:28:17] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -67.353097 | E_var:     0.3446 | E_err:   0.009172
[2025-10-23 13:28:30] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -67.371101 | E_var:     0.3525 | E_err:   0.009277
[2025-10-23 13:28:43] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -67.343260 | E_var:     0.4447 | E_err:   0.010420
[2025-10-23 13:28:56] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -67.361624 | E_var:     0.3310 | E_err:   0.008989
[2025-10-23 13:29:09] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -67.354810 | E_var:     0.3063 | E_err:   0.008647
[2025-10-23 13:29:22] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -67.357169 | E_var:     0.3720 | E_err:   0.009529
[2025-10-23 13:29:36] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -67.354789 | E_var:     0.4027 | E_err:   0.009915
[2025-10-23 13:29:49] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -67.368367 | E_var:     0.3164 | E_err:   0.008789
[2025-10-23 13:30:02] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -67.352057 | E_var:     0.3640 | E_err:   0.009427
[2025-10-23 13:30:15] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -67.346994 | E_var:     0.3779 | E_err:   0.009606
[2025-10-23 13:30:28] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -67.366542 | E_var:     0.3419 | E_err:   0.009136
[2025-10-23 13:30:41] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -67.358651 | E_var:     0.3799 | E_err:   0.009631
[2025-10-23 13:30:55] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -67.344801 | E_var:     0.3910 | E_err:   0.009770
[2025-10-23 13:31:08] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -67.344990 | E_var:     0.3512 | E_err:   0.009259
[2025-10-23 13:31:21] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -67.362701 | E_var:     0.3090 | E_err:   0.008686
[2025-10-23 13:31:34] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -67.347247 | E_var:     0.2985 | E_err:   0.008537
[2025-10-23 13:31:47] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -67.358892 | E_var:     0.5155 | E_err:   0.011218
[2025-10-23 13:32:00] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -67.357413 | E_var:     0.3762 | E_err:   0.009584
[2025-10-23 13:32:14] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -67.360678 | E_var:     0.3718 | E_err:   0.009528
[2025-10-23 13:32:27] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -67.362645 | E_var:     0.3951 | E_err:   0.009822
[2025-10-23 13:32:40] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -67.363640 | E_var:     0.4444 | E_err:   0.010416
[2025-10-23 13:32:53] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -67.347942 | E_var:     0.3292 | E_err:   0.008965
[2025-10-23 13:33:06] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -67.346464 | E_var:     0.3459 | E_err:   0.009189
[2025-10-23 13:33:19] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -67.367199 | E_var:     0.4578 | E_err:   0.010572
[2025-10-23 13:33:32] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -67.342791 | E_var:     0.3357 | E_err:   0.009053
[2025-10-23 13:33:46] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -67.361494 | E_var:     0.3586 | E_err:   0.009357
[2025-10-23 13:33:59] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -67.359872 | E_var:     0.4573 | E_err:   0.010567
[2025-10-23 13:34:12] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -67.348029 | E_var:     0.4402 | E_err:   0.010366
[2025-10-23 13:34:25] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -67.343714 | E_var:     0.3581 | E_err:   0.009350
[2025-10-23 13:34:38] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -67.358736 | E_var:     0.9099 | E_err:   0.014904
[2025-10-23 13:34:51] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -67.350878 | E_var:     0.3148 | E_err:   0.008766
[2025-10-23 13:35:05] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -67.356420 | E_var:     0.5437 | E_err:   0.011521
[2025-10-23 13:35:18] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -67.364839 | E_var:     0.3131 | E_err:   0.008744
[2025-10-23 13:35:31] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -67.364873 | E_var:     0.3284 | E_err:   0.008955
[2025-10-23 13:35:44] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -67.364111 | E_var:     0.6150 | E_err:   0.012253
[2025-10-23 13:35:57] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -67.371663 | E_var:     0.3955 | E_err:   0.009826
[2025-10-23 13:36:10] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -67.371005 | E_var:     0.3364 | E_err:   0.009062
[2025-10-23 13:36:24] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -67.352836 | E_var:     0.3937 | E_err:   0.009804
[2025-10-23 13:36:37] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -67.342782 | E_var:     0.4256 | E_err:   0.010194
[2025-10-23 13:36:50] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -67.358736 | E_var:     0.4204 | E_err:   0.010132
[2025-10-23 13:37:03] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -67.351668 | E_var:     0.3511 | E_err:   0.009258
[2025-10-23 13:37:16] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -67.346503 | E_var:     0.3497 | E_err:   0.009240
[2025-10-23 13:37:29] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -67.355104 | E_var:     0.3136 | E_err:   0.008750
[2025-10-23 13:37:43] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -67.355155 | E_var:     0.5351 | E_err:   0.011430
[2025-10-23 13:37:56] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -67.372246 | E_var:     0.3913 | E_err:   0.009775
[2025-10-23 13:38:09] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -67.352397 | E_var:     0.3082 | E_err:   0.008675
[2025-10-23 13:38:22] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -67.368342 | E_var:     0.4287 | E_err:   0.010230
[2025-10-23 13:38:35] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -67.366881 | E_var:     0.2982 | E_err:   0.008532
[2025-10-23 13:38:48] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -67.368091 | E_var:     0.3628 | E_err:   0.009411
[2025-10-23 13:39:01] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -67.365186 | E_var:     0.3625 | E_err:   0.009407
[2025-10-23 13:39:15] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -67.363454 | E_var:     0.3131 | E_err:   0.008744
[2025-10-23 13:39:28] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -67.348000 | E_var:     0.3369 | E_err:   0.009070
[2025-10-23 13:39:41] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -67.350647 | E_var:     0.3566 | E_err:   0.009331
[2025-10-23 13:39:54] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -67.361707 | E_var:     0.3943 | E_err:   0.009811
[2025-10-23 13:40:07] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -67.356775 | E_var:     0.3854 | E_err:   0.009700
[2025-10-23 13:40:20] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -67.344452 | E_var:     0.3886 | E_err:   0.009740
[2025-10-23 13:40:34] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -67.358747 | E_var:     0.3673 | E_err:   0.009470
[2025-10-23 13:40:47] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -67.358763 | E_var:     0.3190 | E_err:   0.008825
[2025-10-23 13:41:00] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -67.345463 | E_var:     0.3694 | E_err:   0.009497
[2025-10-23 13:41:13] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -67.355914 | E_var:     0.3402 | E_err:   0.009114
[2025-10-23 13:41:26] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -67.355942 | E_var:     0.3689 | E_err:   0.009490
[2025-10-23 13:41:39] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -67.359472 | E_var:     0.2972 | E_err:   0.008518
[2025-10-23 13:41:53] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -67.350274 | E_var:     0.4220 | E_err:   0.010150
[2025-10-23 13:42:06] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -67.365611 | E_var:     0.3588 | E_err:   0.009360
[2025-10-23 13:42:19] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -67.336206 | E_var:     0.6601 | E_err:   0.012695
[2025-10-23 13:42:32] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -67.360509 | E_var:     0.3723 | E_err:   0.009534
[2025-10-23 13:42:45] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -67.358600 | E_var:     0.3506 | E_err:   0.009252
[2025-10-23 13:42:58] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -67.368420 | E_var:     0.3413 | E_err:   0.009128
[2025-10-23 13:43:11] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -67.365869 | E_var:     0.4373 | E_err:   0.010332
[2025-10-23 13:43:25] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -67.349307 | E_var:     0.3388 | E_err:   0.009094
[2025-10-23 13:43:38] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -67.356944 | E_var:     0.3876 | E_err:   0.009728
[2025-10-23 13:43:51] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -67.358411 | E_var:     0.3940 | E_err:   0.009808
[2025-10-23 13:44:04] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -67.354485 | E_var:     0.3426 | E_err:   0.009145
[2025-10-23 13:44:17] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -67.340383 | E_var:     0.3855 | E_err:   0.009701
[2025-10-23 13:44:30] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -67.363862 | E_var:     0.5614 | E_err:   0.011707
[2025-10-23 13:44:44] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -67.347312 | E_var:     0.3331 | E_err:   0.009018
[2025-10-23 13:44:57] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -67.345018 | E_var:     0.4345 | E_err:   0.010299
[2025-10-23 13:45:10] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -67.349242 | E_var:     0.3473 | E_err:   0.009208
[2025-10-23 13:45:23] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -67.359827 | E_var:     0.3668 | E_err:   0.009463
[2025-10-23 13:45:36] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -67.361083 | E_var:     0.3650 | E_err:   0.009440
[2025-10-23 13:45:49] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -67.372845 | E_var:     0.3920 | E_err:   0.009782
[2025-10-23 13:46:03] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -67.341188 | E_var:     0.3934 | E_err:   0.009801
[2025-10-23 13:46:16] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -67.352889 | E_var:     0.4031 | E_err:   0.009921
[2025-10-23 13:46:29] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -67.375297 | E_var:     0.3693 | E_err:   0.009495
[2025-10-23 13:46:42] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -67.340114 | E_var:     0.3632 | E_err:   0.009417
[2025-10-23 13:46:55] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -67.363306 | E_var:     0.3510 | E_err:   0.009257
[2025-10-23 13:47:08] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -67.354575 | E_var:     0.3850 | E_err:   0.009695
[2025-10-23 13:47:21] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -67.358138 | E_var:     0.3464 | E_err:   0.009196
[2025-10-23 13:47:35] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -67.356693 | E_var:     0.3799 | E_err:   0.009630
[2025-10-23 13:47:48] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -67.351290 | E_var:     0.3340 | E_err:   0.009030
[2025-10-23 13:48:01] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -67.381664 | E_var:     0.4078 | E_err:   0.009978
[2025-10-23 13:48:14] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -67.360854 | E_var:     0.4377 | E_err:   0.010337
[2025-10-23 13:48:27] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -67.353163 | E_var:     0.7487 | E_err:   0.013520
[2025-10-23 13:48:40] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -67.354504 | E_var:     0.3223 | E_err:   0.008871
[2025-10-23 13:48:54] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -67.353021 | E_var:     0.3028 | E_err:   0.008598
[2025-10-23 13:49:07] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -67.339925 | E_var:     0.4963 | E_err:   0.011008
[2025-10-23 13:49:20] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -67.350550 | E_var:     0.4414 | E_err:   0.010381
[2025-10-23 13:49:33] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -67.348495 | E_var:     0.3956 | E_err:   0.009827
[2025-10-23 13:49:46] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -67.360968 | E_var:     0.3456 | E_err:   0.009186
[2025-10-23 13:49:46] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-23 13:49:59] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -67.351417 | E_var:     0.5030 | E_err:   0.011082
[2025-10-23 13:50:13] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -67.354721 | E_var:     0.3824 | E_err:   0.009662
[2025-10-23 13:50:26] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -67.357036 | E_var:     0.3376 | E_err:   0.009078
[2025-10-23 13:50:39] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -67.367029 | E_var:     0.4731 | E_err:   0.010747
[2025-10-23 13:50:52] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -67.363235 | E_var:     0.4062 | E_err:   0.009958
[2025-10-23 13:51:05] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -67.342936 | E_var:     0.4042 | E_err:   0.009934
[2025-10-23 13:51:18] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -67.353509 | E_var:     0.4356 | E_err:   0.010312
[2025-10-23 13:51:32] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -67.351519 | E_var:     0.3203 | E_err:   0.008843
[2025-10-23 13:51:45] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -67.365456 | E_var:     0.3336 | E_err:   0.009024
[2025-10-23 13:51:58] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -67.352109 | E_var:     0.3491 | E_err:   0.009232
[2025-10-23 13:52:11] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -67.367829 | E_var:     0.3774 | E_err:   0.009598
[2025-10-23 13:52:24] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -67.353476 | E_var:     0.3534 | E_err:   0.009289
[2025-10-23 13:52:37] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -67.347519 | E_var:     0.3548 | E_err:   0.009307
[2025-10-23 13:52:51] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -67.347450 | E_var:     0.3555 | E_err:   0.009316
[2025-10-23 13:53:04] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -67.350393 | E_var:     0.3597 | E_err:   0.009371
[2025-10-23 13:53:17] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -67.368707 | E_var:     0.3820 | E_err:   0.009657
[2025-10-23 13:53:30] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -67.349079 | E_var:     0.3390 | E_err:   0.009097
[2025-10-23 13:53:43] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -67.353099 | E_var:     0.3391 | E_err:   0.009099
[2025-10-23 13:53:56] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -67.336199 | E_var:     0.3407 | E_err:   0.009120
[2025-10-23 13:54:10] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -67.355239 | E_var:     0.4409 | E_err:   0.010375
[2025-10-23 13:54:23] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -67.363858 | E_var:     0.3787 | E_err:   0.009615
[2025-10-23 13:54:36] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -67.363940 | E_var:     0.3515 | E_err:   0.009264
[2025-10-23 13:54:49] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -67.345193 | E_var:     0.3844 | E_err:   0.009688
[2025-10-23 13:55:02] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -67.364604 | E_var:     0.2901 | E_err:   0.008416
[2025-10-23 13:55:15] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -67.344081 | E_var:     0.3452 | E_err:   0.009180
[2025-10-23 13:55:28] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -67.354122 | E_var:     0.4512 | E_err:   0.010495
[2025-10-23 13:55:42] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -67.370644 | E_var:     0.3546 | E_err:   0.009304
[2025-10-23 13:55:55] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -67.361634 | E_var:     0.3996 | E_err:   0.009877
[2025-10-23 13:56:08] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -67.350013 | E_var:     0.3481 | E_err:   0.009219
[2025-10-23 13:56:21] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -67.354972 | E_var:     0.3194 | E_err:   0.008831
[2025-10-23 13:56:34] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -67.346962 | E_var:     0.3140 | E_err:   0.008756
[2025-10-23 13:56:47] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -67.355255 | E_var:     0.4050 | E_err:   0.009944
[2025-10-23 13:57:01] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -67.352806 | E_var:     0.3070 | E_err:   0.008658
[2025-10-23 13:57:14] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -67.359734 | E_var:     0.3683 | E_err:   0.009483
[2025-10-23 13:57:27] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -67.361579 | E_var:     0.3423 | E_err:   0.009142
[2025-10-23 13:57:40] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -67.344533 | E_var:     0.3078 | E_err:   0.008668
[2025-10-23 13:57:53] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -67.348800 | E_var:     0.4036 | E_err:   0.009926
[2025-10-23 13:58:06] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -67.343217 | E_var:     0.3280 | E_err:   0.008949
[2025-10-23 13:58:20] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -67.371286 | E_var:     0.3997 | E_err:   0.009878
[2025-10-23 13:58:33] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -67.365650 | E_var:     0.3389 | E_err:   0.009096
[2025-10-23 13:58:46] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -67.359347 | E_var:     0.4103 | E_err:   0.010009
[2025-10-23 13:58:59] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -67.349390 | E_var:     0.3771 | E_err:   0.009595
[2025-10-23 13:59:12] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -67.345395 | E_var:     0.3546 | E_err:   0.009304
[2025-10-23 13:59:25] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -67.345834 | E_var:     0.5590 | E_err:   0.011683
[2025-10-23 13:59:39] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -67.351653 | E_var:     0.3631 | E_err:   0.009415
[2025-10-23 13:59:52] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -67.355480 | E_var:     0.4411 | E_err:   0.010377
[2025-10-23 14:00:05] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -67.344233 | E_var:     0.5100 | E_err:   0.011158
[2025-10-23 14:00:18] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -67.363068 | E_var:     0.3288 | E_err:   0.008959
[2025-10-23 14:00:31] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -67.348298 | E_var:     0.3057 | E_err:   0.008639
[2025-10-23 14:00:44] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -67.345584 | E_var:     0.3102 | E_err:   0.008703
[2025-10-23 14:00:57] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -67.356640 | E_var:     0.3508 | E_err:   0.009254
[2025-10-23 14:01:11] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -67.336736 | E_var:     0.3402 | E_err:   0.009114
[2025-10-23 14:01:24] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -67.354001 | E_var:     0.3458 | E_err:   0.009188
[2025-10-23 14:01:37] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -67.345773 | E_var:     0.3596 | E_err:   0.009369
[2025-10-23 14:01:50] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -67.367068 | E_var:     0.4601 | E_err:   0.010598
[2025-10-23 14:02:03] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -67.353189 | E_var:     0.3628 | E_err:   0.009412
[2025-10-23 14:02:16] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -67.374400 | E_var:     0.5203 | E_err:   0.011271
[2025-10-23 14:02:30] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -67.340211 | E_var:     0.3413 | E_err:   0.009128
[2025-10-23 14:02:43] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -67.349534 | E_var:     0.3792 | E_err:   0.009622
[2025-10-23 14:02:56] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -67.345428 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 14:03:09] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -67.345656 | E_var:     0.3717 | E_err:   0.009526
[2025-10-23 14:03:22] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -67.356963 | E_var:     0.3279 | E_err:   0.008947
[2025-10-23 14:03:35] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -67.345331 | E_var:     0.3327 | E_err:   0.009013
[2025-10-23 14:03:49] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -67.364740 | E_var:     0.3779 | E_err:   0.009605
[2025-10-23 14:04:02] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -67.357911 | E_var:     0.4008 | E_err:   0.009892
[2025-10-23 14:04:15] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -67.345773 | E_var:     0.3487 | E_err:   0.009227
[2025-10-23 14:04:28] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -67.358152 | E_var:     0.4849 | E_err:   0.010880
[2025-10-23 14:04:41] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -67.359709 | E_var:     0.3709 | E_err:   0.009516
[2025-10-23 14:04:54] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -67.360947 | E_var:     0.3379 | E_err:   0.009083
[2025-10-23 14:05:08] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -67.343808 | E_var:     0.3555 | E_err:   0.009317
[2025-10-23 14:05:21] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -67.369122 | E_var:     0.3473 | E_err:   0.009209
[2025-10-23 14:05:34] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -67.350018 | E_var:     0.3632 | E_err:   0.009416
[2025-10-23 14:05:47] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -67.346415 | E_var:     0.3943 | E_err:   0.009812
[2025-10-23 14:06:00] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -67.362753 | E_var:     0.3702 | E_err:   0.009507
[2025-10-23 14:06:13] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -67.364341 | E_var:     0.3318 | E_err:   0.009000
[2025-10-23 14:06:27] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -67.352669 | E_var:     0.3264 | E_err:   0.008927
[2025-10-23 14:06:40] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -67.349290 | E_var:     0.3635 | E_err:   0.009421
[2025-10-23 14:06:53] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -67.351506 | E_var:     0.3356 | E_err:   0.009052
[2025-10-23 14:07:06] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -67.351565 | E_var:     0.4569 | E_err:   0.010562
[2025-10-23 14:07:19] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -67.367751 | E_var:     0.4567 | E_err:   0.010559
[2025-10-23 14:07:32] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -67.358282 | E_var:     0.3419 | E_err:   0.009137
[2025-10-23 14:07:45] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -67.364589 | E_var:     0.3342 | E_err:   0.009032
[2025-10-23 14:07:59] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -67.345763 | E_var:     0.4567 | E_err:   0.010559
[2025-10-23 14:08:12] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -67.343395 | E_var:     0.3628 | E_err:   0.009412
[2025-10-23 14:08:25] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -67.349494 | E_var:     0.3494 | E_err:   0.009236
[2025-10-23 14:08:38] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -67.360365 | E_var:     0.3347 | E_err:   0.009039
[2025-10-23 14:08:51] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -67.367690 | E_var:     0.3679 | E_err:   0.009477
[2025-10-23 14:09:04] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -67.362735 | E_var:     0.4133 | E_err:   0.010045
[2025-10-23 14:09:18] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -67.362440 | E_var:     0.3768 | E_err:   0.009592
[2025-10-23 14:09:31] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -67.364664 | E_var:     0.3040 | E_err:   0.008615
[2025-10-23 14:09:44] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -67.349166 | E_var:     0.3292 | E_err:   0.008964
[2025-10-23 14:09:57] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -67.357910 | E_var:     0.4205 | E_err:   0.010132
[2025-10-23 14:10:10] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -67.342260 | E_var:     0.4053 | E_err:   0.009947
[2025-10-23 14:10:23] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -67.357433 | E_var:     0.6305 | E_err:   0.012407
[2025-10-23 14:10:37] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -67.347807 | E_var:     0.3716 | E_err:   0.009524
[2025-10-23 14:10:50] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -67.357704 | E_var:     0.3384 | E_err:   0.009089
[2025-10-23 14:11:03] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -67.345338 | E_var:     0.3785 | E_err:   0.009613
[2025-10-23 14:11:16] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -67.359046 | E_var:     0.5298 | E_err:   0.011373
[2025-10-23 14:11:29] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -67.358465 | E_var:     0.3922 | E_err:   0.009785
[2025-10-23 14:11:42] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -67.353747 | E_var:     0.3549 | E_err:   0.009309
[2025-10-23 14:11:56] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -67.350938 | E_var:     0.3181 | E_err:   0.008813
[2025-10-23 14:12:09] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -67.363109 | E_var:     0.3828 | E_err:   0.009667
[2025-10-23 14:12:22] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -67.342647 | E_var:     0.4338 | E_err:   0.010291
[2025-10-23 14:12:35] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -67.370020 | E_var:     0.2996 | E_err:   0.008553
[2025-10-23 14:12:48] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -67.354281 | E_var:     0.3971 | E_err:   0.009846
[2025-10-23 14:12:48] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-23 14:13:01] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -67.346387 | E_var:     0.3804 | E_err:   0.009637
[2025-10-23 14:13:15] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -67.361387 | E_var:     0.4425 | E_err:   0.010393
[2025-10-23 14:13:28] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -67.354212 | E_var:     0.3092 | E_err:   0.008688
[2025-10-23 14:13:41] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -67.346920 | E_var:     0.5182 | E_err:   0.011248
[2025-10-23 14:13:54] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -67.351498 | E_var:     0.3307 | E_err:   0.008985
[2025-10-23 14:14:07] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -67.362251 | E_var:     0.3582 | E_err:   0.009351
[2025-10-23 14:14:20] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -67.361381 | E_var:     0.3653 | E_err:   0.009444
[2025-10-23 14:14:34] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -67.361397 | E_var:     0.3078 | E_err:   0.008668
[2025-10-23 14:14:47] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -67.362674 | E_var:     0.3318 | E_err:   0.009001
[2025-10-23 14:15:00] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -67.355295 | E_var:     0.3395 | E_err:   0.009104
[2025-10-23 14:15:13] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -67.365221 | E_var:     0.3661 | E_err:   0.009454
[2025-10-23 14:15:26] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -67.357202 | E_var:     0.3290 | E_err:   0.008963
[2025-10-23 14:15:39] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -67.360017 | E_var:     0.3641 | E_err:   0.009428
[2025-10-23 14:15:53] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -67.355295 | E_var:     0.5293 | E_err:   0.011368
[2025-10-23 14:16:06] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -67.362533 | E_var:     0.3099 | E_err:   0.008698
[2025-10-23 14:16:19] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -67.347717 | E_var:     0.3959 | E_err:   0.009832
[2025-10-23 14:16:32] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -67.365464 | E_var:     0.3696 | E_err:   0.009500
[2025-10-23 14:16:45] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -67.349271 | E_var:     0.3953 | E_err:   0.009824
[2025-10-23 14:16:58] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -67.345524 | E_var:     0.3074 | E_err:   0.008662
[2025-10-23 14:17:12] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -67.353261 | E_var:     0.4208 | E_err:   0.010136
[2025-10-23 14:17:25] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -67.363814 | E_var:     0.3265 | E_err:   0.008929
[2025-10-23 14:17:38] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -67.345247 | E_var:     0.3641 | E_err:   0.009428
[2025-10-23 14:17:51] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -67.357129 | E_var:     0.3451 | E_err:   0.009179
[2025-10-23 14:18:04] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -67.364588 | E_var:     0.3425 | E_err:   0.009144
[2025-10-23 14:18:17] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -67.347626 | E_var:     0.4437 | E_err:   0.010408
[2025-10-23 14:18:31] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -67.358253 | E_var:     0.3672 | E_err:   0.009469
[2025-10-23 14:18:44] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -67.369549 | E_var:     0.2986 | E_err:   0.008538
[2025-10-23 14:18:57] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -67.372793 | E_var:     0.3560 | E_err:   0.009323
[2025-10-23 14:19:10] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -67.354728 | E_var:     0.3045 | E_err:   0.008622
[2025-10-23 14:19:23] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -67.344328 | E_var:     0.3391 | E_err:   0.009099
[2025-10-23 14:19:23] 🔄 RESTART #2 | Period: 600
[2025-10-23 14:19:36] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -67.351828 | E_var:     0.4079 | E_err:   0.009979
[2025-10-23 14:19:50] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -67.349201 | E_var:     0.3695 | E_err:   0.009498
[2025-10-23 14:20:03] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -67.353562 | E_var:     0.3047 | E_err:   0.008625
[2025-10-23 14:20:16] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -67.362768 | E_var:     0.3285 | E_err:   0.008955
[2025-10-23 14:20:29] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -67.350178 | E_var:     0.3213 | E_err:   0.008857
[2025-10-23 14:20:42] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -67.338962 | E_var:     0.3296 | E_err:   0.008970
[2025-10-23 14:20:55] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -67.350107 | E_var:     0.3488 | E_err:   0.009228
[2025-10-23 14:21:09] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -67.353821 | E_var:     0.3508 | E_err:   0.009254
[2025-10-23 14:21:22] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -67.344331 | E_var:     0.3475 | E_err:   0.009210
[2025-10-23 14:21:35] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -67.353418 | E_var:     0.2816 | E_err:   0.008291
[2025-10-23 14:21:48] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -67.353873 | E_var:     0.3036 | E_err:   0.008610
[2025-10-23 14:22:01] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -67.356439 | E_var:     0.3677 | E_err:   0.009475
[2025-10-23 14:22:14] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -67.353777 | E_var:     0.5223 | E_err:   0.011293
[2025-10-23 14:22:28] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -67.354979 | E_var:     0.3406 | E_err:   0.009119
[2025-10-23 14:22:41] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -67.355622 | E_var:     0.3594 | E_err:   0.009368
[2025-10-23 14:22:54] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -67.346231 | E_var:     0.3046 | E_err:   0.008624
[2025-10-23 14:23:07] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -67.364231 | E_var:     0.5471 | E_err:   0.011558
[2025-10-23 14:23:20] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -67.347887 | E_var:     0.3836 | E_err:   0.009677
[2025-10-23 14:23:33] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -67.356838 | E_var:     0.4201 | E_err:   0.010128
[2025-10-23 14:23:47] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -67.359734 | E_var:     0.3651 | E_err:   0.009441
[2025-10-23 14:24:00] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -67.348623 | E_var:     0.3853 | E_err:   0.009699
[2025-10-23 14:24:13] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -67.339701 | E_var:     0.3651 | E_err:   0.009442
[2025-10-23 14:24:26] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -67.337009 | E_var:     0.4349 | E_err:   0.010304
[2025-10-23 14:24:39] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -67.353358 | E_var:     0.3985 | E_err:   0.009863
[2025-10-23 14:24:52] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -67.366785 | E_var:     0.3605 | E_err:   0.009381
[2025-10-23 14:25:06] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -67.360037 | E_var:     0.3236 | E_err:   0.008888
[2025-10-23 14:25:19] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -67.368226 | E_var:     0.4177 | E_err:   0.010098
[2025-10-23 14:25:32] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -67.357585 | E_var:     0.3613 | E_err:   0.009391
[2025-10-23 14:25:45] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -67.358267 | E_var:     0.3417 | E_err:   0.009133
[2025-10-23 14:25:58] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -67.354927 | E_var:     0.3814 | E_err:   0.009650
[2025-10-23 14:26:11] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -67.357224 | E_var:     0.3675 | E_err:   0.009473
[2025-10-23 14:26:25] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -67.358199 | E_var:     0.3564 | E_err:   0.009328
[2025-10-23 14:26:38] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -67.365185 | E_var:     0.3138 | E_err:   0.008753
[2025-10-23 14:26:51] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -67.360756 | E_var:     0.2949 | E_err:   0.008485
[2025-10-23 14:27:04] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -67.362849 | E_var:     0.2726 | E_err:   0.008159
[2025-10-23 14:27:17] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -67.347446 | E_var:     0.3693 | E_err:   0.009495
[2025-10-23 14:27:30] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -67.359751 | E_var:     0.3792 | E_err:   0.009622
[2025-10-23 14:27:44] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -67.350255 | E_var:     0.3953 | E_err:   0.009823
[2025-10-23 14:27:57] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -67.356860 | E_var:     0.3568 | E_err:   0.009333
[2025-10-23 14:28:10] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -67.361134 | E_var:     0.3725 | E_err:   0.009536
[2025-10-23 14:28:23] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -67.365404 | E_var:     0.3822 | E_err:   0.009660
[2025-10-23 14:28:36] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -67.357608 | E_var:     0.3102 | E_err:   0.008702
[2025-10-23 14:28:49] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -67.357586 | E_var:     0.3308 | E_err:   0.008986
[2025-10-23 14:29:03] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -67.360789 | E_var:     0.4466 | E_err:   0.010441
[2025-10-23 14:29:16] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -67.352787 | E_var:     0.3272 | E_err:   0.008938
[2025-10-23 14:29:29] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -67.352189 | E_var:     0.3498 | E_err:   0.009242
[2025-10-23 14:29:42] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -67.345131 | E_var:     0.4515 | E_err:   0.010499
[2025-10-23 14:29:55] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -67.354755 | E_var:     0.3293 | E_err:   0.008966
[2025-10-23 14:30:08] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -67.355659 | E_var:     0.3318 | E_err:   0.009000
[2025-10-23 14:30:21] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -67.360156 | E_var:     0.3163 | E_err:   0.008788
[2025-10-23 14:30:35] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -67.358473 | E_var:     0.4118 | E_err:   0.010027
[2025-10-23 14:30:48] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -67.361121 | E_var:     0.3514 | E_err:   0.009263
[2025-10-23 14:31:01] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -67.350514 | E_var:     0.3230 | E_err:   0.008881
[2025-10-23 14:31:14] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -67.361649 | E_var:     0.3609 | E_err:   0.009387
[2025-10-23 14:31:27] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -67.336300 | E_var:     0.4124 | E_err:   0.010034
[2025-10-23 14:31:40] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -67.361836 | E_var:     0.3955 | E_err:   0.009826
[2025-10-23 14:31:54] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -67.365361 | E_var:     0.3308 | E_err:   0.008987
[2025-10-23 14:32:07] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -67.364730 | E_var:     0.3400 | E_err:   0.009111
[2025-10-23 14:32:20] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -67.349379 | E_var:     0.3395 | E_err:   0.009104
[2025-10-23 14:32:33] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -67.349653 | E_var:     0.4947 | E_err:   0.010990
[2025-10-23 14:32:46] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -67.346728 | E_var:     0.8039 | E_err:   0.014010
[2025-10-23 14:32:59] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -67.373255 | E_var:     0.4103 | E_err:   0.010008
[2025-10-23 14:33:13] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -67.369259 | E_var:     0.3610 | E_err:   0.009388
[2025-10-23 14:33:26] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -67.357420 | E_var:     0.3785 | E_err:   0.009613
[2025-10-23 14:33:39] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -67.347465 | E_var:     0.3059 | E_err:   0.008642
[2025-10-23 14:33:52] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -67.364563 | E_var:     0.4107 | E_err:   0.010014
[2025-10-23 14:34:05] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -67.369215 | E_var:     0.3114 | E_err:   0.008719
[2025-10-23 14:34:18] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -67.337721 | E_var:     0.3675 | E_err:   0.009473
[2025-10-23 14:34:31] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -67.358414 | E_var:     0.4196 | E_err:   0.010122
[2025-10-23 14:34:45] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -67.357391 | E_var:     0.3435 | E_err:   0.009158
[2025-10-23 14:34:58] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -67.352199 | E_var:     0.3808 | E_err:   0.009642
[2025-10-23 14:35:11] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -67.359578 | E_var:     0.2916 | E_err:   0.008437
[2025-10-23 14:35:24] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -67.387127 | E_var:     0.3327 | E_err:   0.009013
[2025-10-23 14:35:37] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -67.344074 | E_var:     0.3933 | E_err:   0.009799
[2025-10-23 14:35:50] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -67.368299 | E_var:     0.3378 | E_err:   0.009081
[2025-10-23 14:35:50] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-23 14:36:04] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -67.366367 | E_var:     0.4325 | E_err:   0.010276
[2025-10-23 14:36:17] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -67.361598 | E_var:     0.3722 | E_err:   0.009533
[2025-10-23 14:36:30] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -67.358771 | E_var:     0.4135 | E_err:   0.010048
[2025-10-23 14:36:43] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -67.355710 | E_var:     0.3343 | E_err:   0.009034
[2025-10-23 14:36:56] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -67.350309 | E_var:     0.3933 | E_err:   0.009799
[2025-10-23 14:37:09] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -67.364537 | E_var:     0.4190 | E_err:   0.010114
[2025-10-23 14:37:23] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -67.346599 | E_var:     0.3727 | E_err:   0.009539
[2025-10-23 14:37:36] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -67.334249 | E_var:     0.6694 | E_err:   0.012784
[2025-10-23 14:37:49] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -67.365364 | E_var:     0.3971 | E_err:   0.009846
[2025-10-23 14:38:02] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -67.353505 | E_var:     0.3308 | E_err:   0.008987
[2025-10-23 14:38:15] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -67.355794 | E_var:     0.3118 | E_err:   0.008725
[2025-10-23 14:38:28] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -67.367834 | E_var:     0.3334 | E_err:   0.009021
[2025-10-23 14:38:42] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -67.353085 | E_var:     0.3186 | E_err:   0.008819
[2025-10-23 14:38:55] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -67.359729 | E_var:     0.3279 | E_err:   0.008947
[2025-10-23 14:39:08] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -67.364126 | E_var:     0.3255 | E_err:   0.008915
[2025-10-23 14:39:21] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -67.347494 | E_var:     0.3449 | E_err:   0.009177
[2025-10-23 14:39:34] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -67.355983 | E_var:     0.3714 | E_err:   0.009522
[2025-10-23 14:39:47] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -67.365487 | E_var:     0.3882 | E_err:   0.009736
[2025-10-23 14:40:01] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -67.379229 | E_var:     0.3087 | E_err:   0.008682
[2025-10-23 14:40:14] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -67.366720 | E_var:     0.2834 | E_err:   0.008318
[2025-10-23 14:40:27] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -67.356604 | E_var:     0.3352 | E_err:   0.009046
[2025-10-23 14:40:40] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -67.346667 | E_var:     0.3617 | E_err:   0.009397
[2025-10-23 14:40:53] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -67.358956 | E_var:     0.3466 | E_err:   0.009198
[2025-10-23 14:41:06] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -67.344896 | E_var:     0.3809 | E_err:   0.009644
[2025-10-23 14:41:20] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -67.351917 | E_var:     0.3622 | E_err:   0.009404
[2025-10-23 14:41:33] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -67.354965 | E_var:     0.3283 | E_err:   0.008952
[2025-10-23 14:41:46] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -67.354889 | E_var:     0.4302 | E_err:   0.010248
[2025-10-23 14:41:59] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -67.372180 | E_var:     0.3719 | E_err:   0.009529
[2025-10-23 14:42:12] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -67.358842 | E_var:     0.3919 | E_err:   0.009781
[2025-10-23 14:42:25] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -67.367242 | E_var:     1.1355 | E_err:   0.016650
[2025-10-23 14:42:39] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -67.333482 | E_var:     0.4218 | E_err:   0.010147
[2025-10-23 14:42:52] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -67.359588 | E_var:     0.4434 | E_err:   0.010404
[2025-10-23 14:43:05] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -67.358529 | E_var:     0.4264 | E_err:   0.010203
[2025-10-23 14:43:18] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -67.358459 | E_var:     0.3813 | E_err:   0.009649
[2025-10-23 14:43:31] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -67.359012 | E_var:     0.3279 | E_err:   0.008947
[2025-10-23 14:43:44] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -67.342419 | E_var:     0.3979 | E_err:   0.009857
[2025-10-23 14:43:58] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -67.341411 | E_var:     0.3466 | E_err:   0.009199
[2025-10-23 14:44:11] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -67.355334 | E_var:     0.3620 | E_err:   0.009401
[2025-10-23 14:44:24] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -67.326294 | E_var:     0.6390 | E_err:   0.012491
[2025-10-23 14:44:37] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -67.363265 | E_var:     0.3397 | E_err:   0.009106
[2025-10-23 14:44:50] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -67.364232 | E_var:     0.3399 | E_err:   0.009110
[2025-10-23 14:45:03] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -67.367598 | E_var:     0.3951 | E_err:   0.009821
[2025-10-23 14:45:17] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -67.355386 | E_var:     0.3143 | E_err:   0.008760
[2025-10-23 14:45:30] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -67.368211 | E_var:     0.3781 | E_err:   0.009607
[2025-10-23 14:45:43] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -67.337330 | E_var:     0.3076 | E_err:   0.008666
[2025-10-23 14:45:56] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -67.370735 | E_var:     0.3128 | E_err:   0.008739
[2025-10-23 14:46:09] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -67.378260 | E_var:     0.3846 | E_err:   0.009690
[2025-10-23 14:46:22] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -67.362401 | E_var:     0.3745 | E_err:   0.009562
[2025-10-23 14:46:36] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -67.351328 | E_var:     0.3493 | E_err:   0.009234
[2025-10-23 14:46:49] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -67.343094 | E_var:     0.3591 | E_err:   0.009363
[2025-10-23 14:47:02] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -67.346801 | E_var:     0.3901 | E_err:   0.009759
[2025-10-23 14:47:15] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -67.343245 | E_var:     0.3677 | E_err:   0.009475
[2025-10-23 14:47:28] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -67.353499 | E_var:     0.4418 | E_err:   0.010386
[2025-10-23 14:47:41] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -67.359358 | E_var:     0.4089 | E_err:   0.009992
[2025-10-23 14:47:55] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -67.352993 | E_var:     0.4382 | E_err:   0.010343
[2025-10-23 14:48:08] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -67.359061 | E_var:     0.4291 | E_err:   0.010236
[2025-10-23 14:48:21] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -67.381944 | E_var:     1.7623 | E_err:   0.020743
[2025-10-23 14:48:34] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -67.344554 | E_var:     0.3617 | E_err:   0.009397
[2025-10-23 14:48:47] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -67.369647 | E_var:     0.3422 | E_err:   0.009140
[2025-10-23 14:49:00] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -67.352518 | E_var:     0.3517 | E_err:   0.009266
[2025-10-23 14:49:14] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -67.359050 | E_var:     0.4253 | E_err:   0.010190
[2025-10-23 14:49:27] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -67.361619 | E_var:     0.3292 | E_err:   0.008965
[2025-10-23 14:49:40] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -67.361367 | E_var:     0.3251 | E_err:   0.008909
[2025-10-23 14:49:53] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -67.356297 | E_var:     0.3937 | E_err:   0.009803
[2025-10-23 14:50:06] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -67.368534 | E_var:     0.3737 | E_err:   0.009552
[2025-10-23 14:50:20] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -67.342704 | E_var:     0.4582 | E_err:   0.010577
[2025-10-23 14:50:33] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -67.349089 | E_var:     0.3454 | E_err:   0.009183
[2025-10-23 14:50:46] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -67.342642 | E_var:     0.5011 | E_err:   0.011061
[2025-10-23 14:50:59] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -67.348557 | E_var:     0.4290 | E_err:   0.010234
[2025-10-23 14:51:12] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -67.366233 | E_var:     0.3375 | E_err:   0.009078
[2025-10-23 14:51:25] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -67.359909 | E_var:     0.5289 | E_err:   0.011363
[2025-10-23 14:51:39] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -67.373580 | E_var:     0.3675 | E_err:   0.009473
[2025-10-23 14:51:52] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -67.354241 | E_var:     0.3060 | E_err:   0.008643
[2025-10-23 14:52:05] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -67.365979 | E_var:     0.4214 | E_err:   0.010143
[2025-10-23 14:52:18] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -67.364812 | E_var:     0.4385 | E_err:   0.010347
[2025-10-23 14:52:31] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -67.365456 | E_var:     0.3549 | E_err:   0.009308
[2025-10-23 14:52:44] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -67.367398 | E_var:     0.3075 | E_err:   0.008664
[2025-10-23 14:52:58] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -67.346832 | E_var:     0.3122 | E_err:   0.008730
[2025-10-23 14:53:11] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -67.363619 | E_var:     0.4041 | E_err:   0.009933
[2025-10-23 14:53:24] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -67.351835 | E_var:     0.3867 | E_err:   0.009716
[2025-10-23 14:53:37] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -67.354496 | E_var:     0.3556 | E_err:   0.009317
[2025-10-23 14:53:50] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -67.359903 | E_var:     0.3955 | E_err:   0.009826
[2025-10-23 14:54:03] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -67.374783 | E_var:     0.3211 | E_err:   0.008854
[2025-10-23 14:54:17] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -67.358361 | E_var:     0.4705 | E_err:   0.010717
[2025-10-23 14:54:30] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -67.349920 | E_var:     0.3378 | E_err:   0.009081
[2025-10-23 14:54:43] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -67.369275 | E_var:     0.3316 | E_err:   0.008997
[2025-10-23 14:54:56] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -67.354739 | E_var:     0.3073 | E_err:   0.008662
[2025-10-23 14:55:09] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -67.367109 | E_var:     0.3770 | E_err:   0.009593
[2025-10-23 14:55:23] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -67.353871 | E_var:     0.3450 | E_err:   0.009178
[2025-10-23 14:55:36] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -67.357756 | E_var:     0.3638 | E_err:   0.009424
[2025-10-23 14:55:49] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -67.349172 | E_var:     0.3641 | E_err:   0.009428
[2025-10-23 14:56:02] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -67.362358 | E_var:     0.4050 | E_err:   0.009944
[2025-10-23 14:56:15] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -67.361405 | E_var:     0.5304 | E_err:   0.011379
[2025-10-23 14:56:28] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -67.360991 | E_var:     0.5663 | E_err:   0.011758
[2025-10-23 14:56:42] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -67.355331 | E_var:     0.3803 | E_err:   0.009636
[2025-10-23 14:56:55] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -67.348393 | E_var:     0.3046 | E_err:   0.008623
[2025-10-23 14:57:08] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -67.351796 | E_var:     0.4003 | E_err:   0.009885
[2025-10-23 14:57:21] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -67.342286 | E_var:     0.5692 | E_err:   0.011789
[2025-10-23 14:57:34] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -67.359612 | E_var:     0.3310 | E_err:   0.008990
[2025-10-23 14:57:47] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -67.349291 | E_var:     0.4339 | E_err:   0.010293
[2025-10-23 14:58:01] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -67.354638 | E_var:     0.3980 | E_err:   0.009858
[2025-10-23 14:58:14] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -67.341385 | E_var:     1.4635 | E_err:   0.018902
[2025-10-23 14:58:27] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -67.351127 | E_var:     0.3192 | E_err:   0.008827
[2025-10-23 14:58:40] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -67.354501 | E_var:     0.3945 | E_err:   0.009813
[2025-10-23 14:58:53] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -67.350408 | E_var:     0.3488 | E_err:   0.009228
[2025-10-23 14:58:53] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-23 14:59:07] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -67.350935 | E_var:     0.3714 | E_err:   0.009522
[2025-10-23 14:59:20] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -67.351841 | E_var:     0.3495 | E_err:   0.009237
[2025-10-23 14:59:33] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -67.359502 | E_var:     0.2993 | E_err:   0.008548
[2025-10-23 14:59:46] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -67.359368 | E_var:     0.5507 | E_err:   0.011595
[2025-10-23 14:59:59] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -67.340793 | E_var:     0.3953 | E_err:   0.009824
[2025-10-23 15:00:13] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -67.356618 | E_var:     0.3266 | E_err:   0.008930
[2025-10-23 15:00:26] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -67.364373 | E_var:     0.3727 | E_err:   0.009539
[2025-10-23 15:00:39] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -67.360386 | E_var:     0.3542 | E_err:   0.009299
[2025-10-23 15:00:52] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -67.347769 | E_var:     0.3474 | E_err:   0.009209
[2025-10-23 15:01:05] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -67.355227 | E_var:     0.3038 | E_err:   0.008612
[2025-10-23 15:01:18] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -67.349975 | E_var:     0.4132 | E_err:   0.010044
[2025-10-23 15:01:32] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -67.363467 | E_var:     0.3391 | E_err:   0.009098
[2025-10-23 15:01:45] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -67.352208 | E_var:     0.3434 | E_err:   0.009157
[2025-10-23 15:01:58] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -67.372453 | E_var:     0.4220 | E_err:   0.010151
[2025-10-23 15:02:11] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -67.362152 | E_var:     0.3206 | E_err:   0.008848
[2025-10-23 15:02:24] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -67.351678 | E_var:     0.3327 | E_err:   0.009012
[2025-10-23 15:02:37] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -67.349622 | E_var:     0.4055 | E_err:   0.009950
[2025-10-23 15:02:51] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -67.355596 | E_var:     0.3258 | E_err:   0.008919
[2025-10-23 15:03:04] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -67.346985 | E_var:     0.3803 | E_err:   0.009636
[2025-10-23 15:03:17] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -67.357928 | E_var:     0.3269 | E_err:   0.008934
[2025-10-23 15:03:30] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -67.346198 | E_var:     0.4007 | E_err:   0.009890
[2025-10-23 15:03:43] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -67.336606 | E_var:     0.5464 | E_err:   0.011550
[2025-10-23 15:03:57] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -67.351916 | E_var:     0.3677 | E_err:   0.009475
[2025-10-23 15:04:10] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -67.371979 | E_var:     0.3440 | E_err:   0.009165
[2025-10-23 15:04:23] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -67.358826 | E_var:     0.3453 | E_err:   0.009182
[2025-10-23 15:04:36] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -67.346024 | E_var:     0.3210 | E_err:   0.008852
[2025-10-23 15:04:49] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -67.334236 | E_var:     0.4891 | E_err:   0.010927
[2025-10-23 15:05:02] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -67.363093 | E_var:     0.3166 | E_err:   0.008792
[2025-10-23 15:05:16] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -67.356057 | E_var:     0.3572 | E_err:   0.009339
[2025-10-23 15:05:29] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -67.344105 | E_var:     0.3306 | E_err:   0.008984
[2025-10-23 15:05:42] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -67.360437 | E_var:     0.3039 | E_err:   0.008613
[2025-10-23 15:05:55] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -67.355788 | E_var:     0.6307 | E_err:   0.012409
[2025-10-23 15:06:08] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -67.342870 | E_var:     0.3291 | E_err:   0.008964
[2025-10-23 15:06:22] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -67.359912 | E_var:     0.3466 | E_err:   0.009198
[2025-10-23 15:06:35] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -67.377681 | E_var:     0.3278 | E_err:   0.008946
[2025-10-23 15:06:48] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -67.360400 | E_var:     0.3605 | E_err:   0.009381
[2025-10-23 15:07:01] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -67.343860 | E_var:     0.4217 | E_err:   0.010146
[2025-10-23 15:07:14] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -67.349668 | E_var:     0.3696 | E_err:   0.009499
[2025-10-23 15:07:27] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -67.368293 | E_var:     0.3418 | E_err:   0.009135
[2025-10-23 15:07:41] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -67.343572 | E_var:     0.4866 | E_err:   0.010899
[2025-10-23 15:07:54] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -67.349031 | E_var:     0.3820 | E_err:   0.009657
[2025-10-23 15:08:07] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -67.360694 | E_var:     0.4536 | E_err:   0.010524
[2025-10-23 15:08:20] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -67.353391 | E_var:     0.2965 | E_err:   0.008508
[2025-10-23 15:08:33] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -67.342950 | E_var:     0.4112 | E_err:   0.010020
[2025-10-23 15:08:47] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -67.368953 | E_var:     0.3307 | E_err:   0.008985
[2025-10-23 15:09:00] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -67.350668 | E_var:     0.3171 | E_err:   0.008799
[2025-10-23 15:09:13] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -67.367784 | E_var:     0.3634 | E_err:   0.009420
[2025-10-23 15:09:26] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -67.357010 | E_var:     0.3876 | E_err:   0.009727
[2025-10-23 15:09:39] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -67.369531 | E_var:     0.6590 | E_err:   0.012684
[2025-10-23 15:09:52] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -67.374236 | E_var:     0.3200 | E_err:   0.008839
[2025-10-23 15:10:06] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -67.369616 | E_var:     0.4649 | E_err:   0.010654
[2025-10-23 15:10:19] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -67.361989 | E_var:     0.3494 | E_err:   0.009236
[2025-10-23 15:10:32] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -67.357073 | E_var:     0.5113 | E_err:   0.011173
[2025-10-23 15:10:45] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -67.366397 | E_var:     0.3535 | E_err:   0.009290
[2025-10-23 15:10:58] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -67.353545 | E_var:     0.3400 | E_err:   0.009111
[2025-10-23 15:11:12] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -67.369573 | E_var:     0.3939 | E_err:   0.009807
[2025-10-23 15:11:25] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -67.355353 | E_var:     0.3670 | E_err:   0.009466
[2025-10-23 15:11:38] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -67.347793 | E_var:     0.3191 | E_err:   0.008826
[2025-10-23 15:11:51] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -67.352134 | E_var:     0.3480 | E_err:   0.009218
[2025-10-23 15:12:04] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -67.361782 | E_var:     0.3492 | E_err:   0.009233
[2025-10-23 15:12:17] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -67.357728 | E_var:     0.3130 | E_err:   0.008742
[2025-10-23 15:12:31] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -67.358448 | E_var:     0.7373 | E_err:   0.013417
[2025-10-23 15:12:44] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -67.339422 | E_var:     0.4245 | E_err:   0.010180
[2025-10-23 15:12:57] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -67.356248 | E_var:     0.3319 | E_err:   0.009001
[2025-10-23 15:13:10] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -67.343980 | E_var:     0.3238 | E_err:   0.008891
[2025-10-23 15:13:23] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -67.363714 | E_var:     0.3086 | E_err:   0.008680
[2025-10-23 15:13:36] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -67.371055 | E_var:     0.3923 | E_err:   0.009786
[2025-10-23 15:13:50] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -67.364878 | E_var:     0.4772 | E_err:   0.010793
[2025-10-23 15:14:03] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -67.354744 | E_var:     0.3256 | E_err:   0.008915
[2025-10-23 15:14:16] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -67.338964 | E_var:     0.3491 | E_err:   0.009232
[2025-10-23 15:14:29] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -67.356239 | E_var:     0.3075 | E_err:   0.008664
[2025-10-23 15:14:42] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -67.353794 | E_var:     0.3258 | E_err:   0.008919
[2025-10-23 15:14:56] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -67.348798 | E_var:     0.3719 | E_err:   0.009529
[2025-10-23 15:15:09] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -67.361824 | E_var:     0.3896 | E_err:   0.009753
[2025-10-23 15:15:22] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -67.362254 | E_var:     0.3324 | E_err:   0.009008
[2025-10-23 15:15:35] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -67.344301 | E_var:     0.3694 | E_err:   0.009497
[2025-10-23 15:15:48] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -67.349241 | E_var:     0.3621 | E_err:   0.009402
[2025-10-23 15:16:01] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -67.360956 | E_var:     0.4115 | E_err:   0.010023
[2025-10-23 15:16:15] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -67.360969 | E_var:     0.3396 | E_err:   0.009105
[2025-10-23 15:16:28] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -67.348112 | E_var:     0.3588 | E_err:   0.009360
[2025-10-23 15:16:41] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -67.341635 | E_var:     0.5020 | E_err:   0.011071
[2025-10-23 15:16:54] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -67.353440 | E_var:     0.3875 | E_err:   0.009726
[2025-10-23 15:17:07] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -67.359708 | E_var:     0.3088 | E_err:   0.008682
[2025-10-23 15:17:21] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -67.367529 | E_var:     0.4290 | E_err:   0.010235
[2025-10-23 15:17:34] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -67.364256 | E_var:     0.3159 | E_err:   0.008782
[2025-10-23 15:17:47] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -67.360150 | E_var:     0.5650 | E_err:   0.011745
[2025-10-23 15:18:00] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -67.351485 | E_var:     0.3485 | E_err:   0.009224
[2025-10-23 15:18:13] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -67.364053 | E_var:     0.3758 | E_err:   0.009579
[2025-10-23 15:18:26] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -67.341096 | E_var:     0.3057 | E_err:   0.008639
[2025-10-23 15:18:40] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -67.350515 | E_var:     0.3368 | E_err:   0.009067
[2025-10-23 15:18:53] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -67.349051 | E_var:     0.3040 | E_err:   0.008615
[2025-10-23 15:19:06] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -67.352001 | E_var:     0.3670 | E_err:   0.009465
[2025-10-23 15:19:19] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -67.348437 | E_var:     0.3646 | E_err:   0.009435
[2025-10-23 15:19:32] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -67.360748 | E_var:     0.3339 | E_err:   0.009029
[2025-10-23 15:19:46] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -67.352425 | E_var:     0.3238 | E_err:   0.008891
[2025-10-23 15:19:59] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -67.361083 | E_var:     0.3111 | E_err:   0.008716
[2025-10-23 15:20:12] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -67.352218 | E_var:     0.5191 | E_err:   0.011258
[2025-10-23 15:20:25] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -67.345188 | E_var:     0.3278 | E_err:   0.008946
[2025-10-23 15:20:38] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -67.366111 | E_var:     0.3567 | E_err:   0.009332
[2025-10-23 15:20:52] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -67.350723 | E_var:     0.3592 | E_err:   0.009364
[2025-10-23 15:21:05] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -67.360642 | E_var:     0.4214 | E_err:   0.010143
[2025-10-23 15:21:18] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -67.348674 | E_var:     0.3440 | E_err:   0.009164
[2025-10-23 15:21:31] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -67.349241 | E_var:     0.3206 | E_err:   0.008847
[2025-10-23 15:21:44] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -67.357110 | E_var:     0.3695 | E_err:   0.009498
[2025-10-23 15:21:57] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -67.355380 | E_var:     0.3744 | E_err:   0.009560
[2025-10-23 15:21:58] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-23 15:22:11] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -67.349898 | E_var:     0.3386 | E_err:   0.009092
[2025-10-23 15:22:24] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -67.350704 | E_var:     0.4452 | E_err:   0.010426
[2025-10-23 15:22:37] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -67.342124 | E_var:     0.3214 | E_err:   0.008859
[2025-10-23 15:22:50] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -67.354479 | E_var:     0.3311 | E_err:   0.008991
[2025-10-23 15:23:03] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -67.365418 | E_var:     0.3441 | E_err:   0.009166
[2025-10-23 15:23:17] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -67.381901 | E_var:     0.3675 | E_err:   0.009472
[2025-10-23 15:23:30] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -67.354229 | E_var:     0.3361 | E_err:   0.009059
[2025-10-23 15:23:43] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -67.355585 | E_var:     0.3117 | E_err:   0.008723
[2025-10-23 15:23:56] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -67.349364 | E_var:     0.3351 | E_err:   0.009045
[2025-10-23 15:24:09] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -67.374789 | E_var:     0.4681 | E_err:   0.010690
[2025-10-23 15:24:23] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -67.360325 | E_var:     0.3287 | E_err:   0.008958
[2025-10-23 15:24:36] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -67.334620 | E_var:     0.4904 | E_err:   0.010942
[2025-10-23 15:24:49] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -67.349643 | E_var:     0.3646 | E_err:   0.009435
[2025-10-23 15:25:02] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -67.345104 | E_var:     0.3856 | E_err:   0.009702
[2025-10-23 15:25:15] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -67.344286 | E_var:     0.5452 | E_err:   0.011537
[2025-10-23 15:25:28] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -67.343814 | E_var:     0.3592 | E_err:   0.009365
[2025-10-23 15:25:42] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -67.354466 | E_var:     0.3273 | E_err:   0.008939
[2025-10-23 15:25:55] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -67.359934 | E_var:     0.3627 | E_err:   0.009410
[2025-10-23 15:26:08] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -67.347415 | E_var:     0.3697 | E_err:   0.009501
[2025-10-23 15:26:21] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -67.382761 | E_var:     0.3663 | E_err:   0.009457
[2025-10-23 15:26:34] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -67.339985 | E_var:     0.3328 | E_err:   0.009014
[2025-10-23 15:26:48] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -67.354663 | E_var:     0.3561 | E_err:   0.009324
[2025-10-23 15:27:01] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -67.360764 | E_var:     0.3468 | E_err:   0.009201
[2025-10-23 15:27:14] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -67.350495 | E_var:     0.3146 | E_err:   0.008764
[2025-10-23 15:27:27] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -67.357018 | E_var:     0.3084 | E_err:   0.008678
[2025-10-23 15:27:40] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -67.344694 | E_var:     0.2861 | E_err:   0.008358
[2025-10-23 15:27:53] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -67.353625 | E_var:     0.3508 | E_err:   0.009255
[2025-10-23 15:28:07] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -67.362463 | E_var:     0.3221 | E_err:   0.008868
[2025-10-23 15:28:20] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -67.352327 | E_var:     0.4082 | E_err:   0.009983
[2025-10-23 15:28:33] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -67.354988 | E_var:     0.3922 | E_err:   0.009786
[2025-10-23 15:28:46] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -67.361207 | E_var:     0.3859 | E_err:   0.009706
[2025-10-23 15:28:59] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -67.349553 | E_var:     0.3738 | E_err:   0.009553
[2025-10-23 15:29:12] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -67.360321 | E_var:     0.3418 | E_err:   0.009135
[2025-10-23 15:29:26] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -67.354918 | E_var:     0.3452 | E_err:   0.009180
[2025-10-23 15:29:39] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -67.355485 | E_var:     0.3731 | E_err:   0.009545
[2025-10-23 15:29:52] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -67.346240 | E_var:     0.5562 | E_err:   0.011652
[2025-10-23 15:30:05] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -67.362551 | E_var:     0.3597 | E_err:   0.009371
[2025-10-23 15:30:18] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -67.351751 | E_var:     0.3899 | E_err:   0.009756
[2025-10-23 15:30:32] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -67.352972 | E_var:     0.3535 | E_err:   0.009290
[2025-10-23 15:30:45] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -67.355463 | E_var:     0.3152 | E_err:   0.008773
[2025-10-23 15:30:58] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -67.342101 | E_var:     0.3351 | E_err:   0.009045
[2025-10-23 15:31:11] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -67.356851 | E_var:     0.4906 | E_err:   0.010944
[2025-10-23 15:31:24] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -67.349804 | E_var:     0.3580 | E_err:   0.009348
[2025-10-23 15:31:38] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -67.361129 | E_var:     0.3636 | E_err:   0.009422
[2025-10-23 15:31:51] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -67.359309 | E_var:     0.3389 | E_err:   0.009097
[2025-10-23 15:32:04] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -67.351652 | E_var:     0.3510 | E_err:   0.009257
[2025-10-23 15:32:17] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -67.347465 | E_var:     0.4037 | E_err:   0.009928
[2025-10-23 15:32:30] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -67.352916 | E_var:     0.3071 | E_err:   0.008658
[2025-10-23 15:32:43] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -67.366137 | E_var:     0.3160 | E_err:   0.008783
[2025-10-23 15:32:57] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -67.345841 | E_var:     0.4112 | E_err:   0.010019
[2025-10-23 15:33:10] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -67.362571 | E_var:     0.3854 | E_err:   0.009701
[2025-10-23 15:33:23] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -67.368261 | E_var:     0.4293 | E_err:   0.010238
[2025-10-23 15:33:36] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -67.361660 | E_var:     0.3257 | E_err:   0.008917
[2025-10-23 15:33:49] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -67.361764 | E_var:     0.4044 | E_err:   0.009936
[2025-10-23 15:34:02] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -67.357598 | E_var:     0.2878 | E_err:   0.008382
[2025-10-23 15:34:16] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -67.351554 | E_var:     0.3310 | E_err:   0.008989
[2025-10-23 15:34:29] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -67.354344 | E_var:     0.2884 | E_err:   0.008390
[2025-10-23 15:34:42] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -67.345328 | E_var:     0.3073 | E_err:   0.008661
[2025-10-23 15:34:55] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -67.360928 | E_var:     0.3395 | E_err:   0.009105
[2025-10-23 15:35:08] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -67.360408 | E_var:     0.2890 | E_err:   0.008399
[2025-10-23 15:35:21] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -67.369052 | E_var:     0.4378 | E_err:   0.010338
[2025-10-23 15:35:35] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -67.350035 | E_var:     0.3318 | E_err:   0.009000
[2025-10-23 15:35:48] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -67.352938 | E_var:     0.3372 | E_err:   0.009073
[2025-10-23 15:36:01] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -67.347733 | E_var:     0.3682 | E_err:   0.009482
[2025-10-23 15:36:14] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -67.344349 | E_var:     0.3110 | E_err:   0.008714
[2025-10-23 15:36:27] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -67.361033 | E_var:     0.3936 | E_err:   0.009803
[2025-10-23 15:36:41] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -67.357435 | E_var:     0.3482 | E_err:   0.009220
[2025-10-23 15:36:54] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -67.337052 | E_var:     0.3247 | E_err:   0.008903
[2025-10-23 15:37:07] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -67.353328 | E_var:     0.4551 | E_err:   0.010541
[2025-10-23 15:37:20] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -67.365043 | E_var:     0.3752 | E_err:   0.009571
[2025-10-23 15:37:33] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -67.357173 | E_var:     0.3480 | E_err:   0.009218
[2025-10-23 15:37:46] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -67.337354 | E_var:     0.8260 | E_err:   0.014201
[2025-10-23 15:38:00] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -67.353517 | E_var:     0.5154 | E_err:   0.011217
[2025-10-23 15:38:13] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -67.363770 | E_var:     0.3939 | E_err:   0.009806
[2025-10-23 15:38:26] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -67.350253 | E_var:     0.3157 | E_err:   0.008780
[2025-10-23 15:38:39] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -67.357868 | E_var:     0.3795 | E_err:   0.009625
[2025-10-23 15:38:52] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -67.368068 | E_var:     0.3346 | E_err:   0.009038
[2025-10-23 15:39:05] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -67.369300 | E_var:     0.4163 | E_err:   0.010081
[2025-10-23 15:39:19] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -67.357037 | E_var:     0.4406 | E_err:   0.010372
[2025-10-23 15:39:32] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -67.364751 | E_var:     0.3247 | E_err:   0.008903
[2025-10-23 15:39:45] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -67.362233 | E_var:     0.3660 | E_err:   0.009452
[2025-10-23 15:39:58] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -67.358659 | E_var:     0.3329 | E_err:   0.009015
[2025-10-23 15:40:11] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -67.351615 | E_var:     0.3545 | E_err:   0.009303
[2025-10-23 15:40:25] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -67.356758 | E_var:     0.3682 | E_err:   0.009481
[2025-10-23 15:40:38] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -67.354882 | E_var:     0.3651 | E_err:   0.009441
[2025-10-23 15:40:51] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -67.333812 | E_var:     0.2857 | E_err:   0.008352
[2025-10-23 15:41:04] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -67.354152 | E_var:     0.3759 | E_err:   0.009580
[2025-10-23 15:41:17] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -67.360104 | E_var:     0.3269 | E_err:   0.008933
[2025-10-23 15:41:30] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -67.353987 | E_var:     0.3510 | E_err:   0.009257
[2025-10-23 15:41:44] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -67.343364 | E_var:     0.4937 | E_err:   0.010979
[2025-10-23 15:41:57] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -67.334253 | E_var:     0.5357 | E_err:   0.011437
[2025-10-23 15:42:10] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -67.361498 | E_var:     0.3781 | E_err:   0.009608
[2025-10-23 15:42:23] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -67.361546 | E_var:     0.3266 | E_err:   0.008930
[2025-10-23 15:42:36] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -67.341423 | E_var:     0.5423 | E_err:   0.011506
[2025-10-23 15:42:49] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -67.363526 | E_var:     0.4167 | E_err:   0.010087
[2025-10-23 15:43:03] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -67.354859 | E_var:     0.3183 | E_err:   0.008816
[2025-10-23 15:43:16] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -67.373973 | E_var:     0.3493 | E_err:   0.009235
[2025-10-23 15:43:29] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -67.368047 | E_var:     0.4549 | E_err:   0.010538
[2025-10-23 15:43:42] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -67.365384 | E_var:     0.3783 | E_err:   0.009610
[2025-10-23 15:43:55] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -67.368823 | E_var:     0.3552 | E_err:   0.009312
[2025-10-23 15:44:09] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -67.341430 | E_var:     0.5223 | E_err:   0.011292
[2025-10-23 15:44:22] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -67.369304 | E_var:     0.3220 | E_err:   0.008867
[2025-10-23 15:44:35] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -67.373956 | E_var:     0.4176 | E_err:   0.010097
[2025-10-23 15:44:48] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -67.350453 | E_var:     0.3480 | E_err:   0.009218
[2025-10-23 15:45:01] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -67.355393 | E_var:     0.3945 | E_err:   0.009814
[2025-10-23 15:45:01] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-23 15:45:14] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -67.362268 | E_var:     0.3959 | E_err:   0.009832
[2025-10-23 15:45:28] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -67.352940 | E_var:     0.3324 | E_err:   0.009008
[2025-10-23 15:45:42] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -67.351396 | E_var:     0.4654 | E_err:   0.010659
[2025-10-23 15:45:55] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -67.350650 | E_var:     0.5960 | E_err:   0.012062
[2025-10-23 15:46:08] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -67.363064 | E_var:     0.4081 | E_err:   0.009982
[2025-10-23 15:46:21] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -67.359058 | E_var:     0.3783 | E_err:   0.009610
[2025-10-23 15:46:34] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -67.352812 | E_var:     0.3190 | E_err:   0.008825
[2025-10-23 15:46:47] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -67.355797 | E_var:     0.3564 | E_err:   0.009328
[2025-10-23 15:47:01] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -67.346447 | E_var:     0.4831 | E_err:   0.010860
[2025-10-23 15:47:14] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -67.356216 | E_var:     0.3478 | E_err:   0.009215
[2025-10-23 15:47:27] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -67.349818 | E_var:     0.3967 | E_err:   0.009841
[2025-10-23 15:47:40] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -67.376102 | E_var:     0.4638 | E_err:   0.010641
[2025-10-23 15:47:53] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -67.357556 | E_var:     0.4728 | E_err:   0.010744
[2025-10-23 15:48:07] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -67.366540 | E_var:     0.3283 | E_err:   0.008953
[2025-10-23 15:48:20] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -67.348316 | E_var:     0.3523 | E_err:   0.009274
[2025-10-23 15:48:33] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -67.357837 | E_var:     0.3402 | E_err:   0.009113
[2025-10-23 15:48:46] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -67.363973 | E_var:     0.3104 | E_err:   0.008706
[2025-10-23 15:48:59] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -67.364601 | E_var:     0.4702 | E_err:   0.010714
[2025-10-23 15:49:12] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -67.356174 | E_var:     0.3220 | E_err:   0.008867
[2025-10-23 15:49:26] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -67.363920 | E_var:     0.3748 | E_err:   0.009565
[2025-10-23 15:49:39] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -67.368782 | E_var:     0.4140 | E_err:   0.010054
[2025-10-23 15:49:52] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -67.350744 | E_var:     0.4561 | E_err:   0.010552
[2025-10-23 15:50:05] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -67.356948 | E_var:     0.3640 | E_err:   0.009427
[2025-10-23 15:50:18] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -67.363134 | E_var:     0.4804 | E_err:   0.010830
[2025-10-23 15:50:31] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -67.357129 | E_var:     0.3391 | E_err:   0.009098
[2025-10-23 15:50:45] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -67.357927 | E_var:     0.4207 | E_err:   0.010134
[2025-10-23 15:50:58] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -67.370791 | E_var:     0.4113 | E_err:   0.010021
[2025-10-23 15:51:11] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -67.335672 | E_var:     0.4061 | E_err:   0.009957
[2025-10-23 15:51:24] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -67.354161 | E_var:     0.4454 | E_err:   0.010428
[2025-10-23 15:51:37] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -67.345574 | E_var:     0.3472 | E_err:   0.009207
[2025-10-23 15:51:50] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -67.343153 | E_var:     0.3411 | E_err:   0.009125
[2025-10-23 15:52:04] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -67.349095 | E_var:     0.4748 | E_err:   0.010766
[2025-10-23 15:52:17] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -67.361653 | E_var:     0.3778 | E_err:   0.009604
[2025-10-23 15:52:30] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -67.352798 | E_var:     0.3824 | E_err:   0.009662
[2025-10-23 15:52:43] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -67.360511 | E_var:     0.3149 | E_err:   0.008768
[2025-10-23 15:52:56] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -67.327759 | E_var:     0.4558 | E_err:   0.010549
[2025-10-23 15:53:09] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -67.357198 | E_var:     0.2989 | E_err:   0.008543
[2025-10-23 15:53:23] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -67.342180 | E_var:     0.3811 | E_err:   0.009646
[2025-10-23 15:53:36] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -67.324885 | E_var:     0.3190 | E_err:   0.008825
[2025-10-23 15:53:49] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -67.336411 | E_var:     0.4564 | E_err:   0.010556
[2025-10-23 15:54:02] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -67.351068 | E_var:     0.3203 | E_err:   0.008843
[2025-10-23 15:54:15] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -67.349604 | E_var:     0.2920 | E_err:   0.008443
[2025-10-23 15:54:29] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -67.352924 | E_var:     0.3727 | E_err:   0.009539
[2025-10-23 15:54:42] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -67.352550 | E_var:     0.3346 | E_err:   0.009038
[2025-10-23 15:54:55] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -67.353300 | E_var:     0.4650 | E_err:   0.010654
[2025-10-23 15:55:08] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -67.357161 | E_var:     0.3407 | E_err:   0.009121
[2025-10-23 15:55:21] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -67.362392 | E_var:     0.3656 | E_err:   0.009447
[2025-10-23 15:55:34] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -67.364659 | E_var:     0.3061 | E_err:   0.008644
[2025-10-23 15:55:48] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -67.375175 | E_var:     0.3289 | E_err:   0.008961
[2025-10-23 15:56:01] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -67.357798 | E_var:     0.4101 | E_err:   0.010006
[2025-10-23 15:56:14] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -67.340198 | E_var:     0.3046 | E_err:   0.008624
[2025-10-23 15:56:27] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -67.349199 | E_var:     0.3440 | E_err:   0.009164
[2025-10-23 15:56:40] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -67.362942 | E_var:     0.3400 | E_err:   0.009110
[2025-10-23 15:56:53] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -67.345714 | E_var:     0.3780 | E_err:   0.009607
[2025-10-23 15:57:07] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -67.363361 | E_var:     0.3224 | E_err:   0.008872
[2025-10-23 15:57:20] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -67.370381 | E_var:     0.3696 | E_err:   0.009500
[2025-10-23 15:57:33] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -67.369658 | E_var:     0.3430 | E_err:   0.009150
[2025-10-23 15:57:46] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -67.350430 | E_var:     0.2735 | E_err:   0.008171
[2025-10-23 15:57:59] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -67.365467 | E_var:     0.3971 | E_err:   0.009846
[2025-10-23 15:58:12] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -67.369241 | E_var:     0.3359 | E_err:   0.009056
[2025-10-23 15:58:26] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -67.353230 | E_var:     0.3604 | E_err:   0.009380
[2025-10-23 15:58:39] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -67.357148 | E_var:     0.4248 | E_err:   0.010183
[2025-10-23 15:58:52] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -67.353816 | E_var:     0.3692 | E_err:   0.009493
[2025-10-23 15:59:05] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -67.358249 | E_var:     0.3287 | E_err:   0.008958
[2025-10-23 15:59:18] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -67.351299 | E_var:     0.3139 | E_err:   0.008754
[2025-10-23 15:59:32] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -67.333413 | E_var:     0.3929 | E_err:   0.009794
[2025-10-23 15:59:45] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -67.362537 | E_var:     0.4335 | E_err:   0.010288
[2025-10-23 15:59:58] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -67.338638 | E_var:     0.3046 | E_err:   0.008624
[2025-10-23 16:00:11] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -67.371189 | E_var:     0.2821 | E_err:   0.008299
[2025-10-23 16:00:24] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -67.359591 | E_var:     0.3321 | E_err:   0.009004
[2025-10-23 16:00:37] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -67.356722 | E_var:     0.4236 | E_err:   0.010169
[2025-10-23 16:00:51] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -67.365562 | E_var:     0.3309 | E_err:   0.008988
[2025-10-23 16:01:04] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -67.359149 | E_var:     0.2958 | E_err:   0.008497
[2025-10-23 16:01:17] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -67.358045 | E_var:     0.3059 | E_err:   0.008641
[2025-10-23 16:01:30] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -67.348999 | E_var:     0.4019 | E_err:   0.009905
[2025-10-23 16:01:43] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -67.357456 | E_var:     0.5027 | E_err:   0.011078
[2025-10-23 16:01:56] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -67.359834 | E_var:     0.3208 | E_err:   0.008850
[2025-10-23 16:02:10] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -67.344344 | E_var:     0.3178 | E_err:   0.008808
[2025-10-23 16:02:23] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -67.360678 | E_var:     0.4168 | E_err:   0.010088
[2025-10-23 16:02:36] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -67.334746 | E_var:     0.4222 | E_err:   0.010153
[2025-10-23 16:02:49] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -67.359203 | E_var:     0.3787 | E_err:   0.009615
[2025-10-23 16:03:02] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -67.362646 | E_var:     0.3628 | E_err:   0.009411
[2025-10-23 16:03:15] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -67.354778 | E_var:     0.3392 | E_err:   0.009100
[2025-10-23 16:03:29] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -67.365478 | E_var:     0.3102 | E_err:   0.008702
[2025-10-23 16:03:42] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -67.365982 | E_var:     0.3386 | E_err:   0.009092
[2025-10-23 16:03:55] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -67.357404 | E_var:     0.3533 | E_err:   0.009287
[2025-10-23 16:04:08] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -67.340294 | E_var:     0.3300 | E_err:   0.008975
[2025-10-23 16:04:21] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -67.352938 | E_var:     0.3396 | E_err:   0.009105
[2025-10-23 16:04:35] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -67.360405 | E_var:     0.3575 | E_err:   0.009342
[2025-10-23 16:04:48] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -67.381452 | E_var:     0.4258 | E_err:   0.010196
[2025-10-23 16:05:01] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -67.377606 | E_var:     0.3362 | E_err:   0.009060
[2025-10-23 16:05:14] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -67.367232 | E_var:     0.3434 | E_err:   0.009157
[2025-10-23 16:05:27] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -67.359478 | E_var:     0.3247 | E_err:   0.008904
[2025-10-23 16:05:40] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -67.355701 | E_var:     0.4322 | E_err:   0.010272
[2025-10-23 16:05:54] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -67.360786 | E_var:     0.3179 | E_err:   0.008810
[2025-10-23 16:06:07] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -67.356984 | E_var:     0.9016 | E_err:   0.014836
[2025-10-23 16:06:20] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -67.347833 | E_var:     0.3072 | E_err:   0.008661
[2025-10-23 16:06:33] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -67.372582 | E_var:     0.3986 | E_err:   0.009864
[2025-10-23 16:06:46] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -67.353496 | E_var:     0.3669 | E_err:   0.009465
[2025-10-23 16:07:00] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -67.345457 | E_var:     0.3413 | E_err:   0.009128
[2025-10-23 16:07:13] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -67.352816 | E_var:     0.3839 | E_err:   0.009682
[2025-10-23 16:07:26] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -67.373570 | E_var:     0.3259 | E_err:   0.008920
[2025-10-23 16:07:39] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -67.344798 | E_var:     0.3684 | E_err:   0.009484
[2025-10-23 16:07:52] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -67.356245 | E_var:     0.4744 | E_err:   0.010762
[2025-10-23 16:08:05] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -67.360507 | E_var:     0.3621 | E_err:   0.009402
[2025-10-23 16:08:05] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-23 16:08:19] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -67.349710 | E_var:     0.3864 | E_err:   0.009713
[2025-10-23 16:08:32] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -67.352964 | E_var:     0.3034 | E_err:   0.008606
[2025-10-23 16:08:45] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -67.350748 | E_var:     0.3241 | E_err:   0.008895
[2025-10-23 16:08:58] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -67.354106 | E_var:     0.3789 | E_err:   0.009618
[2025-10-23 16:09:11] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -67.358838 | E_var:     0.3579 | E_err:   0.009348
[2025-10-23 16:09:25] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -67.368841 | E_var:     0.3381 | E_err:   0.009086
[2025-10-23 16:09:38] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -67.373051 | E_var:     0.3928 | E_err:   0.009793
[2025-10-23 16:09:51] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -67.364875 | E_var:     0.3592 | E_err:   0.009364
[2025-10-23 16:10:04] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -67.359953 | E_var:     0.3139 | E_err:   0.008754
[2025-10-23 16:10:17] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -67.370678 | E_var:     0.3826 | E_err:   0.009665
[2025-10-23 16:10:31] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -67.359469 | E_var:     0.3440 | E_err:   0.009165
[2025-10-23 16:10:44] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -67.343774 | E_var:     0.2853 | E_err:   0.008346
[2025-10-23 16:10:57] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -67.349372 | E_var:     0.3450 | E_err:   0.009177
[2025-10-23 16:11:10] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -67.362063 | E_var:     0.3054 | E_err:   0.008635
[2025-10-23 16:11:23] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -67.362148 | E_var:     0.4336 | E_err:   0.010289
[2025-10-23 16:11:36] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -67.361407 | E_var:     0.3319 | E_err:   0.009001
[2025-10-23 16:11:50] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -67.358099 | E_var:     0.3962 | E_err:   0.009835
[2025-10-23 16:12:03] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -67.351332 | E_var:     0.3901 | E_err:   0.009760
[2025-10-23 16:12:16] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -67.352482 | E_var:     0.3799 | E_err:   0.009630
[2025-10-23 16:12:29] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -67.353092 | E_var:     0.3278 | E_err:   0.008945
[2025-10-23 16:12:42] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -67.350625 | E_var:     0.3548 | E_err:   0.009307
[2025-10-23 16:12:55] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -67.363199 | E_var:     0.3804 | E_err:   0.009637
[2025-10-23 16:13:09] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -67.338764 | E_var:     0.3502 | E_err:   0.009247
[2025-10-23 16:13:22] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -67.362439 | E_var:     0.3901 | E_err:   0.009760
[2025-10-23 16:13:35] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -67.356485 | E_var:     0.3592 | E_err:   0.009365
[2025-10-23 16:13:48] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -67.353083 | E_var:     0.3579 | E_err:   0.009348
[2025-10-23 16:14:01] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -67.371216 | E_var:     0.3252 | E_err:   0.008910
[2025-10-23 16:14:15] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -67.362415 | E_var:     0.3226 | E_err:   0.008875
[2025-10-23 16:14:28] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -67.360447 | E_var:     0.3470 | E_err:   0.009204
[2025-10-23 16:14:41] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -67.363265 | E_var:     0.3310 | E_err:   0.008989
[2025-10-23 16:14:54] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -67.358243 | E_var:     0.3144 | E_err:   0.008761
[2025-10-23 16:15:07] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -67.357491 | E_var:     0.4166 | E_err:   0.010085
[2025-10-23 16:15:20] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -67.353529 | E_var:     0.3463 | E_err:   0.009194
[2025-10-23 16:15:34] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -67.362734 | E_var:     0.4981 | E_err:   0.011028
[2025-10-23 16:15:47] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -67.367074 | E_var:     0.3711 | E_err:   0.009518
[2025-10-23 16:16:00] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -67.352849 | E_var:     0.3150 | E_err:   0.008769
[2025-10-23 16:16:13] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -67.373191 | E_var:     0.3396 | E_err:   0.009106
[2025-10-23 16:16:26] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -67.366335 | E_var:     0.3421 | E_err:   0.009139
[2025-10-23 16:16:40] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -67.362062 | E_var:     0.3298 | E_err:   0.008974
[2025-10-23 16:16:53] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -67.355490 | E_var:     0.4310 | E_err:   0.010258
[2025-10-23 16:17:06] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -67.365920 | E_var:     0.3850 | E_err:   0.009695
[2025-10-23 16:17:19] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -67.351214 | E_var:     0.3103 | E_err:   0.008703
[2025-10-23 16:17:32] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -67.364463 | E_var:     0.5141 | E_err:   0.011204
[2025-10-23 16:17:45] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -67.360698 | E_var:     0.3717 | E_err:   0.009526
[2025-10-23 16:17:59] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -67.363352 | E_var:     0.3936 | E_err:   0.009802
[2025-10-23 16:18:12] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -67.364254 | E_var:     0.3602 | E_err:   0.009378
[2025-10-23 16:18:25] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -67.365472 | E_var:     0.3197 | E_err:   0.008835
[2025-10-23 16:18:38] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -67.354272 | E_var:     0.3040 | E_err:   0.008615
[2025-10-23 16:18:51] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -67.353969 | E_var:     0.3471 | E_err:   0.009206
[2025-10-23 16:19:04] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -67.352949 | E_var:     0.3359 | E_err:   0.009055
[2025-10-23 16:19:18] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -67.369858 | E_var:     0.3370 | E_err:   0.009070
[2025-10-23 16:19:31] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -67.358946 | E_var:     0.3165 | E_err:   0.008790
[2025-10-23 16:19:44] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -67.367139 | E_var:     0.2992 | E_err:   0.008547
[2025-10-23 16:19:57] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -67.351323 | E_var:     0.3256 | E_err:   0.008916
[2025-10-23 16:20:10] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -67.345239 | E_var:     0.3418 | E_err:   0.009135
[2025-10-23 16:20:24] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -67.354386 | E_var:     0.3243 | E_err:   0.008898
[2025-10-23 16:20:37] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -67.361232 | E_var:     0.3414 | E_err:   0.009129
[2025-10-23 16:20:50] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -67.362049 | E_var:     0.5365 | E_err:   0.011445
[2025-10-23 16:21:03] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -67.353941 | E_var:     0.3368 | E_err:   0.009067
[2025-10-23 16:21:16] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -67.360866 | E_var:     0.3545 | E_err:   0.009303
[2025-10-23 16:21:30] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -67.359627 | E_var:     0.3199 | E_err:   0.008838
[2025-10-23 16:21:43] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -67.362591 | E_var:     0.2806 | E_err:   0.008276
[2025-10-23 16:21:56] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -67.357711 | E_var:     0.3089 | E_err:   0.008684
[2025-10-23 16:22:09] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -67.358697 | E_var:     0.3392 | E_err:   0.009100
[2025-10-23 16:22:22] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -67.353633 | E_var:     0.3488 | E_err:   0.009228
[2025-10-23 16:22:35] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -67.365931 | E_var:     0.3062 | E_err:   0.008647
[2025-10-23 16:22:49] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -67.345083 | E_var:     0.3132 | E_err:   0.008744
[2025-10-23 16:23:02] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -67.348808 | E_var:     0.4104 | E_err:   0.010010
[2025-10-23 16:23:15] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -67.357908 | E_var:     0.3214 | E_err:   0.008859
[2025-10-23 16:23:28] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -67.347937 | E_var:     0.3281 | E_err:   0.008950
[2025-10-23 16:23:41] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -67.367102 | E_var:     0.3456 | E_err:   0.009185
[2025-10-23 16:23:55] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -67.360996 | E_var:     0.4317 | E_err:   0.010267
[2025-10-23 16:24:08] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -67.346670 | E_var:     0.3730 | E_err:   0.009542
[2025-10-23 16:24:21] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -67.361136 | E_var:     0.3654 | E_err:   0.009445
[2025-10-23 16:24:34] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -67.355729 | E_var:     0.3636 | E_err:   0.009421
[2025-10-23 16:24:47] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -67.367321 | E_var:     0.3222 | E_err:   0.008870
[2025-10-23 16:25:00] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -67.352659 | E_var:     0.4361 | E_err:   0.010319
[2025-10-23 16:25:14] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -67.364910 | E_var:     0.3246 | E_err:   0.008903
[2025-10-23 16:25:27] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -67.347575 | E_var:     0.3796 | E_err:   0.009627
[2025-10-23 16:25:40] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -67.348455 | E_var:     0.3427 | E_err:   0.009147
[2025-10-23 16:25:53] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -67.339694 | E_var:     0.4274 | E_err:   0.010214
[2025-10-23 16:26:06] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -67.354700 | E_var:     0.3449 | E_err:   0.009177
[2025-10-23 16:26:20] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -67.357183 | E_var:     0.4167 | E_err:   0.010086
[2025-10-23 16:26:33] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -67.344107 | E_var:     0.3186 | E_err:   0.008819
[2025-10-23 16:26:46] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -67.374589 | E_var:     0.3506 | E_err:   0.009252
[2025-10-23 16:26:59] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -67.353136 | E_var:     0.4795 | E_err:   0.010819
[2025-10-23 16:27:12] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -67.366600 | E_var:     0.4447 | E_err:   0.010420
[2025-10-23 16:27:25] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -67.359990 | E_var:     0.3683 | E_err:   0.009482
[2025-10-23 16:27:39] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -67.334384 | E_var:     0.5253 | E_err:   0.011324
[2025-10-23 16:27:52] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -67.363988 | E_var:     0.3256 | E_err:   0.008916
[2025-10-23 16:28:05] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -67.361751 | E_var:     0.3309 | E_err:   0.008988
[2025-10-23 16:28:18] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -67.361801 | E_var:     0.3648 | E_err:   0.009438
[2025-10-23 16:28:31] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -67.361362 | E_var:     0.3149 | E_err:   0.008769
[2025-10-23 16:28:45] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -67.354677 | E_var:     0.3631 | E_err:   0.009415
[2025-10-23 16:28:58] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -67.363083 | E_var:     0.3075 | E_err:   0.008664
[2025-10-23 16:29:11] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -67.362077 | E_var:     0.3449 | E_err:   0.009176
[2025-10-23 16:29:24] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -67.360887 | E_var:     0.5749 | E_err:   0.011847
[2025-10-23 16:29:37] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -67.360406 | E_var:     0.4217 | E_err:   0.010146
[2025-10-23 16:29:50] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -67.349575 | E_var:     0.3961 | E_err:   0.009834
[2025-10-23 16:30:04] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -67.342988 | E_var:     0.3922 | E_err:   0.009785
[2025-10-23 16:30:17] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -67.371692 | E_var:     0.4330 | E_err:   0.010282
[2025-10-23 16:30:30] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -67.350696 | E_var:     0.3754 | E_err:   0.009574
[2025-10-23 16:30:43] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -67.361561 | E_var:     0.3040 | E_err:   0.008615
[2025-10-23 16:30:56] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -67.363189 | E_var:     0.3332 | E_err:   0.009019
[2025-10-23 16:31:10] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -67.358687 | E_var:     0.4165 | E_err:   0.010084
[2025-10-23 16:31:10] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-23 16:31:10] ======================================================================================================
[2025-10-23 16:31:10] ✅ Training completed successfully
[2025-10-23 16:31:10] Total restarts: 2
[2025-10-23 16:31:14] Final Energy: -67.35868690 ± 0.01008438
[2025-10-23 16:31:14] Final Variance: 0.416542
[2025-10-23 16:31:14] ======================================================================================================
[2025-10-23 16:31:14] ======================================================================================================
[2025-10-23 16:31:14] Training completed | Runtime: 13888.6s
