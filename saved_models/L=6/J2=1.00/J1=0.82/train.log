[2025-10-23 08:47:18] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_001050.pkl
[2025-10-23 08:47:18]   - 迭代次数: 1050
[2025-10-23 08:47:18]   - 能量: -65.507439+0.001841j ± 0.009676, Var: 0.383475
[2025-10-23 08:47:18]   - 时间戳: 2025-10-23T08:46:54.019220+08:00
[2025-10-23 08:47:43] ✓ 变分状态参数已从checkpoint恢复
[2025-10-23 08:47:43] ======================================================================================================
[2025-10-23 08:47:43] GCNN for Shastry-Sutherland Model
[2025-10-23 08:47:43] ======================================================================================================
[2025-10-23 08:47:43] System parameters:
[2025-10-23 08:47:43]   - System size: L=6, N=144
[2025-10-23 08:47:43]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-10-23 08:47:43] ------------------------------------------------------------------------------------------------------
[2025-10-23 08:47:43] Model parameters:
[2025-10-23 08:47:43]   - Number of layers = 4
[2025-10-23 08:47:43]   - Number of features = 4
[2025-10-23 08:47:43]   - Total parameters = 28252
[2025-10-23 08:47:43] ------------------------------------------------------------------------------------------------------
[2025-10-23 08:47:43] Training parameters:
[2025-10-23 08:47:43]   - Total iterations: 1050
[2025-10-23 08:47:43]   - Annealing cycles: 3
[2025-10-23 08:47:43]   - Initial period: 150
[2025-10-23 08:47:43]   - Period multiplier: 2.0
[2025-10-23 08:47:43]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-23 08:47:43]   - Samples: 4096
[2025-10-23 08:47:43]   - Discarded samples: 0
[2025-10-23 08:47:43]   - Chunk size: 4096
[2025-10-23 08:47:43]   - Diagonal shift: 0.15
[2025-10-23 08:47:43]   - Gradient clipping: 1.0
[2025-10-23 08:47:43]   - Checkpoint enabled: interval=105
[2025-10-23 08:47:43]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.82/training/checkpoints
[2025-10-23 08:47:43]   - Resuming from iteration: 1050
[2025-10-23 08:47:43] ------------------------------------------------------------------------------------------------------
[2025-10-23 08:47:43] Device status:
[2025-10-23 08:47:43]   - Devices model: NVIDIA H200 NVL
[2025-10-23 08:47:43]   - Number of devices: 1
[2025-10-23 08:47:43]   - Sharding: True
[2025-10-23 08:47:44] ======================================================================================================
[2025-10-23 08:48:35] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -66.419876 | E_var:     1.3058 | E_err:   0.017855
[2025-10-23 08:49:08] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -66.437801 | E_var:     0.6608 | E_err:   0.012701
[2025-10-23 08:49:22] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -66.416644 | E_var:     0.5943 | E_err:   0.012046
[2025-10-23 08:49:35] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -66.422421 | E_var:     0.5131 | E_err:   0.011193
[2025-10-23 08:49:48] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -66.413183 | E_var:     0.5663 | E_err:   0.011759
[2025-10-23 08:50:01] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -66.419383 | E_var:     0.5147 | E_err:   0.011209
[2025-10-23 08:50:14] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -66.433107 | E_var:     0.4409 | E_err:   0.010375
[2025-10-23 08:50:27] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -66.429224 | E_var:     0.5224 | E_err:   0.011293
[2025-10-23 08:50:40] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -66.427051 | E_var:     0.4503 | E_err:   0.010485
[2025-10-23 08:50:54] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -66.429974 | E_var:     0.5060 | E_err:   0.011115
[2025-10-23 08:51:07] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -66.436147 | E_var:     0.4618 | E_err:   0.010619
[2025-10-23 08:51:20] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -66.422931 | E_var:     0.4247 | E_err:   0.010183
[2025-10-23 08:51:33] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -66.427825 | E_var:     0.4111 | E_err:   0.010019
[2025-10-23 08:51:46] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -66.426872 | E_var:     0.3788 | E_err:   0.009617
[2025-10-23 08:52:00] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -66.432778 | E_var:     0.5359 | E_err:   0.011439
[2025-10-23 08:52:13] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -66.423877 | E_var:     0.3582 | E_err:   0.009351
[2025-10-23 08:52:26] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -66.438197 | E_var:     0.3640 | E_err:   0.009428
[2025-10-23 08:52:39] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -66.435862 | E_var:     0.3556 | E_err:   0.009318
[2025-10-23 08:52:52] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -66.422076 | E_var:     0.4848 | E_err:   0.010880
[2025-10-23 08:53:05] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -66.430634 | E_var:     0.3848 | E_err:   0.009692
[2025-10-23 08:53:19] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -66.438303 | E_var:     0.4184 | E_err:   0.010107
[2025-10-23 08:53:32] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -66.422171 | E_var:     0.4954 | E_err:   0.010997
[2025-10-23 08:53:45] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -66.419606 | E_var:     0.3853 | E_err:   0.009699
[2025-10-23 08:53:58] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -66.435750 | E_var:     0.3911 | E_err:   0.009771
[2025-10-23 08:54:11] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -66.434988 | E_var:     0.4236 | E_err:   0.010170
[2025-10-23 08:54:24] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -66.423399 | E_var:     0.3562 | E_err:   0.009326
[2025-10-23 08:54:38] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -66.428031 | E_var:     0.3742 | E_err:   0.009559
[2025-10-23 08:54:51] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -66.414807 | E_var:     0.3258 | E_err:   0.008918
[2025-10-23 08:55:04] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -66.434591 | E_var:     0.3600 | E_err:   0.009375
[2025-10-23 08:55:17] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -66.442509 | E_var:     0.3620 | E_err:   0.009401
[2025-10-23 08:55:30] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -66.446912 | E_var:     1.2308 | E_err:   0.017335
[2025-10-23 08:55:43] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -66.419883 | E_var:     0.5718 | E_err:   0.011816
[2025-10-23 08:55:57] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -66.410852 | E_var:     0.4545 | E_err:   0.010533
[2025-10-23 08:56:10] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -66.416701 | E_var:     0.3820 | E_err:   0.009657
[2025-10-23 08:56:23] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -66.424575 | E_var:     0.4826 | E_err:   0.010855
[2025-10-23 08:56:36] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -66.431607 | E_var:     0.4065 | E_err:   0.009962
[2025-10-23 08:56:49] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -66.431100 | E_var:     0.3533 | E_err:   0.009288
[2025-10-23 08:57:03] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -66.419148 | E_var:     0.3706 | E_err:   0.009512
[2025-10-23 08:57:16] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -66.422358 | E_var:     0.4188 | E_err:   0.010111
[2025-10-23 08:57:29] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -66.431179 | E_var:     0.3951 | E_err:   0.009821
[2025-10-23 08:57:42] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -66.444606 | E_var:     0.3891 | E_err:   0.009747
[2025-10-23 08:57:55] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -66.423409 | E_var:     0.3487 | E_err:   0.009227
[2025-10-23 08:58:08] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -66.437082 | E_var:     0.3515 | E_err:   0.009264
[2025-10-23 08:58:22] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -66.426468 | E_var:     0.3882 | E_err:   0.009735
[2025-10-23 08:58:35] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -66.424042 | E_var:     0.3916 | E_err:   0.009778
[2025-10-23 08:58:48] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -66.430932 | E_var:     0.4316 | E_err:   0.010265
[2025-10-23 08:59:01] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -66.405791 | E_var:     0.4352 | E_err:   0.010308
[2025-10-23 08:59:14] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -66.421966 | E_var:     0.3904 | E_err:   0.009763
[2025-10-23 08:59:27] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -66.431749 | E_var:     0.4088 | E_err:   0.009990
[2025-10-23 08:59:41] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -66.432124 | E_var:     0.4078 | E_err:   0.009978
[2025-10-23 08:59:54] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -66.440507 | E_var:     0.3741 | E_err:   0.009557
[2025-10-23 09:00:07] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -66.421375 | E_var:     0.4765 | E_err:   0.010786
[2025-10-23 09:00:20] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -66.437331 | E_var:     0.3942 | E_err:   0.009810
[2025-10-23 09:00:33] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -66.437104 | E_var:     0.5485 | E_err:   0.011572
[2025-10-23 09:00:47] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -66.424931 | E_var:     0.4481 | E_err:   0.010459
[2025-10-23 09:01:00] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -66.422796 | E_var:     0.3881 | E_err:   0.009735
[2025-10-23 09:01:13] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -66.421941 | E_var:     0.3885 | E_err:   0.009739
[2025-10-23 09:01:26] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -66.445595 | E_var:     0.3954 | E_err:   0.009825
[2025-10-23 09:01:39] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -66.430776 | E_var:     0.4173 | E_err:   0.010093
[2025-10-23 09:01:52] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -66.419194 | E_var:     0.4596 | E_err:   0.010593
[2025-10-23 09:02:06] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -66.435228 | E_var:     0.4404 | E_err:   0.010369
[2025-10-23 09:02:19] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -66.422236 | E_var:     0.3625 | E_err:   0.009408
[2025-10-23 09:02:32] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -66.433439 | E_var:     0.3528 | E_err:   0.009281
[2025-10-23 09:02:45] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -66.416558 | E_var:     0.3968 | E_err:   0.009843
[2025-10-23 09:02:58] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -66.430263 | E_var:     0.4097 | E_err:   0.010001
[2025-10-23 09:03:11] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -66.440556 | E_var:     0.3465 | E_err:   0.009197
[2025-10-23 09:03:25] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -66.440183 | E_var:     0.3377 | E_err:   0.009080
[2025-10-23 09:03:38] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -66.442469 | E_var:     0.4111 | E_err:   0.010018
[2025-10-23 09:03:51] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -66.441285 | E_var:     0.3703 | E_err:   0.009508
[2025-10-23 09:04:04] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -66.432515 | E_var:     0.4181 | E_err:   0.010103
[2025-10-23 09:04:17] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -66.434720 | E_var:     0.4455 | E_err:   0.010429
[2025-10-23 09:04:30] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -66.413114 | E_var:     0.4481 | E_err:   0.010459
[2025-10-23 09:04:44] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -66.433471 | E_var:     0.3797 | E_err:   0.009628
[2025-10-23 09:04:57] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -66.413309 | E_var:     0.4529 | E_err:   0.010515
[2025-10-23 09:05:10] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -66.422847 | E_var:     0.3784 | E_err:   0.009612
[2025-10-23 09:05:23] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -66.421087 | E_var:     0.3443 | E_err:   0.009168
[2025-10-23 09:05:36] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -66.419530 | E_var:     0.4219 | E_err:   0.010149
[2025-10-23 09:05:49] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -66.436835 | E_var:     0.3585 | E_err:   0.009356
[2025-10-23 09:06:03] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -66.436715 | E_var:     0.3843 | E_err:   0.009687
[2025-10-23 09:06:16] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -66.437722 | E_var:     0.4432 | E_err:   0.010402
[2025-10-23 09:06:29] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -66.433070 | E_var:     0.3569 | E_err:   0.009335
[2025-10-23 09:06:42] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -66.448314 | E_var:     0.3108 | E_err:   0.008711
[2025-10-23 09:06:55] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -66.444120 | E_var:     0.5540 | E_err:   0.011630
[2025-10-23 09:07:09] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -66.412697 | E_var:     0.3921 | E_err:   0.009784
[2025-10-23 09:07:22] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -66.425671 | E_var:     0.5160 | E_err:   0.011224
[2025-10-23 09:07:35] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -66.425600 | E_var:     0.4784 | E_err:   0.010808
[2025-10-23 09:07:48] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -66.440886 | E_var:     0.3537 | E_err:   0.009292
[2025-10-23 09:08:01] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -66.440138 | E_var:     0.3765 | E_err:   0.009587
[2025-10-23 09:08:14] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -66.426761 | E_var:     0.4875 | E_err:   0.010909
[2025-10-23 09:08:28] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -66.421167 | E_var:     0.3437 | E_err:   0.009161
[2025-10-23 09:08:41] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -66.438086 | E_var:     0.4225 | E_err:   0.010157
[2025-10-23 09:08:54] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -66.436393 | E_var:     0.3940 | E_err:   0.009807
[2025-10-23 09:09:07] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -66.436235 | E_var:     0.3527 | E_err:   0.009280
[2025-10-23 09:09:20] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -66.421539 | E_var:     0.4115 | E_err:   0.010023
[2025-10-23 09:09:33] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -66.420991 | E_var:     0.3412 | E_err:   0.009127
[2025-10-23 09:09:47] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -66.419393 | E_var:     0.3994 | E_err:   0.009875
[2025-10-23 09:10:00] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -66.404240 | E_var:     0.4103 | E_err:   0.010009
[2025-10-23 09:10:13] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -66.440899 | E_var:     0.3695 | E_err:   0.009498
[2025-10-23 09:10:26] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -66.422744 | E_var:     0.4476 | E_err:   0.010454
[2025-10-23 09:10:39] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -66.431404 | E_var:     0.3535 | E_err:   0.009290
[2025-10-23 09:10:52] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -66.429697 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 09:11:06] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -66.414921 | E_var:     0.4721 | E_err:   0.010736
[2025-10-23 09:11:19] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -66.414891 | E_var:     0.4588 | E_err:   0.010584
[2025-10-23 09:11:32] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -66.425809 | E_var:     0.3697 | E_err:   0.009501
[2025-10-23 09:11:45] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -66.412637 | E_var:     0.5116 | E_err:   0.011176
[2025-10-23 09:11:45] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-10-23 09:11:58] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -66.434862 | E_var:     0.4071 | E_err:   0.009969
[2025-10-23 09:12:12] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -66.431233 | E_var:     0.3699 | E_err:   0.009503
[2025-10-23 09:12:25] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -66.429098 | E_var:     0.3848 | E_err:   0.009693
[2025-10-23 09:12:38] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -66.442193 | E_var:     0.4222 | E_err:   0.010153
[2025-10-23 09:12:51] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -66.432719 | E_var:     0.3893 | E_err:   0.009749
[2025-10-23 09:13:04] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -66.427071 | E_var:     0.4181 | E_err:   0.010103
[2025-10-23 09:13:17] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -66.429179 | E_var:     0.4345 | E_err:   0.010300
[2025-10-23 09:13:31] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -66.416296 | E_var:     0.3967 | E_err:   0.009841
[2025-10-23 09:13:44] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -66.426523 | E_var:     0.7733 | E_err:   0.013740
[2025-10-23 09:13:57] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -66.426421 | E_var:     0.4121 | E_err:   0.010031
[2025-10-23 09:14:10] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -66.442176 | E_var:     0.3645 | E_err:   0.009433
[2025-10-23 09:14:23] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -66.422459 | E_var:     0.4418 | E_err:   0.010385
[2025-10-23 09:14:36] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -66.432018 | E_var:     0.3960 | E_err:   0.009833
[2025-10-23 09:14:50] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -66.431713 | E_var:     0.3602 | E_err:   0.009377
[2025-10-23 09:15:03] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -66.431247 | E_var:     0.3817 | E_err:   0.009654
[2025-10-23 09:15:16] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -66.423901 | E_var:     0.3949 | E_err:   0.009819
[2025-10-23 09:15:29] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -66.437061 | E_var:     0.3842 | E_err:   0.009685
[2025-10-23 09:15:42] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -66.443478 | E_var:     0.3829 | E_err:   0.009669
[2025-10-23 09:15:55] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -66.426590 | E_var:     0.4246 | E_err:   0.010182
[2025-10-23 09:16:09] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -66.413445 | E_var:     0.4156 | E_err:   0.010073
[2025-10-23 09:16:22] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -66.439971 | E_var:     0.3966 | E_err:   0.009840
[2025-10-23 09:16:35] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -66.436781 | E_var:     0.3704 | E_err:   0.009509
[2025-10-23 09:16:48] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -66.427469 | E_var:     0.3719 | E_err:   0.009529
[2025-10-23 09:17:01] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -66.428651 | E_var:     0.6143 | E_err:   0.012246
[2025-10-23 09:17:15] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -66.430926 | E_var:     0.3815 | E_err:   0.009651
[2025-10-23 09:17:28] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -66.433398 | E_var:     0.3247 | E_err:   0.008904
[2025-10-23 09:17:41] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -66.430433 | E_var:     0.3817 | E_err:   0.009653
[2025-10-23 09:17:54] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -66.440058 | E_var:     0.4074 | E_err:   0.009973
[2025-10-23 09:18:07] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -66.431463 | E_var:     0.3927 | E_err:   0.009791
[2025-10-23 09:18:20] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -66.421655 | E_var:     0.4577 | E_err:   0.010571
[2025-10-23 09:18:34] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -66.435081 | E_var:     0.3688 | E_err:   0.009489
[2025-10-23 09:18:47] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -66.433081 | E_var:     0.3262 | E_err:   0.008924
[2025-10-23 09:19:00] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -66.425854 | E_var:     0.3750 | E_err:   0.009569
[2025-10-23 09:19:13] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -66.424839 | E_var:     0.4482 | E_err:   0.010461
[2025-10-23 09:19:26] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -66.441027 | E_var:     0.3240 | E_err:   0.008893
[2025-10-23 09:19:39] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -66.419384 | E_var:     0.3793 | E_err:   0.009623
[2025-10-23 09:19:53] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -66.436975 | E_var:     0.4025 | E_err:   0.009913
[2025-10-23 09:20:06] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -66.425151 | E_var:     0.4185 | E_err:   0.010108
[2025-10-23 09:20:19] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -66.424727 | E_var:     0.4061 | E_err:   0.009958
[2025-10-23 09:20:32] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -66.437246 | E_var:     0.5079 | E_err:   0.011136
[2025-10-23 09:20:45] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -66.420550 | E_var:     0.3510 | E_err:   0.009256
[2025-10-23 09:20:58] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -66.427778 | E_var:     0.4285 | E_err:   0.010229
[2025-10-23 09:21:12] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -66.432726 | E_var:     0.3450 | E_err:   0.009178
[2025-10-23 09:21:25] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -66.420113 | E_var:     0.3954 | E_err:   0.009826
[2025-10-23 09:21:38] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -66.427958 | E_var:     0.3518 | E_err:   0.009267
[2025-10-23 09:21:38] 🔄 RESTART #1 | Period: 300
[2025-10-23 09:21:51] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -66.427754 | E_var:     0.3742 | E_err:   0.009558
[2025-10-23 09:22:04] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -66.421939 | E_var:     0.4319 | E_err:   0.010269
[2025-10-23 09:22:18] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -66.441424 | E_var:     0.7024 | E_err:   0.013095
[2025-10-23 09:22:31] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -66.422664 | E_var:     0.3644 | E_err:   0.009432
[2025-10-23 09:22:44] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -66.417612 | E_var:     0.3891 | E_err:   0.009747
[2025-10-23 09:22:57] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -66.411588 | E_var:     0.3811 | E_err:   0.009646
[2025-10-23 09:23:10] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -66.430741 | E_var:     0.4174 | E_err:   0.010095
[2025-10-23 09:23:23] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -66.432360 | E_var:     0.3749 | E_err:   0.009566
[2025-10-23 09:23:37] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -66.434214 | E_var:     0.3444 | E_err:   0.009170
[2025-10-23 09:23:50] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -66.410416 | E_var:     0.4914 | E_err:   0.010953
[2025-10-23 09:24:03] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -66.428678 | E_var:     0.4779 | E_err:   0.010802
[2025-10-23 09:24:16] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -66.437914 | E_var:     0.3533 | E_err:   0.009287
[2025-10-23 09:24:29] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -66.422765 | E_var:     0.3632 | E_err:   0.009416
[2025-10-23 09:24:42] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -66.433718 | E_var:     0.3509 | E_err:   0.009256
[2025-10-23 09:24:56] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -66.434022 | E_var:     0.3735 | E_err:   0.009549
[2025-10-23 09:25:09] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -66.424428 | E_var:     0.4769 | E_err:   0.010790
[2025-10-23 09:25:22] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -66.425067 | E_var:     0.4200 | E_err:   0.010127
[2025-10-23 09:25:35] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -66.448331 | E_var:     0.3906 | E_err:   0.009766
[2025-10-23 09:25:48] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -66.419430 | E_var:     0.5584 | E_err:   0.011676
[2025-10-23 09:26:02] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -66.436667 | E_var:     0.5034 | E_err:   0.011086
[2025-10-23 09:26:15] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -66.407390 | E_var:     0.5529 | E_err:   0.011618
[2025-10-23 09:26:28] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -66.421585 | E_var:     0.3634 | E_err:   0.009419
[2025-10-23 09:26:41] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -66.430577 | E_var:     0.3568 | E_err:   0.009333
[2025-10-23 09:26:54] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -66.408348 | E_var:     0.5029 | E_err:   0.011081
[2025-10-23 09:27:07] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -66.428345 | E_var:     0.3389 | E_err:   0.009096
[2025-10-23 09:27:21] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -66.435172 | E_var:     0.3705 | E_err:   0.009510
[2025-10-23 09:27:34] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -66.441163 | E_var:     0.3548 | E_err:   0.009307
[2025-10-23 09:27:47] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -66.434101 | E_var:     0.4007 | E_err:   0.009891
[2025-10-23 09:28:00] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -66.423174 | E_var:     0.3515 | E_err:   0.009264
[2025-10-23 09:28:13] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -66.426426 | E_var:     0.4528 | E_err:   0.010514
[2025-10-23 09:28:26] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -66.408984 | E_var:     0.4156 | E_err:   0.010072
[2025-10-23 09:28:40] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -66.425624 | E_var:     0.4000 | E_err:   0.009882
[2025-10-23 09:28:53] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -66.424481 | E_var:     0.3780 | E_err:   0.009607
[2025-10-23 09:29:06] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -66.438440 | E_var:     0.4138 | E_err:   0.010051
[2025-10-23 09:29:19] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -66.420622 | E_var:     0.4351 | E_err:   0.010307
[2025-10-23 09:29:32] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -66.429835 | E_var:     0.4188 | E_err:   0.010111
[2025-10-23 09:29:45] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -66.428086 | E_var:     0.4631 | E_err:   0.010634
[2025-10-23 09:29:59] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -66.431979 | E_var:     0.4437 | E_err:   0.010408
[2025-10-23 09:30:12] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -66.429578 | E_var:     0.3459 | E_err:   0.009189
[2025-10-23 09:30:25] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -66.427351 | E_var:     0.4193 | E_err:   0.010118
[2025-10-23 09:30:38] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -66.435602 | E_var:     0.4330 | E_err:   0.010281
[2025-10-23 09:30:51] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -66.431052 | E_var:     0.3695 | E_err:   0.009498
[2025-10-23 09:31:04] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -66.423821 | E_var:     0.5384 | E_err:   0.011464
[2025-10-23 09:31:18] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -66.450262 | E_var:     0.4149 | E_err:   0.010064
[2025-10-23 09:31:31] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -66.454491 | E_var:     0.4159 | E_err:   0.010077
[2025-10-23 09:31:44] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -66.422540 | E_var:     0.5958 | E_err:   0.012060
[2025-10-23 09:31:57] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -66.431896 | E_var:     0.3648 | E_err:   0.009438
[2025-10-23 09:32:10] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -66.424707 | E_var:     0.4079 | E_err:   0.009980
[2025-10-23 09:32:24] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -66.421144 | E_var:     0.5909 | E_err:   0.012011
[2025-10-23 09:32:37] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -66.434940 | E_var:     0.3299 | E_err:   0.008974
[2025-10-23 09:32:50] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -66.409647 | E_var:     0.4267 | E_err:   0.010206
[2025-10-23 09:33:03] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -66.422964 | E_var:     0.5974 | E_err:   0.012077
[2025-10-23 09:33:16] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -66.416679 | E_var:     0.3384 | E_err:   0.009089
[2025-10-23 09:33:29] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -66.429640 | E_var:     0.3049 | E_err:   0.008628
[2025-10-23 09:33:43] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -66.424660 | E_var:     0.4564 | E_err:   0.010556
[2025-10-23 09:33:56] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -66.445784 | E_var:     0.3716 | E_err:   0.009525
[2025-10-23 09:34:09] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -66.426361 | E_var:     0.3158 | E_err:   0.008780
[2025-10-23 09:34:22] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -66.432987 | E_var:     0.3496 | E_err:   0.009239
[2025-10-23 09:34:35] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -66.425942 | E_var:     0.3867 | E_err:   0.009716
[2025-10-23 09:34:48] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -66.437075 | E_var:     0.2995 | E_err:   0.008551
[2025-10-23 09:34:48] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-10-23 09:35:02] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -66.445940 | E_var:     0.4237 | E_err:   0.010171
[2025-10-23 09:35:15] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -66.426166 | E_var:     0.3519 | E_err:   0.009268
[2025-10-23 09:35:28] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -66.443463 | E_var:     0.3838 | E_err:   0.009679
[2025-10-23 09:35:41] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -66.413467 | E_var:     0.3643 | E_err:   0.009430
[2025-10-23 09:35:54] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -66.440776 | E_var:     0.9341 | E_err:   0.015101
[2025-10-23 09:36:07] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -66.416848 | E_var:     0.3441 | E_err:   0.009165
[2025-10-23 09:36:21] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -66.412640 | E_var:     0.3645 | E_err:   0.009433
[2025-10-23 09:36:34] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -66.433258 | E_var:     0.4139 | E_err:   0.010052
[2025-10-23 09:36:47] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -66.413514 | E_var:     0.4015 | E_err:   0.009901
[2025-10-23 09:37:00] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -66.433890 | E_var:     0.3868 | E_err:   0.009718
[2025-10-23 09:37:13] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -66.442267 | E_var:     0.3943 | E_err:   0.009811
[2025-10-23 09:37:27] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -66.443962 | E_var:     0.3862 | E_err:   0.009710
[2025-10-23 09:37:40] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -66.414570 | E_var:     0.4270 | E_err:   0.010211
[2025-10-23 09:37:53] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -66.431045 | E_var:     0.3519 | E_err:   0.009269
[2025-10-23 09:38:06] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -66.440440 | E_var:     0.4974 | E_err:   0.011020
[2025-10-23 09:38:19] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -66.423064 | E_var:     0.3864 | E_err:   0.009713
[2025-10-23 09:38:32] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -66.449838 | E_var:     0.4195 | E_err:   0.010120
[2025-10-23 09:38:45] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -66.428963 | E_var:     0.3404 | E_err:   0.009116
[2025-10-23 09:38:59] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -66.416221 | E_var:     0.4072 | E_err:   0.009971
[2025-10-23 09:39:12] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -66.418597 | E_var:     0.3824 | E_err:   0.009662
[2025-10-23 09:39:25] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -66.416206 | E_var:     0.3661 | E_err:   0.009455
[2025-10-23 09:39:38] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -66.431710 | E_var:     0.3319 | E_err:   0.009002
[2025-10-23 09:39:51] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -66.437640 | E_var:     0.3321 | E_err:   0.009004
[2025-10-23 09:40:04] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -66.428496 | E_var:     0.4030 | E_err:   0.009919
[2025-10-23 09:40:18] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -66.416483 | E_var:     0.5514 | E_err:   0.011602
[2025-10-23 09:40:31] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -66.415685 | E_var:     0.4668 | E_err:   0.010675
[2025-10-23 09:40:44] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -66.425164 | E_var:     0.3941 | E_err:   0.009809
[2025-10-23 09:40:57] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -66.409879 | E_var:     0.4043 | E_err:   0.009935
[2025-10-23 09:41:10] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -66.440922 | E_var:     0.3775 | E_err:   0.009601
[2025-10-23 09:41:23] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -66.418714 | E_var:     0.4203 | E_err:   0.010130
[2025-10-23 09:41:37] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -66.432558 | E_var:     0.3381 | E_err:   0.009085
[2025-10-23 09:41:50] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -66.444313 | E_var:     0.3573 | E_err:   0.009340
[2025-10-23 09:42:03] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -66.424938 | E_var:     0.4760 | E_err:   0.010780
[2025-10-23 09:42:16] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -66.427908 | E_var:     0.4646 | E_err:   0.010650
[2025-10-23 09:42:29] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -66.413605 | E_var:     0.3503 | E_err:   0.009248
[2025-10-23 09:42:42] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -66.437931 | E_var:     0.3418 | E_err:   0.009135
[2025-10-23 09:42:56] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -66.424290 | E_var:     0.3427 | E_err:   0.009147
[2025-10-23 09:43:09] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -66.437819 | E_var:     0.4174 | E_err:   0.010095
[2025-10-23 09:43:22] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -66.422707 | E_var:     0.4893 | E_err:   0.010929
[2025-10-23 09:43:35] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -66.422804 | E_var:     0.3500 | E_err:   0.009244
[2025-10-23 09:43:48] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -66.426947 | E_var:     0.3717 | E_err:   0.009527
[2025-10-23 09:44:01] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -66.426861 | E_var:     0.3193 | E_err:   0.008830
[2025-10-23 09:44:15] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -66.433553 | E_var:     0.4424 | E_err:   0.010393
[2025-10-23 09:44:28] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -66.443509 | E_var:     0.3482 | E_err:   0.009219
[2025-10-23 09:44:41] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -66.437443 | E_var:     0.4924 | E_err:   0.010964
[2025-10-23 09:44:54] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -66.424537 | E_var:     0.4072 | E_err:   0.009970
[2025-10-23 09:45:07] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -66.432830 | E_var:     0.3561 | E_err:   0.009324
[2025-10-23 09:45:20] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -66.424838 | E_var:     0.4040 | E_err:   0.009931
[2025-10-23 09:45:34] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -66.439184 | E_var:     0.4662 | E_err:   0.010669
[2025-10-23 09:45:47] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -66.435825 | E_var:     0.3230 | E_err:   0.008880
[2025-10-23 09:46:00] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -66.435140 | E_var:     0.3811 | E_err:   0.009645
[2025-10-23 09:46:13] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -66.420714 | E_var:     0.6321 | E_err:   0.012422
[2025-10-23 09:46:26] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -66.422361 | E_var:     0.3918 | E_err:   0.009780
[2025-10-23 09:46:39] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -66.434571 | E_var:     0.4063 | E_err:   0.009960
[2025-10-23 09:46:53] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -66.415746 | E_var:     0.3364 | E_err:   0.009063
[2025-10-23 09:47:06] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -66.419642 | E_var:     0.4702 | E_err:   0.010715
[2025-10-23 09:47:19] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -66.441185 | E_var:     0.4349 | E_err:   0.010304
[2025-10-23 09:47:32] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -66.422647 | E_var:     0.4469 | E_err:   0.010445
[2025-10-23 09:47:45] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -66.433881 | E_var:     0.3356 | E_err:   0.009051
[2025-10-23 09:47:58] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -66.435597 | E_var:     0.3611 | E_err:   0.009389
[2025-10-23 09:48:12] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -66.443739 | E_var:     0.3976 | E_err:   0.009853
[2025-10-23 09:48:25] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -66.412438 | E_var:     0.4167 | E_err:   0.010087
[2025-10-23 09:48:38] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -66.413826 | E_var:     0.3835 | E_err:   0.009677
[2025-10-23 09:48:51] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -66.445593 | E_var:     0.3349 | E_err:   0.009042
[2025-10-23 09:49:04] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -66.435690 | E_var:     0.4416 | E_err:   0.010384
[2025-10-23 09:49:18] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -66.436719 | E_var:     0.4817 | E_err:   0.010845
[2025-10-23 09:49:31] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -66.431006 | E_var:     0.6099 | E_err:   0.012203
[2025-10-23 09:49:44] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -66.421606 | E_var:     0.3575 | E_err:   0.009342
[2025-10-23 09:49:57] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -66.424415 | E_var:     0.4256 | E_err:   0.010194
[2025-10-23 09:50:10] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -66.418979 | E_var:     0.4001 | E_err:   0.009883
[2025-10-23 09:50:23] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -66.436775 | E_var:     0.3451 | E_err:   0.009179
[2025-10-23 09:50:37] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -66.440020 | E_var:     0.5047 | E_err:   0.011100
[2025-10-23 09:50:50] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -66.445683 | E_var:     0.3513 | E_err:   0.009261
[2025-10-23 09:51:03] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -66.439646 | E_var:     0.3707 | E_err:   0.009514
[2025-10-23 09:51:16] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -66.436197 | E_var:     0.3406 | E_err:   0.009119
[2025-10-23 09:51:29] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -66.411774 | E_var:     0.4412 | E_err:   0.010379
[2025-10-23 09:51:42] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -66.423151 | E_var:     0.4210 | E_err:   0.010138
[2025-10-23 09:51:56] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -66.428183 | E_var:     0.3850 | E_err:   0.009694
[2025-10-23 09:52:09] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -66.429065 | E_var:     0.4012 | E_err:   0.009897
[2025-10-23 09:52:22] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -66.435657 | E_var:     0.4329 | E_err:   0.010280
[2025-10-23 09:52:35] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -66.423411 | E_var:     0.4404 | E_err:   0.010369
[2025-10-23 09:52:48] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -66.445165 | E_var:     0.6221 | E_err:   0.012324
[2025-10-23 09:53:01] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -66.427668 | E_var:     0.3956 | E_err:   0.009827
[2025-10-23 09:53:15] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -66.443599 | E_var:     0.4598 | E_err:   0.010595
[2025-10-23 09:53:28] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -66.409955 | E_var:     0.4259 | E_err:   0.010197
[2025-10-23 09:53:41] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -66.449306 | E_var:     0.3376 | E_err:   0.009079
[2025-10-23 09:53:54] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -66.441200 | E_var:     0.3510 | E_err:   0.009258
[2025-10-23 09:54:07] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -66.444739 | E_var:     0.3666 | E_err:   0.009461
[2025-10-23 09:54:20] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -66.430703 | E_var:     0.3864 | E_err:   0.009713
[2025-10-23 09:54:34] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -66.439860 | E_var:     0.3764 | E_err:   0.009586
[2025-10-23 09:54:47] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -66.423255 | E_var:     0.3502 | E_err:   0.009247
[2025-10-23 09:55:00] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -66.437217 | E_var:     0.3604 | E_err:   0.009380
[2025-10-23 09:55:13] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -66.409028 | E_var:     0.3959 | E_err:   0.009832
[2025-10-23 09:55:26] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -66.409689 | E_var:     0.4410 | E_err:   0.010377
[2025-10-23 09:55:39] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -66.421386 | E_var:     0.3447 | E_err:   0.009173
[2025-10-23 09:55:53] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -66.413741 | E_var:     0.4462 | E_err:   0.010437
[2025-10-23 09:56:06] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -66.424272 | E_var:     0.3366 | E_err:   0.009066
[2025-10-23 09:56:19] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -66.420519 | E_var:     0.4064 | E_err:   0.009961
[2025-10-23 09:56:32] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -66.423409 | E_var:     0.3646 | E_err:   0.009435
[2025-10-23 09:56:45] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -66.427234 | E_var:     0.4041 | E_err:   0.009932
[2025-10-23 09:56:59] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -66.421484 | E_var:     0.4003 | E_err:   0.009886
[2025-10-23 09:57:12] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -66.434220 | E_var:     0.4258 | E_err:   0.010196
[2025-10-23 09:57:25] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -66.430450 | E_var:     0.3483 | E_err:   0.009221
[2025-10-23 09:57:38] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -66.415928 | E_var:     0.4622 | E_err:   0.010622
[2025-10-23 09:57:51] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -66.433350 | E_var:     0.3326 | E_err:   0.009012
[2025-10-23 09:57:51] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-10-23 09:58:04] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -66.430626 | E_var:     0.5335 | E_err:   0.011413
[2025-10-23 09:58:18] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -66.422806 | E_var:     0.3463 | E_err:   0.009195
[2025-10-23 09:58:31] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -66.432794 | E_var:     0.3294 | E_err:   0.008968
[2025-10-23 09:58:44] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -66.432749 | E_var:     0.3633 | E_err:   0.009417
[2025-10-23 09:58:57] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -66.424194 | E_var:     0.4188 | E_err:   0.010112
[2025-10-23 09:59:10] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -66.425242 | E_var:     0.4852 | E_err:   0.010883
[2025-10-23 09:59:23] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -66.426360 | E_var:     0.3945 | E_err:   0.009814
[2025-10-23 09:59:36] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -66.413932 | E_var:     0.3520 | E_err:   0.009270
[2025-10-23 09:59:49] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -66.417886 | E_var:     0.4076 | E_err:   0.009976
[2025-10-23 10:00:03] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -66.418339 | E_var:     0.4273 | E_err:   0.010214
[2025-10-23 10:00:16] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -66.422499 | E_var:     0.3119 | E_err:   0.008726
[2025-10-23 10:00:29] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -66.433579 | E_var:     0.3088 | E_err:   0.008683
[2025-10-23 10:00:42] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -66.435943 | E_var:     0.3198 | E_err:   0.008836
[2025-10-23 10:00:55] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -66.425058 | E_var:     0.3300 | E_err:   0.008977
[2025-10-23 10:01:08] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -66.429026 | E_var:     0.3853 | E_err:   0.009699
[2025-10-23 10:01:21] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -66.408208 | E_var:     0.4711 | E_err:   0.010725
[2025-10-23 10:01:35] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -66.436197 | E_var:     0.4187 | E_err:   0.010110
[2025-10-23 10:01:48] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -66.412768 | E_var:     0.4008 | E_err:   0.009892
[2025-10-23 10:02:01] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -66.435174 | E_var:     0.4116 | E_err:   0.010025
[2025-10-23 10:02:14] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -66.414869 | E_var:     0.3870 | E_err:   0.009721
[2025-10-23 10:02:27] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -66.422092 | E_var:     0.4881 | E_err:   0.010916
[2025-10-23 10:02:40] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -66.429667 | E_var:     0.3936 | E_err:   0.009802
[2025-10-23 10:02:53] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -66.437811 | E_var:     0.4593 | E_err:   0.010589
[2025-10-23 10:03:07] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -66.428877 | E_var:     0.3622 | E_err:   0.009404
[2025-10-23 10:03:20] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -66.437307 | E_var:     0.6335 | E_err:   0.012436
[2025-10-23 10:03:33] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -66.418868 | E_var:     0.3565 | E_err:   0.009330
[2025-10-23 10:03:46] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -66.426545 | E_var:     0.4511 | E_err:   0.010495
[2025-10-23 10:03:59] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -66.428893 | E_var:     0.3882 | E_err:   0.009735
[2025-10-23 10:04:12] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -66.414732 | E_var:     0.4391 | E_err:   0.010354
[2025-10-23 10:04:25] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -66.421411 | E_var:     0.3657 | E_err:   0.009448
[2025-10-23 10:04:39] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -66.419641 | E_var:     0.4451 | E_err:   0.010425
[2025-10-23 10:04:52] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -66.428812 | E_var:     0.3633 | E_err:   0.009418
[2025-10-23 10:05:05] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -66.436034 | E_var:     0.4721 | E_err:   0.010736
[2025-10-23 10:05:18] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -66.424842 | E_var:     0.3778 | E_err:   0.009603
[2025-10-23 10:05:31] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -66.438065 | E_var:     0.4168 | E_err:   0.010087
[2025-10-23 10:05:44] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -66.430233 | E_var:     0.3387 | E_err:   0.009093
[2025-10-23 10:05:58] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -66.406512 | E_var:     0.4119 | E_err:   0.010028
[2025-10-23 10:06:11] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -66.433239 | E_var:     0.3579 | E_err:   0.009347
[2025-10-23 10:06:24] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -66.444292 | E_var:     0.3785 | E_err:   0.009613
[2025-10-23 10:06:37] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -66.417594 | E_var:     0.4751 | E_err:   0.010769
[2025-10-23 10:06:50] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -66.445958 | E_var:     0.3724 | E_err:   0.009535
[2025-10-23 10:07:03] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -66.425572 | E_var:     0.3463 | E_err:   0.009195
[2025-10-23 10:07:16] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -66.427429 | E_var:     0.3334 | E_err:   0.009022
[2025-10-23 10:07:30] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -66.421018 | E_var:     0.3724 | E_err:   0.009535
[2025-10-23 10:07:43] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -66.429711 | E_var:     0.3545 | E_err:   0.009303
[2025-10-23 10:07:56] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -66.442303 | E_var:     0.3498 | E_err:   0.009242
[2025-10-23 10:08:09] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -66.416476 | E_var:     0.4977 | E_err:   0.011023
[2025-10-23 10:08:22] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -66.428141 | E_var:     0.3782 | E_err:   0.009610
[2025-10-23 10:08:35] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -66.412408 | E_var:     0.3260 | E_err:   0.008922
[2025-10-23 10:08:48] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -66.426230 | E_var:     0.3299 | E_err:   0.008975
[2025-10-23 10:09:02] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -66.434631 | E_var:     0.5681 | E_err:   0.011777
[2025-10-23 10:09:15] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -66.430152 | E_var:     0.3886 | E_err:   0.009740
[2025-10-23 10:09:28] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -66.421773 | E_var:     0.4122 | E_err:   0.010031
[2025-10-23 10:09:41] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -66.432323 | E_var:     0.5157 | E_err:   0.011221
[2025-10-23 10:09:54] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -66.431579 | E_var:     0.7556 | E_err:   0.013582
[2025-10-23 10:10:07] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -66.423404 | E_var:     0.5375 | E_err:   0.011456
[2025-10-23 10:10:20] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -66.435948 | E_var:     0.3321 | E_err:   0.009004
[2025-10-23 10:10:34] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -66.439679 | E_var:     0.3621 | E_err:   0.009403
[2025-10-23 10:10:47] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -66.432265 | E_var:     0.3954 | E_err:   0.009826
[2025-10-23 10:11:00] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -66.432534 | E_var:     0.3624 | E_err:   0.009406
[2025-10-23 10:11:13] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -66.435980 | E_var:     0.3346 | E_err:   0.009038
[2025-10-23 10:11:26] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -66.415614 | E_var:     0.4738 | E_err:   0.010755
[2025-10-23 10:11:39] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -66.415184 | E_var:     0.3702 | E_err:   0.009507
[2025-10-23 10:11:52] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -66.420189 | E_var:     0.4289 | E_err:   0.010233
[2025-10-23 10:12:06] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -66.419795 | E_var:     0.4211 | E_err:   0.010139
[2025-10-23 10:12:19] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -66.425045 | E_var:     0.3921 | E_err:   0.009784
[2025-10-23 10:12:32] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -66.428019 | E_var:     0.4966 | E_err:   0.011011
[2025-10-23 10:12:45] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -66.435742 | E_var:     0.3844 | E_err:   0.009687
[2025-10-23 10:12:58] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -66.425479 | E_var:     0.3597 | E_err:   0.009371
[2025-10-23 10:13:11] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -66.423815 | E_var:     0.3901 | E_err:   0.009759
[2025-10-23 10:13:24] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -66.423830 | E_var:     0.3651 | E_err:   0.009441
[2025-10-23 10:13:38] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -66.433951 | E_var:     0.3978 | E_err:   0.009855
[2025-10-23 10:13:51] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -66.416247 | E_var:     0.3976 | E_err:   0.009853
[2025-10-23 10:14:04] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -66.423111 | E_var:     0.7002 | E_err:   0.013074
[2025-10-23 10:14:17] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -66.429804 | E_var:     0.3911 | E_err:   0.009771
[2025-10-23 10:14:30] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -66.436540 | E_var:     0.4311 | E_err:   0.010259
[2025-10-23 10:14:43] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -66.432901 | E_var:     0.3369 | E_err:   0.009070
[2025-10-23 10:14:56] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -66.422951 | E_var:     0.3777 | E_err:   0.009603
[2025-10-23 10:15:09] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -66.452791 | E_var:     0.3493 | E_err:   0.009235
[2025-10-23 10:15:23] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -66.423718 | E_var:     0.3520 | E_err:   0.009271
[2025-10-23 10:15:36] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -66.421394 | E_var:     0.3731 | E_err:   0.009544
[2025-10-23 10:15:49] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -66.449426 | E_var:     0.5842 | E_err:   0.011943
[2025-10-23 10:16:02] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -66.427605 | E_var:     0.3813 | E_err:   0.009649
[2025-10-23 10:16:15] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -66.400833 | E_var:     0.3756 | E_err:   0.009576
[2025-10-23 10:16:28] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -66.423228 | E_var:     0.4762 | E_err:   0.010783
[2025-10-23 10:16:41] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -66.401565 | E_var:     1.2829 | E_err:   0.017697
[2025-10-23 10:16:55] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -66.419412 | E_var:     0.3804 | E_err:   0.009637
[2025-10-23 10:17:08] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -66.420250 | E_var:     0.3872 | E_err:   0.009723
[2025-10-23 10:17:21] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -66.426343 | E_var:     0.4825 | E_err:   0.010853
[2025-10-23 10:17:34] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -66.415251 | E_var:     0.4114 | E_err:   0.010022
[2025-10-23 10:17:47] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -66.442983 | E_var:     0.4306 | E_err:   0.010253
[2025-10-23 10:18:00] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -66.433554 | E_var:     0.3343 | E_err:   0.009034
[2025-10-23 10:18:13] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -66.420135 | E_var:     0.3615 | E_err:   0.009395
[2025-10-23 10:18:27] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -66.429257 | E_var:     0.3834 | E_err:   0.009675
[2025-10-23 10:18:40] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -66.432691 | E_var:     0.3093 | E_err:   0.008689
[2025-10-23 10:18:53] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -66.433175 | E_var:     0.5938 | E_err:   0.012040
[2025-10-23 10:19:06] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -66.430678 | E_var:     0.3396 | E_err:   0.009105
[2025-10-23 10:19:19] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -66.459061 | E_var:     0.3573 | E_err:   0.009340
[2025-10-23 10:19:32] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -66.438852 | E_var:     0.3245 | E_err:   0.008901
[2025-10-23 10:19:45] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -66.448741 | E_var:     0.3498 | E_err:   0.009241
[2025-10-23 10:19:59] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -66.419415 | E_var:     0.4450 | E_err:   0.010423
[2025-10-23 10:20:12] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -66.434867 | E_var:     0.5312 | E_err:   0.011388
[2025-10-23 10:20:25] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -66.417725 | E_var:     0.3367 | E_err:   0.009067
[2025-10-23 10:20:38] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -66.429677 | E_var:     0.6780 | E_err:   0.012866
[2025-10-23 10:20:51] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -66.431980 | E_var:     0.4248 | E_err:   0.010183
[2025-10-23 10:20:51] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-10-23 10:21:04] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -66.439001 | E_var:     0.3664 | E_err:   0.009457
[2025-10-23 10:21:18] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -66.410874 | E_var:     0.3355 | E_err:   0.009050
[2025-10-23 10:21:31] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -66.444609 | E_var:     0.3672 | E_err:   0.009469
[2025-10-23 10:21:44] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -66.424687 | E_var:     0.4158 | E_err:   0.010075
[2025-10-23 10:21:57] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -66.419789 | E_var:     0.4390 | E_err:   0.010353
[2025-10-23 10:22:10] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -66.438482 | E_var:     0.6088 | E_err:   0.012191
[2025-10-23 10:22:23] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -66.427496 | E_var:     0.3569 | E_err:   0.009334
[2025-10-23 10:22:36] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -66.415438 | E_var:     0.3986 | E_err:   0.009865
[2025-10-23 10:22:50] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -66.449996 | E_var:     0.6314 | E_err:   0.012415
[2025-10-23 10:23:03] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -66.429304 | E_var:     0.3963 | E_err:   0.009837
[2025-10-23 10:23:16] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -66.427576 | E_var:     0.3511 | E_err:   0.009258
[2025-10-23 10:23:29] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -66.431613 | E_var:     0.4237 | E_err:   0.010171
[2025-10-23 10:23:42] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -66.442548 | E_var:     0.4567 | E_err:   0.010559
[2025-10-23 10:23:55] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -66.439240 | E_var:     0.4319 | E_err:   0.010268
[2025-10-23 10:24:09] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -66.436625 | E_var:     0.4306 | E_err:   0.010253
[2025-10-23 10:24:22] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -66.441612 | E_var:     0.3660 | E_err:   0.009453
[2025-10-23 10:24:35] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -66.427750 | E_var:     0.3614 | E_err:   0.009393
[2025-10-23 10:24:48] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -66.435279 | E_var:     0.3893 | E_err:   0.009749
[2025-10-23 10:25:01] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -66.428636 | E_var:     0.4011 | E_err:   0.009896
[2025-10-23 10:25:14] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -66.437707 | E_var:     0.3207 | E_err:   0.008848
[2025-10-23 10:25:27] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -66.445164 | E_var:     0.3627 | E_err:   0.009410
[2025-10-23 10:25:40] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -66.424710 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 10:25:54] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -66.433262 | E_var:     0.3762 | E_err:   0.009584
[2025-10-23 10:26:07] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -66.424772 | E_var:     0.3925 | E_err:   0.009789
[2025-10-23 10:26:20] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -66.426275 | E_var:     0.4370 | E_err:   0.010329
[2025-10-23 10:26:33] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -66.430129 | E_var:     0.4273 | E_err:   0.010214
[2025-10-23 10:26:46] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -66.435434 | E_var:     0.3782 | E_err:   0.009609
[2025-10-23 10:26:59] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -66.428311 | E_var:     0.3334 | E_err:   0.009022
[2025-10-23 10:27:12] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -66.429907 | E_var:     0.5166 | E_err:   0.011231
[2025-10-23 10:27:26] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -66.430737 | E_var:     0.3561 | E_err:   0.009323
[2025-10-23 10:27:26] 🔄 RESTART #2 | Period: 600
[2025-10-23 10:27:39] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -66.413145 | E_var:     0.3585 | E_err:   0.009355
[2025-10-23 10:27:52] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -66.412758 | E_var:     0.4358 | E_err:   0.010315
[2025-10-23 10:28:05] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -66.417035 | E_var:     0.4667 | E_err:   0.010674
[2025-10-23 10:28:18] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -66.411167 | E_var:     0.3791 | E_err:   0.009620
[2025-10-23 10:28:31] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -66.430729 | E_var:     0.3968 | E_err:   0.009843
[2025-10-23 10:28:44] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -66.419729 | E_var:     0.4168 | E_err:   0.010088
[2025-10-23 10:28:58] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -66.417602 | E_var:     0.3710 | E_err:   0.009518
[2025-10-23 10:29:11] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -66.415792 | E_var:     0.4162 | E_err:   0.010081
[2025-10-23 10:29:24] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -66.441325 | E_var:     0.3658 | E_err:   0.009451
[2025-10-23 10:29:37] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -66.425405 | E_var:     0.3676 | E_err:   0.009473
[2025-10-23 10:29:50] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -66.436496 | E_var:     0.3269 | E_err:   0.008934
[2025-10-23 10:30:03] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -66.429983 | E_var:     0.3514 | E_err:   0.009262
[2025-10-23 10:30:16] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -66.434824 | E_var:     0.3245 | E_err:   0.008901
[2025-10-23 10:30:30] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -66.410316 | E_var:     0.5038 | E_err:   0.011091
[2025-10-23 10:30:43] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -66.439716 | E_var:     0.3865 | E_err:   0.009713
[2025-10-23 10:30:56] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -66.414531 | E_var:     0.3657 | E_err:   0.009449
[2025-10-23 10:31:09] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -66.435159 | E_var:     0.3786 | E_err:   0.009614
[2025-10-23 10:31:22] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -66.437072 | E_var:     0.3430 | E_err:   0.009151
[2025-10-23 10:31:35] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -66.432321 | E_var:     0.3782 | E_err:   0.009609
[2025-10-23 10:31:48] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -66.434272 | E_var:     0.3314 | E_err:   0.008996
[2025-10-23 10:32:02] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -66.426932 | E_var:     0.5361 | E_err:   0.011440
[2025-10-23 10:32:15] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -66.450210 | E_var:     0.4023 | E_err:   0.009910
[2025-10-23 10:32:28] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -66.409733 | E_var:     0.4367 | E_err:   0.010325
[2025-10-23 10:32:41] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -66.419455 | E_var:     0.4053 | E_err:   0.009948
[2025-10-23 10:32:54] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -66.430985 | E_var:     0.4899 | E_err:   0.010936
[2025-10-23 10:33:07] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -66.421059 | E_var:     0.4201 | E_err:   0.010127
[2025-10-23 10:33:20] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -66.416116 | E_var:     0.3262 | E_err:   0.008924
[2025-10-23 10:33:33] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -66.426800 | E_var:     0.7415 | E_err:   0.013454
[2025-10-23 10:33:47] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -66.435941 | E_var:     0.4238 | E_err:   0.010172
[2025-10-23 10:34:00] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -66.428068 | E_var:     0.3343 | E_err:   0.009034
[2025-10-23 10:34:13] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -66.438976 | E_var:     0.8744 | E_err:   0.014611
[2025-10-23 10:34:26] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -66.424878 | E_var:     0.4092 | E_err:   0.009995
[2025-10-23 10:34:39] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -66.429397 | E_var:     0.3803 | E_err:   0.009636
[2025-10-23 10:34:52] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -66.425236 | E_var:     0.3685 | E_err:   0.009485
[2025-10-23 10:35:05] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -66.424682 | E_var:     0.3836 | E_err:   0.009678
[2025-10-23 10:35:19] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -66.416785 | E_var:     0.4249 | E_err:   0.010186
[2025-10-23 10:35:32] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -66.427867 | E_var:     0.3655 | E_err:   0.009446
[2025-10-23 10:35:45] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -66.435585 | E_var:     0.4142 | E_err:   0.010056
[2025-10-23 10:35:58] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -66.420574 | E_var:     0.3873 | E_err:   0.009725
[2025-10-23 10:36:11] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -66.432559 | E_var:     0.3299 | E_err:   0.008975
[2025-10-23 10:36:24] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -66.441188 | E_var:     0.3974 | E_err:   0.009850
[2025-10-23 10:36:37] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -66.435008 | E_var:     0.3083 | E_err:   0.008676
[2025-10-23 10:36:51] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -66.420438 | E_var:     0.4170 | E_err:   0.010090
[2025-10-23 10:37:04] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -66.416216 | E_var:     0.3584 | E_err:   0.009354
[2025-10-23 10:37:17] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -66.438389 | E_var:     0.3573 | E_err:   0.009340
[2025-10-23 10:37:30] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -66.420198 | E_var:     0.3677 | E_err:   0.009475
[2025-10-23 10:37:43] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -66.418551 | E_var:     0.4471 | E_err:   0.010448
[2025-10-23 10:37:56] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -66.435510 | E_var:     0.4020 | E_err:   0.009907
[2025-10-23 10:38:09] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -66.448782 | E_var:     0.4016 | E_err:   0.009902
[2025-10-23 10:38:22] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -66.423269 | E_var:     0.3346 | E_err:   0.009038
[2025-10-23 10:38:36] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -66.424718 | E_var:     0.3746 | E_err:   0.009563
[2025-10-23 10:38:49] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -66.443568 | E_var:     0.3590 | E_err:   0.009362
[2025-10-23 10:39:02] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -66.427234 | E_var:     0.3838 | E_err:   0.009680
[2025-10-23 10:39:15] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -66.437086 | E_var:     0.4195 | E_err:   0.010120
[2025-10-23 10:39:28] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -66.443236 | E_var:     0.3781 | E_err:   0.009608
[2025-10-23 10:39:41] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -66.434178 | E_var:     0.3350 | E_err:   0.009044
[2025-10-23 10:39:54] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -66.421374 | E_var:     0.3084 | E_err:   0.008677
[2025-10-23 10:40:08] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -66.431129 | E_var:     0.3698 | E_err:   0.009502
[2025-10-23 10:40:21] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -66.427894 | E_var:     0.4307 | E_err:   0.010254
[2025-10-23 10:40:34] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -66.433059 | E_var:     0.4214 | E_err:   0.010143
[2025-10-23 10:40:47] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -66.427688 | E_var:     0.4256 | E_err:   0.010193
[2025-10-23 10:41:00] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -66.430279 | E_var:     0.4787 | E_err:   0.010810
[2025-10-23 10:41:13] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -66.437942 | E_var:     0.3338 | E_err:   0.009027
[2025-10-23 10:41:27] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -66.427986 | E_var:     0.3925 | E_err:   0.009789
[2025-10-23 10:41:40] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -66.431649 | E_var:     0.5560 | E_err:   0.011651
[2025-10-23 10:41:53] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -66.423941 | E_var:     0.3638 | E_err:   0.009424
[2025-10-23 10:42:06] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -66.420125 | E_var:     0.3298 | E_err:   0.008974
[2025-10-23 10:42:19] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -66.430243 | E_var:     0.3557 | E_err:   0.009319
[2025-10-23 10:42:32] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -66.427467 | E_var:     0.3790 | E_err:   0.009620
[2025-10-23 10:42:45] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -66.427445 | E_var:     0.5672 | E_err:   0.011768
[2025-10-23 10:42:58] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -66.436259 | E_var:     0.3359 | E_err:   0.009056
[2025-10-23 10:43:12] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -66.436118 | E_var:     0.5601 | E_err:   0.011694
[2025-10-23 10:43:25] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -66.442223 | E_var:     0.3466 | E_err:   0.009199
[2025-10-23 10:43:38] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -66.425889 | E_var:     0.3623 | E_err:   0.009405
[2025-10-23 10:43:51] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -66.427083 | E_var:     0.7075 | E_err:   0.013143
[2025-10-23 10:43:51] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-10-23 10:44:04] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -66.443814 | E_var:     0.4277 | E_err:   0.010218
[2025-10-23 10:44:17] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -66.409106 | E_var:     0.4765 | E_err:   0.010786
[2025-10-23 10:44:31] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -66.439376 | E_var:     0.3298 | E_err:   0.008973
[2025-10-23 10:44:44] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -66.435671 | E_var:     0.4173 | E_err:   0.010093
[2025-10-23 10:44:57] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -66.439143 | E_var:     0.3645 | E_err:   0.009434
[2025-10-23 10:45:10] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -66.418922 | E_var:     0.3712 | E_err:   0.009520
[2025-10-23 10:45:23] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -66.428813 | E_var:     0.3775 | E_err:   0.009600
[2025-10-23 10:45:36] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -66.428551 | E_var:     0.4066 | E_err:   0.009964
[2025-10-23 10:45:49] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -66.437196 | E_var:     0.3504 | E_err:   0.009250
[2025-10-23 10:46:03] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -66.442647 | E_var:     0.3680 | E_err:   0.009478
[2025-10-23 10:46:16] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -66.413115 | E_var:     0.3877 | E_err:   0.009729
[2025-10-23 10:46:29] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -66.417154 | E_var:     0.3659 | E_err:   0.009452
[2025-10-23 10:46:42] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -66.439094 | E_var:     0.3478 | E_err:   0.009215
[2025-10-23 10:46:55] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -66.448756 | E_var:     0.6050 | E_err:   0.012153
[2025-10-23 10:47:08] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -66.425143 | E_var:     0.3515 | E_err:   0.009264
[2025-10-23 10:47:21] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -66.433153 | E_var:     0.4066 | E_err:   0.009963
[2025-10-23 10:47:35] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -66.430954 | E_var:     0.3439 | E_err:   0.009163
[2025-10-23 10:47:48] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -66.419464 | E_var:     0.4365 | E_err:   0.010323
[2025-10-23 10:48:01] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -66.441370 | E_var:     0.3325 | E_err:   0.009010
[2025-10-23 10:48:14] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -66.416493 | E_var:     0.4784 | E_err:   0.010807
[2025-10-23 10:48:27] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -66.429679 | E_var:     0.4262 | E_err:   0.010201
[2025-10-23 10:48:40] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -66.440650 | E_var:     0.3904 | E_err:   0.009763
[2025-10-23 10:48:53] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -66.413536 | E_var:     0.6038 | E_err:   0.012142
[2025-10-23 10:49:07] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -66.427272 | E_var:     0.4110 | E_err:   0.010017
[2025-10-23 10:49:20] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -66.441835 | E_var:     0.4082 | E_err:   0.009983
[2025-10-23 10:49:33] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -66.443618 | E_var:     0.3731 | E_err:   0.009544
[2025-10-23 10:49:46] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -66.443003 | E_var:     0.3575 | E_err:   0.009342
[2025-10-23 10:49:59] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -66.436350 | E_var:     0.4550 | E_err:   0.010540
[2025-10-23 10:50:12] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -66.423626 | E_var:     0.4801 | E_err:   0.010826
[2025-10-23 10:50:25] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -66.425628 | E_var:     0.3510 | E_err:   0.009257
[2025-10-23 10:50:39] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -66.435173 | E_var:     0.3804 | E_err:   0.009636
[2025-10-23 10:50:52] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -66.419572 | E_var:     0.3169 | E_err:   0.008796
[2025-10-23 10:51:05] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -66.409571 | E_var:     0.3790 | E_err:   0.009619
[2025-10-23 10:51:18] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -66.426855 | E_var:     0.4236 | E_err:   0.010170
[2025-10-23 10:51:31] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -66.423076 | E_var:     0.3678 | E_err:   0.009476
[2025-10-23 10:51:44] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -66.413452 | E_var:     0.3335 | E_err:   0.009024
[2025-10-23 10:51:57] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -66.423009 | E_var:     0.3745 | E_err:   0.009562
[2025-10-23 10:52:11] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -66.407305 | E_var:     0.4058 | E_err:   0.009954
[2025-10-23 10:52:24] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -66.434617 | E_var:     0.3209 | E_err:   0.008852
[2025-10-23 10:52:37] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -66.433663 | E_var:     0.3413 | E_err:   0.009128
[2025-10-23 10:52:50] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -66.428905 | E_var:     0.3531 | E_err:   0.009285
[2025-10-23 10:53:03] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -66.426648 | E_var:     0.3437 | E_err:   0.009160
[2025-10-23 10:53:16] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -66.440672 | E_var:     0.3659 | E_err:   0.009452
[2025-10-23 10:53:29] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -66.419592 | E_var:     0.3648 | E_err:   0.009438
[2025-10-23 10:53:43] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -66.439358 | E_var:     0.4776 | E_err:   0.010798
[2025-10-23 10:53:56] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -66.450762 | E_var:     0.4626 | E_err:   0.010628
[2025-10-23 10:54:09] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -66.427793 | E_var:     0.3241 | E_err:   0.008896
[2025-10-23 10:54:22] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -66.435040 | E_var:     0.3826 | E_err:   0.009664
[2025-10-23 10:54:35] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -66.409655 | E_var:     0.3503 | E_err:   0.009248
[2025-10-23 10:54:48] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -66.433131 | E_var:     0.3888 | E_err:   0.009743
[2025-10-23 10:55:01] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -66.429983 | E_var:     0.4482 | E_err:   0.010461
[2025-10-23 10:55:15] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -66.434201 | E_var:     0.3523 | E_err:   0.009275
[2025-10-23 10:55:28] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -66.411351 | E_var:     0.4137 | E_err:   0.010050
[2025-10-23 10:55:41] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -66.423016 | E_var:     0.3918 | E_err:   0.009780
[2025-10-23 10:55:54] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -66.437270 | E_var:     0.4163 | E_err:   0.010081
[2025-10-23 10:56:07] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -66.421703 | E_var:     0.3854 | E_err:   0.009700
[2025-10-23 10:56:20] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -66.427551 | E_var:     0.3560 | E_err:   0.009322
[2025-10-23 10:56:33] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -66.430678 | E_var:     0.4865 | E_err:   0.010898
[2025-10-23 10:56:47] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -66.424449 | E_var:     0.3098 | E_err:   0.008697
[2025-10-23 10:57:00] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -66.418871 | E_var:     0.3289 | E_err:   0.008961
[2025-10-23 10:57:13] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -66.415231 | E_var:     0.5812 | E_err:   0.011912
[2025-10-23 10:57:26] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -66.423945 | E_var:     0.3950 | E_err:   0.009821
[2025-10-23 10:57:39] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -66.434489 | E_var:     0.4049 | E_err:   0.009942
[2025-10-23 10:57:52] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -66.428196 | E_var:     0.4017 | E_err:   0.009903
[2025-10-23 10:58:05] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -66.434468 | E_var:     0.3739 | E_err:   0.009554
[2025-10-23 10:58:19] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -66.426804 | E_var:     0.3745 | E_err:   0.009562
[2025-10-23 10:58:32] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -66.431082 | E_var:     0.4097 | E_err:   0.010001
[2025-10-23 10:58:45] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -66.429425 | E_var:     0.3387 | E_err:   0.009093
[2025-10-23 10:58:58] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -66.431537 | E_var:     0.3647 | E_err:   0.009436
[2025-10-23 10:59:11] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -66.447242 | E_var:     0.4220 | E_err:   0.010151
[2025-10-23 10:59:24] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -66.439467 | E_var:     0.3517 | E_err:   0.009266
[2025-10-23 10:59:37] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -66.419467 | E_var:     0.4120 | E_err:   0.010029
[2025-10-23 10:59:51] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -66.442108 | E_var:     0.3662 | E_err:   0.009455
[2025-10-23 11:00:04] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -66.426913 | E_var:     0.3621 | E_err:   0.009403
[2025-10-23 11:00:17] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -66.441013 | E_var:     0.3724 | E_err:   0.009535
[2025-10-23 11:00:30] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -66.431223 | E_var:     0.3796 | E_err:   0.009627
[2025-10-23 11:00:43] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -66.431178 | E_var:     0.3707 | E_err:   0.009514
[2025-10-23 11:00:56] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -66.410677 | E_var:     0.5198 | E_err:   0.011266
[2025-10-23 11:01:09] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -66.431612 | E_var:     0.4246 | E_err:   0.010181
[2025-10-23 11:01:23] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -66.430355 | E_var:     0.3465 | E_err:   0.009197
[2025-10-23 11:01:36] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -66.416981 | E_var:     0.3849 | E_err:   0.009694
[2025-10-23 11:01:49] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -66.434122 | E_var:     0.4705 | E_err:   0.010718
[2025-10-23 11:02:02] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -66.434756 | E_var:     0.4216 | E_err:   0.010145
[2025-10-23 11:02:15] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -66.431898 | E_var:     0.3399 | E_err:   0.009110
[2025-10-23 11:02:28] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -66.417701 | E_var:     0.3460 | E_err:   0.009191
[2025-10-23 11:02:41] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -66.430929 | E_var:     0.3541 | E_err:   0.009297
[2025-10-23 11:02:54] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -66.437812 | E_var:     0.4301 | E_err:   0.010247
[2025-10-23 11:03:08] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -66.423431 | E_var:     0.4031 | E_err:   0.009920
[2025-10-23 11:03:21] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -66.439800 | E_var:     0.4218 | E_err:   0.010148
[2025-10-23 11:03:34] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -66.437103 | E_var:     0.4012 | E_err:   0.009897
[2025-10-23 11:03:47] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -66.433315 | E_var:     0.5518 | E_err:   0.011607
[2025-10-23 11:04:00] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -66.436296 | E_var:     0.4272 | E_err:   0.010212
[2025-10-23 11:04:13] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -66.428564 | E_var:     0.3489 | E_err:   0.009230
[2025-10-23 11:04:26] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -66.434925 | E_var:     0.3697 | E_err:   0.009500
[2025-10-23 11:04:40] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -66.414929 | E_var:     0.4912 | E_err:   0.010951
[2025-10-23 11:04:53] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -66.422890 | E_var:     0.3546 | E_err:   0.009305
[2025-10-23 11:05:06] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -66.423379 | E_var:     0.4079 | E_err:   0.009979
[2025-10-23 11:05:19] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -66.428088 | E_var:     0.3852 | E_err:   0.009698
[2025-10-23 11:05:32] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -66.427831 | E_var:     0.3979 | E_err:   0.009856
[2025-10-23 11:05:45] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -66.419995 | E_var:     0.4149 | E_err:   0.010065
[2025-10-23 11:05:59] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -66.433995 | E_var:     0.3447 | E_err:   0.009174
[2025-10-23 11:06:12] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -66.428360 | E_var:     0.4790 | E_err:   0.010814
[2025-10-23 11:06:25] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -66.443113 | E_var:     0.3912 | E_err:   0.009773
[2025-10-23 11:06:38] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -66.438956 | E_var:     0.3530 | E_err:   0.009284
[2025-10-23 11:06:51] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -66.441435 | E_var:     0.4401 | E_err:   0.010366
[2025-10-23 11:06:51] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-10-23 11:07:04] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -66.443140 | E_var:     0.4087 | E_err:   0.009989
[2025-10-23 11:07:17] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -66.438437 | E_var:     0.3568 | E_err:   0.009333
[2025-10-23 11:07:31] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -66.406038 | E_var:     1.5328 | E_err:   0.019344
[2025-10-23 11:07:44] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -66.433423 | E_var:     0.4464 | E_err:   0.010440
[2025-10-23 11:07:57] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -66.444988 | E_var:     0.3749 | E_err:   0.009567
[2025-10-23 11:08:10] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -66.408529 | E_var:     0.3631 | E_err:   0.009415
[2025-10-23 11:08:23] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -66.450788 | E_var:     0.4126 | E_err:   0.010037
[2025-10-23 11:08:36] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -66.428624 | E_var:     0.4412 | E_err:   0.010378
[2025-10-23 11:08:49] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -66.418771 | E_var:     0.4143 | E_err:   0.010057
[2025-10-23 11:09:03] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -66.412857 | E_var:     0.3678 | E_err:   0.009476
[2025-10-23 11:09:16] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -66.438036 | E_var:     0.3764 | E_err:   0.009586
[2025-10-23 11:09:29] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -66.419535 | E_var:     0.3387 | E_err:   0.009093
[2025-10-23 11:09:42] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -66.416808 | E_var:     0.4626 | E_err:   0.010627
[2025-10-23 11:09:55] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -66.426668 | E_var:     0.4639 | E_err:   0.010642
[2025-10-23 11:10:08] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -66.436449 | E_var:     0.3633 | E_err:   0.009418
[2025-10-23 11:10:21] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -66.432182 | E_var:     0.4389 | E_err:   0.010352
[2025-10-23 11:10:35] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -66.413619 | E_var:     0.3665 | E_err:   0.009460
[2025-10-23 11:10:48] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -66.422965 | E_var:     0.3388 | E_err:   0.009095
[2025-10-23 11:11:01] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -66.441182 | E_var:     0.3436 | E_err:   0.009159
[2025-10-23 11:11:14] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -66.434865 | E_var:     0.4027 | E_err:   0.009915
[2025-10-23 11:11:27] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -66.421299 | E_var:     0.5243 | E_err:   0.011314
[2025-10-23 11:11:40] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -66.424304 | E_var:     0.3482 | E_err:   0.009220
[2025-10-23 11:11:53] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -66.438287 | E_var:     0.3472 | E_err:   0.009207
[2025-10-23 11:12:07] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -66.426666 | E_var:     0.4236 | E_err:   0.010170
[2025-10-23 11:12:20] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -66.434080 | E_var:     0.3773 | E_err:   0.009598
[2025-10-23 11:12:33] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -66.428002 | E_var:     0.6073 | E_err:   0.012176
[2025-10-23 11:12:46] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -66.420272 | E_var:     0.3490 | E_err:   0.009230
[2025-10-23 11:12:59] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -66.427027 | E_var:     0.3284 | E_err:   0.008954
[2025-10-23 11:13:12] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -66.418566 | E_var:     0.4360 | E_err:   0.010318
[2025-10-23 11:13:25] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -66.438474 | E_var:     0.3478 | E_err:   0.009214
[2025-10-23 11:13:39] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -66.445699 | E_var:     0.3594 | E_err:   0.009368
[2025-10-23 11:13:52] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -66.427685 | E_var:     0.3869 | E_err:   0.009719
[2025-10-23 11:14:05] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -66.424848 | E_var:     0.3296 | E_err:   0.008970
[2025-10-23 11:14:18] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -66.438656 | E_var:     0.4408 | E_err:   0.010374
[2025-10-23 11:14:31] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -66.424107 | E_var:     0.3585 | E_err:   0.009356
[2025-10-23 11:14:44] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -66.430855 | E_var:     0.3732 | E_err:   0.009546
[2025-10-23 11:14:57] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -66.409614 | E_var:     0.4296 | E_err:   0.010241
[2025-10-23 11:15:11] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -66.414561 | E_var:     0.3913 | E_err:   0.009774
[2025-10-23 11:15:24] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -66.426304 | E_var:     0.4551 | E_err:   0.010541
[2025-10-23 11:15:37] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -66.447545 | E_var:     0.3731 | E_err:   0.009544
[2025-10-23 11:15:50] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -66.437122 | E_var:     0.3674 | E_err:   0.009471
[2025-10-23 11:16:03] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -66.444016 | E_var:     0.3204 | E_err:   0.008844
[2025-10-23 11:16:16] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -66.431998 | E_var:     0.3806 | E_err:   0.009640
[2025-10-23 11:16:29] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -66.450087 | E_var:     0.3621 | E_err:   0.009402
[2025-10-23 11:16:43] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -66.425099 | E_var:     0.3754 | E_err:   0.009573
[2025-10-23 11:16:56] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -66.430178 | E_var:     0.3050 | E_err:   0.008630
[2025-10-23 11:17:09] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -66.432210 | E_var:     0.4607 | E_err:   0.010606
[2025-10-23 11:17:22] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -66.430010 | E_var:     0.3444 | E_err:   0.009170
[2025-10-23 11:17:35] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -66.434743 | E_var:     0.3221 | E_err:   0.008868
[2025-10-23 11:17:48] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -66.430123 | E_var:     0.3760 | E_err:   0.009581
[2025-10-23 11:18:01] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -66.439395 | E_var:     0.3982 | E_err:   0.009860
[2025-10-23 11:18:15] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -66.441689 | E_var:     0.4025 | E_err:   0.009913
[2025-10-23 11:18:28] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -66.445076 | E_var:     0.4192 | E_err:   0.010116
[2025-10-23 11:18:41] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -66.429022 | E_var:     0.3233 | E_err:   0.008884
[2025-10-23 11:18:54] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -66.430806 | E_var:     0.4662 | E_err:   0.010668
[2025-10-23 11:19:07] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -66.419588 | E_var:     0.3819 | E_err:   0.009656
[2025-10-23 11:19:20] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -66.438706 | E_var:     0.3554 | E_err:   0.009314
[2025-10-23 11:19:33] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -66.431908 | E_var:     0.5608 | E_err:   0.011701
[2025-10-23 11:19:47] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -66.427687 | E_var:     0.3567 | E_err:   0.009332
[2025-10-23 11:20:00] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -66.440210 | E_var:     0.3584 | E_err:   0.009354
[2025-10-23 11:20:13] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -66.414051 | E_var:     0.3785 | E_err:   0.009613
[2025-10-23 11:20:26] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -66.440663 | E_var:     0.3659 | E_err:   0.009452
[2025-10-23 11:20:39] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -66.423789 | E_var:     0.4214 | E_err:   0.010143
[2025-10-23 11:20:52] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -66.440758 | E_var:     0.3630 | E_err:   0.009414
[2025-10-23 11:21:06] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -66.429953 | E_var:     0.3482 | E_err:   0.009220
[2025-10-23 11:21:19] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -66.421915 | E_var:     0.3919 | E_err:   0.009782
[2025-10-23 11:21:32] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -66.430638 | E_var:     0.3504 | E_err:   0.009250
[2025-10-23 11:21:45] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -66.436326 | E_var:     0.5703 | E_err:   0.011800
[2025-10-23 11:21:58] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -66.444301 | E_var:     0.3375 | E_err:   0.009077
[2025-10-23 11:22:11] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -66.439937 | E_var:     0.4088 | E_err:   0.009990
[2025-10-23 11:22:25] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -66.426391 | E_var:     0.3783 | E_err:   0.009610
[2025-10-23 11:22:38] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -66.435060 | E_var:     0.5126 | E_err:   0.011187
[2025-10-23 11:22:51] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -66.422518 | E_var:     0.3653 | E_err:   0.009444
[2025-10-23 11:23:04] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -66.430842 | E_var:     0.4122 | E_err:   0.010032
[2025-10-23 11:23:17] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -66.410813 | E_var:     0.4627 | E_err:   0.010628
[2025-10-23 11:23:31] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -66.427035 | E_var:     0.4564 | E_err:   0.010555
[2025-10-23 11:23:44] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -66.423635 | E_var:     0.3354 | E_err:   0.009049
[2025-10-23 11:23:57] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -66.430176 | E_var:     0.4665 | E_err:   0.010672
[2025-10-23 11:24:10] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -66.436029 | E_var:     0.3927 | E_err:   0.009791
[2025-10-23 11:24:23] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -66.423256 | E_var:     0.3327 | E_err:   0.009013
[2025-10-23 11:24:36] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -66.435709 | E_var:     0.3977 | E_err:   0.009854
[2025-10-23 11:24:50] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -66.411510 | E_var:     0.4477 | E_err:   0.010455
[2025-10-23 11:25:03] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -66.428741 | E_var:     0.3660 | E_err:   0.009453
[2025-10-23 11:25:16] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -66.441889 | E_var:     0.3968 | E_err:   0.009842
[2025-10-23 11:25:29] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -66.449779 | E_var:     0.4008 | E_err:   0.009892
[2025-10-23 11:25:42] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -66.424851 | E_var:     0.3908 | E_err:   0.009767
[2025-10-23 11:25:55] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -66.431091 | E_var:     0.4379 | E_err:   0.010340
[2025-10-23 11:26:09] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -66.434697 | E_var:     0.3805 | E_err:   0.009638
[2025-10-23 11:26:22] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -66.435326 | E_var:     0.4299 | E_err:   0.010245
[2025-10-23 11:26:35] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -66.424644 | E_var:     0.4217 | E_err:   0.010146
[2025-10-23 11:26:48] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -66.436078 | E_var:     0.3477 | E_err:   0.009213
[2025-10-23 11:27:01] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -66.430836 | E_var:     0.3505 | E_err:   0.009250
[2025-10-23 11:27:14] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -66.427786 | E_var:     0.3834 | E_err:   0.009675
[2025-10-23 11:27:28] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -66.429067 | E_var:     0.3658 | E_err:   0.009451
[2025-10-23 11:27:41] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -66.424197 | E_var:     0.3924 | E_err:   0.009787
[2025-10-23 11:27:54] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -66.436247 | E_var:     0.3547 | E_err:   0.009305
[2025-10-23 11:28:07] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -66.418584 | E_var:     0.4004 | E_err:   0.009887
[2025-10-23 11:28:20] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -66.444232 | E_var:     0.3894 | E_err:   0.009750
[2025-10-23 11:28:33] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -66.450669 | E_var:     0.3229 | E_err:   0.008879
[2025-10-23 11:28:46] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -66.442189 | E_var:     0.3686 | E_err:   0.009486
[2025-10-23 11:29:00] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -66.424456 | E_var:     0.3850 | E_err:   0.009695
[2025-10-23 11:29:13] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -66.442568 | E_var:     0.3680 | E_err:   0.009479
[2025-10-23 11:29:26] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -66.442469 | E_var:     0.3690 | E_err:   0.009491
[2025-10-23 11:29:39] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -66.433955 | E_var:     0.3550 | E_err:   0.009309
[2025-10-23 11:29:52] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -66.425376 | E_var:     0.3282 | E_err:   0.008951
[2025-10-23 11:29:52] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-10-23 11:30:06] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -66.435842 | E_var:     0.4938 | E_err:   0.010979
[2025-10-23 11:30:19] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -66.421722 | E_var:     0.3338 | E_err:   0.009028
[2025-10-23 11:30:32] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -66.425032 | E_var:     0.3930 | E_err:   0.009796
[2025-10-23 11:30:45] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -66.425251 | E_var:     0.4029 | E_err:   0.009918
[2025-10-23 11:30:58] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -66.418560 | E_var:     0.4186 | E_err:   0.010109
[2025-10-23 11:31:11] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -66.410678 | E_var:     0.8451 | E_err:   0.014364
[2025-10-23 11:31:24] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -66.432619 | E_var:     0.4224 | E_err:   0.010155
[2025-10-23 11:31:38] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -66.416030 | E_var:     0.3456 | E_err:   0.009185
[2025-10-23 11:31:51] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -66.424419 | E_var:     0.3555 | E_err:   0.009317
[2025-10-23 11:32:04] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -66.418574 | E_var:     0.5892 | E_err:   0.011994
[2025-10-23 11:32:17] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -66.435852 | E_var:     0.3450 | E_err:   0.009178
[2025-10-23 11:32:30] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -66.445795 | E_var:     0.3878 | E_err:   0.009730
[2025-10-23 11:32:43] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -66.419073 | E_var:     0.3503 | E_err:   0.009248
[2025-10-23 11:32:57] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -66.430208 | E_var:     0.3580 | E_err:   0.009349
[2025-10-23 11:33:10] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -66.415846 | E_var:     0.4051 | E_err:   0.009945
[2025-10-23 11:33:23] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -66.420405 | E_var:     0.3678 | E_err:   0.009476
[2025-10-23 11:33:36] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -66.438628 | E_var:     0.3455 | E_err:   0.009184
[2025-10-23 11:33:49] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -66.426342 | E_var:     0.3983 | E_err:   0.009862
[2025-10-23 11:34:02] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -66.422041 | E_var:     0.4102 | E_err:   0.010007
[2025-10-23 11:34:16] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -66.437168 | E_var:     0.4109 | E_err:   0.010016
[2025-10-23 11:34:29] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -66.423237 | E_var:     0.3696 | E_err:   0.009499
[2025-10-23 11:34:42] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -66.438571 | E_var:     0.4171 | E_err:   0.010091
[2025-10-23 11:34:55] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -66.433620 | E_var:     0.3753 | E_err:   0.009572
[2025-10-23 11:35:08] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -66.446272 | E_var:     0.3266 | E_err:   0.008929
[2025-10-23 11:35:21] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -66.439139 | E_var:     0.4024 | E_err:   0.009912
[2025-10-23 11:35:35] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -66.428734 | E_var:     0.3298 | E_err:   0.008973
[2025-10-23 11:35:48] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -66.432788 | E_var:     0.5228 | E_err:   0.011298
[2025-10-23 11:36:01] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -66.440023 | E_var:     0.3926 | E_err:   0.009790
[2025-10-23 11:36:14] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -66.439160 | E_var:     0.4370 | E_err:   0.010329
[2025-10-23 11:36:27] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -66.437031 | E_var:     0.3890 | E_err:   0.009745
[2025-10-23 11:36:40] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -66.419064 | E_var:     0.3934 | E_err:   0.009800
[2025-10-23 11:36:54] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -66.427994 | E_var:     0.3825 | E_err:   0.009664
[2025-10-23 11:37:07] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -66.431558 | E_var:     0.3077 | E_err:   0.008667
[2025-10-23 11:37:20] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -66.418527 | E_var:     0.4127 | E_err:   0.010038
[2025-10-23 11:37:33] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -66.423071 | E_var:     0.4298 | E_err:   0.010243
[2025-10-23 11:37:47] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -66.420440 | E_var:     0.3915 | E_err:   0.009776
[2025-10-23 11:38:00] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -66.435031 | E_var:     0.5374 | E_err:   0.011454
[2025-10-23 11:38:13] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -66.431725 | E_var:     0.3577 | E_err:   0.009345
[2025-10-23 11:38:26] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -66.430173 | E_var:     0.3816 | E_err:   0.009652
[2025-10-23 11:38:39] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -66.416963 | E_var:     0.4040 | E_err:   0.009932
[2025-10-23 11:38:52] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -66.440482 | E_var:     0.3890 | E_err:   0.009746
[2025-10-23 11:39:05] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -66.434700 | E_var:     0.4211 | E_err:   0.010139
[2025-10-23 11:39:19] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -66.431659 | E_var:     0.3524 | E_err:   0.009275
[2025-10-23 11:39:32] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -66.420549 | E_var:     0.3604 | E_err:   0.009380
[2025-10-23 11:39:45] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -66.418961 | E_var:     0.5222 | E_err:   0.011291
[2025-10-23 11:39:58] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -66.423235 | E_var:     0.3896 | E_err:   0.009753
[2025-10-23 11:40:11] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -66.429159 | E_var:     0.3622 | E_err:   0.009404
[2025-10-23 11:40:24] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -66.422805 | E_var:     0.3426 | E_err:   0.009146
[2025-10-23 11:40:38] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -66.429765 | E_var:     0.4673 | E_err:   0.010681
[2025-10-23 11:40:51] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -66.419086 | E_var:     0.3753 | E_err:   0.009573
[2025-10-23 11:41:04] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -66.404864 | E_var:     0.4143 | E_err:   0.010057
[2025-10-23 11:41:17] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -66.440513 | E_var:     0.3443 | E_err:   0.009168
[2025-10-23 11:41:30] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -66.432851 | E_var:     0.3986 | E_err:   0.009865
[2025-10-23 11:41:43] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -66.444571 | E_var:     0.4507 | E_err:   0.010490
[2025-10-23 11:41:56] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -66.435725 | E_var:     0.3710 | E_err:   0.009518
[2025-10-23 11:42:10] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -66.421498 | E_var:     0.3692 | E_err:   0.009494
[2025-10-23 11:42:23] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -66.427246 | E_var:     0.3716 | E_err:   0.009524
[2025-10-23 11:42:36] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -66.434747 | E_var:     0.3823 | E_err:   0.009661
[2025-10-23 11:42:49] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -66.432473 | E_var:     0.3678 | E_err:   0.009475
[2025-10-23 11:43:02] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -66.421853 | E_var:     0.4040 | E_err:   0.009931
[2025-10-23 11:43:15] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -66.437245 | E_var:     0.3996 | E_err:   0.009877
[2025-10-23 11:43:29] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -66.435219 | E_var:     0.3253 | E_err:   0.008912
[2025-10-23 11:43:42] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -66.414886 | E_var:     0.5263 | E_err:   0.011336
[2025-10-23 11:43:55] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -66.429131 | E_var:     0.4467 | E_err:   0.010443
[2025-10-23 11:44:08] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -66.423257 | E_var:     0.3214 | E_err:   0.008858
[2025-10-23 11:44:21] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -66.431665 | E_var:     0.3435 | E_err:   0.009158
[2025-10-23 11:44:34] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -66.427914 | E_var:     0.4130 | E_err:   0.010041
[2025-10-23 11:44:48] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -66.431547 | E_var:     0.3671 | E_err:   0.009467
[2025-10-23 11:45:01] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -66.444456 | E_var:     0.4158 | E_err:   0.010076
[2025-10-23 11:45:14] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -66.418982 | E_var:     0.4132 | E_err:   0.010044
[2025-10-23 11:45:27] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -66.443281 | E_var:     0.3494 | E_err:   0.009236
[2025-10-23 11:45:40] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -66.431480 | E_var:     0.4232 | E_err:   0.010165
[2025-10-23 11:45:53] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -66.416363 | E_var:     0.3904 | E_err:   0.009763
[2025-10-23 11:46:06] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -66.434328 | E_var:     0.3412 | E_err:   0.009127
[2025-10-23 11:46:20] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -66.428593 | E_var:     0.4088 | E_err:   0.009990
[2025-10-23 11:46:33] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -66.434151 | E_var:     0.4109 | E_err:   0.010016
[2025-10-23 11:46:46] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -66.404100 | E_var:     0.3559 | E_err:   0.009321
[2025-10-23 11:46:59] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -66.430410 | E_var:     0.5220 | E_err:   0.011289
[2025-10-23 11:47:12] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -66.436553 | E_var:     0.3357 | E_err:   0.009053
[2025-10-23 11:47:25] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -66.428227 | E_var:     0.3316 | E_err:   0.008997
[2025-10-23 11:47:39] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -66.450206 | E_var:     0.3713 | E_err:   0.009521
[2025-10-23 11:47:52] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -66.421087 | E_var:     0.3787 | E_err:   0.009616
[2025-10-23 11:48:05] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -66.436744 | E_var:     0.3884 | E_err:   0.009737
[2025-10-23 11:48:18] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -66.422743 | E_var:     0.3200 | E_err:   0.008839
[2025-10-23 11:48:31] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -66.428781 | E_var:     0.5469 | E_err:   0.011555
[2025-10-23 11:48:44] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -66.427239 | E_var:     0.3555 | E_err:   0.009316
[2025-10-23 11:48:58] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -66.414098 | E_var:     0.3489 | E_err:   0.009230
[2025-10-23 11:49:11] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -66.442019 | E_var:     0.3509 | E_err:   0.009256
[2025-10-23 11:49:24] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -66.424735 | E_var:     0.3969 | E_err:   0.009844
[2025-10-23 11:49:37] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -66.430005 | E_var:     0.5080 | E_err:   0.011136
[2025-10-23 11:49:50] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -66.421818 | E_var:     0.3383 | E_err:   0.009088
[2025-10-23 11:50:03] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -66.424233 | E_var:     0.3687 | E_err:   0.009488
[2025-10-23 11:50:16] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -66.431534 | E_var:     0.3891 | E_err:   0.009746
[2025-10-23 11:50:30] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -66.421489 | E_var:     0.4629 | E_err:   0.010631
[2025-10-23 11:50:43] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -66.418540 | E_var:     0.4644 | E_err:   0.010648
[2025-10-23 11:50:56] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -66.432375 | E_var:     0.3662 | E_err:   0.009455
[2025-10-23 11:51:09] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -66.445818 | E_var:     0.4494 | E_err:   0.010474
[2025-10-23 11:51:22] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -66.430453 | E_var:     0.5367 | E_err:   0.011446
[2025-10-23 11:51:35] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -66.441394 | E_var:     0.4126 | E_err:   0.010036
[2025-10-23 11:51:49] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -66.423314 | E_var:     0.3308 | E_err:   0.008987
[2025-10-23 11:52:02] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -66.438433 | E_var:     0.4079 | E_err:   0.009979
[2025-10-23 11:52:15] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -66.440489 | E_var:     0.4205 | E_err:   0.010132
[2025-10-23 11:52:28] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -66.418784 | E_var:     0.5034 | E_err:   0.011086
[2025-10-23 11:52:41] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -66.423868 | E_var:     0.3398 | E_err:   0.009108
[2025-10-23 11:52:54] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -66.430389 | E_var:     0.3061 | E_err:   0.008644
[2025-10-23 11:52:54] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-10-23 11:53:08] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -66.432148 | E_var:     0.4743 | E_err:   0.010761
[2025-10-23 11:53:21] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -66.443775 | E_var:     0.3566 | E_err:   0.009331
[2025-10-23 11:53:34] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -66.422361 | E_var:     0.3666 | E_err:   0.009461
[2025-10-23 11:53:47] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -66.433920 | E_var:     0.4792 | E_err:   0.010817
[2025-10-23 11:54:00] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -66.432645 | E_var:     0.3943 | E_err:   0.009812
[2025-10-23 11:54:13] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -66.449392 | E_var:     0.3462 | E_err:   0.009193
[2025-10-23 11:54:27] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -66.428732 | E_var:     0.3396 | E_err:   0.009105
[2025-10-23 11:54:40] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -66.417992 | E_var:     0.3716 | E_err:   0.009525
[2025-10-23 11:54:53] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -66.430115 | E_var:     0.3572 | E_err:   0.009339
[2025-10-23 11:55:06] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -66.437914 | E_var:     0.6828 | E_err:   0.012911
[2025-10-23 11:55:19] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -66.415854 | E_var:     0.3803 | E_err:   0.009636
[2025-10-23 11:55:32] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -66.443900 | E_var:     0.3449 | E_err:   0.009177
[2025-10-23 11:55:45] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -66.435029 | E_var:     0.3299 | E_err:   0.008975
[2025-10-23 11:55:59] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -66.426836 | E_var:     0.3951 | E_err:   0.009821
[2025-10-23 11:56:12] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -66.414371 | E_var:     2.3252 | E_err:   0.023826
[2025-10-23 11:56:25] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -66.425955 | E_var:     0.4032 | E_err:   0.009921
[2025-10-23 11:56:38] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -66.427694 | E_var:     0.3818 | E_err:   0.009654
[2025-10-23 11:56:51] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -66.434050 | E_var:     0.3732 | E_err:   0.009545
[2025-10-23 11:57:04] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -66.406168 | E_var:     0.4798 | E_err:   0.010824
[2025-10-23 11:57:18] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -66.429729 | E_var:     0.3307 | E_err:   0.008985
[2025-10-23 11:57:31] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -66.450206 | E_var:     0.3312 | E_err:   0.008992
[2025-10-23 11:57:44] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -66.416475 | E_var:     0.3648 | E_err:   0.009437
[2025-10-23 11:57:57] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -66.424087 | E_var:     0.3625 | E_err:   0.009408
[2025-10-23 11:58:10] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -66.433109 | E_var:     0.4196 | E_err:   0.010121
[2025-10-23 11:58:23] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -66.433566 | E_var:     0.3177 | E_err:   0.008807
[2025-10-23 11:58:37] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -66.423531 | E_var:     0.3575 | E_err:   0.009343
[2025-10-23 11:58:50] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -66.417945 | E_var:     0.4683 | E_err:   0.010692
[2025-10-23 11:59:03] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -66.410451 | E_var:     0.4194 | E_err:   0.010119
[2025-10-23 11:59:16] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -66.448108 | E_var:     0.4201 | E_err:   0.010127
[2025-10-23 11:59:29] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -66.430086 | E_var:     0.5225 | E_err:   0.011294
[2025-10-23 11:59:42] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -66.424944 | E_var:     0.3938 | E_err:   0.009805
[2025-10-23 11:59:56] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -66.433039 | E_var:     0.3622 | E_err:   0.009403
[2025-10-23 12:00:09] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -66.439216 | E_var:     0.3796 | E_err:   0.009627
[2025-10-23 12:00:22] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -66.426794 | E_var:     0.3823 | E_err:   0.009661
[2025-10-23 12:00:35] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -66.421473 | E_var:     0.4403 | E_err:   0.010367
[2025-10-23 12:00:48] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -66.456857 | E_var:     0.3660 | E_err:   0.009453
[2025-10-23 12:01:01] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -66.438133 | E_var:     0.4249 | E_err:   0.010185
[2025-10-23 12:01:14] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -66.424692 | E_var:     0.4453 | E_err:   0.010427
[2025-10-23 12:01:28] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -66.426341 | E_var:     0.4387 | E_err:   0.010350
[2025-10-23 12:01:41] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -66.423528 | E_var:     0.6256 | E_err:   0.012358
[2025-10-23 12:01:54] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -66.445695 | E_var:     0.3352 | E_err:   0.009047
[2025-10-23 12:02:07] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -66.427343 | E_var:     0.5052 | E_err:   0.011106
[2025-10-23 12:02:20] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -66.431787 | E_var:     0.3514 | E_err:   0.009262
[2025-10-23 12:02:33] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -66.435502 | E_var:     0.3462 | E_err:   0.009194
[2025-10-23 12:02:47] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -66.437860 | E_var:     0.3842 | E_err:   0.009685
[2025-10-23 12:03:00] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -66.408108 | E_var:     0.4019 | E_err:   0.009905
[2025-10-23 12:03:13] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -66.431319 | E_var:     0.4135 | E_err:   0.010048
[2025-10-23 12:03:26] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -66.438994 | E_var:     0.3887 | E_err:   0.009741
[2025-10-23 12:03:39] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -66.426472 | E_var:     0.3935 | E_err:   0.009802
[2025-10-23 12:03:52] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -66.434508 | E_var:     0.3466 | E_err:   0.009199
[2025-10-23 12:04:06] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -66.432640 | E_var:     0.3303 | E_err:   0.008979
[2025-10-23 12:04:19] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -66.436339 | E_var:     0.3493 | E_err:   0.009235
[2025-10-23 12:04:32] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -66.418549 | E_var:     0.3833 | E_err:   0.009674
[2025-10-23 12:04:45] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -66.428969 | E_var:     0.3288 | E_err:   0.008960
[2025-10-23 12:04:58] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -66.420613 | E_var:     0.3955 | E_err:   0.009826
[2025-10-23 12:05:11] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -66.435475 | E_var:     0.4749 | E_err:   0.010768
[2025-10-23 12:05:24] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -66.434201 | E_var:     0.4857 | E_err:   0.010890
[2025-10-23 12:05:38] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -66.415450 | E_var:     0.4417 | E_err:   0.010384
[2025-10-23 12:05:51] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -66.415997 | E_var:     0.3578 | E_err:   0.009347
[2025-10-23 12:06:04] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -66.433580 | E_var:     0.4542 | E_err:   0.010531
[2025-10-23 12:06:17] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -66.443875 | E_var:     0.4603 | E_err:   0.010601
[2025-10-23 12:06:30] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -66.437073 | E_var:     0.3918 | E_err:   0.009780
[2025-10-23 12:06:43] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -66.415192 | E_var:     0.6295 | E_err:   0.012397
[2025-10-23 12:06:57] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -66.421254 | E_var:     0.3939 | E_err:   0.009807
[2025-10-23 12:07:10] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -66.430084 | E_var:     0.3688 | E_err:   0.009488
[2025-10-23 12:07:23] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -66.427929 | E_var:     0.3764 | E_err:   0.009586
[2025-10-23 12:07:36] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -66.424137 | E_var:     0.3456 | E_err:   0.009185
[2025-10-23 12:07:49] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -66.443240 | E_var:     0.3287 | E_err:   0.008958
[2025-10-23 12:08:02] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -66.457120 | E_var:     0.4088 | E_err:   0.009991
[2025-10-23 12:08:16] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -66.421750 | E_var:     0.4366 | E_err:   0.010325
[2025-10-23 12:08:29] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -66.443157 | E_var:     0.4230 | E_err:   0.010162
[2025-10-23 12:08:42] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -66.437651 | E_var:     0.3849 | E_err:   0.009693
[2025-10-23 12:08:55] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -66.455270 | E_var:     0.3644 | E_err:   0.009432
[2025-10-23 12:09:08] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -66.450183 | E_var:     0.3528 | E_err:   0.009280
[2025-10-23 12:09:21] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -66.440515 | E_var:     0.4086 | E_err:   0.009988
[2025-10-23 12:09:34] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -66.426486 | E_var:     0.4175 | E_err:   0.010096
[2025-10-23 12:09:48] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -66.426054 | E_var:     0.3573 | E_err:   0.009339
[2025-10-23 12:10:01] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -66.449802 | E_var:     0.3915 | E_err:   0.009776
[2025-10-23 12:10:14] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -66.428964 | E_var:     0.4020 | E_err:   0.009907
[2025-10-23 12:10:27] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -66.434141 | E_var:     0.3489 | E_err:   0.009229
[2025-10-23 12:10:41] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -66.436990 | E_var:     0.3683 | E_err:   0.009483
[2025-10-23 12:10:54] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -66.451087 | E_var:     0.3277 | E_err:   0.008944
[2025-10-23 12:11:07] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -66.448625 | E_var:     0.3964 | E_err:   0.009838
[2025-10-23 12:11:20] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -66.439083 | E_var:     0.3940 | E_err:   0.009807
[2025-10-23 12:11:33] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -66.440733 | E_var:     0.3467 | E_err:   0.009200
[2025-10-23 12:11:46] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -66.439386 | E_var:     0.3370 | E_err:   0.009070
[2025-10-23 12:11:59] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -66.438511 | E_var:     0.3756 | E_err:   0.009576
[2025-10-23 12:12:13] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -66.426354 | E_var:     0.5389 | E_err:   0.011470
[2025-10-23 12:12:26] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -66.428985 | E_var:     0.3254 | E_err:   0.008913
[2025-10-23 12:12:39] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -66.425343 | E_var:     0.3598 | E_err:   0.009373
[2025-10-23 12:12:52] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -66.424866 | E_var:     0.3441 | E_err:   0.009166
[2025-10-23 12:13:05] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -66.435949 | E_var:     0.4433 | E_err:   0.010404
[2025-10-23 12:13:18] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -66.421692 | E_var:     0.3963 | E_err:   0.009837
[2025-10-23 12:13:31] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -66.428274 | E_var:     0.3573 | E_err:   0.009340
[2025-10-23 12:13:45] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -66.420077 | E_var:     0.3776 | E_err:   0.009602
[2025-10-23 12:13:58] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -66.439120 | E_var:     0.5018 | E_err:   0.011069
[2025-10-23 12:14:11] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -66.433212 | E_var:     0.4210 | E_err:   0.010139
[2025-10-23 12:14:24] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -66.449283 | E_var:     0.3745 | E_err:   0.009562
[2025-10-23 12:14:37] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -66.445329 | E_var:     0.3120 | E_err:   0.008727
[2025-10-23 12:14:50] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -66.417818 | E_var:     0.3524 | E_err:   0.009275
[2025-10-23 12:15:04] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -66.438686 | E_var:     0.4228 | E_err:   0.010160
[2025-10-23 12:15:17] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -66.416518 | E_var:     0.3887 | E_err:   0.009742
[2025-10-23 12:15:30] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -66.433536 | E_var:     0.3765 | E_err:   0.009588
[2025-10-23 12:15:43] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -66.425668 | E_var:     0.4177 | E_err:   0.010099
[2025-10-23 12:15:56] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -66.444838 | E_var:     0.3573 | E_err:   0.009340
[2025-10-23 12:15:56] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-10-23 12:16:09] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -66.444447 | E_var:     0.3490 | E_err:   0.009231
[2025-10-23 12:16:22] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -66.436366 | E_var:     0.3866 | E_err:   0.009715
[2025-10-23 12:16:36] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -66.426633 | E_var:     0.3937 | E_err:   0.009804
[2025-10-23 12:16:49] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -66.410516 | E_var:     0.3942 | E_err:   0.009810
[2025-10-23 12:17:02] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -66.432141 | E_var:     0.4016 | E_err:   0.009902
[2025-10-23 12:17:15] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -66.426971 | E_var:     0.3991 | E_err:   0.009870
[2025-10-23 12:17:28] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -66.435448 | E_var:     0.3522 | E_err:   0.009273
[2025-10-23 12:17:41] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -66.426475 | E_var:     0.4883 | E_err:   0.010919
[2025-10-23 12:17:54] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -66.419846 | E_var:     0.4020 | E_err:   0.009907
[2025-10-23 12:18:08] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -66.436958 | E_var:     0.4240 | E_err:   0.010174
[2025-10-23 12:18:21] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -66.433730 | E_var:     0.3761 | E_err:   0.009582
[2025-10-23 12:18:34] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -66.442009 | E_var:     0.4287 | E_err:   0.010230
[2025-10-23 12:18:47] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -66.430620 | E_var:     0.4461 | E_err:   0.010436
[2025-10-23 12:19:00] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -66.433571 | E_var:     0.4189 | E_err:   0.010113
[2025-10-23 12:19:13] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -66.417875 | E_var:     0.3645 | E_err:   0.009433
[2025-10-23 12:19:26] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -66.438974 | E_var:     0.3792 | E_err:   0.009622
[2025-10-23 12:19:40] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -66.416992 | E_var:     0.3185 | E_err:   0.008818
[2025-10-23 12:19:53] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -66.439729 | E_var:     0.4184 | E_err:   0.010107
[2025-10-23 12:20:06] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -66.421205 | E_var:     0.3222 | E_err:   0.008869
[2025-10-23 12:20:19] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -66.435173 | E_var:     0.3802 | E_err:   0.009635
[2025-10-23 12:20:32] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -66.436877 | E_var:     0.3108 | E_err:   0.008710
[2025-10-23 12:20:45] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -66.425644 | E_var:     0.3311 | E_err:   0.008990
[2025-10-23 12:20:58] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -66.433013 | E_var:     0.4150 | E_err:   0.010066
[2025-10-23 12:21:12] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -66.425077 | E_var:     0.3474 | E_err:   0.009209
[2025-10-23 12:21:25] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -66.429527 | E_var:     0.3675 | E_err:   0.009472
[2025-10-23 12:21:38] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -66.415976 | E_var:     0.3854 | E_err:   0.009700
[2025-10-23 12:21:51] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -66.436075 | E_var:     0.3938 | E_err:   0.009805
[2025-10-23 12:22:04] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -66.440663 | E_var:     0.3811 | E_err:   0.009646
[2025-10-23 12:22:17] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -66.427735 | E_var:     0.5200 | E_err:   0.011267
[2025-10-23 12:22:30] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -66.411324 | E_var:     0.3370 | E_err:   0.009071
[2025-10-23 12:22:44] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -66.411729 | E_var:     0.4385 | E_err:   0.010346
[2025-10-23 12:22:57] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -66.420623 | E_var:     0.5168 | E_err:   0.011233
[2025-10-23 12:23:10] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -66.444840 | E_var:     0.3148 | E_err:   0.008766
[2025-10-23 12:23:23] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -66.429694 | E_var:     0.3357 | E_err:   0.009053
[2025-10-23 12:23:36] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -66.429444 | E_var:     0.4294 | E_err:   0.010239
[2025-10-23 12:23:49] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -66.422163 | E_var:     0.3508 | E_err:   0.009254
[2025-10-23 12:24:02] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -66.436805 | E_var:     0.3829 | E_err:   0.009669
[2025-10-23 12:24:16] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -66.419682 | E_var:     0.3851 | E_err:   0.009696
[2025-10-23 12:24:29] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -66.407948 | E_var:     0.3723 | E_err:   0.009533
[2025-10-23 12:24:42] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -66.423662 | E_var:     0.3845 | E_err:   0.009689
[2025-10-23 12:24:55] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -66.454349 | E_var:     0.3478 | E_err:   0.009215
[2025-10-23 12:25:08] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -66.413629 | E_var:     0.4589 | E_err:   0.010584
[2025-10-23 12:25:21] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -66.417660 | E_var:     0.3591 | E_err:   0.009363
[2025-10-23 12:25:34] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -66.443416 | E_var:     0.3555 | E_err:   0.009316
[2025-10-23 12:25:47] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -66.441908 | E_var:     0.3765 | E_err:   0.009587
[2025-10-23 12:26:01] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -66.426756 | E_var:     0.3278 | E_err:   0.008946
[2025-10-23 12:26:14] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -66.436824 | E_var:     0.4319 | E_err:   0.010269
[2025-10-23 12:26:27] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -66.431956 | E_var:     0.3374 | E_err:   0.009077
[2025-10-23 12:26:40] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -66.430424 | E_var:     0.3736 | E_err:   0.009550
[2025-10-23 12:26:53] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -66.425239 | E_var:     0.5072 | E_err:   0.011128
[2025-10-23 12:27:06] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -66.439575 | E_var:     0.4340 | E_err:   0.010294
[2025-10-23 12:27:20] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -66.444492 | E_var:     0.4840 | E_err:   0.010870
[2025-10-23 12:27:33] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -66.431736 | E_var:     0.3051 | E_err:   0.008630
[2025-10-23 12:27:46] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -66.418989 | E_var:     0.4359 | E_err:   0.010316
[2025-10-23 12:27:59] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -66.434039 | E_var:     0.3372 | E_err:   0.009073
[2025-10-23 12:28:12] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -66.437787 | E_var:     0.4259 | E_err:   0.010197
[2025-10-23 12:28:25] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -66.434977 | E_var:     0.3756 | E_err:   0.009576
[2025-10-23 12:28:38] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -66.422105 | E_var:     0.4463 | E_err:   0.010438
[2025-10-23 12:28:52] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -66.427361 | E_var:     0.3579 | E_err:   0.009348
[2025-10-23 12:29:05] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -66.422864 | E_var:     0.3170 | E_err:   0.008797
[2025-10-23 12:29:18] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -66.437298 | E_var:     0.5158 | E_err:   0.011222
[2025-10-23 12:29:31] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -66.438432 | E_var:     0.3722 | E_err:   0.009532
[2025-10-23 12:29:44] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -66.423385 | E_var:     0.3735 | E_err:   0.009549
[2025-10-23 12:29:57] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -66.434742 | E_var:     0.3950 | E_err:   0.009820
[2025-10-23 12:30:10] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -66.421764 | E_var:     3.0010 | E_err:   0.027068
[2025-10-23 12:30:23] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -66.427766 | E_var:     0.4065 | E_err:   0.009962
[2025-10-23 12:30:37] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -66.437880 | E_var:     0.3213 | E_err:   0.008857
[2025-10-23 12:30:50] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -66.444425 | E_var:     0.3909 | E_err:   0.009769
[2025-10-23 12:31:03] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -66.441494 | E_var:     0.3993 | E_err:   0.009873
[2025-10-23 12:31:16] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -66.441314 | E_var:     0.4067 | E_err:   0.009965
[2025-10-23 12:31:29] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -66.452356 | E_var:     0.4781 | E_err:   0.010803
[2025-10-23 12:31:42] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -66.432155 | E_var:     0.3426 | E_err:   0.009145
[2025-10-23 12:31:55] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -66.428182 | E_var:     0.3391 | E_err:   0.009098
[2025-10-23 12:32:09] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -66.441506 | E_var:     0.3236 | E_err:   0.008889
[2025-10-23 12:32:22] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -66.425918 | E_var:     0.4243 | E_err:   0.010178
[2025-10-23 12:32:35] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -66.422194 | E_var:     0.3597 | E_err:   0.009371
[2025-10-23 12:32:48] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -66.434631 | E_var:     0.3605 | E_err:   0.009382
[2025-10-23 12:33:01] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -66.429379 | E_var:     0.3716 | E_err:   0.009525
[2025-10-23 12:33:14] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -66.440392 | E_var:     0.3282 | E_err:   0.008951
[2025-10-23 12:33:27] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -66.417307 | E_var:     0.3430 | E_err:   0.009151
[2025-10-23 12:33:41] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -66.417028 | E_var:     0.3338 | E_err:   0.009028
[2025-10-23 12:33:54] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -66.432855 | E_var:     0.3504 | E_err:   0.009249
[2025-10-23 12:34:07] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -66.421134 | E_var:     0.3771 | E_err:   0.009595
[2025-10-23 12:34:20] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -66.427502 | E_var:     0.3874 | E_err:   0.009725
[2025-10-23 12:34:33] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -66.430044 | E_var:     0.4397 | E_err:   0.010361
[2025-10-23 12:34:46] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -66.421303 | E_var:     0.4158 | E_err:   0.010075
[2025-10-23 12:34:59] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -66.432943 | E_var:     0.4877 | E_err:   0.010912
[2025-10-23 12:35:13] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -66.430761 | E_var:     0.3691 | E_err:   0.009492
[2025-10-23 12:35:26] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -66.432327 | E_var:     0.3159 | E_err:   0.008782
[2025-10-23 12:35:39] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -66.412533 | E_var:     0.3645 | E_err:   0.009433
[2025-10-23 12:35:52] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -66.428809 | E_var:     0.3253 | E_err:   0.008911
[2025-10-23 12:36:05] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -66.430792 | E_var:     0.3155 | E_err:   0.008777
[2025-10-23 12:36:18] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -66.439622 | E_var:     0.3707 | E_err:   0.009513
[2025-10-23 12:36:31] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -66.445496 | E_var:     0.3432 | E_err:   0.009154
[2025-10-23 12:36:45] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -66.430232 | E_var:     0.3961 | E_err:   0.009834
[2025-10-23 12:36:58] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -66.452398 | E_var:     0.3595 | E_err:   0.009368
[2025-10-23 12:37:11] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -66.418215 | E_var:     0.3671 | E_err:   0.009468
[2025-10-23 12:37:24] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -66.424617 | E_var:     0.3899 | E_err:   0.009756
[2025-10-23 12:37:37] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -66.430625 | E_var:     0.3409 | E_err:   0.009123
[2025-10-23 12:37:50] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -66.425791 | E_var:     0.5268 | E_err:   0.011340
[2025-10-23 12:38:03] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -66.412069 | E_var:     0.3957 | E_err:   0.009829
[2025-10-23 12:38:17] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -66.426953 | E_var:     0.3480 | E_err:   0.009218
[2025-10-23 12:38:30] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -66.440261 | E_var:     0.4132 | E_err:   0.010044
[2025-10-23 12:38:43] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -66.424081 | E_var:     0.3809 | E_err:   0.009644
[2025-10-23 12:38:56] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -66.444141 | E_var:     0.3228 | E_err:   0.008877
[2025-10-23 12:38:56] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-23 12:38:56] ======================================================================================================
[2025-10-23 12:38:56] ✅ Training completed successfully
[2025-10-23 12:38:56] Total restarts: 2
[2025-10-23 12:39:01] Final Energy: -66.44414114 ± 0.00887683
[2025-10-23 12:39:01] Final Variance: 0.322757
[2025-10-23 12:39:01] ======================================================================================================
[2025-10-23 12:39:01] ======================================================================================================
[2025-10-23 12:39:01] Training completed | Runtime: 13877.3s
