[2025-10-06 01:43:17] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.78/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 01:43:17]   - 迭代次数: final
[2025-10-06 01:43:17]   - 能量: -27.950664-0.000441j ± 0.005445, Var: 0.121421
[2025-10-06 01:43:17]   - 时间戳: 2025-10-06T01:43:05.538415+08:00
[2025-10-06 01:43:31] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 01:43:31] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 01:43:31] ======================================================================================================
[2025-10-06 01:43:31] GCNN for Shastry-Sutherland Model
[2025-10-06 01:43:31] ======================================================================================================
[2025-10-06 01:43:31] System parameters:
[2025-10-06 01:43:31]   - System size: L=4, N=64
[2025-10-06 01:43:31]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-10-06 01:43:32] ------------------------------------------------------------------------------------------------------
[2025-10-06 01:43:32] Model parameters:
[2025-10-06 01:43:32]   - Number of layers = 4
[2025-10-06 01:43:32]   - Number of features = 4
[2025-10-06 01:43:32]   - Total parameters = 12572
[2025-10-06 01:43:32] ------------------------------------------------------------------------------------------------------
[2025-10-06 01:43:32] Training parameters:
[2025-10-06 01:43:32]   - Total iterations: 1050
[2025-10-06 01:43:32]   - Annealing cycles: 3
[2025-10-06 01:43:32]   - Initial period: 150
[2025-10-06 01:43:32]   - Period multiplier: 2.0
[2025-10-06 01:43:32]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 01:43:32]   - Samples: 4096
[2025-10-06 01:43:32]   - Discarded samples: 0
[2025-10-06 01:43:32]   - Chunk size: 4096
[2025-10-06 01:43:32]   - Diagonal shift: 0.15
[2025-10-06 01:43:32]   - Gradient clipping: 1.0
[2025-10-06 01:43:32]   - Checkpoint enabled: interval=100
[2025-10-06 01:43:32]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.79/model_L4F4/training/checkpoints
[2025-10-06 01:43:32] ------------------------------------------------------------------------------------------------------
[2025-10-06 01:43:32] Device status:
[2025-10-06 01:43:32]   - Devices model: NVIDIA H200 NVL
[2025-10-06 01:43:32]   - Number of devices: 1
[2025-10-06 01:43:32]   - Sharding: True
[2025-10-06 01:43:32] ======================================================================================================
[2025-10-06 01:44:05] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -28.361423 | E_var:     0.3120 | E_err:   0.008728
[2025-10-06 01:44:23] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -28.362701 | E_var:     0.2323 | E_err:   0.007530
[2025-10-06 01:44:26] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -28.371767 | E_var:     0.1897 | E_err:   0.006806
[2025-10-06 01:44:28] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -28.359328 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 01:44:30] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -28.359268 | E_var:     0.1295 | E_err:   0.005622
[2025-10-06 01:44:33] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -28.354938 | E_var:     0.1372 | E_err:   0.005788
[2025-10-06 01:44:35] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -28.351931 | E_var:     0.1388 | E_err:   0.005820
[2025-10-06 01:44:38] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -28.358415 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 01:44:40] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -28.356859 | E_var:     0.1421 | E_err:   0.005891
[2025-10-06 01:44:43] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -28.357601 | E_var:     0.1487 | E_err:   0.006025
[2025-10-06 01:44:45] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -28.363370 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 01:44:48] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -28.368776 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 01:44:50] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -28.363672 | E_var:     0.1435 | E_err:   0.005918
[2025-10-06 01:44:52] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -28.363282 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 01:44:55] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -28.356149 | E_var:     0.1280 | E_err:   0.005590
[2025-10-06 01:44:57] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -28.362019 | E_var:     0.1427 | E_err:   0.005903
[2025-10-06 01:45:00] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -28.364733 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 01:45:02] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -28.357864 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 01:45:05] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -28.353540 | E_var:     0.1360 | E_err:   0.005763
[2025-10-06 01:45:07] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -28.360220 | E_var:     0.1125 | E_err:   0.005242
[2025-10-06 01:45:09] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -28.361653 | E_var:     0.1436 | E_err:   0.005922
[2025-10-06 01:45:12] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -28.355312 | E_var:     0.1210 | E_err:   0.005434
[2025-10-06 01:45:14] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -28.355586 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 01:45:17] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -28.353563 | E_var:     0.1567 | E_err:   0.006185
[2025-10-06 01:45:19] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -28.363789 | E_var:     0.1797 | E_err:   0.006624
[2025-10-06 01:45:22] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -28.364353 | E_var:     0.1581 | E_err:   0.006213
[2025-10-06 01:45:24] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -28.348717 | E_var:     0.1851 | E_err:   0.006723
[2025-10-06 01:45:26] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -28.359657 | E_var:     0.1560 | E_err:   0.006172
[2025-10-06 01:45:29] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -28.356517 | E_var:     0.1545 | E_err:   0.006142
[2025-10-06 01:45:31] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -28.359435 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 01:45:34] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -28.350066 | E_var:     0.1055 | E_err:   0.005075
[2025-10-06 01:45:36] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -28.359268 | E_var:     0.0992 | E_err:   0.004922
[2025-10-06 01:45:39] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -28.352752 | E_var:     0.1461 | E_err:   0.005973
[2025-10-06 01:45:41] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -28.363715 | E_var:     0.1447 | E_err:   0.005944
[2025-10-06 01:45:43] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -28.368854 | E_var:     0.1398 | E_err:   0.005841
[2025-10-06 01:45:46] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -28.353144 | E_var:     0.1156 | E_err:   0.005311
[2025-10-06 01:45:48] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -28.364060 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 01:45:51] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -28.363822 | E_var:     0.1229 | E_err:   0.005478
[2025-10-06 01:45:53] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -28.363274 | E_var:     0.1191 | E_err:   0.005392
[2025-10-06 01:45:56] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -28.361132 | E_var:     0.1129 | E_err:   0.005249
[2025-10-06 01:45:58] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -28.361556 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 01:46:00] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -28.346144 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 01:46:03] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -28.359778 | E_var:     0.1152 | E_err:   0.005302
[2025-10-06 01:46:05] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -28.354360 | E_var:     0.1400 | E_err:   0.005845
[2025-10-06 01:46:08] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -28.358423 | E_var:     0.1395 | E_err:   0.005837
[2025-10-06 01:46:10] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -28.363263 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 01:46:13] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -28.356673 | E_var:     0.1095 | E_err:   0.005170
[2025-10-06 01:46:15] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -28.366676 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 01:46:17] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -28.352990 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 01:46:20] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -28.371112 | E_var:     0.1207 | E_err:   0.005427
[2025-10-06 01:46:22] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -28.359098 | E_var:     0.1120 | E_err:   0.005228
[2025-10-06 01:46:25] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -28.361983 | E_var:     0.1672 | E_err:   0.006388
[2025-10-06 01:46:27] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -28.356462 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 01:46:30] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -28.355378 | E_var:     0.0990 | E_err:   0.004917
[2025-10-06 01:46:32] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -28.354556 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 01:46:34] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -28.371927 | E_var:     0.1841 | E_err:   0.006704
[2025-10-06 01:46:37] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -28.350200 | E_var:     0.1397 | E_err:   0.005839
[2025-10-06 01:46:39] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -28.354003 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 01:46:42] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -28.371290 | E_var:     0.1195 | E_err:   0.005402
[2025-10-06 01:46:44] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -28.362182 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 01:46:46] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -28.355193 | E_var:     0.1322 | E_err:   0.005682
[2025-10-06 01:46:49] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -28.366479 | E_var:     0.1262 | E_err:   0.005551
[2025-10-06 01:46:51] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -28.360147 | E_var:     0.1129 | E_err:   0.005249
[2025-10-06 01:46:54] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -28.355224 | E_var:     0.2380 | E_err:   0.007622
[2025-10-06 01:46:56] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -28.364102 | E_var:     0.1226 | E_err:   0.005472
[2025-10-06 01:46:59] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -28.356482 | E_var:     0.1467 | E_err:   0.005985
[2025-10-06 01:47:01] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -28.358144 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 01:47:03] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -28.366930 | E_var:     0.1262 | E_err:   0.005550
[2025-10-06 01:47:06] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -28.356393 | E_var:     0.1109 | E_err:   0.005203
[2025-10-06 01:47:08] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -28.355917 | E_var:     0.1278 | E_err:   0.005586
[2025-10-06 01:47:11] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -28.357261 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 01:47:13] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -28.367182 | E_var:     0.1117 | E_err:   0.005222
[2025-10-06 01:47:16] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -28.361444 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 01:47:18] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -28.352314 | E_var:     0.1153 | E_err:   0.005306
[2025-10-06 01:47:20] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -28.354622 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 01:47:23] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -28.356200 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 01:47:25] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -28.357930 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 01:47:28] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -28.359469 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 01:47:30] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -28.360630 | E_var:     0.0976 | E_err:   0.004881
[2025-10-06 01:47:32] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -28.362796 | E_var:     0.1119 | E_err:   0.005228
[2025-10-06 01:47:35] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -28.362971 | E_var:     0.1080 | E_err:   0.005135
[2025-10-06 01:47:37] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -28.362914 | E_var:     0.0877 | E_err:   0.004628
[2025-10-06 01:47:40] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -28.364260 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 01:47:42] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -28.354889 | E_var:     0.1224 | E_err:   0.005467
[2025-10-06 01:47:45] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -28.366026 | E_var:     0.1460 | E_err:   0.005970
[2025-10-06 01:47:47] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -28.372377 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 01:47:49] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -28.362924 | E_var:     0.1454 | E_err:   0.005959
[2025-10-06 01:47:52] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -28.367618 | E_var:     0.1249 | E_err:   0.005522
[2025-10-06 01:47:54] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -28.361965 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 01:47:57] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -28.351149 | E_var:     0.0863 | E_err:   0.004589
[2025-10-06 01:47:59] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -28.356096 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 01:48:02] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -28.356655 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 01:48:04] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -28.362392 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 01:48:06] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -28.362123 | E_var:     0.1138 | E_err:   0.005272
[2025-10-06 01:48:09] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -28.349342 | E_var:     0.1396 | E_err:   0.005837
[2025-10-06 01:48:11] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -28.363838 | E_var:     0.1131 | E_err:   0.005256
[2025-10-06 01:48:14] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -28.363107 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 01:48:16] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -28.368685 | E_var:     0.4045 | E_err:   0.009938
[2025-10-06 01:48:19] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -28.349734 | E_var:     0.1231 | E_err:   0.005483
[2025-10-06 01:48:21] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -28.357494 | E_var:     0.1241 | E_err:   0.005505
[2025-10-06 01:48:21] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 01:48:23] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -28.363991 | E_var:     0.1040 | E_err:   0.005039
[2025-10-06 01:48:26] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -28.363534 | E_var:     0.8277 | E_err:   0.014215
[2025-10-06 01:48:28] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -28.363922 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 01:48:31] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -28.361653 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 01:48:33] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -28.362169 | E_var:     0.1144 | E_err:   0.005284
[2025-10-06 01:48:36] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -28.361791 | E_var:     0.1381 | E_err:   0.005806
[2025-10-06 01:48:38] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -28.354451 | E_var:     0.1585 | E_err:   0.006220
[2025-10-06 01:48:40] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -28.357911 | E_var:     0.1065 | E_err:   0.005100
[2025-10-06 01:48:43] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -28.353084 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 01:48:45] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -28.362163 | E_var:     0.1553 | E_err:   0.006157
[2025-10-06 01:48:48] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -28.363604 | E_var:     0.0988 | E_err:   0.004911
[2025-10-06 01:48:50] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -28.358001 | E_var:     0.1026 | E_err:   0.005005
[2025-10-06 01:48:53] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -28.362113 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 01:48:55] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -28.361813 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 01:48:57] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -28.363508 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 01:49:00] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -28.365155 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 01:49:02] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -28.360238 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 01:49:05] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -28.368010 | E_var:     0.1246 | E_err:   0.005515
[2025-10-06 01:49:07] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -28.369453 | E_var:     0.2936 | E_err:   0.008467
[2025-10-06 01:49:09] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -28.365103 | E_var:     0.1327 | E_err:   0.005691
[2025-10-06 01:49:12] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -28.357380 | E_var:     0.2548 | E_err:   0.007886
[2025-10-06 01:49:14] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -28.362763 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 01:49:17] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -28.357544 | E_var:     0.1245 | E_err:   0.005512
[2025-10-06 01:49:19] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -28.352710 | E_var:     0.1683 | E_err:   0.006411
[2025-10-06 01:49:22] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -28.358207 | E_var:     0.1601 | E_err:   0.006253
[2025-10-06 01:49:24] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -28.372794 | E_var:     0.1390 | E_err:   0.005826
[2025-10-06 01:49:26] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -28.360248 | E_var:     0.1293 | E_err:   0.005617
[2025-10-06 01:49:29] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -28.352144 | E_var:     0.1065 | E_err:   0.005098
[2025-10-06 01:49:31] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -28.363459 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 01:49:34] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -28.350869 | E_var:     1.5041 | E_err:   0.019163
[2025-10-06 01:49:36] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -28.370746 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 01:49:39] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -28.359670 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 01:49:41] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -28.361014 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 01:49:43] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -28.352460 | E_var:     0.1141 | E_err:   0.005279
[2025-10-06 01:49:46] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -28.357499 | E_var:     0.1219 | E_err:   0.005455
[2025-10-06 01:49:48] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -28.355590 | E_var:     0.2156 | E_err:   0.007255
[2025-10-06 01:49:51] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -28.359820 | E_var:     0.1195 | E_err:   0.005402
[2025-10-06 01:49:53] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -28.348905 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 01:49:56] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -28.361072 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 01:49:58] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -28.357153 | E_var:     0.1344 | E_err:   0.005727
[2025-10-06 01:50:00] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -28.362636 | E_var:     0.1065 | E_err:   0.005099
[2025-10-06 01:50:03] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -28.363900 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 01:50:05] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -28.363004 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 01:50:08] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -28.357264 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 01:50:10] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -28.363143 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 01:50:12] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -28.361595 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 01:50:15] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -28.361783 | E_var:     0.1342 | E_err:   0.005723
[2025-10-06 01:50:17] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -28.356517 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 01:50:20] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -28.366940 | E_var:     0.1346 | E_err:   0.005733
[2025-10-06 01:50:22] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -28.361490 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 01:50:22] 🔄 RESTART #1 | Period: 300
[2025-10-06 01:50:25] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -28.360452 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 01:50:27] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -28.357849 | E_var:     0.1446 | E_err:   0.005941
[2025-10-06 01:50:29] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -28.363275 | E_var:     0.1247 | E_err:   0.005518
[2025-10-06 01:50:32] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -28.348818 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 01:50:34] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -28.343588 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 01:50:37] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -28.367678 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 01:50:39] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -28.368280 | E_var:     0.1279 | E_err:   0.005589
[2025-10-06 01:50:42] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -28.369281 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 01:50:44] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -28.353461 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 01:50:46] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -28.360172 | E_var:     0.0948 | E_err:   0.004810
[2025-10-06 01:50:49] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -28.358762 | E_var:     0.1479 | E_err:   0.006008
[2025-10-06 01:50:51] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -28.355456 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 01:50:54] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -28.354030 | E_var:     0.1651 | E_err:   0.006348
[2025-10-06 01:50:56] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -28.360640 | E_var:     0.1274 | E_err:   0.005578
[2025-10-06 01:50:59] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -28.357557 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 01:51:01] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -28.348182 | E_var:     0.1393 | E_err:   0.005831
[2025-10-06 01:51:03] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -28.351758 | E_var:     0.1589 | E_err:   0.006228
[2025-10-06 01:51:06] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -28.357747 | E_var:     0.1144 | E_err:   0.005284
[2025-10-06 01:51:08] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -28.360047 | E_var:     0.1017 | E_err:   0.004984
[2025-10-06 01:51:11] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -28.363941 | E_var:     0.1224 | E_err:   0.005466
[2025-10-06 01:51:13] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -28.366891 | E_var:     0.1032 | E_err:   0.005021
[2025-10-06 01:51:15] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -28.357205 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 01:51:18] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -28.361904 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 01:51:20] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -28.353195 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 01:51:23] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -28.359226 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 01:51:25] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -28.365469 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 01:51:28] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -28.358486 | E_var:     0.1113 | E_err:   0.005212
[2025-10-06 01:51:30] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -28.357495 | E_var:     0.1113 | E_err:   0.005214
[2025-10-06 01:51:32] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -28.349468 | E_var:     0.1294 | E_err:   0.005622
[2025-10-06 01:51:35] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -28.365429 | E_var:     0.1101 | E_err:   0.005186
[2025-10-06 01:51:37] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -28.361535 | E_var:     0.1194 | E_err:   0.005398
[2025-10-06 01:51:40] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -28.360820 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 01:51:42] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -28.359086 | E_var:     0.3599 | E_err:   0.009373
[2025-10-06 01:51:45] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -28.360735 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 01:51:47] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -28.353932 | E_var:     0.1137 | E_err:   0.005268
[2025-10-06 01:51:49] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -28.352627 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 01:51:52] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -28.362020 | E_var:     0.1352 | E_err:   0.005745
[2025-10-06 01:51:54] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -28.354701 | E_var:     0.1566 | E_err:   0.006183
[2025-10-06 01:51:57] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -28.357189 | E_var:     0.2510 | E_err:   0.007829
[2025-10-06 01:51:59] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -28.350740 | E_var:     0.1573 | E_err:   0.006198
[2025-10-06 01:52:01] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -28.361252 | E_var:     0.1112 | E_err:   0.005211
[2025-10-06 01:52:04] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -28.349372 | E_var:     0.1730 | E_err:   0.006499
[2025-10-06 01:52:06] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -28.360911 | E_var:     0.1283 | E_err:   0.005596
[2025-10-06 01:52:09] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -28.353948 | E_var:     0.1301 | E_err:   0.005636
[2025-10-06 01:52:11] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -28.360643 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 01:52:14] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -28.361636 | E_var:     0.1369 | E_err:   0.005782
[2025-10-06 01:52:16] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -28.361630 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 01:52:18] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -28.358090 | E_var:     0.1388 | E_err:   0.005821
[2025-10-06 01:52:21] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -28.364226 | E_var:     0.0948 | E_err:   0.004810
[2025-10-06 01:52:23] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -28.360816 | E_var:     0.1368 | E_err:   0.005780
[2025-10-06 01:52:23] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 01:52:26] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -28.361056 | E_var:     0.1390 | E_err:   0.005825
[2025-10-06 01:52:28] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -28.365686 | E_var:     0.1399 | E_err:   0.005843
[2025-10-06 01:52:31] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -28.350855 | E_var:     0.1062 | E_err:   0.005091
[2025-10-06 01:52:33] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -28.368632 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 01:52:35] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -28.357609 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 01:52:38] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -28.358419 | E_var:     0.1147 | E_err:   0.005293
[2025-10-06 01:52:40] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -28.358313 | E_var:     0.1562 | E_err:   0.006176
[2025-10-06 01:52:43] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -28.347691 | E_var:     0.1686 | E_err:   0.006417
[2025-10-06 01:52:45] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -28.361857 | E_var:     0.1467 | E_err:   0.005984
[2025-10-06 01:52:48] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -28.364604 | E_var:     0.1263 | E_err:   0.005552
[2025-10-06 01:52:50] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -28.351937 | E_var:     0.1617 | E_err:   0.006284
[2025-10-06 01:52:52] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -28.363482 | E_var:     0.1248 | E_err:   0.005521
[2025-10-06 01:52:55] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -28.360103 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 01:52:57] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -28.363128 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 01:53:00] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -28.353954 | E_var:     0.1435 | E_err:   0.005920
[2025-10-06 01:53:02] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -28.363710 | E_var:     0.1111 | E_err:   0.005207
[2025-10-06 01:53:05] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -28.362974 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 01:53:07] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -28.360251 | E_var:     0.1247 | E_err:   0.005517
[2025-10-06 01:53:09] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -28.365659 | E_var:     0.1164 | E_err:   0.005332
[2025-10-06 01:53:12] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -28.364248 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 01:53:14] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -28.354020 | E_var:     0.1385 | E_err:   0.005814
[2025-10-06 01:53:17] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -28.351887 | E_var:     0.1209 | E_err:   0.005432
[2025-10-06 01:53:19] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -28.359930 | E_var:     0.1210 | E_err:   0.005434
[2025-10-06 01:53:22] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -28.369033 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 01:53:24] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -28.362499 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 01:53:26] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -28.367176 | E_var:     0.1324 | E_err:   0.005685
[2025-10-06 01:53:29] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -28.360016 | E_var:     0.1285 | E_err:   0.005602
[2025-10-06 01:53:31] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -28.349990 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 01:53:34] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -28.354049 | E_var:     0.3864 | E_err:   0.009713
[2025-10-06 01:53:36] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -28.350994 | E_var:     0.1335 | E_err:   0.005710
[2025-10-06 01:53:39] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -28.368468 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 01:53:41] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -28.367015 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 01:53:43] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -28.362151 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 01:53:46] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -28.370337 | E_var:     0.1204 | E_err:   0.005421
[2025-10-06 01:53:48] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -28.363595 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 01:53:51] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -28.364339 | E_var:     0.0978 | E_err:   0.004885
[2025-10-06 01:53:53] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -28.356817 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 01:53:56] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -28.361250 | E_var:     0.1185 | E_err:   0.005379
[2025-10-06 01:53:58] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -28.361589 | E_var:     0.1030 | E_err:   0.005016
[2025-10-06 01:54:00] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -28.366373 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 01:54:03] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -28.362294 | E_var:     0.1070 | E_err:   0.005110
[2025-10-06 01:54:05] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -28.365680 | E_var:     0.1369 | E_err:   0.005781
[2025-10-06 01:54:08] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -28.350223 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 01:54:10] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -28.352230 | E_var:     0.1170 | E_err:   0.005344
[2025-10-06 01:54:13] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -28.378194 | E_var:     0.2094 | E_err:   0.007150
[2025-10-06 01:54:15] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -28.357969 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 01:54:17] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -28.361620 | E_var:     0.1345 | E_err:   0.005730
[2025-10-06 01:54:20] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -28.360235 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 01:54:22] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -28.352164 | E_var:     0.1855 | E_err:   0.006730
[2025-10-06 01:54:25] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -28.359291 | E_var:     0.1152 | E_err:   0.005304
[2025-10-06 01:54:27] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -28.363176 | E_var:     0.1241 | E_err:   0.005505
[2025-10-06 01:54:30] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -28.358514 | E_var:     0.1337 | E_err:   0.005714
[2025-10-06 01:54:32] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -28.362634 | E_var:     0.3723 | E_err:   0.009534
[2025-10-06 01:54:34] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -28.360223 | E_var:     0.1065 | E_err:   0.005100
[2025-10-06 01:54:37] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -28.357873 | E_var:     0.1078 | E_err:   0.005129
[2025-10-06 01:54:39] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -28.356576 | E_var:     0.0953 | E_err:   0.004825
[2025-10-06 01:54:42] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -28.363887 | E_var:     0.1172 | E_err:   0.005350
[2025-10-06 01:54:44] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -28.350082 | E_var:     0.1301 | E_err:   0.005636
[2025-10-06 01:54:46] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -28.351418 | E_var:     0.1336 | E_err:   0.005712
[2025-10-06 01:54:49] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -28.355812 | E_var:     0.1101 | E_err:   0.005185
[2025-10-06 01:54:51] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -28.363005 | E_var:     0.1317 | E_err:   0.005670
[2025-10-06 01:54:54] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -28.362051 | E_var:     0.1047 | E_err:   0.005055
[2025-10-06 01:54:56] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -28.344565 | E_var:     0.3774 | E_err:   0.009599
[2025-10-06 01:54:59] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -28.363555 | E_var:     0.1187 | E_err:   0.005383
[2025-10-06 01:55:01] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -28.359380 | E_var:     0.1351 | E_err:   0.005744
[2025-10-06 01:55:03] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -28.361108 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 01:55:06] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -28.354021 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 01:55:08] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -28.360977 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 01:55:11] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -28.355951 | E_var:     0.1585 | E_err:   0.006220
[2025-10-06 01:55:13] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -28.364867 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 01:55:15] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -28.349986 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 01:55:18] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -28.356598 | E_var:     0.0917 | E_err:   0.004731
[2025-10-06 01:55:20] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -28.355392 | E_var:     0.1379 | E_err:   0.005802
[2025-10-06 01:55:23] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -28.351414 | E_var:     0.1039 | E_err:   0.005037
[2025-10-06 01:55:25] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -28.357468 | E_var:     0.1259 | E_err:   0.005545
[2025-10-06 01:55:28] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -28.365885 | E_var:     0.1542 | E_err:   0.006135
[2025-10-06 01:55:30] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -28.361371 | E_var:     0.1596 | E_err:   0.006242
[2025-10-06 01:55:32] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -28.362137 | E_var:     0.1335 | E_err:   0.005709
[2025-10-06 01:55:35] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -28.358880 | E_var:     0.1115 | E_err:   0.005216
[2025-10-06 01:55:37] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -28.369153 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 01:55:40] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -28.347005 | E_var:     0.2096 | E_err:   0.007154
[2025-10-06 01:55:42] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -28.357618 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 01:55:45] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -28.353573 | E_var:     0.1121 | E_err:   0.005232
[2025-10-06 01:55:47] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -28.349108 | E_var:     0.1169 | E_err:   0.005343
[2025-10-06 01:55:49] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -28.366408 | E_var:     0.1218 | E_err:   0.005454
[2025-10-06 01:55:52] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -28.374121 | E_var:     0.2047 | E_err:   0.007070
[2025-10-06 01:55:54] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -28.361390 | E_var:     0.1293 | E_err:   0.005618
[2025-10-06 01:55:57] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -28.362559 | E_var:     0.1097 | E_err:   0.005175
[2025-10-06 01:55:59] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -28.364002 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 01:56:02] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -28.365937 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 01:56:04] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -28.362535 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 01:56:06] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -28.364175 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 01:56:09] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -28.365124 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 01:56:11] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -28.354421 | E_var:     0.3315 | E_err:   0.008996
[2025-10-06 01:56:14] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -28.357659 | E_var:     0.1040 | E_err:   0.005039
[2025-10-06 01:56:16] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -28.359205 | E_var:     0.1595 | E_err:   0.006240
[2025-10-06 01:56:19] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -28.356943 | E_var:     0.1239 | E_err:   0.005500
[2025-10-06 01:56:21] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -28.364787 | E_var:     0.1361 | E_err:   0.005765
[2025-10-06 01:56:23] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -28.363608 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 01:56:26] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -28.360779 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 01:56:26] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 01:56:28] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -28.359253 | E_var:     0.1452 | E_err:   0.005953
[2025-10-06 01:56:31] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -28.355983 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 01:56:33] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -28.363516 | E_var:     0.1327 | E_err:   0.005692
[2025-10-06 01:56:36] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -28.358463 | E_var:     0.1086 | E_err:   0.005148
[2025-10-06 01:56:38] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -28.361603 | E_var:     0.2666 | E_err:   0.008067
[2025-10-06 01:56:40] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -28.360340 | E_var:     0.1054 | E_err:   0.005072
[2025-10-06 01:56:43] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -28.357274 | E_var:     0.1229 | E_err:   0.005479
[2025-10-06 01:56:45] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -28.353104 | E_var:     0.1189 | E_err:   0.005388
[2025-10-06 01:56:48] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -28.362746 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 01:56:50] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -28.356958 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 01:56:52] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -28.361435 | E_var:     0.1395 | E_err:   0.005837
[2025-10-06 01:56:55] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -28.359972 | E_var:     0.1099 | E_err:   0.005179
[2025-10-06 01:56:57] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -28.356610 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 01:57:00] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -28.356839 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 01:57:02] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -28.362279 | E_var:     0.1197 | E_err:   0.005406
[2025-10-06 01:57:05] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -28.364288 | E_var:     0.1717 | E_err:   0.006474
[2025-10-06 01:57:07] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -28.357541 | E_var:     0.1566 | E_err:   0.006182
[2025-10-06 01:57:09] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -28.354507 | E_var:     0.1127 | E_err:   0.005246
[2025-10-06 01:57:12] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -28.365459 | E_var:     0.1268 | E_err:   0.005563
[2025-10-06 01:57:14] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -28.357622 | E_var:     0.1595 | E_err:   0.006241
[2025-10-06 01:57:17] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -28.362566 | E_var:     0.1223 | E_err:   0.005465
[2025-10-06 01:57:19] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -28.371788 | E_var:     0.0983 | E_err:   0.004900
[2025-10-06 01:57:22] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -28.356150 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 01:57:24] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -28.355867 | E_var:     0.1231 | E_err:   0.005483
[2025-10-06 01:57:26] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -28.355775 | E_var:     0.1567 | E_err:   0.006185
[2025-10-06 01:57:29] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -28.357378 | E_var:     0.1148 | E_err:   0.005294
[2025-10-06 01:57:31] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -28.354256 | E_var:     0.1055 | E_err:   0.005076
[2025-10-06 01:57:34] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -28.355446 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 01:57:36] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -28.360347 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 01:57:38] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -28.355993 | E_var:     0.1285 | E_err:   0.005602
[2025-10-06 01:57:41] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -28.366753 | E_var:     0.1280 | E_err:   0.005590
[2025-10-06 01:57:43] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -28.357025 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 01:57:46] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -28.357178 | E_var:     0.1286 | E_err:   0.005603
[2025-10-06 01:57:48] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -28.367479 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 01:57:51] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -28.358624 | E_var:     0.1434 | E_err:   0.005916
[2025-10-06 01:57:53] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -28.360401 | E_var:     0.1434 | E_err:   0.005917
[2025-10-06 01:57:55] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -28.347269 | E_var:     0.1476 | E_err:   0.006003
[2025-10-06 01:57:58] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -28.354645 | E_var:     0.1142 | E_err:   0.005279
[2025-10-06 01:58:00] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -28.360043 | E_var:     0.1083 | E_err:   0.005142
[2025-10-06 01:58:03] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -28.360458 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 01:58:05] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -28.354632 | E_var:     0.1772 | E_err:   0.006577
[2025-10-06 01:58:08] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -28.366152 | E_var:     0.1244 | E_err:   0.005512
[2025-10-06 01:58:10] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -28.353669 | E_var:     0.1548 | E_err:   0.006148
[2025-10-06 01:58:12] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -28.366722 | E_var:     0.1550 | E_err:   0.006152
[2025-10-06 01:58:15] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -28.359589 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 01:58:17] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -28.360577 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 01:58:20] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -28.359355 | E_var:     0.2683 | E_err:   0.008093
[2025-10-06 01:58:22] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -28.362044 | E_var:     0.1118 | E_err:   0.005225
[2025-10-06 01:58:24] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -28.367054 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 01:58:27] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -28.360683 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 01:58:29] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -28.364266 | E_var:     0.1382 | E_err:   0.005809
[2025-10-06 01:58:32] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -28.356846 | E_var:     0.1118 | E_err:   0.005224
[2025-10-06 01:58:34] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -28.362076 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 01:58:37] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -28.349351 | E_var:     0.1603 | E_err:   0.006256
[2025-10-06 01:58:39] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -28.362252 | E_var:     0.1093 | E_err:   0.005167
[2025-10-06 01:58:41] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -28.362743 | E_var:     0.1109 | E_err:   0.005204
[2025-10-06 01:58:44] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -28.349728 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 01:58:46] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -28.362870 | E_var:     0.1364 | E_err:   0.005771
[2025-10-06 01:58:49] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -28.362332 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 01:58:51] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -28.364792 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 01:58:54] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -28.359563 | E_var:     0.0879 | E_err:   0.004632
[2025-10-06 01:58:56] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -28.363635 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 01:58:58] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -28.356056 | E_var:     0.1245 | E_err:   0.005512
[2025-10-06 01:59:01] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -28.354912 | E_var:     0.1059 | E_err:   0.005086
[2025-10-06 01:59:03] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -28.354202 | E_var:     0.0977 | E_err:   0.004884
[2025-10-06 01:59:06] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -28.352677 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 01:59:08] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -28.359586 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 01:59:10] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -28.361695 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 01:59:13] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -28.358975 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 01:59:15] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -28.364559 | E_var:     0.1129 | E_err:   0.005251
[2025-10-06 01:59:18] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -28.363749 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 01:59:20] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -28.346548 | E_var:     0.1508 | E_err:   0.006069
[2025-10-06 01:59:23] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -28.362940 | E_var:     0.1261 | E_err:   0.005550
[2025-10-06 01:59:25] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -28.350549 | E_var:     0.1010 | E_err:   0.004966
[2025-10-06 01:59:27] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -28.361988 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 01:59:30] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -28.360687 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 01:59:32] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -28.362365 | E_var:     0.1654 | E_err:   0.006355
[2025-10-06 01:59:35] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -28.350130 | E_var:     0.0929 | E_err:   0.004764
[2025-10-06 01:59:37] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -28.360697 | E_var:     0.1145 | E_err:   0.005287
[2025-10-06 01:59:40] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -28.366414 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 01:59:42] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -28.358726 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 01:59:44] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -28.349070 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 01:59:47] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -28.368015 | E_var:     0.1797 | E_err:   0.006624
[2025-10-06 01:59:49] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -28.357964 | E_var:     0.1051 | E_err:   0.005066
[2025-10-06 01:59:52] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -28.350163 | E_var:     0.1229 | E_err:   0.005479
[2025-10-06 01:59:54] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -28.355287 | E_var:     0.1735 | E_err:   0.006508
[2025-10-06 01:59:57] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -28.357146 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 01:59:59] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -28.348087 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 02:00:01] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -28.364193 | E_var:     0.1698 | E_err:   0.006439
[2025-10-06 02:00:04] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -28.358873 | E_var:     0.1369 | E_err:   0.005781
[2025-10-06 02:00:06] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -28.361066 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 02:00:09] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -28.364601 | E_var:     0.0961 | E_err:   0.004843
[2025-10-06 02:00:11] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -28.361124 | E_var:     0.1046 | E_err:   0.005054
[2025-10-06 02:00:13] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -28.356935 | E_var:     0.1525 | E_err:   0.006101
[2025-10-06 02:00:16] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -28.359446 | E_var:     0.1266 | E_err:   0.005561
[2025-10-06 02:00:18] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -28.365734 | E_var:     0.0918 | E_err:   0.004733
[2025-10-06 02:00:21] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -28.363870 | E_var:     0.1173 | E_err:   0.005351
[2025-10-06 02:00:23] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -28.347386 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 02:00:26] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -28.363815 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 02:00:28] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -28.360553 | E_var:     0.1282 | E_err:   0.005595
[2025-10-06 02:00:28] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 02:00:30] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -28.361282 | E_var:     0.2340 | E_err:   0.007558
[2025-10-06 02:00:33] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -28.358320 | E_var:     0.1327 | E_err:   0.005693
[2025-10-06 02:00:35] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -28.358726 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 02:00:38] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -28.364544 | E_var:     0.1188 | E_err:   0.005385
[2025-10-06 02:00:40] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -28.360203 | E_var:     0.1450 | E_err:   0.005950
[2025-10-06 02:00:43] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -28.350672 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 02:00:45] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -28.352853 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 02:00:47] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -28.363012 | E_var:     0.0971 | E_err:   0.004869
[2025-10-06 02:00:50] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -28.357395 | E_var:     0.0952 | E_err:   0.004822
[2025-10-06 02:00:52] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -28.362049 | E_var:     0.1078 | E_err:   0.005129
[2025-10-06 02:00:55] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -28.361199 | E_var:     0.1269 | E_err:   0.005567
[2025-10-06 02:00:57] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -28.358248 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 02:01:00] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -28.354417 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 02:01:02] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -28.353910 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 02:01:04] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -28.357935 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 02:01:07] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -28.356237 | E_var:     0.1046 | E_err:   0.005053
[2025-10-06 02:01:09] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -28.356734 | E_var:     0.1306 | E_err:   0.005647
[2025-10-06 02:01:12] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -28.371914 | E_var:     0.1884 | E_err:   0.006782
[2025-10-06 02:01:14] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -28.363695 | E_var:     0.1131 | E_err:   0.005255
[2025-10-06 02:01:16] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -28.363516 | E_var:     0.1225 | E_err:   0.005470
[2025-10-06 02:01:19] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -28.361063 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 02:01:21] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -28.361551 | E_var:     0.1830 | E_err:   0.006684
[2025-10-06 02:01:24] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -28.360203 | E_var:     0.1202 | E_err:   0.005418
[2025-10-06 02:01:26] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -28.361533 | E_var:     0.1051 | E_err:   0.005064
[2025-10-06 02:01:29] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -28.363754 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 02:01:31] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -28.360434 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 02:01:33] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -28.358039 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 02:01:36] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -28.360763 | E_var:     0.0980 | E_err:   0.004892
[2025-10-06 02:01:38] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -28.351563 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 02:01:41] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -28.354937 | E_var:     0.1185 | E_err:   0.005379
[2025-10-06 02:01:43] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -28.350429 | E_var:     0.1668 | E_err:   0.006382
[2025-10-06 02:01:46] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -28.366045 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 02:01:48] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -28.354430 | E_var:     0.1165 | E_err:   0.005332
[2025-10-06 02:01:50] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -28.358965 | E_var:     0.1225 | E_err:   0.005469
[2025-10-06 02:01:53] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -28.363787 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 02:01:55] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -28.360284 | E_var:     0.1674 | E_err:   0.006393
[2025-10-06 02:01:58] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -28.371581 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 02:02:00] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -28.347274 | E_var:     0.2464 | E_err:   0.007756
[2025-10-06 02:02:02] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -28.363899 | E_var:     0.1289 | E_err:   0.005609
[2025-10-06 02:02:05] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -28.357340 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 02:02:07] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -28.359430 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 02:02:10] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -28.365081 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 02:02:12] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -28.360752 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 02:02:15] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -28.363555 | E_var:     0.1218 | E_err:   0.005453
[2025-10-06 02:02:17] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -28.366278 | E_var:     0.1144 | E_err:   0.005284
[2025-10-06 02:02:19] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -28.367777 | E_var:     0.1522 | E_err:   0.006095
[2025-10-06 02:02:22] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -28.362929 | E_var:     0.1108 | E_err:   0.005201
[2025-10-06 02:02:24] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -28.356989 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 02:02:27] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -28.365296 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 02:02:29] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -28.367468 | E_var:     0.1296 | E_err:   0.005624
[2025-10-06 02:02:29] 🔄 RESTART #2 | Period: 600
[2025-10-06 02:02:32] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -28.361743 | E_var:     0.1160 | E_err:   0.005322
[2025-10-06 02:02:34] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -28.358579 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 02:02:36] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -28.364987 | E_var:     0.1320 | E_err:   0.005676
[2025-10-06 02:02:39] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -28.362925 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 02:02:41] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -28.353692 | E_var:     0.1334 | E_err:   0.005706
[2025-10-06 02:02:44] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -28.361708 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 02:02:46] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -28.352501 | E_var:     0.1250 | E_err:   0.005523
[2025-10-06 02:02:49] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -28.356757 | E_var:     0.0994 | E_err:   0.004927
[2025-10-06 02:02:51] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -28.356577 | E_var:     0.1353 | E_err:   0.005748
[2025-10-06 02:02:53] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -28.361416 | E_var:     0.1317 | E_err:   0.005670
[2025-10-06 02:02:56] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -28.358442 | E_var:     0.1178 | E_err:   0.005364
[2025-10-06 02:02:58] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -28.359049 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 02:03:01] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -28.357117 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 02:03:03] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -28.360998 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 02:03:05] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -28.359805 | E_var:     0.1059 | E_err:   0.005086
[2025-10-06 02:03:08] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -28.369621 | E_var:     0.1204 | E_err:   0.005422
[2025-10-06 02:03:10] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -28.356651 | E_var:     0.1234 | E_err:   0.005488
[2025-10-06 02:03:13] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -28.365719 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 02:03:15] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -28.364813 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 02:03:18] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -28.361906 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 02:03:20] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -28.364665 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 02:03:22] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -28.361576 | E_var:     0.0960 | E_err:   0.004842
[2025-10-06 02:03:25] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -28.358290 | E_var:     0.1363 | E_err:   0.005769
[2025-10-06 02:03:27] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -28.357955 | E_var:     0.1198 | E_err:   0.005407
[2025-10-06 02:03:30] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -28.357711 | E_var:     0.1259 | E_err:   0.005545
[2025-10-06 02:03:32] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -28.369505 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 02:03:35] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -28.364123 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 02:03:37] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -28.351703 | E_var:     0.1280 | E_err:   0.005590
[2025-10-06 02:03:39] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -28.363336 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 02:03:42] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -28.358792 | E_var:     0.1033 | E_err:   0.005023
[2025-10-06 02:03:44] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -28.356133 | E_var:     0.1318 | E_err:   0.005672
[2025-10-06 02:03:47] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -28.355187 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 02:03:49] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -28.348464 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 02:03:52] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -28.359792 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 02:03:54] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -28.362045 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 02:03:56] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -28.359017 | E_var:     0.1814 | E_err:   0.006654
[2025-10-06 02:03:59] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -28.366207 | E_var:     0.1123 | E_err:   0.005237
[2025-10-06 02:04:01] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -28.359649 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 02:04:04] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -28.360216 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 02:04:06] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -28.350808 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 02:04:08] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -28.357521 | E_var:     0.1236 | E_err:   0.005494
[2025-10-06 02:04:11] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -28.360541 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 02:04:13] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -28.353775 | E_var:     0.1653 | E_err:   0.006353
[2025-10-06 02:04:16] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -28.365311 | E_var:     0.1309 | E_err:   0.005652
[2025-10-06 02:04:18] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -28.350434 | E_var:     0.1348 | E_err:   0.005737
[2025-10-06 02:04:21] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -28.361770 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 02:04:23] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -28.360246 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 02:04:25] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -28.361332 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 02:04:28] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -28.355086 | E_var:     0.1528 | E_err:   0.006108
[2025-10-06 02:04:30] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -28.366918 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 02:04:30] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 02:04:33] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -28.355956 | E_var:     0.1337 | E_err:   0.005713
[2025-10-06 02:04:35] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -28.369136 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 02:04:38] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -28.363523 | E_var:     0.1163 | E_err:   0.005328
[2025-10-06 02:04:40] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -28.364324 | E_var:     0.0996 | E_err:   0.004930
[2025-10-06 02:04:42] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -28.360000 | E_var:     0.1198 | E_err:   0.005409
[2025-10-06 02:04:45] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -28.354594 | E_var:     0.1012 | E_err:   0.004972
[2025-10-06 02:04:47] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -28.356362 | E_var:     0.0992 | E_err:   0.004922
[2025-10-06 02:04:50] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -28.371437 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 02:04:52] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -28.355844 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 02:04:54] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -28.358625 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 02:04:57] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -28.352604 | E_var:     0.2011 | E_err:   0.007007
[2025-10-06 02:04:59] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -28.363245 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 02:05:02] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -28.360932 | E_var:     0.1240 | E_err:   0.005501
[2025-10-06 02:05:04] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -28.362151 | E_var:     0.1249 | E_err:   0.005522
[2025-10-06 02:05:07] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -28.366722 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 02:05:09] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -28.356302 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 02:05:11] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -28.359429 | E_var:     0.1281 | E_err:   0.005592
[2025-10-06 02:05:14] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -28.353929 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 02:05:16] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -28.357042 | E_var:     0.1297 | E_err:   0.005627
[2025-10-06 02:05:19] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -28.370593 | E_var:     0.1137 | E_err:   0.005268
[2025-10-06 02:05:21] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -28.349869 | E_var:     0.1123 | E_err:   0.005237
[2025-10-06 02:05:24] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -28.360423 | E_var:     0.1191 | E_err:   0.005391
[2025-10-06 02:05:26] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -28.364538 | E_var:     0.1129 | E_err:   0.005250
[2025-10-06 02:05:28] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -28.356677 | E_var:     0.1088 | E_err:   0.005153
[2025-10-06 02:05:31] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -28.359014 | E_var:     0.1314 | E_err:   0.005664
[2025-10-06 02:05:33] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -28.358304 | E_var:     0.1126 | E_err:   0.005244
[2025-10-06 02:05:36] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -28.355574 | E_var:     0.1189 | E_err:   0.005388
[2025-10-06 02:05:38] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -28.360653 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 02:05:40] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -28.353891 | E_var:     0.1602 | E_err:   0.006254
[2025-10-06 02:05:43] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -28.366613 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 02:05:45] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -28.358361 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 02:05:48] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -28.352452 | E_var:     0.1085 | E_err:   0.005147
[2025-10-06 02:05:50] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -28.356576 | E_var:     0.1131 | E_err:   0.005256
[2025-10-06 02:05:53] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -28.356005 | E_var:     0.1263 | E_err:   0.005553
[2025-10-06 02:05:55] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -28.355220 | E_var:     0.1110 | E_err:   0.005207
[2025-10-06 02:05:57] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -28.364294 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 02:06:00] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -28.364773 | E_var:     0.1023 | E_err:   0.004999
[2025-10-06 02:06:02] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -28.358431 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 02:06:05] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -28.358140 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 02:06:07] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -28.361249 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 02:06:10] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -28.368107 | E_var:     0.1001 | E_err:   0.004942
[2025-10-06 02:06:12] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -28.355657 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 02:06:14] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -28.345740 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 02:06:17] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -28.354999 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 02:06:19] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -28.365546 | E_var:     0.1351 | E_err:   0.005743
[2025-10-06 02:06:22] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -28.361067 | E_var:     0.1060 | E_err:   0.005086
[2025-10-06 02:06:24] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -28.366296 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 02:06:27] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -28.361026 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 02:06:29] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -28.365923 | E_var:     0.2035 | E_err:   0.007048
[2025-10-06 02:06:31] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -28.368088 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 02:06:34] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -28.351806 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 02:06:36] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -28.361096 | E_var:     0.1243 | E_err:   0.005509
[2025-10-06 02:06:39] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -28.358640 | E_var:     0.1072 | E_err:   0.005117
[2025-10-06 02:06:41] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -28.352188 | E_var:     0.1828 | E_err:   0.006680
[2025-10-06 02:06:44] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -28.368703 | E_var:     0.1111 | E_err:   0.005207
[2025-10-06 02:06:46] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -28.358900 | E_var:     0.1999 | E_err:   0.006987
[2025-10-06 02:06:48] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -28.356022 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 02:06:51] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -28.356400 | E_var:     0.1197 | E_err:   0.005405
[2025-10-06 02:06:53] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -28.358707 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 02:06:56] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -28.359606 | E_var:     0.1188 | E_err:   0.005387
[2025-10-06 02:06:58] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -28.360647 | E_var:     0.1353 | E_err:   0.005748
[2025-10-06 02:07:01] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -28.353781 | E_var:     0.1669 | E_err:   0.006383
[2025-10-06 02:07:03] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -28.368179 | E_var:     0.1405 | E_err:   0.005858
[2025-10-06 02:07:05] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -28.359606 | E_var:     0.1083 | E_err:   0.005142
[2025-10-06 02:07:08] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -28.355399 | E_var:     0.1114 | E_err:   0.005215
[2025-10-06 02:07:10] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -28.364542 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 02:07:13] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -28.362169 | E_var:     0.1266 | E_err:   0.005559
[2025-10-06 02:07:15] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -28.360687 | E_var:     0.1180 | E_err:   0.005366
[2025-10-06 02:07:17] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -28.356092 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 02:07:20] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -28.358216 | E_var:     0.1080 | E_err:   0.005134
[2025-10-06 02:07:22] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -28.356713 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 02:07:25] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -28.355005 | E_var:     0.1242 | E_err:   0.005506
[2025-10-06 02:07:27] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -28.370634 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 02:07:30] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -28.359160 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 02:07:32] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -28.362523 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 02:07:34] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -28.360780 | E_var:     0.1145 | E_err:   0.005287
[2025-10-06 02:07:37] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -28.361230 | E_var:     0.1313 | E_err:   0.005663
[2025-10-06 02:07:39] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -28.364908 | E_var:     0.1216 | E_err:   0.005449
[2025-10-06 02:07:42] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -28.362876 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 02:07:44] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -28.361414 | E_var:     0.1165 | E_err:   0.005332
[2025-10-06 02:07:46] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -28.358749 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 02:07:49] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -28.349110 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 02:07:51] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -28.361310 | E_var:     0.1183 | E_err:   0.005375
[2025-10-06 02:07:54] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -28.360450 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 02:07:56] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -28.359365 | E_var:     0.1035 | E_err:   0.005027
[2025-10-06 02:07:59] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -28.366182 | E_var:     0.1149 | E_err:   0.005296
[2025-10-06 02:08:01] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -28.362712 | E_var:     0.1156 | E_err:   0.005312
[2025-10-06 02:08:03] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -28.359276 | E_var:     0.1216 | E_err:   0.005448
[2025-10-06 02:08:06] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -28.356386 | E_var:     0.1150 | E_err:   0.005298
[2025-10-06 02:08:08] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -28.361473 | E_var:     0.1130 | E_err:   0.005252
[2025-10-06 02:08:11] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -28.359478 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 02:08:13] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -28.360138 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 02:08:16] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -28.360836 | E_var:     0.1091 | E_err:   0.005160
[2025-10-06 02:08:18] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -28.362068 | E_var:     0.1335 | E_err:   0.005708
[2025-10-06 02:08:20] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -28.354714 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 02:08:23] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -28.362475 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 02:08:25] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -28.356633 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 02:08:28] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -28.346396 | E_var:     0.1162 | E_err:   0.005327
[2025-10-06 02:08:30] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -28.359170 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 02:08:33] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -28.355717 | E_var:     0.1329 | E_err:   0.005697
[2025-10-06 02:08:33] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 02:08:35] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -28.353636 | E_var:     0.1074 | E_err:   0.005121
[2025-10-06 02:08:37] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -28.367122 | E_var:     0.1165 | E_err:   0.005332
[2025-10-06 02:08:40] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -28.362249 | E_var:     0.2664 | E_err:   0.008065
[2025-10-06 02:08:42] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -28.351940 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 02:08:45] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -28.360132 | E_var:     0.1270 | E_err:   0.005569
[2025-10-06 02:08:47] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -28.356226 | E_var:     0.1015 | E_err:   0.004979
[2025-10-06 02:08:50] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -28.360713 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 02:08:52] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -28.360694 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 02:08:54] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -28.359551 | E_var:     0.1110 | E_err:   0.005207
[2025-10-06 02:08:57] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -28.362972 | E_var:     0.1117 | E_err:   0.005223
[2025-10-06 02:08:59] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -28.357466 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 02:09:02] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -28.358549 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 02:09:04] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -28.368283 | E_var:     0.1306 | E_err:   0.005646
[2025-10-06 02:09:07] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -28.360901 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 02:09:09] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -28.358828 | E_var:     0.1484 | E_err:   0.006019
[2025-10-06 02:09:11] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -28.357249 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 02:09:14] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -28.366994 | E_var:     0.1125 | E_err:   0.005242
[2025-10-06 02:09:16] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -28.369785 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 02:09:19] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -28.370546 | E_var:     0.1128 | E_err:   0.005247
[2025-10-06 02:09:21] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -28.358681 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 02:09:24] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -28.355360 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 02:09:26] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -28.366835 | E_var:     0.1133 | E_err:   0.005258
[2025-10-06 02:09:28] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -28.354035 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 02:09:31] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -28.370721 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 02:09:33] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -28.364126 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 02:09:36] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -28.358631 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 02:09:38] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -28.354707 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 02:09:41] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -28.351595 | E_var:     0.1121 | E_err:   0.005232
[2025-10-06 02:09:43] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -28.361046 | E_var:     0.1410 | E_err:   0.005867
[2025-10-06 02:09:45] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -28.360532 | E_var:     0.1173 | E_err:   0.005350
[2025-10-06 02:09:48] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -28.357490 | E_var:     0.1065 | E_err:   0.005098
[2025-10-06 02:09:50] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -28.361676 | E_var:     0.1468 | E_err:   0.005987
[2025-10-06 02:09:53] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -28.366271 | E_var:     0.1025 | E_err:   0.005002
[2025-10-06 02:09:55] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -28.363985 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 02:09:58] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -28.365048 | E_var:     0.1295 | E_err:   0.005623
[2025-10-06 02:10:00] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -28.362891 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 02:10:02] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -28.359218 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 02:10:05] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -28.353006 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 02:10:07] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -28.350287 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 02:10:10] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -28.367008 | E_var:     0.1355 | E_err:   0.005751
[2025-10-06 02:10:12] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -28.368572 | E_var:     0.1608 | E_err:   0.006265
[2025-10-06 02:10:15] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -28.360556 | E_var:     0.1318 | E_err:   0.005673
[2025-10-06 02:10:17] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -28.362333 | E_var:     0.1232 | E_err:   0.005484
[2025-10-06 02:10:19] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -28.353296 | E_var:     0.1127 | E_err:   0.005244
[2025-10-06 02:10:22] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -28.353446 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 02:10:24] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -28.361141 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 02:10:27] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -28.371034 | E_var:     0.1111 | E_err:   0.005209
[2025-10-06 02:10:29] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -28.358554 | E_var:     0.1368 | E_err:   0.005780
[2025-10-06 02:10:31] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -28.364195 | E_var:     0.0844 | E_err:   0.004539
[2025-10-06 02:10:34] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -28.358514 | E_var:     0.1147 | E_err:   0.005292
[2025-10-06 02:10:36] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -28.364936 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 02:10:39] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -28.351401 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 02:10:41] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -28.358576 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 02:10:44] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -28.358038 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 02:10:46] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -28.363216 | E_var:     0.1351 | E_err:   0.005743
[2025-10-06 02:10:48] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -28.355762 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 02:10:51] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -28.354305 | E_var:     0.0973 | E_err:   0.004875
[2025-10-06 02:10:53] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -28.357429 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 02:10:56] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -28.355688 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 02:10:58] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -28.351808 | E_var:     0.1201 | E_err:   0.005416
[2025-10-06 02:11:00] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -28.364907 | E_var:     0.1158 | E_err:   0.005318
[2025-10-06 02:11:03] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -28.365343 | E_var:     0.1366 | E_err:   0.005776
[2025-10-06 02:11:05] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -28.354796 | E_var:     0.0965 | E_err:   0.004855
[2025-10-06 02:11:08] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -28.354089 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 02:11:10] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -28.360930 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 02:11:13] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -28.355722 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 02:11:15] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -28.364767 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 02:11:17] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -28.358168 | E_var:     0.1221 | E_err:   0.005460
[2025-10-06 02:11:20] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -28.350684 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 02:11:22] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -28.370669 | E_var:     0.1191 | E_err:   0.005392
[2025-10-06 02:11:25] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -28.355010 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 02:11:27] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -28.360104 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 02:11:30] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -28.362982 | E_var:     0.1325 | E_err:   0.005688
[2025-10-06 02:11:32] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -28.350680 | E_var:     0.1253 | E_err:   0.005532
[2025-10-06 02:11:34] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -28.357022 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 02:11:37] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -28.354097 | E_var:     0.1096 | E_err:   0.005174
[2025-10-06 02:11:39] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -28.349847 | E_var:     0.1287 | E_err:   0.005604
[2025-10-06 02:11:42] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -28.365937 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 02:11:44] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -28.356476 | E_var:     0.1314 | E_err:   0.005664
[2025-10-06 02:11:47] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -28.358482 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 02:11:49] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -28.362357 | E_var:     0.0884 | E_err:   0.004647
[2025-10-06 02:11:51] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -28.366852 | E_var:     0.1369 | E_err:   0.005780
[2025-10-06 02:11:54] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -28.365685 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 02:11:56] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -28.352864 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 02:11:59] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -28.358512 | E_var:     0.1454 | E_err:   0.005959
[2025-10-06 02:12:01] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -28.360303 | E_var:     0.0946 | E_err:   0.004807
[2025-10-06 02:12:04] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -28.357741 | E_var:     0.1314 | E_err:   0.005664
[2025-10-06 02:12:06] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -28.357799 | E_var:     0.1221 | E_err:   0.005459
[2025-10-06 02:12:08] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -28.365754 | E_var:     0.1248 | E_err:   0.005520
[2025-10-06 02:12:11] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -28.367000 | E_var:     0.1080 | E_err:   0.005134
[2025-10-06 02:12:13] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -28.357567 | E_var:     0.1108 | E_err:   0.005201
[2025-10-06 02:12:16] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -28.361527 | E_var:     0.0968 | E_err:   0.004861
[2025-10-06 02:12:18] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -28.355827 | E_var:     0.0919 | E_err:   0.004735
[2025-10-06 02:12:20] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -28.355296 | E_var:     0.1120 | E_err:   0.005229
[2025-10-06 02:12:23] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -28.356769 | E_var:     0.1108 | E_err:   0.005201
[2025-10-06 02:12:25] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -28.354961 | E_var:     0.1191 | E_err:   0.005392
[2025-10-06 02:12:28] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -28.357110 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 02:12:30] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -28.362010 | E_var:     0.1318 | E_err:   0.005672
[2025-10-06 02:12:33] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -28.357871 | E_var:     0.1187 | E_err:   0.005383
[2025-10-06 02:12:35] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -28.359214 | E_var:     0.0771 | E_err:   0.004338
[2025-10-06 02:12:35] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 02:12:37] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -28.359283 | E_var:     0.1338 | E_err:   0.005716
[2025-10-06 02:12:40] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -28.361517 | E_var:     0.1117 | E_err:   0.005222
[2025-10-06 02:12:42] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -28.368695 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 02:12:45] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -28.356263 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 02:12:47] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -28.351799 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 02:12:50] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -28.356336 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 02:12:52] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -28.356925 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 02:12:54] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -28.353498 | E_var:     0.1158 | E_err:   0.005316
[2025-10-06 02:12:57] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -28.358479 | E_var:     0.1116 | E_err:   0.005221
[2025-10-06 02:12:59] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -28.362750 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 02:13:02] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -28.359886 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 02:13:04] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -28.358105 | E_var:     0.1377 | E_err:   0.005799
[2025-10-06 02:13:07] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -28.361264 | E_var:     0.0920 | E_err:   0.004739
[2025-10-06 02:13:09] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -28.363108 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 02:13:11] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -28.359423 | E_var:     0.1163 | E_err:   0.005329
[2025-10-06 02:13:14] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -28.357483 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 02:13:16] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -28.351886 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 02:13:19] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -28.357077 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 02:13:21] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -28.363168 | E_var:     0.1404 | E_err:   0.005855
[2025-10-06 02:13:24] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -28.352959 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 02:13:26] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -28.353951 | E_var:     0.0994 | E_err:   0.004926
[2025-10-06 02:13:28] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -28.360134 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 02:13:31] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -28.363297 | E_var:     0.1162 | E_err:   0.005327
[2025-10-06 02:13:33] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -28.367632 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 02:13:36] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -28.353934 | E_var:     0.1273 | E_err:   0.005576
[2025-10-06 02:13:38] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -28.364144 | E_var:     0.1134 | E_err:   0.005261
[2025-10-06 02:13:41] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -28.349270 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 02:13:43] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -28.354406 | E_var:     0.1414 | E_err:   0.005877
[2025-10-06 02:13:45] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -28.367448 | E_var:     0.1359 | E_err:   0.005759
[2025-10-06 02:13:48] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -28.355934 | E_var:     0.1301 | E_err:   0.005637
[2025-10-06 02:13:50] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -28.356313 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 02:13:53] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -28.356653 | E_var:     0.1281 | E_err:   0.005592
[2025-10-06 02:13:55] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -28.347980 | E_var:     0.1666 | E_err:   0.006377
[2025-10-06 02:13:57] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -28.352212 | E_var:     0.1200 | E_err:   0.005414
[2025-10-06 02:14:00] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -28.356932 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 02:14:02] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -28.372883 | E_var:     0.1046 | E_err:   0.005052
[2025-10-06 02:14:05] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -28.357867 | E_var:     0.1047 | E_err:   0.005057
[2025-10-06 02:14:07] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -28.360072 | E_var:     0.1238 | E_err:   0.005497
[2025-10-06 02:14:10] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -28.353192 | E_var:     0.0976 | E_err:   0.004880
[2025-10-06 02:14:12] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -28.364490 | E_var:     0.1339 | E_err:   0.005718
[2025-10-06 02:14:14] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -28.345310 | E_var:     0.1601 | E_err:   0.006251
[2025-10-06 02:14:17] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -28.356903 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 02:14:19] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -28.349454 | E_var:     0.1322 | E_err:   0.005682
[2025-10-06 02:14:22] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -28.369218 | E_var:     0.2978 | E_err:   0.008526
[2025-10-06 02:14:24] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -28.357499 | E_var:     0.1284 | E_err:   0.005599
[2025-10-06 02:14:26] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -28.362344 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 02:14:29] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -28.365811 | E_var:     0.1717 | E_err:   0.006474
[2025-10-06 02:14:31] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -28.362050 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 02:14:34] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -28.368649 | E_var:     0.1164 | E_err:   0.005332
[2025-10-06 02:14:36] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -28.358715 | E_var:     0.1046 | E_err:   0.005054
[2025-10-06 02:14:39] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -28.362779 | E_var:     0.1358 | E_err:   0.005759
[2025-10-06 02:14:41] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -28.360552 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 02:14:44] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -28.354553 | E_var:     0.1446 | E_err:   0.005942
[2025-10-06 02:14:46] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -28.366510 | E_var:     0.1248 | E_err:   0.005519
[2025-10-06 02:14:48] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -28.364458 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 02:14:51] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -28.356021 | E_var:     0.1017 | E_err:   0.004982
[2025-10-06 02:14:53] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -28.358521 | E_var:     0.1298 | E_err:   0.005630
[2025-10-06 02:14:56] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -28.355521 | E_var:     0.1245 | E_err:   0.005512
[2025-10-06 02:14:58] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -28.362050 | E_var:     0.1122 | E_err:   0.005234
[2025-10-06 02:15:00] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -28.366442 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 02:15:03] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -28.369148 | E_var:     0.1388 | E_err:   0.005820
[2025-10-06 02:15:05] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -28.358271 | E_var:     0.1072 | E_err:   0.005117
[2025-10-06 02:15:08] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -28.361862 | E_var:     0.1463 | E_err:   0.005976
[2025-10-06 02:15:10] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -28.350815 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 02:15:13] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -28.351078 | E_var:     0.1289 | E_err:   0.005609
[2025-10-06 02:15:15] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -28.356845 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 02:15:17] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -28.355641 | E_var:     0.2021 | E_err:   0.007025
[2025-10-06 02:15:20] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -28.360830 | E_var:     0.1175 | E_err:   0.005357
[2025-10-06 02:15:22] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -28.350403 | E_var:     0.1290 | E_err:   0.005611
[2025-10-06 02:15:25] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -28.364424 | E_var:     0.2152 | E_err:   0.007249
[2025-10-06 02:15:27] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -28.366370 | E_var:     0.2227 | E_err:   0.007374
[2025-10-06 02:15:29] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -28.360512 | E_var:     0.1282 | E_err:   0.005594
[2025-10-06 02:15:32] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -28.361561 | E_var:     0.1370 | E_err:   0.005784
[2025-10-06 02:15:34] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -28.360026 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 02:15:37] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -28.358228 | E_var:     0.1272 | E_err:   0.005572
[2025-10-06 02:15:39] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -28.358539 | E_var:     0.1512 | E_err:   0.006077
[2025-10-06 02:15:42] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -28.368239 | E_var:     0.1212 | E_err:   0.005439
[2025-10-06 02:15:44] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -28.361547 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 02:15:46] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -28.362701 | E_var:     0.1070 | E_err:   0.005110
[2025-10-06 02:15:49] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -28.360489 | E_var:     0.1514 | E_err:   0.006079
[2025-10-06 02:15:51] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -28.369615 | E_var:     0.1392 | E_err:   0.005830
[2025-10-06 02:15:54] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -28.344698 | E_var:     0.2146 | E_err:   0.007237
[2025-10-06 02:15:56] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -28.365827 | E_var:     0.1086 | E_err:   0.005150
[2025-10-06 02:15:59] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -28.369192 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 02:16:01] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -28.372190 | E_var:     0.1456 | E_err:   0.005962
[2025-10-06 02:16:03] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -28.367854 | E_var:     0.1502 | E_err:   0.006055
[2025-10-06 02:16:06] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -28.358246 | E_var:     0.1204 | E_err:   0.005422
[2025-10-06 02:16:08] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -28.367216 | E_var:     0.5386 | E_err:   0.011467
[2025-10-06 02:16:11] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -28.360935 | E_var:     0.1300 | E_err:   0.005634
[2025-10-06 02:16:13] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -28.347684 | E_var:     0.1508 | E_err:   0.006067
[2025-10-06 02:16:16] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -28.363444 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 02:16:18] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -28.361353 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 02:16:20] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -28.358519 | E_var:     0.1372 | E_err:   0.005788
[2025-10-06 02:16:23] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -28.369545 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 02:16:25] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -28.363665 | E_var:     0.1408 | E_err:   0.005863
[2025-10-06 02:16:28] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -28.365039 | E_var:     0.1087 | E_err:   0.005151
[2025-10-06 02:16:30] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -28.361746 | E_var:     0.1254 | E_err:   0.005534
[2025-10-06 02:16:32] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -28.361840 | E_var:     0.2379 | E_err:   0.007621
[2025-10-06 02:16:35] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -28.359187 | E_var:     0.1294 | E_err:   0.005620
[2025-10-06 02:16:37] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -28.351330 | E_var:     0.1676 | E_err:   0.006397
[2025-10-06 02:16:37] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 02:16:40] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -28.358201 | E_var:     0.1338 | E_err:   0.005716
[2025-10-06 02:16:42] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -28.363217 | E_var:     0.0862 | E_err:   0.004586
[2025-10-06 02:16:45] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -28.363612 | E_var:     0.2519 | E_err:   0.007842
[2025-10-06 02:16:47] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -28.360900 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 02:16:49] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -28.370623 | E_var:     0.1370 | E_err:   0.005784
[2025-10-06 02:16:52] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -28.354104 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 02:16:54] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -28.354137 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 02:16:57] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -28.360375 | E_var:     0.1371 | E_err:   0.005785
[2025-10-06 02:16:59] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -28.363848 | E_var:     0.1136 | E_err:   0.005266
[2025-10-06 02:17:02] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -28.357807 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 02:17:04] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -28.357861 | E_var:     0.1084 | E_err:   0.005145
[2025-10-06 02:17:06] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -28.362134 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 02:17:09] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -28.361312 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 02:17:11] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -28.347361 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 02:17:14] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -28.364085 | E_var:     0.1298 | E_err:   0.005628
[2025-10-06 02:17:16] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -28.361363 | E_var:     0.1411 | E_err:   0.005868
[2025-10-06 02:17:18] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -28.360334 | E_var:     0.1104 | E_err:   0.005192
[2025-10-06 02:17:21] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -28.360091 | E_var:     0.1118 | E_err:   0.005225
[2025-10-06 02:17:23] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -28.358684 | E_var:     0.1572 | E_err:   0.006194
[2025-10-06 02:17:26] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -28.356686 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 02:17:28] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -28.354394 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 02:17:31] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -28.359329 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 02:17:33] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -28.351997 | E_var:     0.1309 | E_err:   0.005653
[2025-10-06 02:17:35] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -28.359089 | E_var:     0.1021 | E_err:   0.004994
[2025-10-06 02:17:38] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -28.355710 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 02:17:40] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -28.357058 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 02:17:43] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -28.359899 | E_var:     0.2159 | E_err:   0.007259
[2025-10-06 02:17:45] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -28.359502 | E_var:     0.1141 | E_err:   0.005279
[2025-10-06 02:17:48] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -28.362929 | E_var:     0.1190 | E_err:   0.005391
[2025-10-06 02:17:50] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -28.351217 | E_var:     0.1065 | E_err:   0.005099
[2025-10-06 02:17:52] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -28.359092 | E_var:     0.1605 | E_err:   0.006260
[2025-10-06 02:17:55] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -28.358452 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 02:17:57] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -28.355332 | E_var:     0.1248 | E_err:   0.005519
[2025-10-06 02:18:00] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -28.372805 | E_var:     0.2390 | E_err:   0.007639
[2025-10-06 02:18:02] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -28.362456 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 02:18:05] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -28.362950 | E_var:     0.1426 | E_err:   0.005901
[2025-10-06 02:18:07] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -28.361934 | E_var:     0.1104 | E_err:   0.005191
[2025-10-06 02:18:09] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -28.357899 | E_var:     0.1253 | E_err:   0.005531
[2025-10-06 02:18:12] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -28.367623 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 02:18:14] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -28.363421 | E_var:     0.1968 | E_err:   0.006931
[2025-10-06 02:18:17] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -28.354534 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 02:18:19] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -28.356105 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 02:18:21] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -28.358952 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 02:18:24] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -28.354881 | E_var:     0.1191 | E_err:   0.005392
[2025-10-06 02:18:26] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -28.367102 | E_var:     0.3970 | E_err:   0.009845
[2025-10-06 02:18:29] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -28.358845 | E_var:     0.1120 | E_err:   0.005228
[2025-10-06 02:18:31] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -28.362056 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 02:18:34] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -28.362317 | E_var:     0.0913 | E_err:   0.004720
[2025-10-06 02:18:36] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -28.354775 | E_var:     0.1729 | E_err:   0.006497
[2025-10-06 02:18:38] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -28.362371 | E_var:     0.1236 | E_err:   0.005494
[2025-10-06 02:18:41] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -28.352746 | E_var:     0.1091 | E_err:   0.005160
[2025-10-06 02:18:43] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -28.359379 | E_var:     0.1526 | E_err:   0.006104
[2025-10-06 02:18:46] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -28.355946 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 02:18:48] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -28.359076 | E_var:     0.1317 | E_err:   0.005670
[2025-10-06 02:18:51] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -28.362452 | E_var:     0.2521 | E_err:   0.007846
[2025-10-06 02:18:53] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -28.355724 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 02:18:55] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -28.365086 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 02:18:58] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -28.357772 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 02:19:00] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -28.361235 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 02:19:03] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -28.360839 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 02:19:05] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -28.357681 | E_var:     0.1150 | E_err:   0.005299
[2025-10-06 02:19:08] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -28.370230 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 02:19:10] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -28.361469 | E_var:     0.1021 | E_err:   0.004993
[2025-10-06 02:19:12] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -28.349428 | E_var:     0.1694 | E_err:   0.006431
[2025-10-06 02:19:15] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -28.364464 | E_var:     0.1149 | E_err:   0.005296
[2025-10-06 02:19:17] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -28.358053 | E_var:     0.1001 | E_err:   0.004943
[2025-10-06 02:19:20] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -28.358445 | E_var:     0.1227 | E_err:   0.005474
[2025-10-06 02:19:22] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -28.362495 | E_var:     0.1293 | E_err:   0.005618
[2025-10-06 02:19:25] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -28.361086 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 02:19:27] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -28.358089 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 02:19:29] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -28.357253 | E_var:     0.1100 | E_err:   0.005182
[2025-10-06 02:19:32] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -28.356553 | E_var:     0.0886 | E_err:   0.004651
[2025-10-06 02:19:34] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -28.355837 | E_var:     0.1276 | E_err:   0.005581
[2025-10-06 02:19:37] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -28.358367 | E_var:     0.1220 | E_err:   0.005458
[2025-10-06 02:19:39] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -28.362512 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 02:19:42] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -28.361094 | E_var:     0.1123 | E_err:   0.005236
[2025-10-06 02:19:44] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -28.360352 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 02:19:46] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -28.363781 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 02:19:49] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -28.365243 | E_var:     0.1224 | E_err:   0.005467
[2025-10-06 02:19:51] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -28.368503 | E_var:     0.1488 | E_err:   0.006028
[2025-10-06 02:19:54] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -28.363859 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 02:19:56] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -28.364633 | E_var:     0.1611 | E_err:   0.006272
[2025-10-06 02:19:58] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -28.363948 | E_var:     0.1537 | E_err:   0.006126
[2025-10-06 02:20:01] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -28.364530 | E_var:     0.1461 | E_err:   0.005973
[2025-10-06 02:20:03] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -28.365500 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 02:20:06] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -28.350392 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 02:20:08] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -28.359077 | E_var:     0.1273 | E_err:   0.005574
[2025-10-06 02:20:11] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -28.358565 | E_var:     0.1193 | E_err:   0.005396
[2025-10-06 02:20:13] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -28.359800 | E_var:     0.1042 | E_err:   0.005045
[2025-10-06 02:20:15] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -28.361390 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 02:20:18] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -28.362871 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 02:20:20] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -28.362773 | E_var:     0.1264 | E_err:   0.005556
[2025-10-06 02:20:23] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -28.358089 | E_var:     0.1511 | E_err:   0.006074
[2025-10-06 02:20:25] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -28.357276 | E_var:     0.0758 | E_err:   0.004302
[2025-10-06 02:20:28] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -28.357691 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 02:20:30] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -28.359363 | E_var:     0.1429 | E_err:   0.005907
[2025-10-06 02:20:32] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -28.362262 | E_var:     0.1389 | E_err:   0.005823
[2025-10-06 02:20:35] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -28.363693 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 02:20:37] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -28.369606 | E_var:     0.1273 | E_err:   0.005576
[2025-10-06 02:20:40] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -28.358787 | E_var:     0.1147 | E_err:   0.005292
[2025-10-06 02:20:40] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 02:20:42] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -28.352295 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 02:20:44] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -28.367591 | E_var:     0.1535 | E_err:   0.006122
[2025-10-06 02:20:47] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -28.361925 | E_var:     0.1217 | E_err:   0.005451
[2025-10-06 02:20:49] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -28.361014 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 02:20:52] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -28.350352 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 02:20:54] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -28.359248 | E_var:     0.0941 | E_err:   0.004793
[2025-10-06 02:20:57] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -28.358529 | E_var:     0.1016 | E_err:   0.004980
[2025-10-06 02:20:59] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -28.352777 | E_var:     0.2541 | E_err:   0.007877
[2025-10-06 02:21:01] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -28.361947 | E_var:     0.1427 | E_err:   0.005901
[2025-10-06 02:21:04] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -28.362930 | E_var:     0.1145 | E_err:   0.005288
[2025-10-06 02:21:06] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -28.356864 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 02:21:09] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -28.352842 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 02:21:11] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -28.362664 | E_var:     0.1212 | E_err:   0.005441
[2025-10-06 02:21:14] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -28.371084 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 02:21:16] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -28.353787 | E_var:     0.0885 | E_err:   0.004648
[2025-10-06 02:21:18] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -28.362762 | E_var:     0.1416 | E_err:   0.005879
[2025-10-06 02:21:21] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -28.361372 | E_var:     0.1501 | E_err:   0.006053
[2025-10-06 02:21:23] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -28.354708 | E_var:     0.1583 | E_err:   0.006217
[2025-10-06 02:21:26] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -28.353423 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 02:21:28] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -28.357487 | E_var:     0.1523 | E_err:   0.006098
[2025-10-06 02:21:30] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -28.362489 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 02:21:33] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -28.367039 | E_var:     0.1263 | E_err:   0.005554
[2025-10-06 02:21:35] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -28.366532 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 02:21:38] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -28.367849 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 02:21:40] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -28.365334 | E_var:     0.1301 | E_err:   0.005635
[2025-10-06 02:21:43] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -28.359109 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 02:21:45] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -28.365226 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 02:21:47] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -28.358444 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 02:21:50] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -28.360302 | E_var:     0.1301 | E_err:   0.005637
[2025-10-06 02:21:52] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -28.353842 | E_var:     0.1711 | E_err:   0.006463
[2025-10-06 02:21:55] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -28.358900 | E_var:     0.1279 | E_err:   0.005587
[2025-10-06 02:21:57] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -28.370862 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 02:22:00] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -28.366148 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 02:22:02] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -28.351329 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 02:22:04] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -28.352603 | E_var:     0.1119 | E_err:   0.005226
[2025-10-06 02:22:07] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -28.354189 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 02:22:09] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -28.363250 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 02:22:12] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -28.366961 | E_var:     0.1774 | E_err:   0.006581
[2025-10-06 02:22:14] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -28.358184 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 02:22:17] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -28.365406 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 02:22:19] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -28.359910 | E_var:     0.1420 | E_err:   0.005887
[2025-10-06 02:22:21] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -28.361351 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 02:22:24] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -28.354684 | E_var:     0.0963 | E_err:   0.004849
[2025-10-06 02:22:26] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -28.356626 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 02:22:29] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -28.357732 | E_var:     0.0914 | E_err:   0.004723
[2025-10-06 02:22:31] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -28.351558 | E_var:     0.2016 | E_err:   0.007016
[2025-10-06 02:22:33] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -28.359428 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 02:22:36] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -28.367993 | E_var:     0.1334 | E_err:   0.005706
[2025-10-06 02:22:38] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -28.353968 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 02:22:41] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -28.367369 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 02:22:43] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -28.354893 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 02:22:46] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -28.366607 | E_var:     0.1094 | E_err:   0.005168
[2025-10-06 02:22:48] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -28.346799 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 02:22:50] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -28.356495 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 02:22:53] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -28.363706 | E_var:     0.1122 | E_err:   0.005234
[2025-10-06 02:22:55] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -28.355839 | E_var:     0.1040 | E_err:   0.005039
[2025-10-06 02:22:58] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -28.367698 | E_var:     0.1076 | E_err:   0.005124
[2025-10-06 02:23:00] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -28.365396 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 02:23:03] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -28.362349 | E_var:     0.1188 | E_err:   0.005386
[2025-10-06 02:23:05] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -28.354694 | E_var:     0.1733 | E_err:   0.006504
[2025-10-06 02:23:07] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -28.351091 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 02:23:10] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -28.365305 | E_var:     0.1375 | E_err:   0.005794
[2025-10-06 02:23:12] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -28.359313 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 02:23:15] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -28.360922 | E_var:     0.1437 | E_err:   0.005922
[2025-10-06 02:23:17] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -28.369162 | E_var:     0.0972 | E_err:   0.004872
[2025-10-06 02:23:20] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -28.369243 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 02:23:22] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -28.362062 | E_var:     0.0898 | E_err:   0.004681
[2025-10-06 02:23:24] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -28.361017 | E_var:     0.1264 | E_err:   0.005555
[2025-10-06 02:23:27] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -28.358733 | E_var:     0.1034 | E_err:   0.005023
[2025-10-06 02:23:29] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -28.363006 | E_var:     0.1234 | E_err:   0.005490
[2025-10-06 02:23:32] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -28.352076 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 02:23:34] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -28.354213 | E_var:     0.1193 | E_err:   0.005396
[2025-10-06 02:23:37] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -28.363948 | E_var:     0.1169 | E_err:   0.005343
[2025-10-06 02:23:39] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -28.351524 | E_var:     0.1164 | E_err:   0.005331
[2025-10-06 02:23:41] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -28.358444 | E_var:     0.1148 | E_err:   0.005294
[2025-10-06 02:23:44] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -28.360892 | E_var:     0.1816 | E_err:   0.006658
[2025-10-06 02:23:46] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -28.362321 | E_var:     0.1023 | E_err:   0.004996
[2025-10-06 02:23:49] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -28.358576 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 02:23:51] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -28.351068 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 02:23:53] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -28.364698 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 02:23:56] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -28.359133 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 02:23:58] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -28.364062 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 02:24:01] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -28.353723 | E_var:     0.1871 | E_err:   0.006758
[2025-10-06 02:24:03] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -28.360887 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 02:24:06] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -28.369045 | E_var:     0.2422 | E_err:   0.007690
[2025-10-06 02:24:08] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -28.362450 | E_var:     0.0961 | E_err:   0.004843
[2025-10-06 02:24:10] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -28.356692 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 02:24:13] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -28.366331 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 02:24:15] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -28.361262 | E_var:     0.1402 | E_err:   0.005851
[2025-10-06 02:24:18] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -28.356962 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 02:24:20] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -28.364872 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 02:24:23] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -28.355861 | E_var:     0.1292 | E_err:   0.005617
[2025-10-06 02:24:25] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -28.363571 | E_var:     0.1176 | E_err:   0.005357
[2025-10-06 02:24:27] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -28.368051 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 02:24:30] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -28.361589 | E_var:     0.0954 | E_err:   0.004827
[2025-10-06 02:24:32] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -28.360273 | E_var:     0.0858 | E_err:   0.004577
[2025-10-06 02:24:35] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -28.351512 | E_var:     0.1148 | E_err:   0.005294
[2025-10-06 02:24:37] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -28.356738 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 02:24:39] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -28.358211 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 02:24:42] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -28.360586 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 02:24:42] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 02:24:44] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -28.357901 | E_var:     0.1163 | E_err:   0.005329
[2025-10-06 02:24:47] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -28.356300 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 02:24:49] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -28.357361 | E_var:     0.1136 | E_err:   0.005267
[2025-10-06 02:24:52] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -28.359373 | E_var:     0.0988 | E_err:   0.004910
[2025-10-06 02:24:54] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -28.366143 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 02:24:56] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -28.362995 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 02:24:59] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -28.366357 | E_var:     0.1617 | E_err:   0.006284
[2025-10-06 02:25:01] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -28.357068 | E_var:     0.1024 | E_err:   0.005001
[2025-10-06 02:25:04] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -28.350145 | E_var:     0.1229 | E_err:   0.005478
[2025-10-06 02:25:06] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -28.350404 | E_var:     0.1230 | E_err:   0.005481
[2025-10-06 02:25:09] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -28.370166 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 02:25:11] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -28.357936 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 02:25:13] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -28.355398 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 02:25:16] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -28.365971 | E_var:     0.1216 | E_err:   0.005450
[2025-10-06 02:25:18] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -28.365696 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 02:25:21] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -28.353833 | E_var:     0.1250 | E_err:   0.005525
[2025-10-06 02:25:23] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -28.367974 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 02:25:25] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -28.362038 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 02:25:28] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -28.359310 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 02:25:30] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -28.364326 | E_var:     0.1069 | E_err:   0.005109
[2025-10-06 02:25:33] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -28.352413 | E_var:     0.1128 | E_err:   0.005248
[2025-10-06 02:25:35] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -28.367288 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 02:25:38] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -28.355786 | E_var:     0.0970 | E_err:   0.004866
[2025-10-06 02:25:40] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -28.357590 | E_var:     0.0922 | E_err:   0.004745
[2025-10-06 02:25:42] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -28.364563 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 02:25:45] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -28.359752 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 02:25:47] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -28.363222 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 02:25:50] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -28.361856 | E_var:     0.0996 | E_err:   0.004930
[2025-10-06 02:25:52] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -28.357686 | E_var:     0.1041 | E_err:   0.005041
[2025-10-06 02:25:55] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -28.368341 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 02:25:57] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -28.360923 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 02:25:59] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -28.359004 | E_var:     0.1521 | E_err:   0.006094
[2025-10-06 02:26:02] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -28.351902 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 02:26:04] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -28.357950 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 02:26:07] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -28.346086 | E_var:     0.1342 | E_err:   0.005723
[2025-10-06 02:26:09] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -28.355612 | E_var:     0.1742 | E_err:   0.006522
[2025-10-06 02:26:11] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -28.364158 | E_var:     0.0924 | E_err:   0.004751
[2025-10-06 02:26:14] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -28.349084 | E_var:     0.0997 | E_err:   0.004933
[2025-10-06 02:26:16] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -28.363878 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 02:26:19] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -28.360143 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 02:26:21] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -28.366297 | E_var:     0.1217 | E_err:   0.005451
[2025-10-06 02:26:24] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -28.356992 | E_var:     0.0887 | E_err:   0.004652
[2025-10-06 02:26:26] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -28.364921 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 02:26:28] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -28.365096 | E_var:     0.0937 | E_err:   0.004784
[2025-10-06 02:26:31] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -28.365240 | E_var:     0.0905 | E_err:   0.004699
[2025-10-06 02:26:33] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -28.362006 | E_var:     0.1199 | E_err:   0.005410
[2025-10-06 02:26:36] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -28.370958 | E_var:     0.1127 | E_err:   0.005246
[2025-10-06 02:26:38] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -28.366508 | E_var:     0.0952 | E_err:   0.004821
[2025-10-06 02:26:41] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -28.360044 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 02:26:43] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -28.361681 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 02:26:43] ======================================================================================================
[2025-10-06 02:26:43] ✅ Training completed successfully
[2025-10-06 02:26:43] Total restarts: 2
[2025-10-06 02:26:44] Final Energy: -28.36168076 ± 0.00511538
[2025-10-06 02:26:44] Final Variance: 0.107181
[2025-10-06 02:26:44] ======================================================================================================
[2025-10-06 02:26:44] ======================================================================================================
[2025-10-06 02:26:44] Training completed | Runtime: 2592.2s
[2025-10-06 02:26:45] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 02:26:45] ======================================================================================================
