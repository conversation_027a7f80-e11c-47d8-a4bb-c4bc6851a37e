[2025-10-06 00:15:50] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 00:15:50]   - 迭代次数: final
[2025-10-06 00:15:50]   - 能量: -27.122373+0.000234j ± 0.006194, Var: 0.157143
[2025-10-06 00:15:50]   - 时间戳: 2025-10-02T16:26:17.722871+08:00
[2025-10-06 00:16:05] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 00:16:05] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 00:16:05] ======================================================================================================
[2025-10-06 00:16:05] GCNN for Shastry-Sutherland Model
[2025-10-06 00:16:05] ======================================================================================================
[2025-10-06 00:16:05] System parameters:
[2025-10-06 00:16:05]   - System size: L=4, N=64
[2025-10-06 00:16:05]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-10-06 00:16:05] ------------------------------------------------------------------------------------------------------
[2025-10-06 00:16:05] Model parameters:
[2025-10-06 00:16:05]   - Number of layers = 4
[2025-10-06 00:16:05]   - Number of features = 4
[2025-10-06 00:16:05]   - Total parameters = 12572
[2025-10-06 00:16:05] ------------------------------------------------------------------------------------------------------
[2025-10-06 00:16:05] Training parameters:
[2025-10-06 00:16:05]   - Total iterations: 1050
[2025-10-06 00:16:05]   - Annealing cycles: 3
[2025-10-06 00:16:05]   - Initial period: 150
[2025-10-06 00:16:05]   - Period multiplier: 2.0
[2025-10-06 00:16:05]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 00:16:05]   - Samples: 4096
[2025-10-06 00:16:05]   - Discarded samples: 0
[2025-10-06 00:16:05]   - Chunk size: 4096
[2025-10-06 00:16:05]   - Diagonal shift: 0.15
[2025-10-06 00:16:05]   - Gradient clipping: 1.0
[2025-10-06 00:16:05]   - Checkpoint enabled: interval=100
[2025-10-06 00:16:05]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.77/model_L4F4/training/checkpoints
[2025-10-06 00:16:05] ------------------------------------------------------------------------------------------------------
[2025-10-06 00:16:05] Device status:
[2025-10-06 00:16:05]   - Devices model: NVIDIA H200 NVL
[2025-10-06 00:16:05]   - Number of devices: 1
[2025-10-06 00:16:05]   - Sharding: True
[2025-10-06 00:16:06] ======================================================================================================
[2025-10-06 00:16:39] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -27.523582 | E_var:     0.4321 | E_err:   0.010271
[2025-10-06 00:16:58] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -27.528034 | E_var:     0.3239 | E_err:   0.008892
[2025-10-06 00:17:01] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -27.537070 | E_var:     0.2309 | E_err:   0.007508
[2025-10-06 00:17:03] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -27.534699 | E_var:     0.2071 | E_err:   0.007111
[2025-10-06 00:17:06] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -27.544784 | E_var:     0.2301 | E_err:   0.007495
[2025-10-06 00:17:08] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -27.534999 | E_var:     0.2952 | E_err:   0.008489
[2025-10-06 00:17:10] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -27.539808 | E_var:     0.1562 | E_err:   0.006175
[2025-10-06 00:17:13] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -27.523070 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 00:17:15] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -27.536387 | E_var:     0.1541 | E_err:   0.006133
[2025-10-06 00:17:18] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -27.537243 | E_var:     0.2108 | E_err:   0.007174
[2025-10-06 00:17:20] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -27.528128 | E_var:     0.1641 | E_err:   0.006329
[2025-10-06 00:17:22] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -27.530984 | E_var:     0.1710 | E_err:   0.006462
[2025-10-06 00:17:25] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -27.547739 | E_var:     0.1387 | E_err:   0.005819
[2025-10-06 00:17:27] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -27.542607 | E_var:     0.1895 | E_err:   0.006802
[2025-10-06 00:17:30] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -27.534715 | E_var:     0.1424 | E_err:   0.005897
[2025-10-06 00:17:32] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -27.529832 | E_var:     0.1480 | E_err:   0.006010
[2025-10-06 00:17:35] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -27.534493 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 00:17:37] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -27.530954 | E_var:     0.1996 | E_err:   0.006980
[2025-10-06 00:17:39] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -27.526837 | E_var:     0.2283 | E_err:   0.007466
[2025-10-06 00:17:42] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -27.530547 | E_var:     0.1426 | E_err:   0.005901
[2025-10-06 00:17:44] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -27.542578 | E_var:     0.1471 | E_err:   0.005992
[2025-10-06 00:17:47] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -27.524394 | E_var:     0.1733 | E_err:   0.006504
[2025-10-06 00:17:49] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -27.529978 | E_var:     0.3910 | E_err:   0.009770
[2025-10-06 00:17:51] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -27.542807 | E_var:     0.1622 | E_err:   0.006294
[2025-10-06 00:17:54] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -27.533157 | E_var:     0.1694 | E_err:   0.006431
[2025-10-06 00:17:56] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -27.534310 | E_var:     0.1554 | E_err:   0.006160
[2025-10-06 00:17:59] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -27.537512 | E_var:     0.1583 | E_err:   0.006218
[2025-10-06 00:18:01] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -27.533421 | E_var:     0.1342 | E_err:   0.005724
[2025-10-06 00:18:04] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -27.530578 | E_var:     0.1765 | E_err:   0.006565
[2025-10-06 00:18:06] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -27.527099 | E_var:     0.1718 | E_err:   0.006476
[2025-10-06 00:18:08] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -27.532671 | E_var:     0.1937 | E_err:   0.006876
[2025-10-06 00:18:11] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -27.536111 | E_var:     0.1904 | E_err:   0.006817
[2025-10-06 00:18:13] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -27.535094 | E_var:     0.1729 | E_err:   0.006496
[2025-10-06 00:18:16] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -27.526593 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 00:18:18] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -27.534277 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 00:18:20] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -27.537265 | E_var:     0.1638 | E_err:   0.006325
[2025-10-06 00:18:23] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -27.536497 | E_var:     0.1583 | E_err:   0.006217
[2025-10-06 00:18:25] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -27.534733 | E_var:     0.1666 | E_err:   0.006378
[2025-10-06 00:18:28] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -27.532849 | E_var:     0.1471 | E_err:   0.005993
[2025-10-06 00:18:30] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -27.540065 | E_var:     0.1564 | E_err:   0.006179
[2025-10-06 00:18:33] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -27.535499 | E_var:     0.1495 | E_err:   0.006041
[2025-10-06 00:18:35] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -27.538906 | E_var:     0.1755 | E_err:   0.006546
[2025-10-06 00:18:37] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -27.532394 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 00:18:40] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -27.540017 | E_var:     0.1923 | E_err:   0.006852
[2025-10-06 00:18:42] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -27.540960 | E_var:     0.1365 | E_err:   0.005772
[2025-10-06 00:18:45] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -27.533729 | E_var:     0.1603 | E_err:   0.006256
[2025-10-06 00:18:47] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -27.528298 | E_var:     0.2169 | E_err:   0.007277
[2025-10-06 00:18:49] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -27.529988 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 00:18:52] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -27.532998 | E_var:     0.1529 | E_err:   0.006109
[2025-10-06 00:18:54] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -27.533944 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 00:18:57] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -27.525183 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 00:18:59] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -27.550918 | E_var:     0.2103 | E_err:   0.007165
[2025-10-06 00:19:02] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -27.539684 | E_var:     0.1603 | E_err:   0.006257
[2025-10-06 00:19:04] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -27.534659 | E_var:     0.1685 | E_err:   0.006414
[2025-10-06 00:19:06] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -27.546451 | E_var:     0.2063 | E_err:   0.007098
[2025-10-06 00:19:09] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -27.539295 | E_var:     0.1867 | E_err:   0.006751
[2025-10-06 00:19:11] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -27.530369 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 00:19:14] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -27.535346 | E_var:     0.1716 | E_err:   0.006472
[2025-10-06 00:19:16] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -27.534594 | E_var:     0.1662 | E_err:   0.006369
[2025-10-06 00:19:18] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -27.536616 | E_var:     0.1631 | E_err:   0.006310
[2025-10-06 00:19:21] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -27.534087 | E_var:     0.1422 | E_err:   0.005893
[2025-10-06 00:19:23] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -27.537914 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 00:19:26] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -27.536780 | E_var:     0.1785 | E_err:   0.006602
[2025-10-06 00:19:28] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -27.523195 | E_var:     0.2251 | E_err:   0.007413
[2025-10-06 00:19:31] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -27.540468 | E_var:     0.2161 | E_err:   0.007263
[2025-10-06 00:19:33] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -27.533679 | E_var:     0.1329 | E_err:   0.005696
[2025-10-06 00:19:35] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -27.535257 | E_var:     0.1577 | E_err:   0.006204
[2025-10-06 00:19:38] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -27.534758 | E_var:     0.1914 | E_err:   0.006835
[2025-10-06 00:19:40] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -27.530654 | E_var:     0.1819 | E_err:   0.006663
[2025-10-06 00:19:43] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -27.548913 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 00:19:45] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -27.536283 | E_var:     0.1629 | E_err:   0.006306
[2025-10-06 00:19:47] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -27.527482 | E_var:     0.1583 | E_err:   0.006217
[2025-10-06 00:19:50] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -27.534955 | E_var:     0.1655 | E_err:   0.006356
[2025-10-06 00:19:52] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -27.534022 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 00:19:55] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -27.530054 | E_var:     0.1461 | E_err:   0.005972
[2025-10-06 00:19:57] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -27.535660 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 00:19:59] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -27.534959 | E_var:     0.1330 | E_err:   0.005698
[2025-10-06 00:20:02] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -27.535035 | E_var:     0.1440 | E_err:   0.005930
[2025-10-06 00:20:04] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -27.537485 | E_var:     0.1621 | E_err:   0.006292
[2025-10-06 00:20:07] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -27.533685 | E_var:     0.1609 | E_err:   0.006267
[2025-10-06 00:20:09] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -27.542146 | E_var:     0.1609 | E_err:   0.006267
[2025-10-06 00:20:12] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -27.530774 | E_var:     0.1387 | E_err:   0.005819
[2025-10-06 00:20:14] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -27.533726 | E_var:     0.1733 | E_err:   0.006504
[2025-10-06 00:20:16] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -27.530516 | E_var:     0.1780 | E_err:   0.006593
[2025-10-06 00:20:19] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -27.528434 | E_var:     0.1540 | E_err:   0.006132
[2025-10-06 00:20:21] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -27.553312 | E_var:     0.2298 | E_err:   0.007490
[2025-10-06 00:20:24] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -27.519419 | E_var:     0.8011 | E_err:   0.013985
[2025-10-06 00:20:26] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -27.537595 | E_var:     0.1554 | E_err:   0.006160
[2025-10-06 00:20:29] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -27.533483 | E_var:     0.2068 | E_err:   0.007106
[2025-10-06 00:20:31] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -27.524659 | E_var:     0.1353 | E_err:   0.005747
[2025-10-06 00:20:33] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -27.529590 | E_var:     0.2135 | E_err:   0.007220
[2025-10-06 00:20:36] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -27.549825 | E_var:     0.1558 | E_err:   0.006168
[2025-10-06 00:20:38] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -27.530312 | E_var:     0.2523 | E_err:   0.007849
[2025-10-06 00:20:41] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -27.529276 | E_var:     0.1536 | E_err:   0.006124
[2025-10-06 00:20:43] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -27.542271 | E_var:     0.1989 | E_err:   0.006968
[2025-10-06 00:20:45] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -27.533839 | E_var:     0.1507 | E_err:   0.006065
[2025-10-06 00:20:48] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -27.539978 | E_var:     0.1785 | E_err:   0.006601
[2025-10-06 00:20:50] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -27.526184 | E_var:     0.1959 | E_err:   0.006916
[2025-10-06 00:20:53] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -27.542057 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 00:20:55] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -27.535429 | E_var:     0.1707 | E_err:   0.006456
[2025-10-06 00:20:55] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 00:20:57] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -27.544891 | E_var:     0.2806 | E_err:   0.008277
[2025-10-06 00:21:00] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -27.535695 | E_var:     0.1601 | E_err:   0.006253
[2025-10-06 00:21:02] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -27.542286 | E_var:     0.1573 | E_err:   0.006197
[2025-10-06 00:21:05] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -27.539712 | E_var:     0.1198 | E_err:   0.005409
[2025-10-06 00:21:07] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -27.532697 | E_var:     0.2580 | E_err:   0.007936
[2025-10-06 00:21:10] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -27.536997 | E_var:     0.1671 | E_err:   0.006386
[2025-10-06 00:21:12] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -27.533717 | E_var:     0.1823 | E_err:   0.006672
[2025-10-06 00:21:14] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -27.537552 | E_var:     0.1584 | E_err:   0.006220
[2025-10-06 00:21:17] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -27.545567 | E_var:     0.2327 | E_err:   0.007538
[2025-10-06 00:21:19] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -27.540405 | E_var:     0.1935 | E_err:   0.006874
[2025-10-06 00:21:22] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -27.531404 | E_var:     0.1393 | E_err:   0.005831
[2025-10-06 00:21:24] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -27.530241 | E_var:     0.1423 | E_err:   0.005893
[2025-10-06 00:21:26] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -27.534032 | E_var:     0.1823 | E_err:   0.006671
[2025-10-06 00:21:29] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -27.532190 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 00:21:31] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -27.531967 | E_var:     0.1645 | E_err:   0.006338
[2025-10-06 00:21:34] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -27.530016 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 00:21:36] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -27.535141 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 00:21:39] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -27.540329 | E_var:     0.1653 | E_err:   0.006352
[2025-10-06 00:21:41] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -27.534492 | E_var:     0.2194 | E_err:   0.007319
[2025-10-06 00:21:43] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -27.535687 | E_var:     0.1627 | E_err:   0.006302
[2025-10-06 00:21:46] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -27.528247 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 00:21:48] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -27.530621 | E_var:     0.1563 | E_err:   0.006178
[2025-10-06 00:21:51] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -27.534327 | E_var:     0.1383 | E_err:   0.005810
[2025-10-06 00:21:53] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -27.533872 | E_var:     0.1601 | E_err:   0.006251
[2025-10-06 00:21:55] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -27.536942 | E_var:     0.1236 | E_err:   0.005494
[2025-10-06 00:21:58] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -27.533108 | E_var:     0.1615 | E_err:   0.006280
[2025-10-06 00:22:00] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -27.541641 | E_var:     0.1468 | E_err:   0.005986
[2025-10-06 00:22:03] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -27.533462 | E_var:     0.1474 | E_err:   0.005999
[2025-10-06 00:22:05] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -27.546625 | E_var:     0.1870 | E_err:   0.006756
[2025-10-06 00:22:08] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -27.537622 | E_var:     0.1986 | E_err:   0.006963
[2025-10-06 00:22:10] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -27.530202 | E_var:     0.2030 | E_err:   0.007040
[2025-10-06 00:22:12] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -27.537452 | E_var:     0.1416 | E_err:   0.005879
[2025-10-06 00:22:15] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -27.533812 | E_var:     0.1553 | E_err:   0.006158
[2025-10-06 00:22:17] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -27.523171 | E_var:     0.1301 | E_err:   0.005636
[2025-10-06 00:22:20] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -27.536073 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 00:22:22] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -27.540907 | E_var:     0.2639 | E_err:   0.008026
[2025-10-06 00:22:24] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -27.533998 | E_var:     0.1516 | E_err:   0.006085
[2025-10-06 00:22:27] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -27.533178 | E_var:     0.1618 | E_err:   0.006285
[2025-10-06 00:22:29] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -27.531922 | E_var:     0.1743 | E_err:   0.006524
[2025-10-06 00:22:32] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -27.533191 | E_var:     0.1373 | E_err:   0.005790
[2025-10-06 00:22:34] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -27.542956 | E_var:     0.1349 | E_err:   0.005738
[2025-10-06 00:22:37] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -27.538474 | E_var:     0.1784 | E_err:   0.006599
[2025-10-06 00:22:39] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -27.533392 | E_var:     0.1589 | E_err:   0.006229
[2025-10-06 00:22:41] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -27.533982 | E_var:     0.1505 | E_err:   0.006062
[2025-10-06 00:22:44] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -27.542035 | E_var:     0.1639 | E_err:   0.006325
[2025-10-06 00:22:46] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -27.538680 | E_var:     0.1790 | E_err:   0.006611
[2025-10-06 00:22:49] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -27.527402 | E_var:     0.2078 | E_err:   0.007122
[2025-10-06 00:22:51] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -27.544842 | E_var:     0.1975 | E_err:   0.006945
[2025-10-06 00:22:54] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -27.547381 | E_var:     0.1620 | E_err:   0.006288
[2025-10-06 00:22:56] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -27.534149 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 00:22:56] 🔄 RESTART #1 | Period: 300
[2025-10-06 00:22:58] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -27.530927 | E_var:     0.1716 | E_err:   0.006473
[2025-10-06 00:23:01] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -27.529747 | E_var:     0.1756 | E_err:   0.006549
[2025-10-06 00:23:03] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -27.522843 | E_var:     0.1452 | E_err:   0.005954
[2025-10-06 00:23:06] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -27.525622 | E_var:     0.1762 | E_err:   0.006559
[2025-10-06 00:23:08] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -27.529092 | E_var:     0.1981 | E_err:   0.006954
[2025-10-06 00:23:10] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -27.544165 | E_var:     0.1484 | E_err:   0.006019
[2025-10-06 00:23:13] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -27.537559 | E_var:     0.1827 | E_err:   0.006678
[2025-10-06 00:23:15] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -27.544184 | E_var:     0.1431 | E_err:   0.005912
[2025-10-06 00:23:18] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -27.547987 | E_var:     0.7057 | E_err:   0.013126
[2025-10-06 00:23:20] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -27.539788 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 00:23:22] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -27.524897 | E_var:     0.3366 | E_err:   0.009065
[2025-10-06 00:23:25] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -27.528845 | E_var:     0.1731 | E_err:   0.006500
[2025-10-06 00:23:27] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -27.538451 | E_var:     0.1698 | E_err:   0.006439
[2025-10-06 00:23:30] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -27.531312 | E_var:     0.1973 | E_err:   0.006941
[2025-10-06 00:23:32] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -27.530964 | E_var:     0.2075 | E_err:   0.007118
[2025-10-06 00:23:34] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -27.542630 | E_var:     0.1640 | E_err:   0.006327
[2025-10-06 00:23:37] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -27.533742 | E_var:     0.1446 | E_err:   0.005941
[2025-10-06 00:23:39] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -27.530881 | E_var:     0.1496 | E_err:   0.006043
[2025-10-06 00:23:42] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -27.536008 | E_var:     0.1446 | E_err:   0.005941
[2025-10-06 00:23:44] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -27.538916 | E_var:     0.1707 | E_err:   0.006455
[2025-10-06 00:23:47] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -27.539146 | E_var:     0.1399 | E_err:   0.005845
[2025-10-06 00:23:49] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -27.536227 | E_var:     0.1347 | E_err:   0.005735
[2025-10-06 00:23:51] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -27.524786 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 00:23:54] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -27.542675 | E_var:     0.1523 | E_err:   0.006098
[2025-10-06 00:23:56] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -27.531837 | E_var:     0.2515 | E_err:   0.007836
[2025-10-06 00:23:59] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -27.533862 | E_var:     0.1481 | E_err:   0.006012
[2025-10-06 00:24:01] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -27.539650 | E_var:     0.1346 | E_err:   0.005733
[2025-10-06 00:24:04] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -27.543427 | E_var:     0.1924 | E_err:   0.006853
[2025-10-06 00:24:06] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -27.533542 | E_var:     0.1692 | E_err:   0.006427
[2025-10-06 00:24:08] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -27.528700 | E_var:     0.1389 | E_err:   0.005823
[2025-10-06 00:24:11] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -27.542565 | E_var:     0.1431 | E_err:   0.005910
[2025-10-06 00:24:13] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -27.555258 | E_var:     0.1534 | E_err:   0.006120
[2025-10-06 00:24:16] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -27.539168 | E_var:     0.1399 | E_err:   0.005843
[2025-10-06 00:24:18] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -27.536828 | E_var:     0.1393 | E_err:   0.005832
[2025-10-06 00:24:20] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -27.540906 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 00:24:23] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -27.538629 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 00:24:25] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -27.533178 | E_var:     0.1462 | E_err:   0.005975
[2025-10-06 00:24:28] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -27.529441 | E_var:     0.1446 | E_err:   0.005942
[2025-10-06 00:24:30] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -27.534826 | E_var:     0.3232 | E_err:   0.008883
[2025-10-06 00:24:33] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -27.537293 | E_var:     0.2506 | E_err:   0.007822
[2025-10-06 00:24:35] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -27.535306 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 00:24:37] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -27.536281 | E_var:     0.1538 | E_err:   0.006128
[2025-10-06 00:24:40] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -27.534893 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 00:24:42] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -27.541246 | E_var:     0.1425 | E_err:   0.005898
[2025-10-06 00:24:45] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -27.543581 | E_var:     0.2632 | E_err:   0.008017
[2025-10-06 00:24:47] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -27.540279 | E_var:     0.1639 | E_err:   0.006326
[2025-10-06 00:24:49] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -27.538528 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 00:24:52] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -27.530721 | E_var:     0.1726 | E_err:   0.006492
[2025-10-06 00:24:54] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -27.521250 | E_var:     0.1800 | E_err:   0.006630
[2025-10-06 00:24:57] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -27.532923 | E_var:     0.1487 | E_err:   0.006025
[2025-10-06 00:24:57] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 00:24:59] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -27.534926 | E_var:     0.1209 | E_err:   0.005433
[2025-10-06 00:25:02] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -27.527738 | E_var:     0.3500 | E_err:   0.009244
[2025-10-06 00:25:04] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -27.533171 | E_var:     0.1488 | E_err:   0.006028
[2025-10-06 00:25:06] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -27.535511 | E_var:     0.1469 | E_err:   0.005988
[2025-10-06 00:25:09] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -27.523299 | E_var:     0.2068 | E_err:   0.007106
[2025-10-06 00:25:11] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -27.540828 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 00:25:14] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -27.543045 | E_var:     1.0465 | E_err:   0.015984
[2025-10-06 00:25:16] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -27.535252 | E_var:     0.1303 | E_err:   0.005640
[2025-10-06 00:25:18] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -27.530435 | E_var:     0.2767 | E_err:   0.008219
[2025-10-06 00:25:21] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -27.534060 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 00:25:23] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -27.533406 | E_var:     0.1620 | E_err:   0.006290
[2025-10-06 00:25:26] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -27.537833 | E_var:     0.1537 | E_err:   0.006126
[2025-10-06 00:25:28] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -27.523283 | E_var:     0.4333 | E_err:   0.010285
[2025-10-06 00:25:30] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -27.534983 | E_var:     0.1544 | E_err:   0.006141
[2025-10-06 00:25:33] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -27.532691 | E_var:     0.1617 | E_err:   0.006284
[2025-10-06 00:25:35] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -27.539757 | E_var:     0.1641 | E_err:   0.006329
[2025-10-06 00:25:38] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -27.534174 | E_var:     0.3021 | E_err:   0.008587
[2025-10-06 00:25:40] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -27.538126 | E_var:     0.1744 | E_err:   0.006524
[2025-10-06 00:25:43] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -27.525765 | E_var:     0.1887 | E_err:   0.006787
[2025-10-06 00:25:45] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -27.540065 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 00:25:47] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -27.528039 | E_var:     0.1758 | E_err:   0.006552
[2025-10-06 00:25:50] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -27.544593 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 00:25:52] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -27.530270 | E_var:     0.1488 | E_err:   0.006028
[2025-10-06 00:25:55] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -27.540025 | E_var:     0.1284 | E_err:   0.005600
[2025-10-06 00:25:57] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -27.531176 | E_var:     0.2128 | E_err:   0.007207
[2025-10-06 00:25:59] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -27.535624 | E_var:     0.1591 | E_err:   0.006233
[2025-10-06 00:26:02] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -27.546406 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 00:26:04] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -27.542318 | E_var:     0.1405 | E_err:   0.005857
[2025-10-06 00:26:07] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -27.531668 | E_var:     0.1621 | E_err:   0.006292
[2025-10-06 00:26:09] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -27.534585 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 00:26:12] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -27.536764 | E_var:     0.1463 | E_err:   0.005977
[2025-10-06 00:26:14] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -27.537589 | E_var:     0.1305 | E_err:   0.005643
[2025-10-06 00:26:16] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -27.544008 | E_var:     0.1538 | E_err:   0.006127
[2025-10-06 00:26:19] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -27.542910 | E_var:     0.1370 | E_err:   0.005783
[2025-10-06 00:26:21] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -27.526067 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 00:26:24] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -27.534699 | E_var:     0.1449 | E_err:   0.005947
[2025-10-06 00:26:26] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -27.536887 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 00:26:29] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -27.540580 | E_var:     0.1546 | E_err:   0.006144
[2025-10-06 00:26:31] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -27.537671 | E_var:     0.1934 | E_err:   0.006872
[2025-10-06 00:26:33] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -27.541371 | E_var:     0.1671 | E_err:   0.006387
[2025-10-06 00:26:36] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -27.524624 | E_var:     0.1716 | E_err:   0.006472
[2025-10-06 00:26:38] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -27.530827 | E_var:     0.1572 | E_err:   0.006194
[2025-10-06 00:26:41] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -27.536031 | E_var:     0.1357 | E_err:   0.005756
[2025-10-06 00:26:43] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -27.531909 | E_var:     0.1973 | E_err:   0.006941
[2025-10-06 00:26:45] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -27.533491 | E_var:     0.1829 | E_err:   0.006683
[2025-10-06 00:26:48] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -27.539093 | E_var:     0.1336 | E_err:   0.005711
[2025-10-06 00:26:50] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -27.537662 | E_var:     0.1554 | E_err:   0.006160
[2025-10-06 00:26:53] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -27.540899 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 00:26:55] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -27.539999 | E_var:     0.4194 | E_err:   0.010119
[2025-10-06 00:26:58] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -27.544599 | E_var:     0.1799 | E_err:   0.006627
[2025-10-06 00:27:00] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -27.535244 | E_var:     0.1681 | E_err:   0.006407
[2025-10-06 00:27:02] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -27.536464 | E_var:     0.1567 | E_err:   0.006186
[2025-10-06 00:27:05] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -27.537930 | E_var:     0.1917 | E_err:   0.006842
[2025-10-06 00:27:07] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -27.529826 | E_var:     0.1882 | E_err:   0.006778
[2025-10-06 00:27:10] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -27.529107 | E_var:     0.2044 | E_err:   0.007065
[2025-10-06 00:27:12] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -27.546140 | E_var:     0.1754 | E_err:   0.006545
[2025-10-06 00:27:14] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -27.533761 | E_var:     0.1467 | E_err:   0.005985
[2025-10-06 00:27:17] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -27.538370 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 00:27:19] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -27.534332 | E_var:     0.1596 | E_err:   0.006242
[2025-10-06 00:27:22] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -27.533383 | E_var:     0.1372 | E_err:   0.005788
[2025-10-06 00:27:24] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -27.537802 | E_var:     0.1293 | E_err:   0.005618
[2025-10-06 00:27:27] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -27.542209 | E_var:     0.1575 | E_err:   0.006201
[2025-10-06 00:27:29] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -27.519274 | E_var:     0.2002 | E_err:   0.006991
[2025-10-06 00:27:31] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -27.538465 | E_var:     0.1365 | E_err:   0.005773
[2025-10-06 00:27:34] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -27.529875 | E_var:     0.1361 | E_err:   0.005763
[2025-10-06 00:27:36] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -27.531384 | E_var:     0.2367 | E_err:   0.007602
[2025-10-06 00:27:39] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -27.533091 | E_var:     0.1451 | E_err:   0.005951
[2025-10-06 00:27:41] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -27.529419 | E_var:     0.1705 | E_err:   0.006451
[2025-10-06 00:27:43] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -27.540223 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 00:27:46] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -27.523840 | E_var:     0.2022 | E_err:   0.007025
[2025-10-06 00:27:48] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -27.524562 | E_var:     0.1303 | E_err:   0.005640
[2025-10-06 00:27:51] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -27.539994 | E_var:     0.1537 | E_err:   0.006126
[2025-10-06 00:27:53] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -27.542724 | E_var:     0.1555 | E_err:   0.006161
[2025-10-06 00:27:56] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -27.541207 | E_var:     0.1510 | E_err:   0.006072
[2025-10-06 00:27:58] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -27.531389 | E_var:     0.2048 | E_err:   0.007071
[2025-10-06 00:28:00] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -27.538954 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 00:28:03] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -27.540303 | E_var:     0.1598 | E_err:   0.006245
[2025-10-06 00:28:05] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -27.537799 | E_var:     0.1780 | E_err:   0.006593
[2025-10-06 00:28:08] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -27.540658 | E_var:     0.1752 | E_err:   0.006540
[2025-10-06 00:28:10] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -27.537807 | E_var:     0.1498 | E_err:   0.006048
[2025-10-06 00:28:12] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -27.533671 | E_var:     0.1534 | E_err:   0.006119
[2025-10-06 00:28:15] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -27.529168 | E_var:     0.1459 | E_err:   0.005968
[2025-10-06 00:28:17] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -27.529545 | E_var:     0.1804 | E_err:   0.006636
[2025-10-06 00:28:20] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -27.536851 | E_var:     0.1393 | E_err:   0.005831
[2025-10-06 00:28:22] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -27.529391 | E_var:     0.1531 | E_err:   0.006113
[2025-10-06 00:28:25] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -27.535611 | E_var:     0.1480 | E_err:   0.006010
[2025-10-06 00:28:27] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -27.530383 | E_var:     0.1434 | E_err:   0.005917
[2025-10-06 00:28:29] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -27.531389 | E_var:     0.1515 | E_err:   0.006081
[2025-10-06 00:28:32] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -27.535450 | E_var:     0.1650 | E_err:   0.006347
[2025-10-06 00:28:34] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -27.531961 | E_var:     0.1459 | E_err:   0.005969
[2025-10-06 00:28:37] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -27.537854 | E_var:     0.1424 | E_err:   0.005897
[2025-10-06 00:28:39] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -27.542838 | E_var:     0.2274 | E_err:   0.007451
[2025-10-06 00:28:41] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -27.536325 | E_var:     0.1647 | E_err:   0.006341
[2025-10-06 00:28:44] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -27.532682 | E_var:     0.2114 | E_err:   0.007183
[2025-10-06 00:28:46] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -27.531615 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 00:28:49] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -27.533214 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 00:28:51] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -27.542183 | E_var:     0.1493 | E_err:   0.006038
[2025-10-06 00:28:54] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -27.542344 | E_var:     0.1359 | E_err:   0.005760
[2025-10-06 00:28:56] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -27.538660 | E_var:     0.2238 | E_err:   0.007392
[2025-10-06 00:28:58] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -27.532162 | E_var:     0.1513 | E_err:   0.006078
[2025-10-06 00:28:58] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 00:29:01] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -27.535846 | E_var:     0.2010 | E_err:   0.007005
[2025-10-06 00:29:03] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -27.528253 | E_var:     0.1267 | E_err:   0.005562
[2025-10-06 00:29:06] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -27.539480 | E_var:     0.1573 | E_err:   0.006197
[2025-10-06 00:29:08] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -27.533335 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 00:29:10] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -27.539484 | E_var:     0.1789 | E_err:   0.006610
[2025-10-06 00:29:13] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -27.540717 | E_var:     0.1624 | E_err:   0.006297
[2025-10-06 00:29:15] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -27.533398 | E_var:     0.2696 | E_err:   0.008113
[2025-10-06 00:29:18] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -27.527893 | E_var:     0.1515 | E_err:   0.006083
[2025-10-06 00:29:20] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -27.537843 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 00:29:22] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -27.533784 | E_var:     0.2583 | E_err:   0.007942
[2025-10-06 00:29:25] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -27.531634 | E_var:     0.1506 | E_err:   0.006063
[2025-10-06 00:29:27] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -27.542589 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 00:29:30] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -27.541179 | E_var:     0.1824 | E_err:   0.006673
[2025-10-06 00:29:32] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -27.537171 | E_var:     0.1517 | E_err:   0.006086
[2025-10-06 00:29:35] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -27.534852 | E_var:     0.1516 | E_err:   0.006085
[2025-10-06 00:29:37] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -27.544265 | E_var:     0.1702 | E_err:   0.006446
[2025-10-06 00:29:39] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -27.539606 | E_var:     0.1642 | E_err:   0.006331
[2025-10-06 00:29:42] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -27.543215 | E_var:     0.1613 | E_err:   0.006275
[2025-10-06 00:29:44] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -27.534956 | E_var:     0.1713 | E_err:   0.006467
[2025-10-06 00:29:47] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -27.525014 | E_var:     0.4182 | E_err:   0.010105
[2025-10-06 00:29:49] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -27.530234 | E_var:     0.1721 | E_err:   0.006482
[2025-10-06 00:29:52] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -27.534962 | E_var:     0.1499 | E_err:   0.006049
[2025-10-06 00:29:54] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -27.532637 | E_var:     0.1552 | E_err:   0.006155
[2025-10-06 00:29:56] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -27.520402 | E_var:     0.2608 | E_err:   0.007979
[2025-10-06 00:29:59] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -27.525726 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 00:30:01] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -27.543310 | E_var:     0.1533 | E_err:   0.006117
[2025-10-06 00:30:04] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -27.542120 | E_var:     0.1338 | E_err:   0.005716
[2025-10-06 00:30:06] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -27.529162 | E_var:     0.1822 | E_err:   0.006670
[2025-10-06 00:30:08] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -27.545945 | E_var:     0.1943 | E_err:   0.006888
[2025-10-06 00:30:11] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -27.549937 | E_var:     0.1573 | E_err:   0.006197
[2025-10-06 00:30:13] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -27.534887 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 00:30:16] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -27.522860 | E_var:     0.1580 | E_err:   0.006211
[2025-10-06 00:30:18] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -27.537054 | E_var:     0.1762 | E_err:   0.006560
[2025-10-06 00:30:21] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -27.524330 | E_var:     0.2531 | E_err:   0.007860
[2025-10-06 00:30:23] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -27.528408 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 00:30:25] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -27.533374 | E_var:     0.1483 | E_err:   0.006017
[2025-10-06 00:30:28] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -27.532050 | E_var:     0.1424 | E_err:   0.005896
[2025-10-06 00:30:30] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -27.542653 | E_var:     0.1704 | E_err:   0.006450
[2025-10-06 00:30:33] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -27.530021 | E_var:     0.2766 | E_err:   0.008217
[2025-10-06 00:30:35] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -27.538534 | E_var:     0.1502 | E_err:   0.006056
[2025-10-06 00:30:37] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -27.534466 | E_var:     0.1813 | E_err:   0.006652
[2025-10-06 00:30:40] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -27.533104 | E_var:     0.1593 | E_err:   0.006236
[2025-10-06 00:30:42] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -27.537724 | E_var:     0.1536 | E_err:   0.006123
[2025-10-06 00:30:45] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -27.539656 | E_var:     0.1855 | E_err:   0.006730
[2025-10-06 00:30:47] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -27.533310 | E_var:     0.1916 | E_err:   0.006839
[2025-10-06 00:30:49] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -27.523482 | E_var:     0.1475 | E_err:   0.006000
[2025-10-06 00:30:52] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -27.548664 | E_var:     0.2717 | E_err:   0.008145
[2025-10-06 00:30:54] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -27.547528 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 00:30:57] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -27.529259 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 00:30:59] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -27.544133 | E_var:     0.1518 | E_err:   0.006087
[2025-10-06 00:31:02] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -27.539494 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 00:31:04] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -27.544382 | E_var:     0.1699 | E_err:   0.006440
[2025-10-06 00:31:06] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -27.531741 | E_var:     0.1878 | E_err:   0.006770
[2025-10-06 00:31:09] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -27.532008 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 00:31:11] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -27.532629 | E_var:     0.1865 | E_err:   0.006748
[2025-10-06 00:31:14] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -27.532472 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 00:31:16] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -27.543788 | E_var:     0.1489 | E_err:   0.006029
[2025-10-06 00:31:18] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -27.534512 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 00:31:21] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -27.536406 | E_var:     0.1999 | E_err:   0.006985
[2025-10-06 00:31:23] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -27.531066 | E_var:     0.1760 | E_err:   0.006555
[2025-10-06 00:31:26] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -27.553521 | E_var:     0.3194 | E_err:   0.008831
[2025-10-06 00:31:28] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -27.535759 | E_var:     0.1575 | E_err:   0.006201
[2025-10-06 00:31:30] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -27.535021 | E_var:     0.1892 | E_err:   0.006797
[2025-10-06 00:31:33] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -27.526707 | E_var:     0.2195 | E_err:   0.007320
[2025-10-06 00:31:35] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -27.535095 | E_var:     0.1816 | E_err:   0.006658
[2025-10-06 00:31:38] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -27.538540 | E_var:     0.1399 | E_err:   0.005844
[2025-10-06 00:31:40] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -27.544081 | E_var:     0.2535 | E_err:   0.007868
[2025-10-06 00:31:43] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -27.535131 | E_var:     0.1495 | E_err:   0.006041
[2025-10-06 00:31:45] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -27.545234 | E_var:     0.1653 | E_err:   0.006353
[2025-10-06 00:31:47] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -27.538592 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 00:31:50] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -27.536361 | E_var:     0.1562 | E_err:   0.006175
[2025-10-06 00:31:52] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -27.541626 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 00:31:55] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -27.541776 | E_var:     0.1668 | E_err:   0.006381
[2025-10-06 00:31:57] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -27.523792 | E_var:     0.1535 | E_err:   0.006122
[2025-10-06 00:31:59] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -27.527087 | E_var:     0.1803 | E_err:   0.006635
[2025-10-06 00:32:02] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -27.540463 | E_var:     0.1198 | E_err:   0.005407
[2025-10-06 00:32:04] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -27.522929 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 00:32:07] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -27.547516 | E_var:     0.1452 | E_err:   0.005954
[2025-10-06 00:32:09] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -27.534002 | E_var:     0.1561 | E_err:   0.006172
[2025-10-06 00:32:12] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -27.540110 | E_var:     0.1626 | E_err:   0.006301
[2025-10-06 00:32:14] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -27.532983 | E_var:     0.2527 | E_err:   0.007855
[2025-10-06 00:32:16] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -27.527114 | E_var:     0.1852 | E_err:   0.006725
[2025-10-06 00:32:19] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -27.535803 | E_var:     0.1751 | E_err:   0.006538
[2025-10-06 00:32:21] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -27.546083 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 00:32:24] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -27.529425 | E_var:     0.1735 | E_err:   0.006508
[2025-10-06 00:32:26] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -27.543638 | E_var:     0.1791 | E_err:   0.006612
[2025-10-06 00:32:28] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -27.534759 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 00:32:31] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -27.542197 | E_var:     0.1345 | E_err:   0.005730
[2025-10-06 00:32:33] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -27.536500 | E_var:     0.2033 | E_err:   0.007046
[2025-10-06 00:32:36] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -27.530091 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 00:32:38] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -27.541957 | E_var:     0.1658 | E_err:   0.006363
[2025-10-06 00:32:40] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -27.540792 | E_var:     0.1590 | E_err:   0.006231
[2025-10-06 00:32:43] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -27.541991 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 00:32:45] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -27.531872 | E_var:     0.1378 | E_err:   0.005800
[2025-10-06 00:32:48] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -27.538790 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 00:32:50] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -27.536265 | E_var:     0.1328 | E_err:   0.005693
[2025-10-06 00:32:52] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -27.528171 | E_var:     0.1479 | E_err:   0.006008
[2025-10-06 00:32:55] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -27.539577 | E_var:     0.1361 | E_err:   0.005765
[2025-10-06 00:32:57] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -27.530178 | E_var:     0.1929 | E_err:   0.006863
[2025-10-06 00:33:00] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -27.533714 | E_var:     0.1577 | E_err:   0.006205
[2025-10-06 00:33:00] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 00:33:02] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -27.534986 | E_var:     0.1409 | E_err:   0.005866
[2025-10-06 00:33:05] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -27.538541 | E_var:     0.1440 | E_err:   0.005929
[2025-10-06 00:33:07] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -27.536299 | E_var:     0.1462 | E_err:   0.005974
[2025-10-06 00:33:09] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -27.543388 | E_var:     0.1508 | E_err:   0.006069
[2025-10-06 00:33:12] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -27.536368 | E_var:     0.1794 | E_err:   0.006619
[2025-10-06 00:33:14] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -27.535620 | E_var:     0.1909 | E_err:   0.006828
[2025-10-06 00:33:17] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -27.537733 | E_var:     0.1338 | E_err:   0.005716
[2025-10-06 00:33:19] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -27.542725 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 00:33:22] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -27.530253 | E_var:     0.1487 | E_err:   0.006025
[2025-10-06 00:33:24] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -27.531292 | E_var:     0.4417 | E_err:   0.010385
[2025-10-06 00:33:26] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -27.533632 | E_var:     0.1459 | E_err:   0.005968
[2025-10-06 00:33:29] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -27.546276 | E_var:     0.1347 | E_err:   0.005735
[2025-10-06 00:33:31] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -27.536884 | E_var:     0.1553 | E_err:   0.006158
[2025-10-06 00:33:34] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -27.550741 | E_var:     0.1910 | E_err:   0.006828
[2025-10-06 00:33:36] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -27.538492 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 00:33:38] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -27.538339 | E_var:     0.1691 | E_err:   0.006426
[2025-10-06 00:33:41] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -27.542976 | E_var:     0.1285 | E_err:   0.005601
[2025-10-06 00:33:43] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -27.541866 | E_var:     0.1386 | E_err:   0.005817
[2025-10-06 00:33:46] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -27.541499 | E_var:     0.1831 | E_err:   0.006685
[2025-10-06 00:33:48] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -27.527554 | E_var:     0.1567 | E_err:   0.006186
[2025-10-06 00:33:50] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -27.533917 | E_var:     0.1466 | E_err:   0.005984
[2025-10-06 00:33:53] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -27.535073 | E_var:     0.1420 | E_err:   0.005888
[2025-10-06 00:33:55] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -27.539894 | E_var:     0.2924 | E_err:   0.008449
[2025-10-06 00:33:58] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -27.544260 | E_var:     0.1554 | E_err:   0.006159
[2025-10-06 00:34:00] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -27.534745 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 00:34:03] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -27.535190 | E_var:     0.1480 | E_err:   0.006011
[2025-10-06 00:34:05] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -27.538117 | E_var:     0.1548 | E_err:   0.006148
[2025-10-06 00:34:07] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -27.545205 | E_var:     0.1787 | E_err:   0.006605
[2025-10-06 00:34:10] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -27.538524 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 00:34:12] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -27.522551 | E_var:     0.1847 | E_err:   0.006716
[2025-10-06 00:34:15] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -27.537490 | E_var:     0.1428 | E_err:   0.005905
[2025-10-06 00:34:17] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -27.535596 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 00:34:19] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -27.526535 | E_var:     0.1891 | E_err:   0.006795
[2025-10-06 00:34:22] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -27.536918 | E_var:     0.1415 | E_err:   0.005878
[2025-10-06 00:34:24] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -27.543590 | E_var:     0.1714 | E_err:   0.006468
[2025-10-06 00:34:27] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -27.532243 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 00:34:29] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -27.540759 | E_var:     0.1255 | E_err:   0.005535
[2025-10-06 00:34:32] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -27.526898 | E_var:     0.1550 | E_err:   0.006152
[2025-10-06 00:34:34] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -27.544100 | E_var:     0.1409 | E_err:   0.005865
[2025-10-06 00:34:36] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -27.541303 | E_var:     0.1632 | E_err:   0.006313
[2025-10-06 00:34:39] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -27.533765 | E_var:     0.1266 | E_err:   0.005558
[2025-10-06 00:34:41] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -27.544367 | E_var:     0.1569 | E_err:   0.006190
[2025-10-06 00:34:44] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -27.526646 | E_var:     0.1484 | E_err:   0.006018
[2025-10-06 00:34:46] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -27.529739 | E_var:     0.1710 | E_err:   0.006461
[2025-10-06 00:34:48] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -27.540691 | E_var:     0.1354 | E_err:   0.005750
[2025-10-06 00:34:51] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -27.527766 | E_var:     0.1602 | E_err:   0.006253
[2025-10-06 00:34:53] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -27.543744 | E_var:     0.1552 | E_err:   0.006155
[2025-10-06 00:34:56] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -27.536881 | E_var:     0.1353 | E_err:   0.005748
[2025-10-06 00:34:58] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -27.532475 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 00:35:00] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -27.529516 | E_var:     0.1570 | E_err:   0.006190
[2025-10-06 00:35:00] 🔄 RESTART #2 | Period: 600
[2025-10-06 00:35:03] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -27.528575 | E_var:     0.1527 | E_err:   0.006105
[2025-10-06 00:35:05] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -27.542544 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 00:35:08] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -27.524715 | E_var:     0.1876 | E_err:   0.006767
[2025-10-06 00:35:10] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -27.538807 | E_var:     0.1539 | E_err:   0.006129
[2025-10-06 00:35:12] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -27.530239 | E_var:     0.1358 | E_err:   0.005757
[2025-10-06 00:35:15] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -27.537497 | E_var:     0.1666 | E_err:   0.006377
[2025-10-06 00:35:17] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -27.527716 | E_var:     0.1814 | E_err:   0.006655
[2025-10-06 00:35:20] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -27.535714 | E_var:     0.1459 | E_err:   0.005969
[2025-10-06 00:35:22] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -27.528468 | E_var:     0.1836 | E_err:   0.006696
[2025-10-06 00:35:25] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -27.536663 | E_var:     0.1847 | E_err:   0.006715
[2025-10-06 00:35:27] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -27.540218 | E_var:     0.1674 | E_err:   0.006392
[2025-10-06 00:35:29] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -27.533329 | E_var:     0.1945 | E_err:   0.006891
[2025-10-06 00:35:32] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -27.534361 | E_var:     0.1488 | E_err:   0.006026
[2025-10-06 00:35:34] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -27.545029 | E_var:     0.1378 | E_err:   0.005799
[2025-10-06 00:35:37] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -27.538507 | E_var:     0.1578 | E_err:   0.006207
[2025-10-06 00:35:39] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -27.531174 | E_var:     0.1168 | E_err:   0.005339
[2025-10-06 00:35:41] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -27.540779 | E_var:     0.2821 | E_err:   0.008300
[2025-10-06 00:35:44] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -27.529889 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 00:35:46] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -27.545558 | E_var:     0.1556 | E_err:   0.006163
[2025-10-06 00:35:49] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -27.542740 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 00:35:51] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -27.540092 | E_var:     0.1549 | E_err:   0.006149
[2025-10-06 00:35:54] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -27.535383 | E_var:     0.1433 | E_err:   0.005916
[2025-10-06 00:35:56] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -27.537665 | E_var:     0.1588 | E_err:   0.006227
[2025-10-06 00:35:58] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -27.529344 | E_var:     0.1564 | E_err:   0.006179
[2025-10-06 00:36:01] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -27.541014 | E_var:     0.1390 | E_err:   0.005826
[2025-10-06 00:36:03] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -27.536297 | E_var:     0.1695 | E_err:   0.006432
[2025-10-06 00:36:06] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -27.534763 | E_var:     0.1621 | E_err:   0.006290
[2025-10-06 00:36:08] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -27.524940 | E_var:     0.1428 | E_err:   0.005905
[2025-10-06 00:36:10] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -27.536811 | E_var:     0.1577 | E_err:   0.006204
[2025-10-06 00:36:13] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -27.537063 | E_var:     0.1545 | E_err:   0.006142
[2025-10-06 00:36:15] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -27.535936 | E_var:     0.1548 | E_err:   0.006149
[2025-10-06 00:36:18] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -27.533902 | E_var:     0.1398 | E_err:   0.005841
[2025-10-06 00:36:20] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -27.537171 | E_var:     0.1504 | E_err:   0.006059
[2025-10-06 00:36:23] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -27.540284 | E_var:     0.1486 | E_err:   0.006023
[2025-10-06 00:36:25] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -27.538593 | E_var:     0.1736 | E_err:   0.006510
[2025-10-06 00:36:27] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -27.536130 | E_var:     0.1468 | E_err:   0.005986
[2025-10-06 00:36:30] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -27.542554 | E_var:     0.1362 | E_err:   0.005767
[2025-10-06 00:36:32] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -27.529114 | E_var:     0.1410 | E_err:   0.005868
[2025-10-06 00:36:35] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -27.538190 | E_var:     0.1350 | E_err:   0.005741
[2025-10-06 00:36:37] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -27.535021 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 00:36:39] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -27.523072 | E_var:     0.1776 | E_err:   0.006586
[2025-10-06 00:36:42] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -27.534013 | E_var:     0.2151 | E_err:   0.007247
[2025-10-06 00:36:44] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -27.538112 | E_var:     0.1204 | E_err:   0.005421
[2025-10-06 00:36:47] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -27.542849 | E_var:     0.1481 | E_err:   0.006014
[2025-10-06 00:36:49] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -27.541887 | E_var:     0.1294 | E_err:   0.005621
[2025-10-06 00:36:51] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -27.544506 | E_var:     0.2373 | E_err:   0.007611
[2025-10-06 00:36:54] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -27.538243 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 00:36:56] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -27.535818 | E_var:     0.1601 | E_err:   0.006252
[2025-10-06 00:36:59] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -27.535231 | E_var:     0.1345 | E_err:   0.005730
[2025-10-06 00:37:01] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -27.530830 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 00:37:01] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 00:37:04] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -27.550188 | E_var:     0.1623 | E_err:   0.006295
[2025-10-06 00:37:06] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -27.534952 | E_var:     0.1422 | E_err:   0.005892
[2025-10-06 00:37:08] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -27.528332 | E_var:     0.2853 | E_err:   0.008346
[2025-10-06 00:37:11] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -27.540518 | E_var:     0.1471 | E_err:   0.005993
[2025-10-06 00:37:13] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -27.534130 | E_var:     0.1958 | E_err:   0.006915
[2025-10-06 00:37:16] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -27.527769 | E_var:     0.1621 | E_err:   0.006291
[2025-10-06 00:37:18] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -27.533216 | E_var:     0.1398 | E_err:   0.005843
[2025-10-06 00:37:21] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -27.541767 | E_var:     0.2857 | E_err:   0.008352
[2025-10-06 00:37:23] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -27.534962 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 00:37:25] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -27.547318 | E_var:     0.1781 | E_err:   0.006594
[2025-10-06 00:37:28] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -27.535604 | E_var:     0.1622 | E_err:   0.006292
[2025-10-06 00:37:30] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -27.530952 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 00:37:33] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -27.549692 | E_var:     0.4283 | E_err:   0.010226
[2025-10-06 00:37:35] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -27.541723 | E_var:     0.1617 | E_err:   0.006283
[2025-10-06 00:37:37] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -27.539254 | E_var:     0.1640 | E_err:   0.006328
[2025-10-06 00:37:40] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -27.541188 | E_var:     0.1517 | E_err:   0.006086
[2025-10-06 00:37:42] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -27.538690 | E_var:     0.1548 | E_err:   0.006147
[2025-10-06 00:37:45] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -27.531291 | E_var:     0.1937 | E_err:   0.006877
[2025-10-06 00:37:47] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -27.539471 | E_var:     0.1370 | E_err:   0.005783
[2025-10-06 00:37:50] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -27.535434 | E_var:     0.1412 | E_err:   0.005872
[2025-10-06 00:37:52] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -27.544285 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 00:37:54] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -27.535194 | E_var:     0.1805 | E_err:   0.006639
[2025-10-06 00:37:57] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -27.536223 | E_var:     0.1253 | E_err:   0.005531
[2025-10-06 00:37:59] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -27.537786 | E_var:     0.1660 | E_err:   0.006366
[2025-10-06 00:38:02] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -27.532883 | E_var:     0.1505 | E_err:   0.006062
[2025-10-06 00:38:04] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -27.534498 | E_var:     0.1358 | E_err:   0.005759
[2025-10-06 00:38:06] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -27.530967 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 00:38:09] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -27.536594 | E_var:     0.1267 | E_err:   0.005562
[2025-10-06 00:38:11] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -27.528076 | E_var:     0.1564 | E_err:   0.006179
[2025-10-06 00:38:14] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -27.535919 | E_var:     0.1789 | E_err:   0.006609
[2025-10-06 00:38:16] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -27.543921 | E_var:     0.1352 | E_err:   0.005746
[2025-10-06 00:38:18] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -27.525936 | E_var:     0.1560 | E_err:   0.006171
[2025-10-06 00:38:21] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -27.537703 | E_var:     0.1639 | E_err:   0.006326
[2025-10-06 00:38:23] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -27.523763 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 00:38:26] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -27.527816 | E_var:     0.1687 | E_err:   0.006418
[2025-10-06 00:38:28] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -27.531521 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 00:38:31] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -27.535980 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 00:38:33] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -27.533685 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 00:38:35] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -27.554002 | E_var:     0.1695 | E_err:   0.006434
[2025-10-06 00:38:38] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -27.539084 | E_var:     0.1564 | E_err:   0.006179
[2025-10-06 00:38:40] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -27.540888 | E_var:     0.1473 | E_err:   0.005996
[2025-10-06 00:38:43] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -27.538669 | E_var:     0.1361 | E_err:   0.005765
[2025-10-06 00:38:45] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -27.545435 | E_var:     0.4815 | E_err:   0.010842
[2025-10-06 00:38:48] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -27.539954 | E_var:     0.1969 | E_err:   0.006933
[2025-10-06 00:38:50] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -27.537466 | E_var:     0.3167 | E_err:   0.008793
[2025-10-06 00:38:52] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -27.542898 | E_var:     0.1577 | E_err:   0.006205
[2025-10-06 00:38:55] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -27.534942 | E_var:     0.1248 | E_err:   0.005520
[2025-10-06 00:38:57] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -27.535844 | E_var:     0.2343 | E_err:   0.007564
[2025-10-06 00:39:00] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -27.539101 | E_var:     0.1825 | E_err:   0.006675
[2025-10-06 00:39:02] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -27.538623 | E_var:     0.1345 | E_err:   0.005731
[2025-10-06 00:39:04] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -27.543271 | E_var:     0.1478 | E_err:   0.006008
[2025-10-06 00:39:07] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -27.533184 | E_var:     0.1144 | E_err:   0.005284
[2025-10-06 00:39:09] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -27.541442 | E_var:     0.1464 | E_err:   0.005978
[2025-10-06 00:39:12] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -27.538409 | E_var:     0.1453 | E_err:   0.005955
[2025-10-06 00:39:14] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -27.543174 | E_var:     0.1641 | E_err:   0.006329
[2025-10-06 00:39:17] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -27.533843 | E_var:     0.1506 | E_err:   0.006063
[2025-10-06 00:39:19] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -27.537563 | E_var:     0.1467 | E_err:   0.005985
[2025-10-06 00:39:21] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -27.544236 | E_var:     0.1477 | E_err:   0.006005
[2025-10-06 00:39:24] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -27.536376 | E_var:     0.1476 | E_err:   0.006002
[2025-10-06 00:39:26] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -27.530314 | E_var:     0.2254 | E_err:   0.007419
[2025-10-06 00:39:29] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -27.529573 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 00:39:31] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -27.535477 | E_var:     0.1426 | E_err:   0.005900
[2025-10-06 00:39:33] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -27.537408 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 00:39:36] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -27.547874 | E_var:     0.2093 | E_err:   0.007148
[2025-10-06 00:39:38] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -27.540005 | E_var:     0.1760 | E_err:   0.006555
[2025-10-06 00:39:41] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -27.538526 | E_var:     0.1465 | E_err:   0.005981
[2025-10-06 00:39:43] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -27.532987 | E_var:     0.1398 | E_err:   0.005843
[2025-10-06 00:39:45] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -27.531110 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 00:39:48] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -27.540698 | E_var:     0.2066 | E_err:   0.007102
[2025-10-06 00:39:50] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -27.550457 | E_var:     0.2036 | E_err:   0.007050
[2025-10-06 00:39:53] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -27.543874 | E_var:     0.1950 | E_err:   0.006900
[2025-10-06 00:39:55] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -27.538115 | E_var:     0.1226 | E_err:   0.005470
[2025-10-06 00:39:58] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -27.535426 | E_var:     0.1495 | E_err:   0.006041
[2025-10-06 00:40:00] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -27.539034 | E_var:     0.1711 | E_err:   0.006462
[2025-10-06 00:40:02] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -27.546310 | E_var:     0.1253 | E_err:   0.005530
[2025-10-06 00:40:05] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -27.534032 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 00:40:07] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -27.541276 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 00:40:10] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -27.536345 | E_var:     0.1390 | E_err:   0.005825
[2025-10-06 00:40:12] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -27.532555 | E_var:     0.1424 | E_err:   0.005896
[2025-10-06 00:40:14] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -27.539801 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 00:40:17] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -27.541145 | E_var:     0.1395 | E_err:   0.005835
[2025-10-06 00:40:19] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -27.543191 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 00:40:22] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -27.535963 | E_var:     0.1747 | E_err:   0.006531
[2025-10-06 00:40:24] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -27.534280 | E_var:     0.1339 | E_err:   0.005719
[2025-10-06 00:40:27] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -27.538459 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 00:40:29] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -27.536632 | E_var:     0.1343 | E_err:   0.005726
[2025-10-06 00:40:31] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -27.524029 | E_var:     0.1502 | E_err:   0.006055
[2025-10-06 00:40:34] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -27.535156 | E_var:     0.1603 | E_err:   0.006256
[2025-10-06 00:40:36] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -27.533018 | E_var:     0.1336 | E_err:   0.005712
[2025-10-06 00:40:39] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -27.547700 | E_var:     0.1419 | E_err:   0.005886
[2025-10-06 00:40:41] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -27.535498 | E_var:     0.1363 | E_err:   0.005768
[2025-10-06 00:40:43] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -27.541691 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 00:40:46] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -27.532118 | E_var:     0.1728 | E_err:   0.006495
[2025-10-06 00:40:48] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -27.540400 | E_var:     0.2559 | E_err:   0.007903
[2025-10-06 00:40:51] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -27.539566 | E_var:     0.1558 | E_err:   0.006167
[2025-10-06 00:40:53] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -27.536372 | E_var:     0.1542 | E_err:   0.006135
[2025-10-06 00:40:55] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -27.543611 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 00:40:58] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -27.540476 | E_var:     0.1392 | E_err:   0.005829
[2025-10-06 00:41:00] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -27.540634 | E_var:     0.1441 | E_err:   0.005930
[2025-10-06 00:41:03] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -27.538509 | E_var:     0.1485 | E_err:   0.006021
[2025-10-06 00:41:03] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 00:41:05] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -27.547980 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 00:41:08] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -27.530285 | E_var:     0.1658 | E_err:   0.006362
[2025-10-06 00:41:10] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -27.535294 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 00:41:12] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -27.529060 | E_var:     0.1461 | E_err:   0.005973
[2025-10-06 00:41:15] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -27.537442 | E_var:     0.1398 | E_err:   0.005842
[2025-10-06 00:41:17] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -27.534207 | E_var:     0.1551 | E_err:   0.006153
[2025-10-06 00:41:20] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -27.536395 | E_var:     0.1387 | E_err:   0.005819
[2025-10-06 00:41:22] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -27.538714 | E_var:     0.1482 | E_err:   0.006014
[2025-10-06 00:41:24] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -27.536464 | E_var:     0.1594 | E_err:   0.006239
[2025-10-06 00:41:27] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -27.532951 | E_var:     0.3427 | E_err:   0.009147
[2025-10-06 00:41:29] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -27.534840 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 00:41:32] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -27.537913 | E_var:     0.1436 | E_err:   0.005921
[2025-10-06 00:41:34] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -27.541617 | E_var:     0.1592 | E_err:   0.006234
[2025-10-06 00:41:41] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -27.532481 | E_var:     0.2283 | E_err:   0.007465
[2025-10-06 00:41:44] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -27.537537 | E_var:     0.2597 | E_err:   0.007963
[2025-10-06 00:41:46] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -27.545063 | E_var:     0.2097 | E_err:   0.007155
[2025-10-06 00:41:49] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -27.535044 | E_var:     0.1702 | E_err:   0.006446
[2025-10-06 00:41:51] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -27.535533 | E_var:     0.1635 | E_err:   0.006317
[2025-10-06 00:41:53] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -27.547826 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 00:41:56] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -27.539532 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 00:41:58] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -27.552262 | E_var:     0.1354 | E_err:   0.005750
[2025-10-06 00:42:01] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -27.537468 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 00:42:03] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -27.538594 | E_var:     0.1266 | E_err:   0.005560
[2025-10-06 00:42:05] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -27.545479 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 00:42:08] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -27.543159 | E_var:     0.1503 | E_err:   0.006057
[2025-10-06 00:42:10] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -27.532828 | E_var:     0.1769 | E_err:   0.006573
[2025-10-06 00:42:13] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -27.548359 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 00:42:15] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -27.535515 | E_var:     0.3106 | E_err:   0.008707
[2025-10-06 00:42:17] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -27.543219 | E_var:     0.1859 | E_err:   0.006737
[2025-10-06 00:42:20] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -27.532884 | E_var:     0.2196 | E_err:   0.007323
[2025-10-06 00:42:22] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -27.529055 | E_var:     0.1847 | E_err:   0.006714
[2025-10-06 00:42:25] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -27.539237 | E_var:     0.1363 | E_err:   0.005769
[2025-10-06 00:42:27] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -27.531335 | E_var:     0.1587 | E_err:   0.006224
[2025-10-06 00:42:30] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -27.538431 | E_var:     0.1440 | E_err:   0.005930
[2025-10-06 00:42:32] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -27.543152 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 00:42:34] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -27.539196 | E_var:     0.1774 | E_err:   0.006580
[2025-10-06 00:42:37] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -27.539078 | E_var:     0.1331 | E_err:   0.005700
[2025-10-06 00:42:39] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -27.525920 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 00:42:42] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -27.527043 | E_var:     0.1554 | E_err:   0.006159
[2025-10-06 00:42:44] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -27.540711 | E_var:     0.1369 | E_err:   0.005781
[2025-10-06 00:42:47] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -27.547821 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 00:42:49] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -27.538216 | E_var:     0.1408 | E_err:   0.005863
[2025-10-06 00:42:51] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -27.536851 | E_var:     0.1788 | E_err:   0.006608
[2025-10-06 00:42:54] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -27.536945 | E_var:     0.1685 | E_err:   0.006413
[2025-10-06 00:42:56] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -27.536307 | E_var:     0.1553 | E_err:   0.006158
[2025-10-06 00:42:59] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -27.536892 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 00:43:01] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -27.549177 | E_var:     0.1776 | E_err:   0.006584
[2025-10-06 00:43:03] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -27.540926 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 00:43:06] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -27.534853 | E_var:     0.2274 | E_err:   0.007451
[2025-10-06 00:43:08] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -27.531464 | E_var:     0.1922 | E_err:   0.006850
[2025-10-06 00:43:11] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -27.539885 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 00:43:13] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -27.534345 | E_var:     0.1910 | E_err:   0.006829
[2025-10-06 00:43:15] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -27.534947 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 00:43:18] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -27.548843 | E_var:     0.1589 | E_err:   0.006228
[2025-10-06 00:43:20] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -27.544570 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 00:43:23] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -27.539012 | E_var:     0.1399 | E_err:   0.005844
[2025-10-06 00:43:25] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -27.530888 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 00:43:28] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -27.531693 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 00:43:30] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -27.530102 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 00:43:32] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -27.535114 | E_var:     0.1525 | E_err:   0.006101
[2025-10-06 00:43:35] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -27.534490 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 00:43:37] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -27.543640 | E_var:     0.1450 | E_err:   0.005950
[2025-10-06 00:43:40] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -27.533135 | E_var:     0.1471 | E_err:   0.005993
[2025-10-06 00:43:42] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -27.536997 | E_var:     0.1201 | E_err:   0.005416
[2025-10-06 00:43:44] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -27.537216 | E_var:     0.1316 | E_err:   0.005667
[2025-10-06 00:43:47] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -27.532770 | E_var:     0.1517 | E_err:   0.006086
[2025-10-06 00:43:49] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -27.539748 | E_var:     0.1766 | E_err:   0.006567
[2025-10-06 00:43:52] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -27.533609 | E_var:     0.1592 | E_err:   0.006234
[2025-10-06 00:43:54] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -27.538487 | E_var:     0.1620 | E_err:   0.006289
[2025-10-06 00:43:56] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -27.542375 | E_var:     0.2084 | E_err:   0.007132
[2025-10-06 00:43:59] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -27.538822 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 00:44:01] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -27.533937 | E_var:     0.1268 | E_err:   0.005564
[2025-10-06 00:44:04] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -27.539262 | E_var:     0.1912 | E_err:   0.006832
[2025-10-06 00:44:06] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -27.535667 | E_var:     0.1390 | E_err:   0.005824
[2025-10-06 00:44:09] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -27.543069 | E_var:     0.1316 | E_err:   0.005668
[2025-10-06 00:44:11] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -27.535862 | E_var:     0.1293 | E_err:   0.005619
[2025-10-06 00:44:13] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -27.542904 | E_var:     0.1552 | E_err:   0.006156
[2025-10-06 00:44:16] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -27.544856 | E_var:     0.1337 | E_err:   0.005713
[2025-10-06 00:44:18] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -27.534782 | E_var:     0.1451 | E_err:   0.005953
[2025-10-06 00:44:21] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -27.536560 | E_var:     0.1674 | E_err:   0.006393
[2025-10-06 00:44:23] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -27.541763 | E_var:     0.1487 | E_err:   0.006025
[2025-10-06 00:44:26] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -27.535393 | E_var:     0.1541 | E_err:   0.006134
[2025-10-06 00:44:28] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -27.536571 | E_var:     0.1760 | E_err:   0.006555
[2025-10-06 00:44:30] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -27.535179 | E_var:     0.1461 | E_err:   0.005971
[2025-10-06 00:44:33] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -27.535943 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 00:44:35] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -27.543889 | E_var:     0.1169 | E_err:   0.005343
[2025-10-06 00:44:38] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -27.546475 | E_var:     0.1404 | E_err:   0.005855
[2025-10-06 00:44:40] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -27.547697 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 00:44:42] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -27.544032 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 00:44:45] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -27.531909 | E_var:     0.2364 | E_err:   0.007598
[2025-10-06 00:44:47] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -27.538345 | E_var:     0.1510 | E_err:   0.006071
[2025-10-06 00:44:50] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -27.539775 | E_var:     0.2018 | E_err:   0.007019
[2025-10-06 00:44:52] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -27.542194 | E_var:     0.1339 | E_err:   0.005717
[2025-10-06 00:44:55] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -27.544493 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 00:44:57] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -27.548435 | E_var:     0.1633 | E_err:   0.006314
[2025-10-06 00:44:59] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -27.535326 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 00:45:02] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -27.532766 | E_var:     0.1886 | E_err:   0.006786
[2025-10-06 00:45:04] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -27.539033 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 00:45:07] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -27.531368 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 00:45:09] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -27.539217 | E_var:     0.1270 | E_err:   0.005568
[2025-10-06 00:45:09] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 00:45:11] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -27.537907 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 00:45:14] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -27.543635 | E_var:     0.2134 | E_err:   0.007217
[2025-10-06 00:45:16] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -27.520747 | E_var:     0.1368 | E_err:   0.005779
[2025-10-06 00:45:19] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -27.552354 | E_var:     0.1952 | E_err:   0.006903
[2025-10-06 00:45:21] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -27.543481 | E_var:     0.1808 | E_err:   0.006644
[2025-10-06 00:45:23] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -27.527933 | E_var:     0.1553 | E_err:   0.006158
[2025-10-06 00:45:26] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -27.545807 | E_var:     0.1365 | E_err:   0.005772
[2025-10-06 00:45:28] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -27.541086 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 00:45:31] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -27.533558 | E_var:     0.1298 | E_err:   0.005630
[2025-10-06 00:45:33] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -27.533133 | E_var:     0.1541 | E_err:   0.006134
[2025-10-06 00:45:36] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -27.538385 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 00:45:38] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -27.540010 | E_var:     0.1482 | E_err:   0.006015
[2025-10-06 00:45:40] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -27.534577 | E_var:     0.1599 | E_err:   0.006248
[2025-10-06 00:45:43] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -27.531457 | E_var:     0.1441 | E_err:   0.005930
[2025-10-06 00:45:45] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -27.540249 | E_var:     0.1585 | E_err:   0.006221
[2025-10-06 00:45:48] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -27.535647 | E_var:     0.1724 | E_err:   0.006488
[2025-10-06 00:45:50] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -27.530105 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 00:45:52] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -27.524524 | E_var:     0.1369 | E_err:   0.005780
[2025-10-06 00:45:55] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -27.541725 | E_var:     0.1256 | E_err:   0.005538
[2025-10-06 00:45:57] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -27.536675 | E_var:     0.1574 | E_err:   0.006200
[2025-10-06 00:46:00] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -27.546136 | E_var:     0.1351 | E_err:   0.005743
[2025-10-06 00:46:02] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -27.544435 | E_var:     0.2103 | E_err:   0.007165
[2025-10-06 00:46:05] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -27.547766 | E_var:     0.1475 | E_err:   0.006000
[2025-10-06 00:46:07] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -27.540375 | E_var:     0.1495 | E_err:   0.006042
[2025-10-06 00:46:09] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -27.541476 | E_var:     0.1592 | E_err:   0.006234
[2025-10-06 00:46:12] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -27.546102 | E_var:     0.1409 | E_err:   0.005865
[2025-10-06 00:46:14] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -27.543271 | E_var:     0.1627 | E_err:   0.006302
[2025-10-06 00:46:17] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -27.537971 | E_var:     0.1517 | E_err:   0.006086
[2025-10-06 00:46:19] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -27.545908 | E_var:     0.1239 | E_err:   0.005501
[2025-10-06 00:46:21] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -27.540426 | E_var:     0.1635 | E_err:   0.006318
[2025-10-06 00:46:24] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -27.530702 | E_var:     0.1707 | E_err:   0.006456
[2025-10-06 00:46:26] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -27.538618 | E_var:     0.1613 | E_err:   0.006276
[2025-10-06 00:46:29] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -27.542926 | E_var:     0.1134 | E_err:   0.005261
[2025-10-06 00:46:31] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -27.524360 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 00:46:34] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -27.540606 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 00:46:36] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -27.539802 | E_var:     0.1617 | E_err:   0.006283
[2025-10-06 00:46:38] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -27.551459 | E_var:     0.1512 | E_err:   0.006076
[2025-10-06 00:46:41] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -27.542221 | E_var:     0.1355 | E_err:   0.005752
[2025-10-06 00:46:43] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -27.530275 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 00:46:46] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -27.534099 | E_var:     0.1226 | E_err:   0.005472
[2025-10-06 00:46:48] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -27.536980 | E_var:     0.1637 | E_err:   0.006322
[2025-10-06 00:46:51] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -27.532068 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 00:46:53] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -27.540147 | E_var:     0.1479 | E_err:   0.006009
[2025-10-06 00:46:55] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -27.534246 | E_var:     0.1409 | E_err:   0.005865
[2025-10-06 00:46:58] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -27.556012 | E_var:     0.1657 | E_err:   0.006360
[2025-10-06 00:47:00] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -27.545424 | E_var:     0.1496 | E_err:   0.006043
[2025-10-06 00:47:03] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -27.538882 | E_var:     0.1643 | E_err:   0.006333
[2025-10-06 00:47:05] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -27.541139 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 00:47:08] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -27.540344 | E_var:     0.1384 | E_err:   0.005813
[2025-10-06 00:47:10] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -27.535533 | E_var:     0.1145 | E_err:   0.005287
[2025-10-06 00:47:12] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -27.536852 | E_var:     0.1385 | E_err:   0.005816
[2025-10-06 00:47:15] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -27.532859 | E_var:     0.1344 | E_err:   0.005728
[2025-10-06 00:47:17] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -27.534293 | E_var:     0.1364 | E_err:   0.005771
[2025-10-06 00:47:20] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -27.524981 | E_var:     0.1704 | E_err:   0.006451
[2025-10-06 00:47:22] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -27.536030 | E_var:     0.1972 | E_err:   0.006939
[2025-10-06 00:47:24] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -27.526070 | E_var:     0.3405 | E_err:   0.009117
[2025-10-06 00:47:27] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -27.545251 | E_var:     0.1257 | E_err:   0.005541
[2025-10-06 00:47:29] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -27.540298 | E_var:     0.1307 | E_err:   0.005648
[2025-10-06 00:47:32] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -27.536936 | E_var:     0.1259 | E_err:   0.005545
[2025-10-06 00:47:34] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -27.544561 | E_var:     0.1905 | E_err:   0.006820
[2025-10-06 00:47:37] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -27.531768 | E_var:     0.1638 | E_err:   0.006324
[2025-10-06 00:47:39] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -27.538340 | E_var:     0.1369 | E_err:   0.005781
[2025-10-06 00:47:41] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -27.542273 | E_var:     0.1227 | E_err:   0.005473
[2025-10-06 00:47:44] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -27.546349 | E_var:     0.1657 | E_err:   0.006361
[2025-10-06 00:47:46] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -27.536428 | E_var:     0.1592 | E_err:   0.006234
[2025-10-06 00:47:49] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -27.530780 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 00:47:51] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -27.541876 | E_var:     0.1535 | E_err:   0.006122
[2025-10-06 00:47:54] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -27.546826 | E_var:     0.1727 | E_err:   0.006494
[2025-10-06 00:47:56] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -27.534028 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 00:47:58] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -27.530595 | E_var:     0.1485 | E_err:   0.006022
[2025-10-06 00:48:01] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -27.534674 | E_var:     0.1652 | E_err:   0.006350
[2025-10-06 00:48:03] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -27.540842 | E_var:     0.1661 | E_err:   0.006368
[2025-10-06 00:48:06] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -27.530687 | E_var:     0.1524 | E_err:   0.006100
[2025-10-06 00:48:08] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -27.547740 | E_var:     0.1546 | E_err:   0.006144
[2025-10-06 00:48:10] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -27.548716 | E_var:     0.1578 | E_err:   0.006206
[2025-10-06 00:48:13] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -27.533514 | E_var:     0.1522 | E_err:   0.006095
[2025-10-06 00:48:15] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -27.540892 | E_var:     0.1432 | E_err:   0.005912
[2025-10-06 00:48:18] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -27.535085 | E_var:     0.1154 | E_err:   0.005309
[2025-10-06 00:48:20] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -27.541783 | E_var:     0.1721 | E_err:   0.006481
[2025-10-06 00:48:23] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -27.542152 | E_var:     0.1491 | E_err:   0.006033
[2025-10-06 00:48:25] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -27.533554 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 00:48:27] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -27.530809 | E_var:     0.3410 | E_err:   0.009124
[2025-10-06 00:48:30] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -27.538376 | E_var:     0.1675 | E_err:   0.006395
[2025-10-06 00:48:32] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -27.538249 | E_var:     0.1386 | E_err:   0.005817
[2025-10-06 00:48:35] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -27.532770 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 00:48:37] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -27.544659 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 00:48:39] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -27.546436 | E_var:     0.1580 | E_err:   0.006210
[2025-10-06 00:48:42] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -27.531575 | E_var:     0.1489 | E_err:   0.006028
[2025-10-06 00:48:44] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -27.539241 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 00:48:47] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -27.540782 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 00:48:49] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -27.536541 | E_var:     0.1451 | E_err:   0.005952
[2025-10-06 00:48:52] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -27.542484 | E_var:     0.1308 | E_err:   0.005650
[2025-10-06 00:48:54] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -27.529598 | E_var:     0.1621 | E_err:   0.006292
[2025-10-06 00:48:56] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -27.538440 | E_var:     0.1864 | E_err:   0.006746
[2025-10-06 00:48:59] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -27.542932 | E_var:     0.1193 | E_err:   0.005398
[2025-10-06 00:49:01] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -27.540460 | E_var:     0.1384 | E_err:   0.005813
[2025-10-06 00:49:04] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -27.536359 | E_var:     0.2084 | E_err:   0.007134
[2025-10-06 00:49:06] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -27.534659 | E_var:     0.4879 | E_err:   0.010914
[2025-10-06 00:49:08] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -27.542771 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 00:49:11] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -27.543767 | E_var:     0.1721 | E_err:   0.006481
[2025-10-06 00:49:11] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 00:49:13] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -27.537831 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 00:49:16] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -27.535953 | E_var:     0.1208 | E_err:   0.005431
[2025-10-06 00:49:18] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -27.533632 | E_var:     0.1970 | E_err:   0.006935
[2025-10-06 00:49:20] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -27.535567 | E_var:     0.1483 | E_err:   0.006016
[2025-10-06 00:49:23] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -27.537431 | E_var:     0.1383 | E_err:   0.005812
[2025-10-06 00:49:25] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -27.542404 | E_var:     0.1432 | E_err:   0.005913
[2025-10-06 00:49:28] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -27.544753 | E_var:     0.1947 | E_err:   0.006894
[2025-10-06 00:49:30] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -27.536248 | E_var:     0.1381 | E_err:   0.005806
[2025-10-06 00:49:33] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -27.537825 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 00:49:35] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -27.543028 | E_var:     0.1306 | E_err:   0.005646
[2025-10-06 00:49:37] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -27.540911 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 00:49:40] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -27.540560 | E_var:     0.1611 | E_err:   0.006272
[2025-10-06 00:49:42] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -27.539373 | E_var:     0.2468 | E_err:   0.007762
[2025-10-06 00:49:45] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -27.538150 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 00:49:47] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -27.538553 | E_var:     0.1462 | E_err:   0.005975
[2025-10-06 00:49:50] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -27.539571 | E_var:     0.1641 | E_err:   0.006329
[2025-10-06 00:49:52] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -27.537961 | E_var:     0.2458 | E_err:   0.007747
[2025-10-06 00:49:54] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -27.543958 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 00:49:57] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -27.543974 | E_var:     0.1426 | E_err:   0.005900
[2025-10-06 00:49:59] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -27.529582 | E_var:     0.1524 | E_err:   0.006101
[2025-10-06 00:50:02] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -27.533413 | E_var:     0.1832 | E_err:   0.006687
[2025-10-06 00:50:04] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -27.536243 | E_var:     0.1850 | E_err:   0.006721
[2025-10-06 00:50:06] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -27.538101 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 00:50:09] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -27.533208 | E_var:     0.1426 | E_err:   0.005900
[2025-10-06 00:50:11] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -27.551031 | E_var:     0.1378 | E_err:   0.005799
[2025-10-06 00:50:14] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -27.538390 | E_var:     0.1239 | E_err:   0.005501
[2025-10-06 00:50:16] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -27.545071 | E_var:     0.1440 | E_err:   0.005929
[2025-10-06 00:50:19] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -27.543702 | E_var:     0.1434 | E_err:   0.005918
[2025-10-06 00:50:21] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -27.537142 | E_var:     0.1509 | E_err:   0.006069
[2025-10-06 00:50:23] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -27.547029 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 00:50:26] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -27.547256 | E_var:     0.1684 | E_err:   0.006412
[2025-10-06 00:50:28] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -27.529040 | E_var:     0.1798 | E_err:   0.006625
[2025-10-06 00:50:31] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -27.534996 | E_var:     0.1707 | E_err:   0.006456
[2025-10-06 00:50:33] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -27.541339 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 00:50:35] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -27.541686 | E_var:     0.2108 | E_err:   0.007173
[2025-10-06 00:50:38] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -27.545301 | E_var:     0.1451 | E_err:   0.005952
[2025-10-06 00:50:40] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -27.540797 | E_var:     0.1346 | E_err:   0.005733
[2025-10-06 00:50:43] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -27.548834 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 00:50:45] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -27.542264 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 00:50:48] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -27.544270 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 00:50:50] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -27.543405 | E_var:     0.1556 | E_err:   0.006163
[2025-10-06 00:50:52] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -27.550253 | E_var:     0.1496 | E_err:   0.006044
[2025-10-06 00:50:55] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -27.555935 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 00:50:57] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -27.530247 | E_var:     0.1279 | E_err:   0.005588
[2025-10-06 00:51:00] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -27.539207 | E_var:     0.1690 | E_err:   0.006422
[2025-10-06 00:51:02] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -27.530588 | E_var:     0.1280 | E_err:   0.005590
[2025-10-06 00:51:04] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -27.544532 | E_var:     0.1380 | E_err:   0.005804
[2025-10-06 00:51:07] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -27.547214 | E_var:     0.1354 | E_err:   0.005750
[2025-10-06 00:51:09] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -27.538217 | E_var:     0.1527 | E_err:   0.006105
[2025-10-06 00:51:12] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -27.537607 | E_var:     0.1698 | E_err:   0.006438
[2025-10-06 00:51:14] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -27.536116 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 00:51:17] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -27.539484 | E_var:     0.1511 | E_err:   0.006073
[2025-10-06 00:51:19] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -27.539899 | E_var:     0.1350 | E_err:   0.005741
[2025-10-06 00:51:21] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -27.539014 | E_var:     0.1164 | E_err:   0.005331
[2025-10-06 00:51:24] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -27.529241 | E_var:     0.1413 | E_err:   0.005873
[2025-10-06 00:51:26] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -27.541467 | E_var:     0.1303 | E_err:   0.005641
[2025-10-06 00:51:29] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -27.539351 | E_var:     0.1274 | E_err:   0.005578
[2025-10-06 00:51:31] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -27.539538 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 00:51:33] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -27.534667 | E_var:     0.2455 | E_err:   0.007741
[2025-10-06 00:51:36] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -27.543833 | E_var:     0.1833 | E_err:   0.006690
[2025-10-06 00:51:38] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -27.540861 | E_var:     0.2294 | E_err:   0.007484
[2025-10-06 00:51:41] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -27.535748 | E_var:     0.1200 | E_err:   0.005413
[2025-10-06 00:51:43] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -27.538520 | E_var:     0.1436 | E_err:   0.005920
[2025-10-06 00:51:45] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -27.545334 | E_var:     0.1319 | E_err:   0.005674
[2025-10-06 00:51:48] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -27.537248 | E_var:     0.1441 | E_err:   0.005932
[2025-10-06 00:51:50] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -27.539464 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 00:51:53] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -27.539148 | E_var:     0.1246 | E_err:   0.005514
[2025-10-06 00:51:55] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -27.533872 | E_var:     0.1259 | E_err:   0.005545
[2025-10-06 00:51:58] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -27.541170 | E_var:     0.1554 | E_err:   0.006159
[2025-10-06 00:52:00] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -27.538026 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 00:52:02] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -27.542173 | E_var:     0.1608 | E_err:   0.006266
[2025-10-06 00:52:05] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -27.534823 | E_var:     0.1469 | E_err:   0.005988
[2025-10-06 00:52:07] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -27.546683 | E_var:     0.1151 | E_err:   0.005301
[2025-10-06 00:52:10] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -27.527947 | E_var:     0.1470 | E_err:   0.005990
[2025-10-06 00:52:12] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -27.536941 | E_var:     0.1436 | E_err:   0.005921
[2025-10-06 00:52:15] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -27.536962 | E_var:     0.1459 | E_err:   0.005968
[2025-10-06 00:52:17] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -27.538296 | E_var:     0.1285 | E_err:   0.005601
[2025-10-06 00:52:19] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -27.541098 | E_var:     0.2082 | E_err:   0.007130
[2025-10-06 00:52:22] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -27.538663 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 00:52:24] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -27.546917 | E_var:     0.1293 | E_err:   0.005618
[2025-10-06 00:52:27] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -27.546266 | E_var:     0.1178 | E_err:   0.005364
[2025-10-06 00:52:29] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -27.537542 | E_var:     0.1990 | E_err:   0.006970
[2025-10-06 00:52:31] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -27.532979 | E_var:     0.1507 | E_err:   0.006066
[2025-10-06 00:52:34] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -27.545853 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 00:52:36] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -27.533953 | E_var:     0.1752 | E_err:   0.006540
[2025-10-06 00:52:39] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -27.535017 | E_var:     0.1806 | E_err:   0.006641
[2025-10-06 00:52:41] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -27.536836 | E_var:     0.1390 | E_err:   0.005826
[2025-10-06 00:52:44] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -27.545648 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 00:52:46] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -27.540389 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 00:52:48] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -27.540920 | E_var:     0.1585 | E_err:   0.006221
[2025-10-06 00:52:51] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -27.538837 | E_var:     0.2106 | E_err:   0.007170
[2025-10-06 00:52:53] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -27.542701 | E_var:     0.1571 | E_err:   0.006193
[2025-10-06 00:52:56] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -27.537197 | E_var:     0.1426 | E_err:   0.005900
[2025-10-06 00:52:58] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -27.533100 | E_var:     0.1565 | E_err:   0.006182
[2025-10-06 00:53:00] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -27.545708 | E_var:     0.1343 | E_err:   0.005726
[2025-10-06 00:53:03] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -27.538007 | E_var:     0.1410 | E_err:   0.005868
[2025-10-06 00:53:05] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -27.543606 | E_var:     0.1296 | E_err:   0.005626
[2025-10-06 00:53:08] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -27.545244 | E_var:     0.1286 | E_err:   0.005603
[2025-10-06 00:53:10] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -27.540387 | E_var:     0.1822 | E_err:   0.006670
[2025-10-06 00:53:12] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -27.524497 | E_var:     0.1382 | E_err:   0.005809
[2025-10-06 00:53:13] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 00:53:15] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -27.537819 | E_var:     0.1476 | E_err:   0.006003
[2025-10-06 00:53:17] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -27.544807 | E_var:     0.1821 | E_err:   0.006667
[2025-10-06 00:53:20] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -27.541761 | E_var:     0.1642 | E_err:   0.006332
[2025-10-06 00:53:22] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -27.542772 | E_var:     0.1374 | E_err:   0.005792
[2025-10-06 00:53:25] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -27.535868 | E_var:     0.1417 | E_err:   0.005883
[2025-10-06 00:53:27] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -27.537565 | E_var:     0.1399 | E_err:   0.005843
[2025-10-06 00:53:29] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -27.544952 | E_var:     0.1551 | E_err:   0.006153
[2025-10-06 00:53:32] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -27.545289 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 00:53:34] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -27.547142 | E_var:     0.1728 | E_err:   0.006494
[2025-10-06 00:53:37] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -27.537977 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 00:53:39] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -27.532138 | E_var:     0.3309 | E_err:   0.008988
[2025-10-06 00:53:41] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -27.545735 | E_var:     0.1378 | E_err:   0.005799
[2025-10-06 00:53:44] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -27.537271 | E_var:     0.1555 | E_err:   0.006162
[2025-10-06 00:53:46] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -27.545065 | E_var:     0.1232 | E_err:   0.005484
[2025-10-06 00:53:49] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -27.527743 | E_var:     0.1870 | E_err:   0.006757
[2025-10-06 00:53:51] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -27.539196 | E_var:     0.1399 | E_err:   0.005845
[2025-10-06 00:53:54] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -27.537589 | E_var:     0.1150 | E_err:   0.005300
[2025-10-06 00:53:56] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -27.541538 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 00:53:58] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -27.546882 | E_var:     0.1385 | E_err:   0.005815
[2025-10-06 00:54:01] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -27.526869 | E_var:     0.1623 | E_err:   0.006296
[2025-10-06 00:54:03] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -27.533985 | E_var:     0.1627 | E_err:   0.006302
[2025-10-06 00:54:06] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -27.525227 | E_var:     0.1943 | E_err:   0.006888
[2025-10-06 00:54:08] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -27.544344 | E_var:     0.1301 | E_err:   0.005635
[2025-10-06 00:54:10] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -27.534707 | E_var:     0.1661 | E_err:   0.006367
[2025-10-06 00:54:13] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -27.532215 | E_var:     0.1163 | E_err:   0.005329
[2025-10-06 00:54:15] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -27.546148 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 00:54:18] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -27.542061 | E_var:     0.1750 | E_err:   0.006536
[2025-10-06 00:54:20] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -27.549318 | E_var:     0.1611 | E_err:   0.006271
[2025-10-06 00:54:23] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -27.544874 | E_var:     0.1617 | E_err:   0.006283
[2025-10-06 00:54:25] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -27.539346 | E_var:     0.1623 | E_err:   0.006295
[2025-10-06 00:54:27] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -27.521054 | E_var:     0.2101 | E_err:   0.007162
[2025-10-06 00:54:30] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -27.543568 | E_var:     0.1270 | E_err:   0.005569
[2025-10-06 00:54:32] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -27.549881 | E_var:     0.1376 | E_err:   0.005795
[2025-10-06 00:54:35] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -27.543943 | E_var:     0.1392 | E_err:   0.005829
[2025-10-06 00:54:37] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -27.537606 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 00:54:39] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -27.531898 | E_var:     0.1356 | E_err:   0.005754
[2025-10-06 00:54:42] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -27.539802 | E_var:     0.1289 | E_err:   0.005609
[2025-10-06 00:54:44] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -27.547061 | E_var:     0.1405 | E_err:   0.005858
[2025-10-06 00:54:47] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -27.541284 | E_var:     0.1670 | E_err:   0.006385
[2025-10-06 00:54:49] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -27.544670 | E_var:     0.1318 | E_err:   0.005673
[2025-10-06 00:54:52] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -27.535656 | E_var:     0.1688 | E_err:   0.006419
[2025-10-06 00:54:54] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -27.544286 | E_var:     0.1706 | E_err:   0.006454
[2025-10-06 00:54:56] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -27.541571 | E_var:     0.1245 | E_err:   0.005512
[2025-10-06 00:54:59] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -27.543069 | E_var:     0.1901 | E_err:   0.006812
[2025-10-06 00:55:01] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -27.540591 | E_var:     0.1589 | E_err:   0.006228
[2025-10-06 00:55:04] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -27.534808 | E_var:     0.1462 | E_err:   0.005975
[2025-10-06 00:55:06] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -27.540850 | E_var:     0.1249 | E_err:   0.005521
[2025-10-06 00:55:08] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -27.543841 | E_var:     0.1464 | E_err:   0.005978
[2025-10-06 00:55:11] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -27.535284 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 00:55:13] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -27.535572 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 00:55:16] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -27.539714 | E_var:     0.1460 | E_err:   0.005971
[2025-10-06 00:55:18] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -27.539836 | E_var:     0.1294 | E_err:   0.005621
[2025-10-06 00:55:21] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -27.534565 | E_var:     0.1211 | E_err:   0.005436
[2025-10-06 00:55:23] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -27.541675 | E_var:     0.1553 | E_err:   0.006158
[2025-10-06 00:55:25] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -27.549651 | E_var:     0.1494 | E_err:   0.006040
[2025-10-06 00:55:28] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -27.532882 | E_var:     0.1312 | E_err:   0.005659
[2025-10-06 00:55:30] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -27.536362 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 00:55:33] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -27.526942 | E_var:     0.1325 | E_err:   0.005689
[2025-10-06 00:55:35] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -27.536945 | E_var:     0.3073 | E_err:   0.008662
[2025-10-06 00:55:37] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -27.535353 | E_var:     0.1449 | E_err:   0.005947
[2025-10-06 00:55:40] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -27.540274 | E_var:     0.1638 | E_err:   0.006323
[2025-10-06 00:55:42] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -27.543888 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 00:55:45] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -27.548917 | E_var:     0.1631 | E_err:   0.006311
[2025-10-06 00:55:47] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -27.539591 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 00:55:49] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -27.548475 | E_var:     0.1789 | E_err:   0.006608
[2025-10-06 00:55:52] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -27.537756 | E_var:     0.1207 | E_err:   0.005428
[2025-10-06 00:55:54] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -27.544510 | E_var:     0.1267 | E_err:   0.005562
[2025-10-06 00:55:57] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -27.543098 | E_var:     0.1210 | E_err:   0.005434
[2025-10-06 00:55:59] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -27.539953 | E_var:     0.1276 | E_err:   0.005581
[2025-10-06 00:56:02] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -27.529382 | E_var:     0.1382 | E_err:   0.005808
[2025-10-06 00:56:04] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -27.541835 | E_var:     0.1345 | E_err:   0.005729
[2025-10-06 00:56:06] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -27.540524 | E_var:     0.1341 | E_err:   0.005722
[2025-10-06 00:56:09] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -27.536869 | E_var:     0.3301 | E_err:   0.008977
[2025-10-06 00:56:11] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -27.538527 | E_var:     0.1284 | E_err:   0.005599
[2025-10-06 00:56:14] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -27.539326 | E_var:     0.1435 | E_err:   0.005919
[2025-10-06 00:56:16] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -27.539425 | E_var:     0.1708 | E_err:   0.006457
[2025-10-06 00:56:18] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -27.534285 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 00:56:21] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -27.539788 | E_var:     0.1502 | E_err:   0.006056
[2025-10-06 00:56:23] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -27.534508 | E_var:     0.1517 | E_err:   0.006085
[2025-10-06 00:56:26] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -27.535054 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 00:56:28] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -27.552420 | E_var:     0.1464 | E_err:   0.005978
[2025-10-06 00:56:31] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -27.539364 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 00:56:33] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -27.544846 | E_var:     0.1675 | E_err:   0.006394
[2025-10-06 00:56:35] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -27.543868 | E_var:     0.1301 | E_err:   0.005637
[2025-10-06 00:56:38] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -27.540434 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 00:56:40] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -27.550842 | E_var:     0.1568 | E_err:   0.006186
[2025-10-06 00:56:43] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -27.547702 | E_var:     0.1632 | E_err:   0.006311
[2025-10-06 00:56:45] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -27.541076 | E_var:     0.1235 | E_err:   0.005491
[2025-10-06 00:56:47] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -27.527654 | E_var:     0.1426 | E_err:   0.005901
[2025-10-06 00:56:50] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -27.534845 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 00:56:52] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -27.530536 | E_var:     0.2201 | E_err:   0.007331
[2025-10-06 00:56:55] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -27.543387 | E_var:     0.2814 | E_err:   0.008289
[2025-10-06 00:56:57] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -27.547843 | E_var:     0.1221 | E_err:   0.005461
[2025-10-06 00:57:00] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -27.532705 | E_var:     0.1378 | E_err:   0.005800
[2025-10-06 00:57:02] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -27.541798 | E_var:     0.1492 | E_err:   0.006035
[2025-10-06 00:57:04] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -27.531988 | E_var:     0.1755 | E_err:   0.006546
[2025-10-06 00:57:07] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -27.540396 | E_var:     0.1515 | E_err:   0.006081
[2025-10-06 00:57:09] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -27.530378 | E_var:     0.1906 | E_err:   0.006821
[2025-10-06 00:57:12] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -27.540179 | E_var:     0.1503 | E_err:   0.006058
[2025-10-06 00:57:14] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -27.540611 | E_var:     0.1621 | E_err:   0.006291
[2025-10-06 00:57:14] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 00:57:16] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -27.542493 | E_var:     0.1578 | E_err:   0.006208
[2025-10-06 00:57:19] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -27.546059 | E_var:     0.1337 | E_err:   0.005714
[2025-10-06 00:57:21] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -27.530928 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 00:57:24] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -27.537022 | E_var:     0.1542 | E_err:   0.006136
[2025-10-06 00:57:26] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -27.544010 | E_var:     0.1605 | E_err:   0.006261
[2025-10-06 00:57:28] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -27.535574 | E_var:     0.1405 | E_err:   0.005857
[2025-10-06 00:57:31] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -27.534518 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 00:57:33] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -27.548927 | E_var:     0.1394 | E_err:   0.005833
[2025-10-06 00:57:36] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -27.529694 | E_var:     0.1443 | E_err:   0.005936
[2025-10-06 00:57:38] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -27.541971 | E_var:     0.1448 | E_err:   0.005945
[2025-10-06 00:57:41] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -27.542178 | E_var:     0.1646 | E_err:   0.006340
[2025-10-06 00:57:43] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -27.543013 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 00:57:45] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -27.543937 | E_var:     0.1423 | E_err:   0.005895
[2025-10-06 00:57:48] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -27.539844 | E_var:     0.1428 | E_err:   0.005905
[2025-10-06 00:57:50] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -27.535563 | E_var:     0.1770 | E_err:   0.006574
[2025-10-06 00:57:53] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -27.546480 | E_var:     0.1667 | E_err:   0.006380
[2025-10-06 00:57:55] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -27.538007 | E_var:     0.1512 | E_err:   0.006076
[2025-10-06 00:57:57] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -27.548782 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 00:58:00] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -27.536680 | E_var:     0.1529 | E_err:   0.006110
[2025-10-06 00:58:02] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -27.543805 | E_var:     0.1522 | E_err:   0.006097
[2025-10-06 00:58:05] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -27.537716 | E_var:     0.1650 | E_err:   0.006347
[2025-10-06 00:58:07] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -27.547791 | E_var:     0.1848 | E_err:   0.006718
[2025-10-06 00:58:09] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -27.533331 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 00:58:12] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -27.532389 | E_var:     0.1432 | E_err:   0.005912
[2025-10-06 00:58:14] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -27.520456 | E_var:     0.1651 | E_err:   0.006348
[2025-10-06 00:58:17] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -27.543973 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 00:58:19] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -27.538055 | E_var:     0.1333 | E_err:   0.005706
[2025-10-06 00:58:22] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -27.538274 | E_var:     0.2011 | E_err:   0.007007
[2025-10-06 00:58:24] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -27.532858 | E_var:     0.1612 | E_err:   0.006274
[2025-10-06 00:58:26] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -27.525882 | E_var:     0.1205 | E_err:   0.005425
[2025-10-06 00:58:29] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -27.541946 | E_var:     0.1335 | E_err:   0.005708
[2025-10-06 00:58:31] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -27.538834 | E_var:     0.1240 | E_err:   0.005501
[2025-10-06 00:58:34] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -27.526449 | E_var:     0.1974 | E_err:   0.006943
[2025-10-06 00:58:36] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -27.535854 | E_var:     0.1162 | E_err:   0.005326
[2025-10-06 00:58:38] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -27.533994 | E_var:     0.3239 | E_err:   0.008893
[2025-10-06 00:58:41] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -27.535001 | E_var:     0.1347 | E_err:   0.005734
[2025-10-06 00:58:43] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -27.543258 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 00:58:46] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -27.540724 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 00:58:48] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -27.543475 | E_var:     0.1451 | E_err:   0.005952
[2025-10-06 00:58:51] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -27.547276 | E_var:     0.1721 | E_err:   0.006482
[2025-10-06 00:58:53] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -27.538598 | E_var:     0.1429 | E_err:   0.005906
[2025-10-06 00:58:55] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -27.546344 | E_var:     0.1537 | E_err:   0.006127
[2025-10-06 00:58:58] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -27.529327 | E_var:     0.1429 | E_err:   0.005906
[2025-10-06 00:59:00] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -27.546822 | E_var:     0.1676 | E_err:   0.006397
[2025-10-06 00:59:03] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -27.546552 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 00:59:05] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -27.539997 | E_var:     0.1581 | E_err:   0.006214
[2025-10-06 00:59:07] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -27.528543 | E_var:     0.1784 | E_err:   0.006599
[2025-10-06 00:59:10] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -27.538872 | E_var:     0.1381 | E_err:   0.005807
[2025-10-06 00:59:12] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -27.530074 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 00:59:15] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -27.531005 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 00:59:15] ======================================================================================================
[2025-10-06 00:59:15] ✅ Training completed successfully
[2025-10-06 00:59:15] Total restarts: 2
[2025-10-06 00:59:15] Final Energy: -27.53100477 ± 0.00647102
[2025-10-06 00:59:15] Final Variance: 0.171516
[2025-10-06 00:59:15] ======================================================================================================
[2025-10-06 00:59:15] ======================================================================================================
[2025-10-06 00:59:15] Training completed | Runtime: 2590.4s
[2025-10-06 00:59:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 00:59:16] ======================================================================================================
