[2025-10-06 04:38:18] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.82/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 04:38:18]   - 迭代次数: final
[2025-10-06 04:38:18]   - 能量: -29.599770-0.001097j ± 0.004723, Var: 0.091378
[2025-10-06 04:38:18]   - 时间戳: 2025-10-06T04:38:00.895957+08:00
[2025-10-06 04:38:32] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 04:38:32] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 04:38:32] ======================================================================================================
[2025-10-06 04:38:32] GCNN for Shastry-Sutherland Model
[2025-10-06 04:38:32] ======================================================================================================
[2025-10-06 04:38:32] System parameters:
[2025-10-06 04:38:32]   - System size: L=4, N=64
[2025-10-06 04:38:32]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-10-06 04:38:32] ------------------------------------------------------------------------------------------------------
[2025-10-06 04:38:32] Model parameters:
[2025-10-06 04:38:32]   - Number of layers = 4
[2025-10-06 04:38:32]   - Number of features = 4
[2025-10-06 04:38:32]   - Total parameters = 12572
[2025-10-06 04:38:32] ------------------------------------------------------------------------------------------------------
[2025-10-06 04:38:32] Training parameters:
[2025-10-06 04:38:32]   - Total iterations: 1050
[2025-10-06 04:38:32]   - Annealing cycles: 3
[2025-10-06 04:38:32]   - Initial period: 150
[2025-10-06 04:38:32]   - Period multiplier: 2.0
[2025-10-06 04:38:32]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 04:38:32]   - Samples: 4096
[2025-10-06 04:38:32]   - Discarded samples: 0
[2025-10-06 04:38:32]   - Chunk size: 4096
[2025-10-06 04:38:32]   - Diagonal shift: 0.15
[2025-10-06 04:38:32]   - Gradient clipping: 1.0
[2025-10-06 04:38:32]   - Checkpoint enabled: interval=100
[2025-10-06 04:38:32]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.83/model_L4F4/training/checkpoints
[2025-10-06 04:38:32] ------------------------------------------------------------------------------------------------------
[2025-10-06 04:38:33] Device status:
[2025-10-06 04:38:33]   - Devices model: NVIDIA H200 NVL
[2025-10-06 04:38:33]   - Number of devices: 1
[2025-10-06 04:38:33]   - Sharding: True
[2025-10-06 04:38:33] ======================================================================================================
[2025-10-06 04:39:07] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -30.018673 | E_var:     0.2051 | E_err:   0.007076
[2025-10-06 04:39:26] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -30.009298 | E_var:     0.1204 | E_err:   0.005422
[2025-10-06 04:39:28] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -29.999349 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 04:39:31] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -30.009459 | E_var:     0.0736 | E_err:   0.004240
[2025-10-06 04:39:33] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -30.014776 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 04:39:36] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -30.009073 | E_var:     0.1021 | E_err:   0.004993
[2025-10-06 04:39:38] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -30.003294 | E_var:     0.0956 | E_err:   0.004830
[2025-10-06 04:39:41] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -30.012692 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 04:39:43] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -30.007223 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 04:39:45] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -30.001276 | E_var:     0.1049 | E_err:   0.005062
[2025-10-06 04:39:48] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -30.010465 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 04:39:50] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -30.009163 | E_var:     0.1078 | E_err:   0.005131
[2025-10-06 04:39:53] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -30.007824 | E_var:     0.0976 | E_err:   0.004882
[2025-10-06 04:39:55] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -30.007414 | E_var:     0.0942 | E_err:   0.004796
[2025-10-06 04:39:58] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -30.012836 | E_var:     0.0855 | E_err:   0.004568
[2025-10-06 04:40:00] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -30.007148 | E_var:     0.0644 | E_err:   0.003965
[2025-10-06 04:40:02] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -30.004030 | E_var:     0.0712 | E_err:   0.004170
[2025-10-06 04:40:05] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -30.008538 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 04:40:07] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -30.003360 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 04:40:10] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -30.006446 | E_var:     0.0661 | E_err:   0.004018
[2025-10-06 04:40:12] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -30.012381 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 04:40:15] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -30.009245 | E_var:     0.0800 | E_err:   0.004420
[2025-10-06 04:40:17] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -30.020489 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 04:40:19] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -30.004633 | E_var:     0.1618 | E_err:   0.006286
[2025-10-06 04:40:22] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -30.004694 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 04:40:24] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -30.008496 | E_var:     0.0691 | E_err:   0.004109
[2025-10-06 04:40:27] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -30.009740 | E_var:     0.0710 | E_err:   0.004164
[2025-10-06 04:40:29] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -30.015458 | E_var:     0.1186 | E_err:   0.005380
[2025-10-06 04:40:32] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -30.009102 | E_var:     0.0763 | E_err:   0.004316
[2025-10-06 04:40:34] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -30.011175 | E_var:     0.0834 | E_err:   0.004514
[2025-10-06 04:40:36] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -30.006124 | E_var:     0.0832 | E_err:   0.004506
[2025-10-06 04:40:39] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -30.011675 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 04:40:41] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -30.011598 | E_var:     0.0798 | E_err:   0.004414
[2025-10-06 04:40:44] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -30.001229 | E_var:     0.0836 | E_err:   0.004517
[2025-10-06 04:40:46] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -30.009931 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 04:40:49] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -30.000592 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 04:40:51] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -30.011697 | E_var:     0.0785 | E_err:   0.004377
[2025-10-06 04:40:54] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -30.016454 | E_var:     0.0685 | E_err:   0.004089
[2025-10-06 04:40:56] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -30.015525 | E_var:     0.0807 | E_err:   0.004439
[2025-10-06 04:40:58] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -30.008577 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 04:41:01] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -30.009990 | E_var:     0.0923 | E_err:   0.004748
[2025-10-06 04:41:03] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -30.007977 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 04:41:06] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -30.013915 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 04:41:08] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -30.010717 | E_var:     0.1234 | E_err:   0.005488
[2025-10-06 04:41:10] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -30.002561 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 04:41:13] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -30.002489 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 04:41:15] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -30.015549 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 04:41:18] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -30.007208 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 04:41:20] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -30.004195 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 04:41:23] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -30.013033 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 04:41:25] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -30.007336 | E_var:     0.0688 | E_err:   0.004098
[2025-10-06 04:41:27] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -30.022130 | E_var:     0.0797 | E_err:   0.004410
[2025-10-06 04:41:30] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -30.015527 | E_var:     0.0648 | E_err:   0.003976
[2025-10-06 04:41:32] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -30.010910 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 04:41:35] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -29.992300 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 04:41:37] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -30.000107 | E_var:     0.1203 | E_err:   0.005420
[2025-10-06 04:41:40] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -30.014490 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 04:41:42] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -30.004424 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 04:41:44] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -30.012750 | E_var:     0.0766 | E_err:   0.004324
[2025-10-06 04:41:47] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -30.005610 | E_var:     0.0641 | E_err:   0.003955
[2025-10-06 04:41:49] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -30.001893 | E_var:     0.0739 | E_err:   0.004247
[2025-10-06 04:41:52] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -30.007790 | E_var:     0.1426 | E_err:   0.005900
[2025-10-06 04:41:54] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -30.008480 | E_var:     0.1586 | E_err:   0.006223
[2025-10-06 04:41:57] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -30.002321 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 04:41:59] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -29.999291 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 04:42:01] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -30.012300 | E_var:     0.1128 | E_err:   0.005248
[2025-10-06 04:42:04] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -30.009096 | E_var:     0.0827 | E_err:   0.004493
[2025-10-06 04:42:06] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -30.002596 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 04:42:09] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -30.003910 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 04:42:11] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -30.006516 | E_var:     0.0964 | E_err:   0.004852
[2025-10-06 04:42:14] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -30.007209 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 04:42:16] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -30.006985 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 04:42:18] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -30.019433 | E_var:     0.1033 | E_err:   0.005023
[2025-10-06 04:42:21] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -30.008892 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 04:42:23] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -30.006638 | E_var:     0.0939 | E_err:   0.004789
[2025-10-06 04:42:26] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -30.012889 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 04:42:28] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -30.005199 | E_var:     0.1135 | E_err:   0.005264
[2025-10-06 04:42:31] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -30.005170 | E_var:     0.0736 | E_err:   0.004240
[2025-10-06 04:42:33] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -30.006355 | E_var:     0.0993 | E_err:   0.004924
[2025-10-06 04:42:35] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -30.011923 | E_var:     0.0982 | E_err:   0.004895
[2025-10-06 04:42:38] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -30.010604 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 04:42:40] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -30.007826 | E_var:     0.1561 | E_err:   0.006173
[2025-10-06 04:42:43] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -29.994416 | E_var:     0.5536 | E_err:   0.011626
[2025-10-06 04:42:45] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -30.006660 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 04:42:48] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -30.002123 | E_var:     0.2166 | E_err:   0.007272
[2025-10-06 04:42:50] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -30.003956 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 04:42:52] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -30.006682 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 04:42:55] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -30.010204 | E_var:     0.1128 | E_err:   0.005247
[2025-10-06 04:42:57] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -30.010798 | E_var:     0.0771 | E_err:   0.004337
[2025-10-06 04:43:00] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -30.005752 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 04:43:02] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -30.001198 | E_var:     0.0913 | E_err:   0.004720
[2025-10-06 04:43:05] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -30.011493 | E_var:     0.0847 | E_err:   0.004548
[2025-10-06 04:43:07] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -30.005654 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 04:43:09] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -30.007229 | E_var:     0.0798 | E_err:   0.004413
[2025-10-06 04:43:12] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -30.005578 | E_var:     0.1618 | E_err:   0.006284
[2025-10-06 04:43:14] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -30.008494 | E_var:     0.1245 | E_err:   0.005514
[2025-10-06 04:43:17] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -30.003822 | E_var:     0.0874 | E_err:   0.004618
[2025-10-06 04:43:19] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -30.004343 | E_var:     0.0758 | E_err:   0.004301
[2025-10-06 04:43:22] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -30.006354 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 04:43:24] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -30.007572 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 04:43:24] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 04:43:26] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -30.006935 | E_var:     0.0869 | E_err:   0.004607
[2025-10-06 04:43:29] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -30.005835 | E_var:     0.0740 | E_err:   0.004249
[2025-10-06 04:43:31] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -30.007307 | E_var:     0.0931 | E_err:   0.004768
[2025-10-06 04:43:34] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -30.009086 | E_var:     0.0809 | E_err:   0.004443
[2025-10-06 04:43:36] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -30.011448 | E_var:     0.0651 | E_err:   0.003986
[2025-10-06 04:43:39] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -30.002920 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 04:43:41] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -30.012426 | E_var:     0.0827 | E_err:   0.004493
[2025-10-06 04:43:44] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -30.007797 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 04:43:46] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -30.013997 | E_var:     0.0712 | E_err:   0.004168
[2025-10-06 04:43:48] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -29.999630 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 04:43:51] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -30.010627 | E_var:     0.0709 | E_err:   0.004159
[2025-10-06 04:43:53] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -30.003032 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 04:43:56] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -30.010131 | E_var:     0.0853 | E_err:   0.004565
[2025-10-06 04:43:58] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -30.005422 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:44:01] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -30.011646 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 04:44:03] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -30.003532 | E_var:     0.0808 | E_err:   0.004442
[2025-10-06 04:44:05] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -30.002755 | E_var:     0.0992 | E_err:   0.004921
[2025-10-06 04:44:08] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -30.011146 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 04:44:10] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -30.015316 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 04:44:13] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -30.006310 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 04:44:15] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -29.999888 | E_var:     0.0814 | E_err:   0.004459
[2025-10-06 04:44:18] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -30.009896 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:44:20] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -30.013822 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 04:44:22] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -30.013340 | E_var:     0.0697 | E_err:   0.004125
[2025-10-06 04:44:25] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -30.005288 | E_var:     0.0845 | E_err:   0.004543
[2025-10-06 04:44:27] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -30.009351 | E_var:     0.0689 | E_err:   0.004102
[2025-10-06 04:44:30] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -30.015907 | E_var:     0.0856 | E_err:   0.004573
[2025-10-06 04:44:32] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -30.008398 | E_var:     0.0709 | E_err:   0.004161
[2025-10-06 04:44:35] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -30.010884 | E_var:     0.0727 | E_err:   0.004214
[2025-10-06 04:44:37] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -30.007546 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 04:44:39] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -30.000988 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 04:44:42] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -30.011326 | E_var:     0.0667 | E_err:   0.004037
[2025-10-06 04:44:44] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -30.012903 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 04:44:47] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -30.015987 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 04:44:49] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -30.011835 | E_var:     0.0873 | E_err:   0.004616
[2025-10-06 04:44:52] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -30.010991 | E_var:     0.0868 | E_err:   0.004602
[2025-10-06 04:44:54] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -30.005601 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 04:44:56] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -30.003568 | E_var:     0.0873 | E_err:   0.004616
[2025-10-06 04:44:59] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -30.002596 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 04:45:01] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -30.001697 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 04:45:04] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -30.005400 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 04:45:06] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -30.011321 | E_var:     0.0961 | E_err:   0.004843
[2025-10-06 04:45:09] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -30.003474 | E_var:     0.0593 | E_err:   0.003804
[2025-10-06 04:45:11] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -30.009341 | E_var:     0.0712 | E_err:   0.004170
[2025-10-06 04:45:13] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -30.002594 | E_var:     0.1065 | E_err:   0.005100
[2025-10-06 04:45:16] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -30.007527 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 04:45:18] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -29.996296 | E_var:     0.1674 | E_err:   0.006393
[2025-10-06 04:45:21] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -30.007567 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 04:45:23] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -30.010502 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 04:45:26] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -30.003118 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 04:45:26] 🔄 RESTART #1 | Period: 300
[2025-10-06 04:45:28] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -30.014173 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 04:45:30] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -30.007472 | E_var:     0.1052 | E_err:   0.005067
[2025-10-06 04:45:33] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -30.005933 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 04:45:35] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -30.002904 | E_var:     0.0688 | E_err:   0.004099
[2025-10-06 04:45:38] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -30.008417 | E_var:     0.0723 | E_err:   0.004201
[2025-10-06 04:45:40] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -30.014029 | E_var:     0.2672 | E_err:   0.008077
[2025-10-06 04:45:43] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -30.015192 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 04:45:45] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -29.998882 | E_var:     0.0907 | E_err:   0.004706
[2025-10-06 04:45:47] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -30.012897 | E_var:     0.0913 | E_err:   0.004720
[2025-10-06 04:45:50] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -30.003003 | E_var:     0.0820 | E_err:   0.004474
[2025-10-06 04:45:52] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -30.006437 | E_var:     0.0761 | E_err:   0.004309
[2025-10-06 04:45:55] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -30.007478 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 04:45:57] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -30.002405 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 04:46:00] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -30.007905 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 04:46:02] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -30.009076 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 04:46:04] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -30.009954 | E_var:     0.0845 | E_err:   0.004543
[2025-10-06 04:46:07] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -30.005996 | E_var:     0.0854 | E_err:   0.004566
[2025-10-06 04:46:09] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -30.018085 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 04:46:12] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -30.005832 | E_var:     0.0800 | E_err:   0.004418
[2025-10-06 04:46:14] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -30.012376 | E_var:     0.0796 | E_err:   0.004409
[2025-10-06 04:46:17] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -30.008985 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 04:46:19] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -30.011936 | E_var:     0.0788 | E_err:   0.004385
[2025-10-06 04:46:21] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -30.005550 | E_var:     0.1001 | E_err:   0.004943
[2025-10-06 04:46:24] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -30.001987 | E_var:     0.0691 | E_err:   0.004109
[2025-10-06 04:46:26] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -29.999716 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 04:46:29] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -30.010676 | E_var:     0.1079 | E_err:   0.005133
[2025-10-06 04:46:31] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -30.014954 | E_var:     0.0751 | E_err:   0.004282
[2025-10-06 04:46:34] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -30.011294 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 04:46:36] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -30.009082 | E_var:     0.0819 | E_err:   0.004472
[2025-10-06 04:46:38] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -30.014891 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 04:46:41] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -30.011075 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 04:46:43] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -30.012205 | E_var:     0.0686 | E_err:   0.004093
[2025-10-06 04:46:46] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -30.009759 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 04:46:48] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -30.010915 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 04:46:51] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -30.009574 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 04:46:53] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -30.011675 | E_var:     0.1228 | E_err:   0.005477
[2025-10-06 04:46:56] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -30.009231 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 04:46:58] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -30.011139 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 04:47:00] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -30.005052 | E_var:     0.0843 | E_err:   0.004536
[2025-10-06 04:47:03] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -30.006329 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 04:47:05] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -30.014025 | E_var:     0.0791 | E_err:   0.004394
[2025-10-06 04:47:08] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -30.011337 | E_var:     0.0940 | E_err:   0.004792
[2025-10-06 04:47:10] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -30.009596 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 04:47:13] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -30.007530 | E_var:     0.0663 | E_err:   0.004024
[2025-10-06 04:47:15] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -30.013002 | E_var:     0.0842 | E_err:   0.004534
[2025-10-06 04:47:17] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -30.005044 | E_var:     0.0707 | E_err:   0.004153
[2025-10-06 04:47:20] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -30.008413 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 04:47:22] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -30.007671 | E_var:     0.0686 | E_err:   0.004093
[2025-10-06 04:47:25] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -30.001833 | E_var:     0.0822 | E_err:   0.004481
[2025-10-06 04:47:27] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -30.002943 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 04:47:27] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 04:47:30] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -30.011771 | E_var:     0.0741 | E_err:   0.004255
[2025-10-06 04:47:32] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -30.013947 | E_var:     0.0717 | E_err:   0.004183
[2025-10-06 04:47:34] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -30.007433 | E_var:     0.0760 | E_err:   0.004307
[2025-10-06 04:47:37] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -30.010889 | E_var:     0.0884 | E_err:   0.004647
[2025-10-06 04:47:39] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -30.014146 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 04:47:42] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -30.010256 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 04:47:44] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -30.001040 | E_var:     0.0931 | E_err:   0.004766
[2025-10-06 04:47:47] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -30.003998 | E_var:     0.0717 | E_err:   0.004184
[2025-10-06 04:47:49] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -30.008892 | E_var:     0.0785 | E_err:   0.004377
[2025-10-06 04:47:51] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -30.007902 | E_var:     0.0704 | E_err:   0.004146
[2025-10-06 04:47:54] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -30.013996 | E_var:     0.0858 | E_err:   0.004577
[2025-10-06 04:47:56] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -30.001273 | E_var:     0.0699 | E_err:   0.004130
[2025-10-06 04:47:59] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -30.006531 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 04:48:01] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -30.008524 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 04:48:04] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -30.015319 | E_var:     0.4298 | E_err:   0.010243
[2025-10-06 04:48:06] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -30.007669 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 04:48:08] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -30.005848 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 04:48:11] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -30.002777 | E_var:     0.0770 | E_err:   0.004337
[2025-10-06 04:48:13] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -30.011969 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 04:48:16] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -30.003446 | E_var:     0.0931 | E_err:   0.004768
[2025-10-06 04:48:18] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -30.005249 | E_var:     0.0776 | E_err:   0.004353
[2025-10-06 04:48:21] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -30.011003 | E_var:     0.1197 | E_err:   0.005406
[2025-10-06 04:48:23] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -29.999244 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 04:48:25] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -30.010349 | E_var:     0.0926 | E_err:   0.004754
[2025-10-06 04:48:28] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -30.013264 | E_var:     0.0859 | E_err:   0.004579
[2025-10-06 04:48:30] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -30.009268 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 04:48:33] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -30.010640 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 04:48:35] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -30.009430 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 04:48:38] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -30.006920 | E_var:     0.0871 | E_err:   0.004611
[2025-10-06 04:48:40] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -30.012225 | E_var:     0.0913 | E_err:   0.004720
[2025-10-06 04:48:42] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -30.004056 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 04:48:45] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -30.015037 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 04:48:47] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -30.009335 | E_var:     0.0836 | E_err:   0.004517
[2025-10-06 04:48:50] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -30.006040 | E_var:     0.0747 | E_err:   0.004270
[2025-10-06 04:48:52] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -30.005721 | E_var:     0.0756 | E_err:   0.004296
[2025-10-06 04:48:55] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -30.012625 | E_var:     0.0652 | E_err:   0.003989
[2025-10-06 04:48:57] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -30.006579 | E_var:     0.0927 | E_err:   0.004758
[2025-10-06 04:49:00] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -30.020015 | E_var:     0.0772 | E_err:   0.004343
[2025-10-06 04:49:02] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -30.012279 | E_var:     0.0758 | E_err:   0.004301
[2025-10-06 04:49:04] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -30.009335 | E_var:     0.0623 | E_err:   0.003900
[2025-10-06 04:49:07] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -30.008841 | E_var:     0.0691 | E_err:   0.004108
[2025-10-06 04:49:09] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -30.014000 | E_var:     0.0654 | E_err:   0.003995
[2025-10-06 04:49:12] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -30.007328 | E_var:     0.0886 | E_err:   0.004651
[2025-10-06 04:49:14] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -30.011381 | E_var:     0.0927 | E_err:   0.004758
[2025-10-06 04:49:17] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -30.007826 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 04:49:19] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -30.008901 | E_var:     0.0727 | E_err:   0.004212
[2025-10-06 04:49:21] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -30.006985 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 04:49:24] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -30.008681 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 04:49:26] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -30.002715 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 04:49:29] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -30.010033 | E_var:     0.1065 | E_err:   0.005098
[2025-10-06 04:49:31] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -30.007207 | E_var:     0.0853 | E_err:   0.004563
[2025-10-06 04:49:34] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -30.010022 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 04:49:36] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -30.002693 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 04:49:38] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -30.008821 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 04:49:41] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -30.011733 | E_var:     0.0693 | E_err:   0.004112
[2025-10-06 04:49:43] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -30.012363 | E_var:     0.0753 | E_err:   0.004288
[2025-10-06 04:49:46] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -30.009977 | E_var:     0.0811 | E_err:   0.004450
[2025-10-06 04:49:48] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -29.999640 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 04:49:51] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -30.002979 | E_var:     0.0753 | E_err:   0.004288
[2025-10-06 04:49:53] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -30.010017 | E_var:     0.0789 | E_err:   0.004389
[2025-10-06 04:49:55] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -30.006348 | E_var:     0.0901 | E_err:   0.004689
[2025-10-06 04:49:58] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -30.001708 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 04:50:00] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -30.006476 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 04:50:03] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -30.009273 | E_var:     0.0898 | E_err:   0.004681
[2025-10-06 04:50:05] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -30.004295 | E_var:     0.0877 | E_err:   0.004626
[2025-10-06 04:50:08] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -30.004437 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 04:50:10] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -30.011883 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 04:50:12] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -30.007064 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 04:50:15] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -30.012413 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 04:50:17] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -30.009422 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 04:50:20] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -30.016453 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 04:50:22] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -29.995450 | E_var:     0.0681 | E_err:   0.004079
[2025-10-06 04:50:25] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -30.009941 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 04:50:27] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -30.015668 | E_var:     0.1040 | E_err:   0.005040
[2025-10-06 04:50:29] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -30.011833 | E_var:     0.1378 | E_err:   0.005801
[2025-10-06 04:50:32] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -30.003188 | E_var:     0.0740 | E_err:   0.004250
[2025-10-06 04:50:34] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -30.011264 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 04:50:37] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -30.005326 | E_var:     0.0723 | E_err:   0.004203
[2025-10-06 04:50:39] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -30.013004 | E_var:     0.1037 | E_err:   0.005032
[2025-10-06 04:50:42] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -30.010760 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 04:50:44] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -30.010772 | E_var:     0.0755 | E_err:   0.004295
[2025-10-06 04:50:46] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -30.013967 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 04:50:49] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -30.003963 | E_var:     0.0758 | E_err:   0.004301
[2025-10-06 04:50:51] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -30.007525 | E_var:     0.1092 | E_err:   0.005162
[2025-10-06 04:50:54] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -29.999023 | E_var:     0.0705 | E_err:   0.004148
[2025-10-06 04:50:56] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -29.995747 | E_var:     0.0827 | E_err:   0.004492
[2025-10-06 04:50:59] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -30.004030 | E_var:     0.0767 | E_err:   0.004328
[2025-10-06 04:51:01] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -30.006363 | E_var:     0.1002 | E_err:   0.004945
[2025-10-06 04:51:03] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -30.009422 | E_var:     0.0943 | E_err:   0.004797
[2025-10-06 04:51:06] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -30.002091 | E_var:     0.1279 | E_err:   0.005589
[2025-10-06 04:51:08] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -30.009139 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 04:51:11] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -29.997147 | E_var:     0.6267 | E_err:   0.012369
[2025-10-06 04:51:13] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -30.002522 | E_var:     0.0807 | E_err:   0.004440
[2025-10-06 04:51:16] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -30.006162 | E_var:     0.0934 | E_err:   0.004775
[2025-10-06 04:51:18] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -30.010166 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 04:51:20] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -30.005308 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 04:51:23] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -30.015531 | E_var:     0.0809 | E_err:   0.004444
[2025-10-06 04:51:25] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -30.008293 | E_var:     0.1034 | E_err:   0.005024
[2025-10-06 04:51:28] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -30.012315 | E_var:     0.0742 | E_err:   0.004255
[2025-10-06 04:51:30] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -30.004949 | E_var:     0.0699 | E_err:   0.004130
[2025-10-06 04:51:30] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 04:51:33] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -30.008609 | E_var:     0.0726 | E_err:   0.004209
[2025-10-06 04:51:35] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -30.009881 | E_var:     0.0907 | E_err:   0.004707
[2025-10-06 04:51:37] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -30.017370 | E_var:     0.0817 | E_err:   0.004467
[2025-10-06 04:51:40] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -30.005802 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 04:51:42] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -30.017189 | E_var:     0.0863 | E_err:   0.004589
[2025-10-06 04:51:45] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -30.006601 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 04:51:47] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -30.003388 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 04:51:50] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -30.014566 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 04:51:52] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -30.011442 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 04:51:54] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -30.010398 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 04:51:57] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -30.004696 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 04:51:59] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -30.001580 | E_var:     0.2680 | E_err:   0.008088
[2025-10-06 04:52:02] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -30.006259 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 04:52:04] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -30.008986 | E_var:     0.0724 | E_err:   0.004204
[2025-10-06 04:52:07] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -30.008327 | E_var:     0.0659 | E_err:   0.004011
[2025-10-06 04:52:09] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -30.014223 | E_var:     0.0654 | E_err:   0.003994
[2025-10-06 04:52:11] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -30.006472 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 04:52:14] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -30.014448 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 04:52:16] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -30.006410 | E_var:     0.0722 | E_err:   0.004198
[2025-10-06 04:52:19] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -30.006687 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 04:52:21] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -29.999540 | E_var:     0.0657 | E_err:   0.004006
[2025-10-06 04:52:24] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -30.007118 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 04:52:26] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -30.010043 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 04:52:28] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -30.014592 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 04:52:31] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -30.010685 | E_var:     0.1717 | E_err:   0.006474
[2025-10-06 04:52:33] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -30.004088 | E_var:     0.0691 | E_err:   0.004108
[2025-10-06 04:52:36] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -30.007589 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 04:52:38] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -30.002011 | E_var:     0.0736 | E_err:   0.004240
[2025-10-06 04:52:41] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -30.003930 | E_var:     0.3148 | E_err:   0.008767
[2025-10-06 04:52:43] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -30.000052 | E_var:     0.0636 | E_err:   0.003942
[2025-10-06 04:52:45] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -30.014294 | E_var:     0.0724 | E_err:   0.004206
[2025-10-06 04:52:48] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -30.009181 | E_var:     0.0613 | E_err:   0.003869
[2025-10-06 04:52:50] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -30.022325 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 04:52:53] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -30.005899 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 04:52:55] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -30.011018 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 04:52:58] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -30.002150 | E_var:     0.0811 | E_err:   0.004451
[2025-10-06 04:53:00] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -30.002976 | E_var:     0.0685 | E_err:   0.004088
[2025-10-06 04:53:02] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -30.006683 | E_var:     0.0904 | E_err:   0.004697
[2025-10-06 04:53:05] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -30.008943 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 04:53:07] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -30.011167 | E_var:     0.0729 | E_err:   0.004220
[2025-10-06 04:53:10] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -30.009197 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 04:53:12] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -30.010963 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 04:53:15] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -30.007107 | E_var:     0.0604 | E_err:   0.003840
[2025-10-06 04:53:17] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -30.015566 | E_var:     0.0871 | E_err:   0.004610
[2025-10-06 04:53:20] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -30.000445 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 04:53:22] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -30.016400 | E_var:     0.0735 | E_err:   0.004235
[2025-10-06 04:53:24] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -30.007630 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 04:53:27] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -30.006233 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 04:53:29] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -30.008228 | E_var:     0.0750 | E_err:   0.004280
[2025-10-06 04:53:32] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -30.016395 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 04:53:34] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -30.001957 | E_var:     0.0689 | E_err:   0.004100
[2025-10-06 04:53:37] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -30.003727 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 04:53:39] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -30.009227 | E_var:     0.0765 | E_err:   0.004323
[2025-10-06 04:53:41] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -30.008867 | E_var:     0.1230 | E_err:   0.005480
[2025-10-06 04:53:44] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -30.013494 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 04:53:46] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -30.007606 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 04:53:49] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -30.017804 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 04:53:51] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -30.016557 | E_var:     0.0771 | E_err:   0.004340
[2025-10-06 04:53:54] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -30.010114 | E_var:     0.0769 | E_err:   0.004334
[2025-10-06 04:53:56] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -29.999266 | E_var:     0.1053 | E_err:   0.005069
[2025-10-06 04:53:58] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -30.008437 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 04:54:01] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -30.012122 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 04:54:03] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -30.006047 | E_var:     0.0754 | E_err:   0.004292
[2025-10-06 04:54:06] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -30.017482 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 04:54:08] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -30.010195 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 04:54:11] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -30.005742 | E_var:     0.0718 | E_err:   0.004187
[2025-10-06 04:54:13] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -30.011731 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 04:54:15] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -30.005360 | E_var:     0.0806 | E_err:   0.004437
[2025-10-06 04:54:18] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -30.005363 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 04:54:20] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -30.010729 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 04:54:23] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -30.011495 | E_var:     0.0931 | E_err:   0.004768
[2025-10-06 04:54:25] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -30.005782 | E_var:     0.0816 | E_err:   0.004462
[2025-10-06 04:54:28] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -30.011701 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 04:54:30] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -30.010115 | E_var:     0.0681 | E_err:   0.004079
[2025-10-06 04:54:32] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -30.010618 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 04:54:35] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -30.011818 | E_var:     0.0824 | E_err:   0.004484
[2025-10-06 04:54:37] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -30.007827 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 04:54:40] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -30.006805 | E_var:     0.1029 | E_err:   0.005011
[2025-10-06 04:54:42] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -30.002021 | E_var:     0.1050 | E_err:   0.005062
[2025-10-06 04:54:45] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -30.011684 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 04:54:47] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -30.003366 | E_var:     0.0859 | E_err:   0.004579
[2025-10-06 04:54:49] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -29.995434 | E_var:     0.0776 | E_err:   0.004354
[2025-10-06 04:54:52] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -30.008721 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 04:54:54] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -30.011172 | E_var:     0.0662 | E_err:   0.004021
[2025-10-06 04:54:57] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -30.006820 | E_var:     0.1672 | E_err:   0.006390
[2025-10-06 04:54:59] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -30.001994 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 04:55:02] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -30.004490 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 04:55:04] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -30.005311 | E_var:     0.0707 | E_err:   0.004154
[2025-10-06 04:55:06] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -29.989758 | E_var:     0.2812 | E_err:   0.008286
[2025-10-06 04:55:09] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -30.012060 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 04:55:11] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -30.011062 | E_var:     0.0640 | E_err:   0.003953
[2025-10-06 04:55:14] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -30.012191 | E_var:     0.0668 | E_err:   0.004039
[2025-10-06 04:55:16] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -30.015411 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 04:55:19] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -30.012941 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 04:55:21] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -30.011862 | E_var:     0.0899 | E_err:   0.004686
[2025-10-06 04:55:23] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -30.016945 | E_var:     0.0887 | E_err:   0.004655
[2025-10-06 04:55:26] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -30.016235 | E_var:     0.0809 | E_err:   0.004445
[2025-10-06 04:55:28] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -30.009583 | E_var:     0.0726 | E_err:   0.004209
[2025-10-06 04:55:31] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -30.001990 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 04:55:33] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -30.003496 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 04:55:33] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 04:55:36] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -30.004844 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 04:55:38] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -30.000324 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:55:41] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -30.000057 | E_var:     0.0856 | E_err:   0.004573
[2025-10-06 04:55:43] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -30.010241 | E_var:     0.0804 | E_err:   0.004430
[2025-10-06 04:55:45] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -30.011087 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 04:55:48] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -30.006384 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 04:55:50] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -30.009785 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 04:55:53] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -30.014574 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 04:55:55] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -30.007438 | E_var:     0.1820 | E_err:   0.006665
[2025-10-06 04:55:58] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -30.006910 | E_var:     0.0723 | E_err:   0.004201
[2025-10-06 04:56:00] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -30.006455 | E_var:     0.0688 | E_err:   0.004100
[2025-10-06 04:56:02] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -30.003996 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 04:56:05] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -30.010861 | E_var:     0.0747 | E_err:   0.004272
[2025-10-06 04:56:07] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -30.018562 | E_var:     0.0933 | E_err:   0.004771
[2025-10-06 04:56:10] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -30.005322 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 04:56:12] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -30.008128 | E_var:     0.0772 | E_err:   0.004341
[2025-10-06 04:56:15] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -30.007208 | E_var:     0.0843 | E_err:   0.004538
[2025-10-06 04:56:17] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -30.003993 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 04:56:19] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -30.018371 | E_var:     0.0720 | E_err:   0.004192
[2025-10-06 04:56:22] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -30.008199 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 04:56:24] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -30.009366 | E_var:     0.0775 | E_err:   0.004349
[2025-10-06 04:56:27] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -30.013251 | E_var:     0.1320 | E_err:   0.005676
[2025-10-06 04:56:29] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -30.007526 | E_var:     0.0951 | E_err:   0.004819
[2025-10-06 04:56:32] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -30.011565 | E_var:     0.0646 | E_err:   0.003972
[2025-10-06 04:56:34] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -30.006479 | E_var:     0.0585 | E_err:   0.003778
[2025-10-06 04:56:36] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -30.009076 | E_var:     0.1081 | E_err:   0.005137
[2025-10-06 04:56:39] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -30.010931 | E_var:     0.0713 | E_err:   0.004172
[2025-10-06 04:56:41] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -30.003180 | E_var:     0.0677 | E_err:   0.004065
[2025-10-06 04:56:44] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -30.009595 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 04:56:46] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -30.000071 | E_var:     0.1163 | E_err:   0.005328
[2025-10-06 04:56:49] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -30.008704 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 04:56:51] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -30.012546 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 04:56:53] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -30.001383 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 04:56:56] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -30.009576 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 04:56:58] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -30.005924 | E_var:     0.0727 | E_err:   0.004212
[2025-10-06 04:57:01] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -30.012917 | E_var:     0.0843 | E_err:   0.004538
[2025-10-06 04:57:03] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -30.009573 | E_var:     0.0668 | E_err:   0.004039
[2025-10-06 04:57:06] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -30.005158 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 04:57:08] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -30.014018 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 04:57:10] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -30.005641 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 04:57:13] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -30.008942 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 04:57:15] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -30.008299 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 04:57:18] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -30.011738 | E_var:     0.0737 | E_err:   0.004243
[2025-10-06 04:57:20] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -30.016409 | E_var:     0.0756 | E_err:   0.004296
[2025-10-06 04:57:23] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -30.014036 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 04:57:25] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -30.005789 | E_var:     0.0709 | E_err:   0.004160
[2025-10-06 04:57:28] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -30.007745 | E_var:     0.0725 | E_err:   0.004207
[2025-10-06 04:57:30] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -30.011189 | E_var:     0.1023 | E_err:   0.004999
[2025-10-06 04:57:32] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -30.012795 | E_var:     0.0774 | E_err:   0.004347
[2025-10-06 04:57:35] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -30.009545 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 04:57:35] 🔄 RESTART #2 | Period: 600
[2025-10-06 04:57:37] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -30.015585 | E_var:     0.0747 | E_err:   0.004270
[2025-10-06 04:57:40] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -30.020773 | E_var:     0.0723 | E_err:   0.004200
[2025-10-06 04:57:42] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -30.009430 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 04:57:45] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -30.000555 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 04:57:47] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -30.005792 | E_var:     0.0731 | E_err:   0.004225
[2025-10-06 04:57:49] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -30.005693 | E_var:     0.0718 | E_err:   0.004187
[2025-10-06 04:57:52] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -30.015608 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 04:57:54] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -30.010024 | E_var:     0.0689 | E_err:   0.004102
[2025-10-06 04:57:57] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -30.004353 | E_var:     0.1358 | E_err:   0.005757
[2025-10-06 04:57:59] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -30.010632 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 04:58:02] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -30.007702 | E_var:     0.0751 | E_err:   0.004282
[2025-10-06 04:58:04] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -30.007121 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 04:58:06] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -30.015055 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 04:58:09] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -30.008451 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 04:58:11] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -30.003237 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 04:58:14] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -30.005497 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 04:58:16] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -30.006747 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 04:58:19] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -30.004236 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 04:58:21] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -30.008987 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 04:58:23] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -30.004266 | E_var:     0.0733 | E_err:   0.004231
[2025-10-06 04:58:26] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -30.000731 | E_var:     0.0982 | E_err:   0.004895
[2025-10-06 04:58:28] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -30.008852 | E_var:     0.0793 | E_err:   0.004400
[2025-10-06 04:58:31] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -30.009153 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 04:58:33] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -30.004991 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 04:58:36] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -30.010515 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 04:58:38] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -29.997105 | E_var:     0.0880 | E_err:   0.004634
[2025-10-06 04:58:40] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -30.002646 | E_var:     0.0682 | E_err:   0.004080
[2025-10-06 04:58:43] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -30.006681 | E_var:     0.0924 | E_err:   0.004751
[2025-10-06 04:58:45] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -30.006117 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 04:58:48] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -30.013750 | E_var:     0.0895 | E_err:   0.004676
[2025-10-06 04:58:50] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -30.013457 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 04:58:52] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -30.011305 | E_var:     0.0883 | E_err:   0.004642
[2025-10-06 04:58:55] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -30.007689 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 04:58:57] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -29.997070 | E_var:     0.0790 | E_err:   0.004393
[2025-10-06 04:59:00] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -30.003076 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 04:59:02] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -30.008477 | E_var:     0.0867 | E_err:   0.004600
[2025-10-06 04:59:05] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -30.010520 | E_var:     0.1255 | E_err:   0.005535
[2025-10-06 04:59:07] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -30.017688 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 04:59:09] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -30.010102 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 04:59:12] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -29.998420 | E_var:     0.1209 | E_err:   0.005433
[2025-10-06 04:59:14] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -30.008492 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 04:59:17] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -30.001727 | E_var:     0.1121 | E_err:   0.005232
[2025-10-06 04:59:19] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -30.000053 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 04:59:22] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -30.014593 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 04:59:24] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -30.008738 | E_var:     0.0680 | E_err:   0.004076
[2025-10-06 04:59:26] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -29.996349 | E_var:     0.1174 | E_err:   0.005355
[2025-10-06 04:59:29] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -30.008768 | E_var:     0.0670 | E_err:   0.004045
[2025-10-06 04:59:31] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -30.007722 | E_var:     0.0681 | E_err:   0.004077
[2025-10-06 04:59:34] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -30.011131 | E_var:     0.0811 | E_err:   0.004450
[2025-10-06 04:59:36] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -30.006712 | E_var:     0.1380 | E_err:   0.005803
[2025-10-06 04:59:36] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 04:59:39] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -30.011229 | E_var:     0.0706 | E_err:   0.004152
[2025-10-06 04:59:41] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -30.008469 | E_var:     0.0749 | E_err:   0.004276
[2025-10-06 04:59:43] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -30.006518 | E_var:     0.0688 | E_err:   0.004097
[2025-10-06 04:59:46] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -30.010781 | E_var:     0.1470 | E_err:   0.005991
[2025-10-06 04:59:48] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -29.998827 | E_var:     0.0805 | E_err:   0.004432
[2025-10-06 04:59:51] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -30.002536 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 04:59:53] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -30.004516 | E_var:     0.0875 | E_err:   0.004621
[2025-10-06 04:59:56] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -30.010713 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 04:59:58] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -30.007320 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 05:00:00] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -30.007911 | E_var:     0.1136 | E_err:   0.005266
[2025-10-06 05:00:03] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -30.002573 | E_var:     0.1148 | E_err:   0.005295
[2025-10-06 05:00:05] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -30.008072 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 05:00:08] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -30.009524 | E_var:     0.2107 | E_err:   0.007173
[2025-10-06 05:00:10] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -30.013118 | E_var:     0.0889 | E_err:   0.004660
[2025-10-06 05:00:13] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -30.013447 | E_var:     0.0675 | E_err:   0.004058
[2025-10-06 05:00:15] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -30.007039 | E_var:     0.0903 | E_err:   0.004696
[2025-10-06 05:00:17] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -30.004470 | E_var:     0.0827 | E_err:   0.004492
[2025-10-06 05:00:20] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -30.012246 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 05:00:22] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -30.004058 | E_var:     0.0803 | E_err:   0.004427
[2025-10-06 05:00:25] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -29.999522 | E_var:     0.1832 | E_err:   0.006688
[2025-10-06 05:00:27] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -30.006426 | E_var:     0.0800 | E_err:   0.004420
[2025-10-06 05:00:30] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -30.005989 | E_var:     0.1307 | E_err:   0.005649
[2025-10-06 05:00:32] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -30.018887 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 05:00:34] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -30.006655 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 05:00:37] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -30.010009 | E_var:     0.1081 | E_err:   0.005137
[2025-10-06 05:00:39] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -30.005161 | E_var:     0.0725 | E_err:   0.004208
[2025-10-06 05:00:42] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -30.002219 | E_var:     0.1187 | E_err:   0.005384
[2025-10-06 05:00:44] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -30.010011 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 05:00:47] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -30.012226 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 05:00:49] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -30.005118 | E_var:     0.1123 | E_err:   0.005235
[2025-10-06 05:00:51] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -30.010943 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 05:00:54] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -30.012114 | E_var:     0.0900 | E_err:   0.004689
[2025-10-06 05:00:56] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -30.008772 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 05:00:59] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -30.008021 | E_var:     0.0650 | E_err:   0.003984
[2025-10-06 05:01:01] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -30.008383 | E_var:     0.0880 | E_err:   0.004635
[2025-10-06 05:01:04] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -30.009359 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 05:01:06] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -30.009441 | E_var:     0.0620 | E_err:   0.003890
[2025-10-06 05:01:08] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -29.996577 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 05:01:11] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -30.013450 | E_var:     0.0850 | E_err:   0.004554
[2025-10-06 05:01:13] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -30.005609 | E_var:     0.0935 | E_err:   0.004778
[2025-10-06 05:01:16] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -30.006619 | E_var:     0.0710 | E_err:   0.004162
[2025-10-06 05:01:18] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -30.010050 | E_var:     0.0813 | E_err:   0.004455
[2025-10-06 05:01:20] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -30.008327 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 05:01:23] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -30.010876 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 05:01:25] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -30.005030 | E_var:     0.0923 | E_err:   0.004748
[2025-10-06 05:01:28] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -30.015285 | E_var:     0.0926 | E_err:   0.004756
[2025-10-06 05:01:30] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -30.013892 | E_var:     0.0741 | E_err:   0.004254
[2025-10-06 05:01:33] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -30.008185 | E_var:     0.0708 | E_err:   0.004159
[2025-10-06 05:01:35] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -30.008558 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 05:01:37] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -30.006124 | E_var:     0.0756 | E_err:   0.004297
[2025-10-06 05:01:40] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -30.017527 | E_var:     0.0808 | E_err:   0.004442
[2025-10-06 05:01:42] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -30.009928 | E_var:     0.0744 | E_err:   0.004262
[2025-10-06 05:01:45] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -30.003814 | E_var:     0.0688 | E_err:   0.004097
[2025-10-06 05:01:47] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -30.008792 | E_var:     0.0709 | E_err:   0.004162
[2025-10-06 05:01:50] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -30.011352 | E_var:     0.0969 | E_err:   0.004865
[2025-10-06 05:01:52] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -30.012223 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 05:01:54] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -30.013916 | E_var:     0.0786 | E_err:   0.004379
[2025-10-06 05:01:57] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -30.010260 | E_var:     0.0745 | E_err:   0.004266
[2025-10-06 05:01:59] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -30.000031 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 05:02:02] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -30.007429 | E_var:     0.0743 | E_err:   0.004258
[2025-10-06 05:02:04] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -30.005150 | E_var:     0.0699 | E_err:   0.004131
[2025-10-06 05:02:07] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -30.007308 | E_var:     0.0906 | E_err:   0.004703
[2025-10-06 05:02:09] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -30.011700 | E_var:     0.0755 | E_err:   0.004293
[2025-10-06 05:02:11] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -30.013335 | E_var:     0.0833 | E_err:   0.004511
[2025-10-06 05:02:14] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -30.008556 | E_var:     0.0728 | E_err:   0.004215
[2025-10-06 05:02:16] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -30.009186 | E_var:     0.0635 | E_err:   0.003939
[2025-10-06 05:02:19] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -30.014220 | E_var:     0.0671 | E_err:   0.004047
[2025-10-06 05:02:21] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -30.010859 | E_var:     0.0730 | E_err:   0.004221
[2025-10-06 05:02:24] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -30.010926 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 05:02:26] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -30.005849 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 05:02:29] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -30.018403 | E_var:     0.0856 | E_err:   0.004573
[2025-10-06 05:02:31] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -30.003217 | E_var:     0.0722 | E_err:   0.004200
[2025-10-06 05:02:33] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -30.006274 | E_var:     0.0947 | E_err:   0.004808
[2025-10-06 05:02:36] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -30.011382 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 05:02:38] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -30.014472 | E_var:     0.0710 | E_err:   0.004164
[2025-10-06 05:02:41] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -30.010587 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 05:02:43] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -30.003543 | E_var:     0.1276 | E_err:   0.005582
[2025-10-06 05:02:45] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -30.002065 | E_var:     0.0684 | E_err:   0.004085
[2025-10-06 05:02:48] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -30.012971 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 05:02:50] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -29.997600 | E_var:     0.0862 | E_err:   0.004589
[2025-10-06 05:02:53] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -30.012983 | E_var:     0.0693 | E_err:   0.004114
[2025-10-06 05:02:55] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -30.012516 | E_var:     0.0677 | E_err:   0.004066
[2025-10-06 05:02:58] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -30.003885 | E_var:     0.0971 | E_err:   0.004870
[2025-10-06 05:03:00] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -30.011946 | E_var:     0.0681 | E_err:   0.004076
[2025-10-06 05:03:02] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -30.020105 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 05:03:05] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -30.004081 | E_var:     0.0865 | E_err:   0.004597
[2025-10-06 05:03:07] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -30.010797 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 05:03:10] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -30.015024 | E_var:     0.1003 | E_err:   0.004950
[2025-10-06 05:03:12] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -30.004313 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 05:03:15] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -30.012311 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 05:03:17] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -30.012668 | E_var:     0.3375 | E_err:   0.009078
[2025-10-06 05:03:19] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -30.016965 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 05:03:22] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -30.017521 | E_var:     0.0977 | E_err:   0.004885
[2025-10-06 05:03:24] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -30.008678 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 05:03:27] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -30.004103 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 05:03:29] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -29.999543 | E_var:     0.8931 | E_err:   0.014766
[2025-10-06 05:03:32] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -30.008511 | E_var:     0.0885 | E_err:   0.004648
[2025-10-06 05:03:34] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -30.014637 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 05:03:37] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -30.007196 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 05:03:39] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -30.009306 | E_var:     0.0757 | E_err:   0.004300
[2025-10-06 05:03:39] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 05:03:41] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -30.011675 | E_var:     0.0995 | E_err:   0.004930
[2025-10-06 05:03:44] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -30.014223 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 05:03:46] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -30.005879 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 05:03:49] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -30.015073 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 05:03:51] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -30.007803 | E_var:     0.0695 | E_err:   0.004120
[2025-10-06 05:03:54] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -30.010261 | E_var:     0.1665 | E_err:   0.006376
[2025-10-06 05:03:56] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -30.007495 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 05:03:58] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -30.004075 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 05:04:01] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -30.012225 | E_var:     0.0676 | E_err:   0.004062
[2025-10-06 05:04:03] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -30.017267 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 05:04:06] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -30.010841 | E_var:     0.0694 | E_err:   0.004116
[2025-10-06 05:04:08] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -30.014814 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 05:04:11] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -30.001560 | E_var:     0.0812 | E_err:   0.004453
[2025-10-06 05:04:13] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -30.007740 | E_var:     0.0758 | E_err:   0.004303
[2025-10-06 05:04:15] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -30.011131 | E_var:     0.0871 | E_err:   0.004610
[2025-10-06 05:04:18] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -30.007420 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 05:04:20] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -30.013393 | E_var:     0.0740 | E_err:   0.004250
[2025-10-06 05:04:23] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -30.004990 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 05:04:25] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -30.006974 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 05:04:28] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -30.013232 | E_var:     0.0777 | E_err:   0.004355
[2025-10-06 05:04:30] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -30.006732 | E_var:     0.1057 | E_err:   0.005081
[2025-10-06 05:04:32] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -30.010151 | E_var:     0.0696 | E_err:   0.004123
[2025-10-06 05:04:35] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -30.001809 | E_var:     0.0699 | E_err:   0.004132
[2025-10-06 05:04:37] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -30.014175 | E_var:     0.1392 | E_err:   0.005830
[2025-10-06 05:04:40] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -30.007599 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 05:04:42] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -30.013922 | E_var:     0.1567 | E_err:   0.006185
[2025-10-06 05:04:45] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -30.001891 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 05:04:47] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -30.014007 | E_var:     0.0927 | E_err:   0.004756
[2025-10-06 05:04:49] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -30.009546 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 05:04:52] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -30.003520 | E_var:     0.0847 | E_err:   0.004547
[2025-10-06 05:04:54] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -30.006970 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 05:04:57] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -30.013451 | E_var:     0.1242 | E_err:   0.005506
[2025-10-06 05:04:59] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -30.017382 | E_var:     0.1544 | E_err:   0.006141
[2025-10-06 05:05:02] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -30.007100 | E_var:     0.0916 | E_err:   0.004728
[2025-10-06 05:05:04] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -30.002630 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 05:05:06] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -30.010446 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 05:05:09] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -30.007857 | E_var:     0.0693 | E_err:   0.004112
[2025-10-06 05:05:11] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -30.010307 | E_var:     0.0691 | E_err:   0.004107
[2025-10-06 05:05:14] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -30.003277 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 05:05:16] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -30.006839 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 05:05:19] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -30.007179 | E_var:     0.1164 | E_err:   0.005330
[2025-10-06 05:05:21] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -30.010847 | E_var:     0.0710 | E_err:   0.004162
[2025-10-06 05:05:23] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -30.007727 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 05:05:26] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -30.007511 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 05:05:28] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -30.000724 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 05:05:31] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -30.010346 | E_var:     0.0726 | E_err:   0.004210
[2025-10-06 05:05:33] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -30.009152 | E_var:     0.0836 | E_err:   0.004518
[2025-10-06 05:05:36] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -30.008157 | E_var:     0.0758 | E_err:   0.004301
[2025-10-06 05:05:38] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -30.002070 | E_var:     0.3642 | E_err:   0.009430
[2025-10-06 05:05:40] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -30.010722 | E_var:     0.0753 | E_err:   0.004289
[2025-10-06 05:05:43] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -30.001859 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 05:05:45] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -30.011286 | E_var:     0.0777 | E_err:   0.004354
[2025-10-06 05:05:48] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -30.010900 | E_var:     0.0689 | E_err:   0.004100
[2025-10-06 05:05:50] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -30.007643 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 05:05:52] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -30.005843 | E_var:     0.0696 | E_err:   0.004123
[2025-10-06 05:05:55] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -30.010942 | E_var:     0.0690 | E_err:   0.004103
[2025-10-06 05:05:57] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -30.015448 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 05:06:00] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -30.018493 | E_var:     0.0705 | E_err:   0.004149
[2025-10-06 05:06:02] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -30.012012 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 05:06:05] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -30.007205 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 05:06:07] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -30.019131 | E_var:     0.1512 | E_err:   0.006075
[2025-10-06 05:06:09] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -30.000744 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 05:06:12] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -30.003571 | E_var:     0.0624 | E_err:   0.003904
[2025-10-06 05:06:14] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -30.004467 | E_var:     0.0811 | E_err:   0.004451
[2025-10-06 05:06:17] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -30.012510 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 05:06:19] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -30.010161 | E_var:     0.0693 | E_err:   0.004114
[2025-10-06 05:06:22] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -30.002736 | E_var:     0.1081 | E_err:   0.005137
[2025-10-06 05:06:24] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -30.005065 | E_var:     0.0871 | E_err:   0.004611
[2025-10-06 05:06:26] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -30.012910 | E_var:     0.0927 | E_err:   0.004758
[2025-10-06 05:06:29] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -30.006800 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 05:06:31] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -30.011536 | E_var:     0.0910 | E_err:   0.004712
[2025-10-06 05:06:34] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -30.001882 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 05:06:36] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -30.009986 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 05:06:39] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -29.999134 | E_var:     0.2154 | E_err:   0.007251
[2025-10-06 05:06:41] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -30.007231 | E_var:     0.0791 | E_err:   0.004396
[2025-10-06 05:06:44] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -30.012762 | E_var:     0.0706 | E_err:   0.004150
[2025-10-06 05:06:46] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -30.015089 | E_var:     0.0755 | E_err:   0.004293
[2025-10-06 05:06:48] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -30.003593 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 05:06:51] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -30.009304 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 05:06:53] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -30.006867 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 05:06:56] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -30.017535 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 05:06:58] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -30.004778 | E_var:     0.0912 | E_err:   0.004720
[2025-10-06 05:07:00] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -30.005746 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 05:07:03] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -30.007945 | E_var:     0.0699 | E_err:   0.004131
[2025-10-06 05:07:05] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -30.008731 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 05:07:08] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -30.008995 | E_var:     0.0719 | E_err:   0.004188
[2025-10-06 05:07:10] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -30.005744 | E_var:     0.0846 | E_err:   0.004546
[2025-10-06 05:07:13] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -30.005879 | E_var:     0.0979 | E_err:   0.004889
[2025-10-06 05:07:15] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -30.002290 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 05:07:17] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -30.005829 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 05:07:20] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -30.009390 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 05:07:22] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -30.013595 | E_var:     0.0784 | E_err:   0.004374
[2025-10-06 05:07:25] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -30.009753 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 05:07:27] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -30.015432 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 05:07:30] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -30.011637 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 05:07:32] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -30.008034 | E_var:     0.0890 | E_err:   0.004660
[2025-10-06 05:07:34] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -30.007674 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 05:07:37] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -30.003927 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 05:07:39] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -30.012312 | E_var:     0.0869 | E_err:   0.004605
[2025-10-06 05:07:42] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -30.009581 | E_var:     0.0658 | E_err:   0.004009
[2025-10-06 05:07:42] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 05:07:44] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -30.006941 | E_var:     0.0940 | E_err:   0.004792
[2025-10-06 05:07:47] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -30.005227 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 05:07:49] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -30.007518 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 05:07:51] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -30.010293 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 05:07:54] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -29.999658 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 05:07:56] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -30.004278 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 05:07:59] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -30.002149 | E_var:     0.0642 | E_err:   0.003958
[2025-10-06 05:08:01] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -30.012865 | E_var:     0.0898 | E_err:   0.004681
[2025-10-06 05:08:04] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -30.005313 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 05:08:06] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -30.008015 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 05:08:08] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -30.012821 | E_var:     0.0835 | E_err:   0.004514
[2025-10-06 05:08:11] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -30.012476 | E_var:     0.0759 | E_err:   0.004306
[2025-10-06 05:08:13] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -30.013419 | E_var:     0.0633 | E_err:   0.003932
[2025-10-06 05:08:16] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -30.009164 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 05:08:18] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -30.007175 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 05:08:21] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -30.002509 | E_var:     0.0744 | E_err:   0.004262
[2025-10-06 05:08:23] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -30.005926 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 05:08:25] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -29.995081 | E_var:     0.1446 | E_err:   0.005942
[2025-10-06 05:08:28] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -30.007528 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 05:08:30] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -30.005445 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 05:08:33] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -30.013959 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 05:08:35] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -30.015973 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 05:08:38] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -30.000954 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 05:08:40] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -30.013319 | E_var:     0.0764 | E_err:   0.004318
[2025-10-06 05:08:42] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -30.010504 | E_var:     0.0693 | E_err:   0.004113
[2025-10-06 05:08:45] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -30.007363 | E_var:     0.0756 | E_err:   0.004295
[2025-10-06 05:08:47] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -30.016091 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 05:08:50] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -30.006699 | E_var:     0.0876 | E_err:   0.004624
[2025-10-06 05:08:52] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -30.009799 | E_var:     0.0699 | E_err:   0.004130
[2025-10-06 05:08:55] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -30.009693 | E_var:     0.0689 | E_err:   0.004102
[2025-10-06 05:08:57] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -30.013375 | E_var:     0.0932 | E_err:   0.004769
[2025-10-06 05:08:59] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -30.021208 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 05:09:02] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -30.016332 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 05:09:04] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -30.002850 | E_var:     0.0872 | E_err:   0.004615
[2025-10-06 05:09:07] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -30.013605 | E_var:     0.0586 | E_err:   0.003783
[2025-10-06 05:09:09] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -30.014932 | E_var:     0.0910 | E_err:   0.004714
[2025-10-06 05:09:12] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -30.004171 | E_var:     0.0842 | E_err:   0.004534
[2025-10-06 05:09:14] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -30.010473 | E_var:     0.1007 | E_err:   0.004959
[2025-10-06 05:09:16] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -30.004889 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 05:09:19] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -30.008494 | E_var:     0.0822 | E_err:   0.004479
[2025-10-06 05:09:21] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -30.016039 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 05:09:24] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -30.014970 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 05:09:26] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -30.007087 | E_var:     0.0802 | E_err:   0.004424
[2025-10-06 05:09:29] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -29.997109 | E_var:     0.2956 | E_err:   0.008495
[2025-10-06 05:09:31] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -30.019594 | E_var:     1.1232 | E_err:   0.016559
[2025-10-06 05:09:33] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -30.005280 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 05:09:36] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -30.009691 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 05:09:38] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -30.014714 | E_var:     0.0751 | E_err:   0.004283
[2025-10-06 05:09:41] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -30.007200 | E_var:     0.0806 | E_err:   0.004436
[2025-10-06 05:09:43] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -30.012595 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 05:09:46] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -30.003505 | E_var:     0.0830 | E_err:   0.004500
[2025-10-06 05:09:48] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -30.010010 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 05:09:51] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -30.008127 | E_var:     0.0637 | E_err:   0.003943
[2025-10-06 05:09:53] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -29.999799 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 05:09:55] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -30.002018 | E_var:     0.0982 | E_err:   0.004897
[2025-10-06 05:09:58] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -30.009449 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 05:10:00] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -30.004891 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 05:10:03] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -30.007163 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 05:10:05] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -30.002674 | E_var:     0.1038 | E_err:   0.005035
[2025-10-06 05:10:07] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -30.013637 | E_var:     0.0699 | E_err:   0.004132
[2025-10-06 05:10:10] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -30.002562 | E_var:     0.1121 | E_err:   0.005232
[2025-10-06 05:10:12] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -30.012408 | E_var:     0.0753 | E_err:   0.004288
[2025-10-06 05:10:15] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -30.009621 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 05:10:17] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -30.012645 | E_var:     0.0806 | E_err:   0.004436
[2025-10-06 05:10:20] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -30.012011 | E_var:     0.0791 | E_err:   0.004393
[2025-10-06 05:10:22] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -30.012665 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 05:10:24] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -30.001505 | E_var:     0.0907 | E_err:   0.004704
[2025-10-06 05:10:27] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -30.006892 | E_var:     0.0863 | E_err:   0.004591
[2025-10-06 05:10:29] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -30.002294 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 05:10:32] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -30.010405 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 05:10:34] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -30.009035 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 05:10:37] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -30.012915 | E_var:     0.0717 | E_err:   0.004184
[2025-10-06 05:10:39] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -30.005016 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 05:10:41] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -30.004253 | E_var:     0.1043 | E_err:   0.005045
[2025-10-06 05:10:44] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -30.007300 | E_var:     0.0747 | E_err:   0.004271
[2025-10-06 05:10:46] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -30.007227 | E_var:     0.0888 | E_err:   0.004655
[2025-10-06 05:10:49] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -30.003110 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 05:10:51] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -30.005251 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 05:10:54] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -30.011136 | E_var:     0.0713 | E_err:   0.004171
[2025-10-06 05:10:56] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -30.006796 | E_var:     0.0789 | E_err:   0.004388
[2025-10-06 05:10:58] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -30.007895 | E_var:     0.0727 | E_err:   0.004214
[2025-10-06 05:11:01] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -30.011174 | E_var:     0.0708 | E_err:   0.004158
[2025-10-06 05:11:03] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -30.000310 | E_var:     0.0818 | E_err:   0.004468
[2025-10-06 05:11:06] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -30.004819 | E_var:     0.0740 | E_err:   0.004251
[2025-10-06 05:11:08] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -30.010313 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 05:11:11] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -30.013225 | E_var:     0.0779 | E_err:   0.004362
[2025-10-06 05:11:13] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -30.017802 | E_var:     0.0670 | E_err:   0.004045
[2025-10-06 05:11:15] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -30.010509 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 05:11:18] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -30.010990 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 05:11:20] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -30.009646 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 05:11:23] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -30.012279 | E_var:     0.0682 | E_err:   0.004080
[2025-10-06 05:11:25] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -30.020122 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 05:11:28] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -30.010775 | E_var:     0.0812 | E_err:   0.004451
[2025-10-06 05:11:30] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -30.011750 | E_var:     0.0682 | E_err:   0.004082
[2025-10-06 05:11:33] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -30.008283 | E_var:     0.0665 | E_err:   0.004031
[2025-10-06 05:11:35] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -30.010518 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 05:11:37] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -30.011697 | E_var:     0.0712 | E_err:   0.004168
[2025-10-06 05:11:40] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -30.012329 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 05:11:42] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -30.019458 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 05:11:45] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -30.003271 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 05:11:45] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 05:11:47] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -30.010373 | E_var:     0.0804 | E_err:   0.004430
[2025-10-06 05:11:50] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -30.005544 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 05:11:52] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -30.011969 | E_var:     0.0747 | E_err:   0.004269
[2025-10-06 05:11:54] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -30.001276 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 05:11:57] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -30.000092 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 05:11:59] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -30.004758 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 05:12:02] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -30.015362 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 05:12:04] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -30.010634 | E_var:     0.0838 | E_err:   0.004523
[2025-10-06 05:12:07] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -30.006628 | E_var:     0.0705 | E_err:   0.004150
[2025-10-06 05:12:09] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -30.007684 | E_var:     0.0745 | E_err:   0.004264
[2025-10-06 05:12:11] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -30.011490 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 05:12:14] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -30.005272 | E_var:     0.1337 | E_err:   0.005714
[2025-10-06 05:12:16] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -30.016087 | E_var:     0.1243 | E_err:   0.005508
[2025-10-06 05:12:19] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -30.005510 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 05:12:21] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -30.015064 | E_var:     0.1252 | E_err:   0.005530
[2025-10-06 05:12:24] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -30.004676 | E_var:     0.0773 | E_err:   0.004344
[2025-10-06 05:12:26] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -30.008090 | E_var:     0.1104 | E_err:   0.005192
[2025-10-06 05:12:28] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -30.009609 | E_var:     0.1129 | E_err:   0.005251
[2025-10-06 05:12:31] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -30.010992 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 05:12:33] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -30.013205 | E_var:     0.0663 | E_err:   0.004022
[2025-10-06 05:12:36] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -30.009521 | E_var:     0.0714 | E_err:   0.004175
[2025-10-06 05:12:38] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -30.009505 | E_var:     0.0779 | E_err:   0.004362
[2025-10-06 05:12:41] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -30.006989 | E_var:     0.0879 | E_err:   0.004632
[2025-10-06 05:12:43] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -30.010235 | E_var:     0.0819 | E_err:   0.004472
[2025-10-06 05:12:45] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -30.007747 | E_var:     0.0709 | E_err:   0.004159
[2025-10-06 05:12:48] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -29.999559 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 05:12:50] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -30.011265 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 05:12:53] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -30.001152 | E_var:     0.0739 | E_err:   0.004247
[2025-10-06 05:12:55] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -30.003867 | E_var:     0.0722 | E_err:   0.004200
[2025-10-06 05:12:57] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -30.008909 | E_var:     0.0653 | E_err:   0.003992
[2025-10-06 05:13:00] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -30.008677 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 05:13:02] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -30.010967 | E_var:     0.0722 | E_err:   0.004199
[2025-10-06 05:13:05] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -30.014819 | E_var:     0.0692 | E_err:   0.004110
[2025-10-06 05:13:07] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -30.011496 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 05:13:10] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -30.013622 | E_var:     0.0888 | E_err:   0.004655
[2025-10-06 05:13:12] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -30.005537 | E_var:     0.0937 | E_err:   0.004783
[2025-10-06 05:13:14] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -30.006512 | E_var:     0.0822 | E_err:   0.004478
[2025-10-06 05:13:17] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -30.000757 | E_var:     0.1278 | E_err:   0.005586
[2025-10-06 05:13:19] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -30.004670 | E_var:     0.1025 | E_err:   0.005002
[2025-10-06 05:13:22] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -30.005989 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 05:13:24] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -30.004079 | E_var:     0.0796 | E_err:   0.004410
[2025-10-06 05:13:27] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -30.011266 | E_var:     0.0794 | E_err:   0.004404
[2025-10-06 05:13:29] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -30.011499 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 05:13:32] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -30.003463 | E_var:     0.0871 | E_err:   0.004611
[2025-10-06 05:13:34] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -30.007542 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 05:13:36] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -30.013761 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 05:13:39] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -30.008477 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 05:13:41] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -30.015216 | E_var:     0.0658 | E_err:   0.004007
[2025-10-06 05:13:44] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -30.002663 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 05:13:46] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -30.017877 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 05:13:49] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -30.005662 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 05:13:51] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -30.007276 | E_var:     0.0778 | E_err:   0.004359
[2025-10-06 05:13:53] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -29.997883 | E_var:     0.3162 | E_err:   0.008786
[2025-10-06 05:13:56] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -30.019017 | E_var:     0.1129 | E_err:   0.005250
[2025-10-06 05:13:58] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -30.015468 | E_var:     0.0884 | E_err:   0.004645
[2025-10-06 05:14:01] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -30.009852 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 05:14:03] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -30.013595 | E_var:     0.0721 | E_err:   0.004194
[2025-10-06 05:14:06] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -29.994694 | E_var:     0.0988 | E_err:   0.004910
[2025-10-06 05:14:08] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -30.002849 | E_var:     0.0933 | E_err:   0.004772
[2025-10-06 05:14:10] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -30.016361 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 05:14:13] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -30.000308 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 05:14:15] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -30.013657 | E_var:     0.0680 | E_err:   0.004074
[2025-10-06 05:14:18] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -30.003049 | E_var:     0.0790 | E_err:   0.004393
[2025-10-06 05:14:20] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -30.009488 | E_var:     0.0673 | E_err:   0.004055
[2025-10-06 05:14:23] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -30.018104 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 05:14:25] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -30.014377 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 05:14:27] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -30.008488 | E_var:     0.0784 | E_err:   0.004374
[2025-10-06 05:14:30] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -30.009683 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 05:14:32] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -30.004450 | E_var:     0.0941 | E_err:   0.004792
[2025-10-06 05:14:35] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -30.006813 | E_var:     0.0727 | E_err:   0.004212
[2025-10-06 05:14:37] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -30.010206 | E_var:     0.0709 | E_err:   0.004161
[2025-10-06 05:14:40] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -30.002736 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 05:14:42] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -30.017894 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 05:14:44] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -30.002986 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 05:14:47] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -30.006855 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 05:14:49] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -30.007016 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 05:14:52] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -30.013295 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 05:14:54] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -30.005383 | E_var:     0.0653 | E_err:   0.003992
[2025-10-06 05:14:56] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -30.012480 | E_var:     0.0656 | E_err:   0.004001
[2025-10-06 05:14:59] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -30.012621 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 05:15:01] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -30.009781 | E_var:     0.0614 | E_err:   0.003870
[2025-10-06 05:15:04] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -30.005773 | E_var:     0.0743 | E_err:   0.004258
[2025-10-06 05:15:06] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -30.009997 | E_var:     0.2049 | E_err:   0.007072
[2025-10-06 05:15:09] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -30.004330 | E_var:     0.0709 | E_err:   0.004162
[2025-10-06 05:15:11] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -29.996170 | E_var:     0.1213 | E_err:   0.005441
[2025-10-06 05:15:13] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -30.017823 | E_var:     0.0800 | E_err:   0.004419
[2025-10-06 05:15:16] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -30.014720 | E_var:     0.0698 | E_err:   0.004128
[2025-10-06 05:15:18] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -30.007304 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 05:15:21] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -30.009199 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 05:15:23] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -30.004365 | E_var:     0.0929 | E_err:   0.004761
[2025-10-06 05:15:26] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -30.010883 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 05:15:28] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -30.004214 | E_var:     0.1016 | E_err:   0.004979
[2025-10-06 05:15:30] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -30.015996 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 05:15:33] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -30.008088 | E_var:     0.0740 | E_err:   0.004251
[2025-10-06 05:15:35] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -30.008246 | E_var:     0.1056 | E_err:   0.005076
[2025-10-06 05:15:38] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -30.006997 | E_var:     0.0697 | E_err:   0.004125
[2025-10-06 05:15:40] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -30.009344 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 05:15:43] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -30.002296 | E_var:     0.0718 | E_err:   0.004187
[2025-10-06 05:15:45] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -30.010503 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 05:15:47] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -30.005418 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 05:15:47] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 05:15:50] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -30.005885 | E_var:     0.0739 | E_err:   0.004248
[2025-10-06 05:15:52] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -30.005692 | E_var:     0.0621 | E_err:   0.003893
[2025-10-06 05:15:55] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -30.007851 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 05:15:57] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -30.010893 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 05:16:00] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -30.009863 | E_var:     0.0689 | E_err:   0.004102
[2025-10-06 05:16:02] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -30.005744 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 05:16:04] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -30.008895 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 05:16:07] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -30.010868 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 05:16:09] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -30.011416 | E_var:     0.0793 | E_err:   0.004400
[2025-10-06 05:16:12] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -29.999795 | E_var:     0.0814 | E_err:   0.004459
[2025-10-06 05:16:14] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -30.009023 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 05:16:17] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -29.998274 | E_var:     0.1149 | E_err:   0.005296
[2025-10-06 05:16:19] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -30.005548 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 05:16:21] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -30.006762 | E_var:     0.0715 | E_err:   0.004179
[2025-10-06 05:16:24] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -30.021602 | E_var:     0.0684 | E_err:   0.004087
[2025-10-06 05:16:26] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -30.014021 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 05:16:29] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -30.006333 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 05:16:31] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -30.011684 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 05:16:34] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -30.007376 | E_var:     0.1065 | E_err:   0.005098
[2025-10-06 05:16:36] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -30.008118 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 05:16:39] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -30.010343 | E_var:     0.0906 | E_err:   0.004702
[2025-10-06 05:16:41] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -30.002614 | E_var:     0.0947 | E_err:   0.004808
[2025-10-06 05:16:43] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -30.009347 | E_var:     0.0823 | E_err:   0.004484
[2025-10-06 05:16:46] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -30.013153 | E_var:     0.0870 | E_err:   0.004608
[2025-10-06 05:16:48] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -30.003216 | E_var:     0.0791 | E_err:   0.004394
[2025-10-06 05:16:51] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -30.007320 | E_var:     0.0737 | E_err:   0.004243
[2025-10-06 05:16:53] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -30.003143 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 05:16:56] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -30.011291 | E_var:     0.0786 | E_err:   0.004382
[2025-10-06 05:16:58] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -30.011654 | E_var:     0.1730 | E_err:   0.006499
[2025-10-06 05:17:00] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -30.008358 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 05:17:03] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -30.004157 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 05:17:05] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -30.009580 | E_var:     0.1135 | E_err:   0.005263
[2025-10-06 05:17:08] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -30.007897 | E_var:     0.0683 | E_err:   0.004083
[2025-10-06 05:17:10] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -30.007467 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 05:17:13] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -30.004859 | E_var:     0.0775 | E_err:   0.004349
[2025-10-06 05:17:15] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -30.003764 | E_var:     0.0702 | E_err:   0.004140
[2025-10-06 05:17:17] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -30.004941 | E_var:     0.0664 | E_err:   0.004027
[2025-10-06 05:17:20] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -30.008852 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 05:17:22] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -30.013233 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 05:17:25] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -30.001963 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 05:17:27] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -30.005752 | E_var:     0.0798 | E_err:   0.004413
[2025-10-06 05:17:30] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -30.016341 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 05:17:32] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -30.004980 | E_var:     0.1385 | E_err:   0.005816
[2025-10-06 05:17:34] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -29.996321 | E_var:     0.0993 | E_err:   0.004925
[2025-10-06 05:17:37] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -30.003668 | E_var:     0.0729 | E_err:   0.004218
[2025-10-06 05:17:39] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -30.018106 | E_var:     0.1001 | E_err:   0.004945
[2025-10-06 05:17:42] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -30.003608 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 05:17:44] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -30.004304 | E_var:     0.0694 | E_err:   0.004117
[2025-10-06 05:17:47] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -30.006105 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 05:17:49] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -30.014709 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 05:17:51] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -30.003629 | E_var:     0.0886 | E_err:   0.004651
[2025-10-06 05:17:54] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -30.009195 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 05:17:56] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -30.011125 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 05:17:59] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -30.019483 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 05:18:01] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -30.008121 | E_var:     0.1153 | E_err:   0.005306
[2025-10-06 05:18:04] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -30.005062 | E_var:     0.0736 | E_err:   0.004239
[2025-10-06 05:18:06] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -30.010270 | E_var:     0.0709 | E_err:   0.004160
[2025-10-06 05:18:08] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -30.013354 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 05:18:11] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -30.009069 | E_var:     0.0622 | E_err:   0.003896
[2025-10-06 05:18:13] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -30.003551 | E_var:     0.0880 | E_err:   0.004635
[2025-10-06 05:18:16] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -30.007717 | E_var:     0.0632 | E_err:   0.003929
[2025-10-06 05:18:18] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -30.007272 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 05:18:21] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -30.017111 | E_var:     0.1289 | E_err:   0.005610
[2025-10-06 05:18:23] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -30.009042 | E_var:     0.0661 | E_err:   0.004018
[2025-10-06 05:18:25] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -30.011582 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 05:18:28] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -30.003446 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 05:18:30] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -30.013500 | E_var:     0.0773 | E_err:   0.004343
[2025-10-06 05:18:33] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -30.010952 | E_var:     0.1031 | E_err:   0.005016
[2025-10-06 05:18:35] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -30.009004 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 05:18:37] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -30.006127 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 05:18:40] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -30.005617 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 05:18:42] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -30.012366 | E_var:     0.0720 | E_err:   0.004194
[2025-10-06 05:18:45] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -30.004731 | E_var:     0.0968 | E_err:   0.004861
[2025-10-06 05:18:47] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -30.011712 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 05:18:50] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -30.015784 | E_var:     0.3194 | E_err:   0.008831
[2025-10-06 05:18:52] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -30.011221 | E_var:     0.0766 | E_err:   0.004326
[2025-10-06 05:18:54] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -30.009139 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 05:18:57] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -30.012438 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 05:18:59] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -30.011233 | E_var:     0.1168 | E_err:   0.005339
[2025-10-06 05:19:02] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -30.003315 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 05:19:04] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -30.009263 | E_var:     0.0630 | E_err:   0.003922
[2025-10-06 05:19:07] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -30.007915 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 05:19:09] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -30.003372 | E_var:     0.0714 | E_err:   0.004176
[2025-10-06 05:19:12] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -30.009373 | E_var:     0.0677 | E_err:   0.004065
[2025-10-06 05:19:14] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -30.011874 | E_var:     0.0637 | E_err:   0.003943
[2025-10-06 05:19:16] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -30.015153 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 05:19:19] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -30.009044 | E_var:     0.0764 | E_err:   0.004318
[2025-10-06 05:19:21] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -30.017870 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 05:19:24] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -30.008399 | E_var:     0.0646 | E_err:   0.003972
[2025-10-06 05:19:26] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -30.009418 | E_var:     0.1054 | E_err:   0.005072
[2025-10-06 05:19:29] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -30.005299 | E_var:     0.0677 | E_err:   0.004066
[2025-10-06 05:19:31] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -30.006546 | E_var:     0.0763 | E_err:   0.004315
[2025-10-06 05:19:33] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -30.004917 | E_var:     0.0689 | E_err:   0.004101
[2025-10-06 05:19:36] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -30.003012 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 05:19:38] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -30.009049 | E_var:     0.0653 | E_err:   0.003993
[2025-10-06 05:19:41] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -30.011257 | E_var:     0.0789 | E_err:   0.004388
[2025-10-06 05:19:43] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -30.015584 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 05:19:46] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -30.014478 | E_var:     0.0667 | E_err:   0.004035
[2025-10-06 05:19:48] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -30.003780 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 05:19:50] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -30.008756 | E_var:     0.0929 | E_err:   0.004764
[2025-10-06 05:19:50] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 05:19:53] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -30.013942 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 05:19:55] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -30.023811 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 05:19:58] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -30.007997 | E_var:     0.0950 | E_err:   0.004816
[2025-10-06 05:20:00] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -30.005980 | E_var:     0.1164 | E_err:   0.005330
[2025-10-06 05:20:03] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -30.007592 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 05:20:05] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -30.010571 | E_var:     0.0728 | E_err:   0.004215
[2025-10-06 05:20:07] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -30.010329 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 05:20:10] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -30.012818 | E_var:     0.0748 | E_err:   0.004274
[2025-10-06 05:20:12] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -30.009171 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 05:20:15] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -30.002042 | E_var:     0.1821 | E_err:   0.006668
[2025-10-06 05:20:17] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -30.009956 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 05:20:19] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -30.009693 | E_var:     0.0981 | E_err:   0.004894
[2025-10-06 05:20:22] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -30.008688 | E_var:     0.0768 | E_err:   0.004330
[2025-10-06 05:20:24] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -30.008087 | E_var:     0.0815 | E_err:   0.004459
[2025-10-06 05:20:27] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -30.011026 | E_var:     0.0776 | E_err:   0.004351
[2025-10-06 05:20:29] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -30.013103 | E_var:     0.0700 | E_err:   0.004133
[2025-10-06 05:20:32] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -30.009138 | E_var:     0.0725 | E_err:   0.004206
[2025-10-06 05:20:34] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -30.006480 | E_var:     0.0663 | E_err:   0.004022
[2025-10-06 05:20:36] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -30.003956 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 05:20:39] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -30.008574 | E_var:     0.0658 | E_err:   0.004008
[2025-10-06 05:20:41] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -30.005975 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 05:20:44] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -30.004184 | E_var:     0.0781 | E_err:   0.004366
[2025-10-06 05:20:46] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -30.010727 | E_var:     0.0736 | E_err:   0.004239
[2025-10-06 05:20:49] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -30.013466 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 05:20:51] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -30.014406 | E_var:     0.0916 | E_err:   0.004728
[2025-10-06 05:20:53] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -30.007026 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 05:20:56] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -30.003930 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 05:20:58] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -30.014181 | E_var:     0.0941 | E_err:   0.004793
[2025-10-06 05:21:01] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -30.011136 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 05:21:03] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -30.006626 | E_var:     0.0840 | E_err:   0.004528
[2025-10-06 05:21:06] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -30.009000 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 05:21:08] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -30.010826 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 05:21:11] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -30.004242 | E_var:     0.1051 | E_err:   0.005064
[2025-10-06 05:21:13] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -30.010692 | E_var:     0.0758 | E_err:   0.004302
[2025-10-06 05:21:15] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -30.002879 | E_var:     0.0714 | E_err:   0.004176
[2025-10-06 05:21:18] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -30.011399 | E_var:     0.0741 | E_err:   0.004253
[2025-10-06 05:21:20] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -30.011364 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 05:21:23] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -30.008799 | E_var:     0.0835 | E_err:   0.004516
[2025-10-06 05:21:25] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -30.007632 | E_var:     0.0683 | E_err:   0.004082
[2025-10-06 05:21:27] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -30.008524 | E_var:     0.0731 | E_err:   0.004225
[2025-10-06 05:21:30] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -29.997840 | E_var:     0.4838 | E_err:   0.010868
[2025-10-06 05:21:32] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -30.014414 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 05:21:35] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -30.007784 | E_var:     0.0926 | E_err:   0.004754
[2025-10-06 05:21:37] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -30.008279 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 05:21:40] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -30.018022 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 05:21:42] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -30.007342 | E_var:     0.0809 | E_err:   0.004443
[2025-10-06 05:21:45] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -30.010514 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 05:21:47] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -30.014885 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 05:21:49] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -30.009682 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 05:21:52] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -30.002829 | E_var:     0.0880 | E_err:   0.004636
[2025-10-06 05:21:52] ======================================================================================================
[2025-10-06 05:21:52] ✅ Training completed successfully
[2025-10-06 05:21:52] Total restarts: 2
[2025-10-06 05:21:53] Final Energy: -30.00282916 ± 0.00463620
[2025-10-06 05:21:53] Final Variance: 0.088041
[2025-10-06 05:21:53] ======================================================================================================
[2025-10-06 05:21:53] ======================================================================================================
[2025-10-06 05:21:53] Training completed | Runtime: 2600.1s
[2025-10-06 05:21:53] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 05:21:53] ======================================================================================================
