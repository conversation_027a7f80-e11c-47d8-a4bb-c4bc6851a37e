[2025-10-02 14:55:27] ==================================================
[2025-10-02 14:55:27] GCNN for Shastry-Sutherland Model
[2025-10-02 14:55:27] ==================================================
[2025-10-02 14:55:27] System parameters:
[2025-10-02 14:55:27]   - System size: L=4, N=64
[2025-10-02 14:55:27]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-02 14:55:27] --------------------------------------------------
[2025-10-02 14:55:27] Model parameters:
[2025-10-02 14:55:27]   - Number of layers = 4
[2025-10-02 14:55:27]   - Number of features = 4
[2025-10-02 14:55:27]   - Total parameters = 12572
[2025-10-02 14:55:27] --------------------------------------------------
[2025-10-02 14:55:27] Training parameters:
[2025-10-02 14:55:27]   - Total iterations: 2250
[2025-10-02 14:55:27]   - Annealing cycles: 4
[2025-10-02 14:55:27]   - Initial period: 150
[2025-10-02 14:55:27]   - Period multiplier: 2.0
[2025-10-02 14:55:27]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-02 14:55:27]   - Samples: 4096
[2025-10-02 14:55:27]   - Discarded samples: 0
[2025-10-02 14:55:27]   - Chunk size: 4096
[2025-10-02 14:55:27]   - Diagonal shift: 0.15
[2025-10-02 14:55:27]   - Gradient clipping: 1.0
[2025-10-02 14:55:27]   - Checkpoint enabled: interval=200
[2025-10-02 14:55:27]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints
[2025-10-02 14:55:27] --------------------------------------------------
[2025-10-02 14:55:27] Device status:
[2025-10-02 14:55:27]   - Devices model: NVIDIA H200 NVL
[2025-10-02 14:55:27]   - Number of devices: 1
[2025-10-02 14:55:27]   - Sharding: True
[2025-10-02 14:55:59] [Iter    1/2250] R0[0/150]    | LR: 0.030000 | E:  32.313068 | E_var:     0.0582 | E_err:   0.003770
[2025-10-02 14:56:02] [Iter    2/2250] R0[2/150]    | LR: 0.029989 | E:  32.309754 | E_var:     0.0783 | E_err:   0.004372
[2025-10-02 14:56:04] [Iter    3/2250] R0[4/150]    | LR: 0.029956 | E:  32.298331 | E_var:     0.1141 | E_err:   0.005277
[2025-10-02 14:56:06] [Iter    4/2250] R0[6/150]    | LR: 0.029901 | E:  32.289742 | E_var:     0.1675 | E_err:   0.006395
[2025-10-02 14:56:09] [Iter    5/2250] R0[8/150]    | LR: 0.029825 | E:  32.263704 | E_var:     0.2743 | E_err:   0.008183
[2025-10-02 14:56:11] [Iter    6/2250] R0[10/150]   | LR: 0.029727 | E:  32.241307 | E_var:     0.4152 | E_err:   0.010068
[2025-10-02 14:56:14] [Iter    7/2250] R0[12/150]   | LR: 0.029607 | E:  32.188568 | E_var:     0.7107 | E_err:   0.013172
[2025-10-02 14:56:16] [Iter    8/2250] R0[14/150]   | LR: 0.029466 | E:  32.102379 | E_var:     1.1899 | E_err:   0.017044
[2025-10-02 14:56:19] [Iter    9/2250] R0[16/150]   | LR: 0.029305 | E:  31.886910 | E_var:     2.1166 | E_err:   0.022732
[2025-10-02 14:56:21] [Iter   10/2250] R0[18/150]   | LR: 0.029122 | E:  31.617056 | E_var:     3.9484 | E_err:   0.031048
[2025-10-02 14:56:23] [Iter   11/2250] R0[20/150]   | LR: 0.028919 | E:  30.955979 | E_var:     8.2460 | E_err:   0.044869
[2025-10-02 14:56:26] [Iter   12/2250] R0[22/150]   | LR: 0.028696 | E:  29.707399 | E_var:    19.8366 | E_err:   0.069591
[2025-10-02 14:56:28] [Iter   13/2250] R0[24/150]   | LR: 0.028454 | E:  26.132141 | E_var:    43.2212 | E_err:   0.102723
[2025-10-02 14:56:31] [Iter   14/2250] R0[26/150]   | LR: 0.028192 | E:  20.128305 | E_var:    68.3635 | E_err:   0.129191
[2025-10-02 14:56:33] [Iter   15/2250] R0[28/150]   | LR: 0.027912 | E:  13.640262 | E_var:    51.6569 | E_err:   0.112301
[2025-10-02 14:56:35] [Iter   16/2250] R0[30/150]   | LR: 0.027613 | E:   9.135591 | E_var:    40.2004 | E_err:   0.099068
[2025-10-02 14:56:38] [Iter   17/2250] R0[32/150]   | LR: 0.027296 | E:   5.710424 | E_var:    33.9887 | E_err:   0.091094
[2025-10-02 14:56:40] [Iter   18/2250] R0[34/150]   | LR: 0.026962 | E:   2.900743 | E_var:    35.4819 | E_err:   0.093073
[2025-10-02 14:56:43] [Iter   19/2250] R0[36/150]   | LR: 0.026612 | E:   0.597557 | E_var:    29.8330 | E_err:   0.085343
[2025-10-02 14:56:45] [Iter   20/2250] R0[38/150]   | LR: 0.026246 | E:  -1.558554 | E_var:    26.9949 | E_err:   0.081182
[2025-10-02 14:56:47] [Iter   21/2250] R0[40/150]   | LR: 0.025864 | E:  -3.339124 | E_var:    24.1607 | E_err:   0.076802
[2025-10-02 14:56:50] [Iter   22/2250] R0[42/150]   | LR: 0.025468 | E:  -4.910927 | E_var:    22.4991 | E_err:   0.074114
[2025-10-02 14:56:52] [Iter   23/2250] R0[44/150]   | LR: 0.025057 | E:  -6.212189 | E_var:    21.4408 | E_err:   0.072350
[2025-10-02 14:56:55] [Iter   24/2250] R0[46/150]   | LR: 0.024634 | E:  -7.701333 | E_var:    20.0381 | E_err:   0.069944
[2025-10-02 14:56:57] [Iter   25/2250] R0[48/150]   | LR: 0.024198 | E:  -8.804916 | E_var:    20.7094 | E_err:   0.071106
[2025-10-02 14:57:00] [Iter   26/2250] R0[50/150]   | LR: 0.023750 | E:  -9.746315 | E_var:    19.2294 | E_err:   0.068518
[2025-10-02 14:57:02] [Iter   27/2250] R0[52/150]   | LR: 0.023291 | E: -10.708668 | E_var:    18.3548 | E_err:   0.066941
[2025-10-02 14:57:04] [Iter   28/2250] R0[54/150]   | LR: 0.022822 | E: -11.494648 | E_var:    16.4844 | E_err:   0.063439
[2025-10-02 14:57:07] [Iter   29/2250] R0[56/150]   | LR: 0.022344 | E: -12.453627 | E_var:    15.7351 | E_err:   0.061980
[2025-10-02 14:57:09] [Iter   30/2250] R0[58/150]   | LR: 0.021857 | E: -13.059939 | E_var:    15.1592 | E_err:   0.060836
[2025-10-02 14:57:12] [Iter   31/2250] R0[60/150]   | LR: 0.021363 | E: -13.853967 | E_var:    14.6528 | E_err:   0.059811
[2025-10-02 14:57:14] [Iter   32/2250] R0[62/150]   | LR: 0.020861 | E: -14.429344 | E_var:    14.4018 | E_err:   0.059296
[2025-10-02 14:57:16] [Iter   33/2250] R0[64/150]   | LR: 0.020354 | E: -15.039292 | E_var:    13.7468 | E_err:   0.057932
[2025-10-02 14:57:19] [Iter   34/2250] R0[66/150]   | LR: 0.019842 | E: -15.327293 | E_var:    12.9706 | E_err:   0.056273
[2025-10-02 14:57:21] [Iter   35/2250] R0[68/150]   | LR: 0.019326 | E: -15.868502 | E_var:    12.5005 | E_err:   0.055244
[2025-10-02 14:57:24] [Iter   36/2250] R0[70/150]   | LR: 0.018807 | E: -16.260723 | E_var:    12.3549 | E_err:   0.054921
[2025-10-02 14:57:26] [Iter   37/2250] R0[72/150]   | LR: 0.018285 | E: -16.669877 | E_var:    12.0851 | E_err:   0.054318
[2025-10-02 14:57:28] [Iter   38/2250] R0[74/150]   | LR: 0.017762 | E: -17.019817 | E_var:    11.4878 | E_err:   0.052959
[2025-10-02 14:57:31] [Iter   39/2250] R0[76/150]   | LR: 0.017238 | E: -17.387215 | E_var:    11.1641 | E_err:   0.052207
[2025-10-02 14:57:33] [Iter   40/2250] R0[78/150]   | LR: 0.016715 | E: -17.628285 | E_var:    11.0191 | E_err:   0.051867
[2025-10-02 14:57:36] [Iter   41/2250] R0[80/150]   | LR: 0.016193 | E: -17.877099 | E_var:    10.5758 | E_err:   0.050813
[2025-10-02 14:57:38] [Iter   42/2250] R0[82/150]   | LR: 0.015674 | E: -18.156154 | E_var:    10.1834 | E_err:   0.049862
[2025-10-02 14:57:41] [Iter   43/2250] R0[84/150]   | LR: 0.015158 | E: -18.359990 | E_var:    10.2768 | E_err:   0.050090
[2025-10-02 14:57:43] [Iter   44/2250] R0[86/150]   | LR: 0.014646 | E: -18.620686 | E_var:    10.1362 | E_err:   0.049746
[2025-10-02 14:57:45] [Iter   45/2250] R0[88/150]   | LR: 0.014139 | E: -18.763408 | E_var:     9.5106 | E_err:   0.048186
[2025-10-02 14:57:48] [Iter   46/2250] R0[90/150]   | LR: 0.013637 | E: -18.958597 | E_var:     9.4604 | E_err:   0.048059
[2025-10-02 14:57:50] [Iter   47/2250] R0[92/150]   | LR: 0.013143 | E: -19.198520 | E_var:     9.4955 | E_err:   0.048148
[2025-10-02 14:57:53] [Iter   48/2250] R0[94/150]   | LR: 0.012656 | E: -19.312243 | E_var:     9.5690 | E_err:   0.048334
[2025-10-02 14:57:55] [Iter   49/2250] R0[96/150]   | LR: 0.012178 | E: -19.515364 | E_var:    11.7184 | E_err:   0.053488
[2025-10-02 14:57:57] [Iter   50/2250] R0[98/150]   | LR: 0.011709 | E: -19.664081 | E_var:     8.7627 | E_err:   0.046253
[2025-10-02 14:58:00] [Iter   51/2250] R0[100/150]  | LR: 0.011250 | E: -19.755996 | E_var:     8.5608 | E_err:   0.045717
[2025-10-02 14:58:02] [Iter   52/2250] R0[102/150]  | LR: 0.010802 | E: -19.917122 | E_var:     8.3471 | E_err:   0.045143
[2025-10-02 14:58:05] [Iter   53/2250] R0[104/150]  | LR: 0.010366 | E: -20.066161 | E_var:     8.4152 | E_err:   0.045326
[2025-10-02 14:58:07] [Iter   54/2250] R0[106/150]  | LR: 0.009943 | E: -20.162744 | E_var:     8.2382 | E_err:   0.044847
[2025-10-02 14:58:10] [Iter   55/2250] R0[108/150]  | LR: 0.009532 | E: -20.326086 | E_var:     7.9469 | E_err:   0.044047
[2025-10-02 14:58:12] [Iter   56/2250] R0[110/150]  | LR: 0.009136 | E: -20.452716 | E_var:     7.6395 | E_err:   0.043187
[2025-10-02 14:58:14] [Iter   57/2250] R0[112/150]  | LR: 0.008754 | E: -20.487671 | E_var:     7.7307 | E_err:   0.043444
[2025-10-02 14:58:17] [Iter   58/2250] R0[114/150]  | LR: 0.008388 | E: -20.623449 | E_var:     7.6994 | E_err:   0.043356
[2025-10-02 14:58:19] [Iter   59/2250] R0[116/150]  | LR: 0.008038 | E: -20.730236 | E_var:     7.5943 | E_err:   0.043059
[2025-10-02 14:58:22] [Iter   60/2250] R0[118/150]  | LR: 0.007704 | E: -20.832429 | E_var:     7.3017 | E_err:   0.042221
[2025-10-02 14:58:24] [Iter   61/2250] R0[120/150]  | LR: 0.007387 | E: -20.890238 | E_var:     7.8083 | E_err:   0.043661
[2025-10-02 14:58:27] [Iter   62/2250] R0[122/150]  | LR: 0.007088 | E: -20.969081 | E_var:     7.4290 | E_err:   0.042588
[2025-10-02 14:58:29] [Iter   63/2250] R0[124/150]  | LR: 0.006808 | E: -21.018416 | E_var:     6.5983 | E_err:   0.040136
[2025-10-02 14:58:31] [Iter   64/2250] R0[126/150]  | LR: 0.006546 | E: -21.108645 | E_var:     8.2544 | E_err:   0.044891
[2025-10-02 14:58:34] [Iter   65/2250] R0[128/150]  | LR: 0.006304 | E: -21.161994 | E_var:     7.0392 | E_err:   0.041455
[2025-10-02 14:58:36] [Iter   66/2250] R0[130/150]  | LR: 0.006081 | E: -21.318810 | E_var:     6.7080 | E_err:   0.040468
[2025-10-02 14:58:39] [Iter   67/2250] R0[132/150]  | LR: 0.005878 | E: -21.337048 | E_var:     6.5371 | E_err:   0.039950
[2025-10-02 14:58:41] [Iter   68/2250] R0[134/150]  | LR: 0.005695 | E: -21.459659 | E_var:     6.5337 | E_err:   0.039939
[2025-10-02 14:58:43] [Iter   69/2250] R0[136/150]  | LR: 0.005534 | E: -21.514416 | E_var:     6.7518 | E_err:   0.040600
[2025-10-02 14:58:46] [Iter   70/2250] R0[138/150]  | LR: 0.005393 | E: -21.546119 | E_var:     6.5262 | E_err:   0.039916
[2025-10-02 14:58:48] [Iter   71/2250] R0[140/150]  | LR: 0.005273 | E: -21.599405 | E_var:     6.2930 | E_err:   0.039197
[2025-10-02 14:58:51] [Iter   72/2250] R0[142/150]  | LR: 0.005175 | E: -21.661164 | E_var:     6.7427 | E_err:   0.040573
[2025-10-02 14:58:53] [Iter   73/2250] R0[144/150]  | LR: 0.005099 | E: -21.712027 | E_var:     6.5029 | E_err:   0.039845
[2025-10-02 14:58:55] [Iter   74/2250] R0[146/150]  | LR: 0.005044 | E: -21.784121 | E_var:     6.2076 | E_err:   0.038930
[2025-10-02 14:58:58] [Iter   75/2250] R0[148/150]  | LR: 0.005011 | E: -21.881013 | E_var:     5.9086 | E_err:   0.037981
[2025-10-02 14:58:58] 🔄 RESTART #1 | Period: 300
[2025-10-02 14:59:00] [Iter   76/2250] R1[0/300]    | LR: 0.030000 | E: -21.905185 | E_var:     5.7444 | E_err:   0.037449
[2025-10-02 14:59:03] [Iter   77/2250] R1[2/300]    | LR: 0.029997 | E: -21.968622 | E_var:     5.7353 | E_err:   0.037419
[2025-10-02 14:59:05] [Iter   78/2250] R1[4/300]    | LR: 0.029989 | E: -22.029959 | E_var:     6.0730 | E_err:   0.038505
[2025-10-02 14:59:07] [Iter   79/2250] R1[6/300]    | LR: 0.029975 | E: -22.027756 | E_var:     5.8554 | E_err:   0.037809
[2025-10-02 14:59:10] [Iter   80/2250] R1[8/300]    | LR: 0.029956 | E: -22.124726 | E_var:     5.5250 | E_err:   0.036727
[2025-10-02 14:59:12] [Iter   81/2250] R1[10/300]   | LR: 0.029932 | E: -22.227479 | E_var:     5.2759 | E_err:   0.035890
[2025-10-02 14:59:15] [Iter   82/2250] R1[12/300]   | LR: 0.029901 | E: -22.287898 | E_var:     5.4613 | E_err:   0.036515
[2025-10-02 14:59:17] [Iter   83/2250] R1[14/300]   | LR: 0.029866 | E: -22.281960 | E_var:     5.0339 | E_err:   0.035057
[2025-10-02 14:59:20] [Iter   84/2250] R1[16/300]   | LR: 0.029825 | E: -22.339026 | E_var:     5.4799 | E_err:   0.036577
[2025-10-02 14:59:22] [Iter   85/2250] R1[18/300]   | LR: 0.029779 | E: -22.390729 | E_var:     5.1330 | E_err:   0.035400
[2025-10-02 14:59:24] [Iter   86/2250] R1[20/300]   | LR: 0.029727 | E: -22.505307 | E_var:     5.0489 | E_err:   0.035109
[2025-10-02 14:59:27] [Iter   87/2250] R1[22/300]   | LR: 0.029670 | E: -22.522754 | E_var:     4.9673 | E_err:   0.034824
[2025-10-02 14:59:29] [Iter   88/2250] R1[24/300]   | LR: 0.029607 | E: -22.569990 | E_var:     5.2802 | E_err:   0.035904
[2025-10-02 14:59:32] [Iter   89/2250] R1[26/300]   | LR: 0.029540 | E: -22.572324 | E_var:     4.8978 | E_err:   0.034580
[2025-10-02 14:59:34] [Iter   90/2250] R1[28/300]   | LR: 0.029466 | E: -22.628839 | E_var:     5.6441 | E_err:   0.037121
[2025-10-02 14:59:36] [Iter   91/2250] R1[30/300]   | LR: 0.029388 | E: -22.722226 | E_var:     4.5603 | E_err:   0.033367
[2025-10-02 14:59:39] [Iter   92/2250] R1[32/300]   | LR: 0.029305 | E: -22.800645 | E_var:     4.8125 | E_err:   0.034277
[2025-10-02 14:59:41] [Iter   93/2250] R1[34/300]   | LR: 0.029216 | E: -22.819871 | E_var:     4.5201 | E_err:   0.033220
[2025-10-02 14:59:44] [Iter   94/2250] R1[36/300]   | LR: 0.029122 | E: -22.867048 | E_var:     4.7845 | E_err:   0.034177
[2025-10-02 14:59:46] [Iter   95/2250] R1[38/300]   | LR: 0.029023 | E: -22.867627 | E_var:     4.5894 | E_err:   0.033473
[2025-10-02 14:59:48] [Iter   96/2250] R1[40/300]   | LR: 0.028919 | E: -22.956584 | E_var:     4.5625 | E_err:   0.033375
[2025-10-02 14:59:51] [Iter   97/2250] R1[42/300]   | LR: 0.028810 | E: -22.984386 | E_var:     4.1634 | E_err:   0.031882
[2025-10-02 14:59:53] [Iter   98/2250] R1[44/300]   | LR: 0.028696 | E: -23.039127 | E_var:     4.8273 | E_err:   0.034330
[2025-10-02 14:59:56] [Iter   99/2250] R1[46/300]   | LR: 0.028578 | E: -23.086158 | E_var:     4.4307 | E_err:   0.032889
[2025-10-02 14:59:58] [Iter  100/2250] R1[48/300]   | LR: 0.028454 | E: -23.112847 | E_var:     4.1086 | E_err:   0.031671
[2025-10-02 15:00:01] [Iter  101/2250] R1[50/300]   | LR: 0.028325 | E: -23.172688 | E_var:     4.2694 | E_err:   0.032285
[2025-10-02 15:00:03] [Iter  102/2250] R1[52/300]   | LR: 0.028192 | E: -23.166433 | E_var:     4.2831 | E_err:   0.032337
[2025-10-02 15:00:05] [Iter  103/2250] R1[54/300]   | LR: 0.028054 | E: -23.310266 | E_var:     3.8622 | E_err:   0.030707
[2025-10-02 15:00:08] [Iter  104/2250] R1[56/300]   | LR: 0.027912 | E: -23.292073 | E_var:     4.5460 | E_err:   0.033315
[2025-10-02 15:00:10] [Iter  105/2250] R1[58/300]   | LR: 0.027764 | E: -23.340516 | E_var:     3.9624 | E_err:   0.031103
[2025-10-02 15:00:13] [Iter  106/2250] R1[60/300]   | LR: 0.027613 | E: -23.365830 | E_var:     3.9619 | E_err:   0.031101
[2025-10-02 15:00:15] [Iter  107/2250] R1[62/300]   | LR: 0.027457 | E: -23.447943 | E_var:     4.2333 | E_err:   0.032148
[2025-10-02 15:00:17] [Iter  108/2250] R1[64/300]   | LR: 0.027296 | E: -23.492724 | E_var:     3.8638 | E_err:   0.030713
[2025-10-02 15:00:20] [Iter  109/2250] R1[66/300]   | LR: 0.027131 | E: -23.577790 | E_var:     4.6704 | E_err:   0.033767
[2025-10-02 15:00:22] [Iter  110/2250] R1[68/300]   | LR: 0.026962 | E: -23.571822 | E_var:     3.9711 | E_err:   0.031137
[2025-10-02 15:00:25] [Iter  111/2250] R1[70/300]   | LR: 0.026789 | E: -23.569470 | E_var:     3.8657 | E_err:   0.030721
[2025-10-02 15:00:27] [Iter  112/2250] R1[72/300]   | LR: 0.026612 | E: -23.619300 | E_var:     3.8270 | E_err:   0.030567
[2025-10-02 15:00:30] [Iter  113/2250] R1[74/300]   | LR: 0.026431 | E: -23.681330 | E_var:     3.7441 | E_err:   0.030234
[2025-10-02 15:00:32] [Iter  114/2250] R1[76/300]   | LR: 0.026246 | E: -23.718263 | E_var:     3.6853 | E_err:   0.029995
[2025-10-02 15:00:34] [Iter  115/2250] R1[78/300]   | LR: 0.026057 | E: -23.790637 | E_var:     3.5555 | E_err:   0.029463
[2025-10-02 15:00:37] [Iter  116/2250] R1[80/300]   | LR: 0.025864 | E: -23.776022 | E_var:     3.6093 | E_err:   0.029685
[2025-10-02 15:00:39] [Iter  117/2250] R1[82/300]   | LR: 0.025668 | E: -23.804928 | E_var:     4.1244 | E_err:   0.031732
[2025-10-02 15:00:42] [Iter  118/2250] R1[84/300]   | LR: 0.025468 | E: -23.866110 | E_var:     3.8751 | E_err:   0.030758
[2025-10-02 15:00:44] [Iter  119/2250] R1[86/300]   | LR: 0.025264 | E: -23.909513 | E_var:     3.6861 | E_err:   0.029999
[2025-10-02 15:00:46] [Iter  120/2250] R1[88/300]   | LR: 0.025057 | E: -23.887776 | E_var:     3.8883 | E_err:   0.030811
[2025-10-02 15:00:49] [Iter  121/2250] R1[90/300]   | LR: 0.024847 | E: -23.944274 | E_var:     3.4266 | E_err:   0.028923
[2025-10-02 15:00:51] [Iter  122/2250] R1[92/300]   | LR: 0.024634 | E: -23.947931 | E_var:     3.6477 | E_err:   0.029842
[2025-10-02 15:00:54] [Iter  123/2250] R1[94/300]   | LR: 0.024417 | E: -24.033551 | E_var:     3.5921 | E_err:   0.029614
[2025-10-02 15:00:56] [Iter  124/2250] R1[96/300]   | LR: 0.024198 | E: -24.052419 | E_var:     3.5949 | E_err:   0.029626
[2025-10-02 15:00:58] [Iter  125/2250] R1[98/300]   | LR: 0.023975 | E: -24.126098 | E_var:     3.5687 | E_err:   0.029517
[2025-10-02 15:01:01] [Iter  126/2250] R1[100/300]  | LR: 0.023750 | E: -24.144705 | E_var:     3.6361 | E_err:   0.029795
[2025-10-02 15:01:03] [Iter  127/2250] R1[102/300]  | LR: 0.023522 | E: -24.151641 | E_var:     3.5267 | E_err:   0.029343
[2025-10-02 15:01:06] [Iter  128/2250] R1[104/300]  | LR: 0.023291 | E: -24.207653 | E_var:     3.4339 | E_err:   0.028954
[2025-10-02 15:01:08] [Iter  129/2250] R1[106/300]  | LR: 0.023058 | E: -24.234629 | E_var:     3.7507 | E_err:   0.030261
[2025-10-02 15:01:11] [Iter  130/2250] R1[108/300]  | LR: 0.022822 | E: -24.307128 | E_var:     3.4538 | E_err:   0.029038
[2025-10-02 15:01:13] [Iter  131/2250] R1[110/300]  | LR: 0.022584 | E: -24.342232 | E_var:     3.8590 | E_err:   0.030694
[2025-10-02 15:01:15] [Iter  132/2250] R1[112/300]  | LR: 0.022344 | E: -24.351507 | E_var:     3.5824 | E_err:   0.029574
[2025-10-02 15:01:18] [Iter  133/2250] R1[114/300]  | LR: 0.022102 | E: -24.413991 | E_var:     3.8334 | E_err:   0.030592
[2025-10-02 15:01:20] [Iter  134/2250] R1[116/300]  | LR: 0.021857 | E: -24.484114 | E_var:     3.6825 | E_err:   0.029984
[2025-10-02 15:01:23] [Iter  135/2250] R1[118/300]  | LR: 0.021611 | E: -24.544162 | E_var:     3.8042 | E_err:   0.030476
[2025-10-02 15:01:25] [Iter  136/2250] R1[120/300]  | LR: 0.021363 | E: -24.613472 | E_var:     3.5388 | E_err:   0.029393
[2025-10-02 15:01:27] [Iter  137/2250] R1[122/300]  | LR: 0.021113 | E: -24.672001 | E_var:     3.5728 | E_err:   0.029534
[2025-10-02 15:01:30] [Iter  138/2250] R1[124/300]  | LR: 0.020861 | E: -24.777112 | E_var:     3.4722 | E_err:   0.029116
[2025-10-02 15:01:32] [Iter  139/2250] R1[126/300]  | LR: 0.020609 | E: -24.806188 | E_var:     3.7587 | E_err:   0.030293
[2025-10-02 15:01:35] [Iter  140/2250] R1[128/300]  | LR: 0.020354 | E: -24.884080 | E_var:     3.7532 | E_err:   0.030271
[2025-10-02 15:01:37] [Iter  141/2250] R1[130/300]  | LR: 0.020099 | E: -24.981080 | E_var:     3.8078 | E_err:   0.030490
[2025-10-02 15:01:39] [Iter  142/2250] R1[132/300]  | LR: 0.019842 | E: -25.049652 | E_var:     3.7346 | E_err:   0.030196
[2025-10-02 15:01:42] [Iter  143/2250] R1[134/300]  | LR: 0.019585 | E: -25.170506 | E_var:     3.4349 | E_err:   0.028959
[2025-10-02 15:01:44] [Iter  144/2250] R1[136/300]  | LR: 0.019326 | E: -25.235999 | E_var:     3.5758 | E_err:   0.029547
[2025-10-02 15:01:47] [Iter  145/2250] R1[138/300]  | LR: 0.019067 | E: -25.341375 | E_var:     3.6427 | E_err:   0.029822
[2025-10-02 15:01:49] [Iter  146/2250] R1[140/300]  | LR: 0.018807 | E: -25.444004 | E_var:     3.3445 | E_err:   0.028575
[2025-10-02 15:01:52] [Iter  147/2250] R1[142/300]  | LR: 0.018546 | E: -25.545632 | E_var:     3.3880 | E_err:   0.028760
[2025-10-02 15:01:54] [Iter  148/2250] R1[144/300]  | LR: 0.018285 | E: -25.644568 | E_var:     3.2247 | E_err:   0.028059
[2025-10-02 15:01:56] [Iter  149/2250] R1[146/300]  | LR: 0.018023 | E: -25.746536 | E_var:     3.4380 | E_err:   0.028972
[2025-10-02 15:01:59] [Iter  150/2250] R1[148/300]  | LR: 0.017762 | E: -25.814453 | E_var:     3.2670 | E_err:   0.028242
[2025-10-02 15:02:01] [Iter  151/2250] R1[150/300]  | LR: 0.017500 | E: -25.909818 | E_var:     2.8973 | E_err:   0.026596
[2025-10-02 15:02:04] [Iter  152/2250] R1[152/300]  | LR: 0.017238 | E: -25.971351 | E_var:     2.6599 | E_err:   0.025483
[2025-10-02 15:02:06] [Iter  153/2250] R1[154/300]  | LR: 0.016977 | E: -26.053287 | E_var:     2.7731 | E_err:   0.026020
[2025-10-02 15:02:08] [Iter  154/2250] R1[156/300]  | LR: 0.016715 | E: -26.132366 | E_var:     2.5732 | E_err:   0.025064
[2025-10-02 15:02:11] [Iter  155/2250] R1[158/300]  | LR: 0.016454 | E: -26.186655 | E_var:     2.3651 | E_err:   0.024030
[2025-10-02 15:02:13] [Iter  156/2250] R1[160/300]  | LR: 0.016193 | E: -26.259484 | E_var:     2.3938 | E_err:   0.024175
[2025-10-02 15:02:16] [Iter  157/2250] R1[162/300]  | LR: 0.015933 | E: -26.347845 | E_var:     2.1877 | E_err:   0.023111
[2025-10-02 15:02:18] [Iter  158/2250] R1[164/300]  | LR: 0.015674 | E: -26.322450 | E_var:     2.1831 | E_err:   0.023086
[2025-10-02 15:02:21] [Iter  159/2250] R1[166/300]  | LR: 0.015415 | E: -26.431022 | E_var:     2.5817 | E_err:   0.025106
[2025-10-02 15:02:23] [Iter  160/2250] R1[168/300]  | LR: 0.015158 | E: -26.435633 | E_var:     1.9736 | E_err:   0.021951
[2025-10-02 15:02:25] [Iter  161/2250] R1[170/300]  | LR: 0.014901 | E: -26.508588 | E_var:     1.8455 | E_err:   0.021227
[2025-10-02 15:02:28] [Iter  162/2250] R1[172/300]  | LR: 0.014646 | E: -26.497196 | E_var:     1.9831 | E_err:   0.022004
[2025-10-02 15:02:30] [Iter  163/2250] R1[174/300]  | LR: 0.014391 | E: -26.541791 | E_var:     1.9809 | E_err:   0.021991
[2025-10-02 15:02:33] [Iter  164/2250] R1[176/300]  | LR: 0.014139 | E: -26.564796 | E_var:     1.7864 | E_err:   0.020884
[2025-10-02 15:02:35] [Iter  165/2250] R1[178/300]  | LR: 0.013887 | E: -26.542646 | E_var:     1.7072 | E_err:   0.020416
[2025-10-02 15:02:37] [Iter  166/2250] R1[180/300]  | LR: 0.013637 | E: -26.584627 | E_var:     1.6331 | E_err:   0.019968
[2025-10-02 15:02:40] [Iter  167/2250] R1[182/300]  | LR: 0.013389 | E: -26.595758 | E_var:     1.6860 | E_err:   0.020288
[2025-10-02 15:02:42] [Iter  168/2250] R1[184/300]  | LR: 0.013143 | E: -26.638907 | E_var:     1.4869 | E_err:   0.019053
[2025-10-02 15:02:45] [Iter  169/2250] R1[186/300]  | LR: 0.012898 | E: -26.658194 | E_var:     1.5406 | E_err:   0.019394
[2025-10-02 15:02:47] [Iter  170/2250] R1[188/300]  | LR: 0.012656 | E: -26.669905 | E_var:     1.6492 | E_err:   0.020066
[2025-10-02 15:02:50] [Iter  171/2250] R1[190/300]  | LR: 0.012416 | E: -26.698154 | E_var:     1.8139 | E_err:   0.021044
[2025-10-02 15:02:52] [Iter  172/2250] R1[192/300]  | LR: 0.012178 | E: -26.686989 | E_var:     1.7343 | E_err:   0.020577
[2025-10-02 15:02:54] [Iter  173/2250] R1[194/300]  | LR: 0.011942 | E: -26.685703 | E_var:     1.2693 | E_err:   0.017603
[2025-10-02 15:02:57] [Iter  174/2250] R1[196/300]  | LR: 0.011709 | E: -26.673226 | E_var:     1.9247 | E_err:   0.021677
[2025-10-02 15:02:59] [Iter  175/2250] R1[198/300]  | LR: 0.011478 | E: -26.691594 | E_var:     1.3347 | E_err:   0.018051
[2025-10-02 15:03:02] [Iter  176/2250] R1[200/300]  | LR: 0.011250 | E: -26.734296 | E_var:     1.4918 | E_err:   0.019084
[2025-10-02 15:03:04] [Iter  177/2250] R1[202/300]  | LR: 0.011025 | E: -26.714491 | E_var:     1.2914 | E_err:   0.017756
[2025-10-02 15:03:06] [Iter  178/2250] R1[204/300]  | LR: 0.010802 | E: -26.762748 | E_var:     1.2903 | E_err:   0.017749
[2025-10-02 15:03:09] [Iter  179/2250] R1[206/300]  | LR: 0.010583 | E: -26.767972 | E_var:     1.3255 | E_err:   0.017989
[2025-10-02 15:03:11] [Iter  180/2250] R1[208/300]  | LR: 0.010366 | E: -26.733091 | E_var:     1.2658 | E_err:   0.017579
[2025-10-02 15:03:14] [Iter  181/2250] R1[210/300]  | LR: 0.010153 | E: -26.742012 | E_var:     1.3346 | E_err:   0.018051
[2025-10-02 15:03:16] [Iter  182/2250] R1[212/300]  | LR: 0.009943 | E: -26.756947 | E_var:     1.3423 | E_err:   0.018103
[2025-10-02 15:03:19] [Iter  183/2250] R1[214/300]  | LR: 0.009736 | E: -26.766512 | E_var:     1.4861 | E_err:   0.019048
[2025-10-02 15:03:21] [Iter  184/2250] R1[216/300]  | LR: 0.009532 | E: -26.782576 | E_var:     1.1847 | E_err:   0.017007
[2025-10-02 15:03:23] [Iter  185/2250] R1[218/300]  | LR: 0.009332 | E: -26.815601 | E_var:     1.2650 | E_err:   0.017574
[2025-10-02 15:03:26] [Iter  186/2250] R1[220/300]  | LR: 0.009136 | E: -26.821309 | E_var:     1.3356 | E_err:   0.018058
[2025-10-02 15:03:28] [Iter  187/2250] R1[222/300]  | LR: 0.008943 | E: -26.797923 | E_var:     1.0867 | E_err:   0.016288
[2025-10-02 15:03:31] [Iter  188/2250] R1[224/300]  | LR: 0.008754 | E: -26.821605 | E_var:     1.2041 | E_err:   0.017146
[2025-10-02 15:03:33] [Iter  189/2250] R1[226/300]  | LR: 0.008569 | E: -26.807944 | E_var:     0.9620 | E_err:   0.015325
[2025-10-02 15:03:35] [Iter  190/2250] R1[228/300]  | LR: 0.008388 | E: -26.825914 | E_var:     1.2093 | E_err:   0.017182
[2025-10-02 15:03:38] [Iter  191/2250] R1[230/300]  | LR: 0.008211 | E: -26.829419 | E_var:     1.1367 | E_err:   0.016659
[2025-10-02 15:03:40] [Iter  192/2250] R1[232/300]  | LR: 0.008038 | E: -26.826794 | E_var:     1.1424 | E_err:   0.016700
[2025-10-02 15:03:43] [Iter  193/2250] R1[234/300]  | LR: 0.007869 | E: -26.858049 | E_var:     1.1908 | E_err:   0.017051
[2025-10-02 15:03:45] [Iter  194/2250] R1[236/300]  | LR: 0.007704 | E: -26.835907 | E_var:     1.5820 | E_err:   0.019653
[2025-10-02 15:03:48] [Iter  195/2250] R1[238/300]  | LR: 0.007543 | E: -26.832138 | E_var:     1.0110 | E_err:   0.015711
[2025-10-02 15:03:50] [Iter  196/2250] R1[240/300]  | LR: 0.007387 | E: -26.852835 | E_var:     1.3402 | E_err:   0.018089
[2025-10-02 15:03:52] [Iter  197/2250] R1[242/300]  | LR: 0.007236 | E: -26.857861 | E_var:     0.9796 | E_err:   0.015465
[2025-10-02 15:03:55] [Iter  198/2250] R1[244/300]  | LR: 0.007088 | E: -26.848026 | E_var:     1.0156 | E_err:   0.015746
[2025-10-02 15:03:57] [Iter  199/2250] R1[246/300]  | LR: 0.006946 | E: -26.855534 | E_var:     0.9427 | E_err:   0.015171
[2025-10-02 15:04:00] [Iter  200/2250] R1[248/300]  | LR: 0.006808 | E: -26.858896 | E_var:     1.0085 | E_err:   0.015691
[2025-10-02 15:04:00] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-02 15:04:02] [Iter  201/2250] R1[250/300]  | LR: 0.006675 | E: -26.868758 | E_var:     0.9959 | E_err:   0.015593
[2025-10-02 15:04:04] [Iter  202/2250] R1[252/300]  | LR: 0.006546 | E: -26.854723 | E_var:     1.0322 | E_err:   0.015874
[2025-10-02 15:04:07] [Iter  203/2250] R1[254/300]  | LR: 0.006422 | E: -26.887972 | E_var:     0.9263 | E_err:   0.015038
[2025-10-02 15:04:09] [Iter  204/2250] R1[256/300]  | LR: 0.006304 | E: -26.851808 | E_var:     1.4780 | E_err:   0.018996
[2025-10-02 15:04:12] [Iter  205/2250] R1[258/300]  | LR: 0.006190 | E: -26.865731 | E_var:     1.1412 | E_err:   0.016692
[2025-10-02 15:04:14] [Iter  206/2250] R1[260/300]  | LR: 0.006081 | E: -26.876271 | E_var:     1.2173 | E_err:   0.017239
[2025-10-02 15:04:16] [Iter  207/2250] R1[262/300]  | LR: 0.005977 | E: -26.889628 | E_var:     1.1193 | E_err:   0.016531
[2025-10-02 15:04:19] [Iter  208/2250] R1[264/300]  | LR: 0.005878 | E: -26.901092 | E_var:     0.9964 | E_err:   0.015597
[2025-10-02 15:04:21] [Iter  209/2250] R1[266/300]  | LR: 0.005784 | E: -26.880191 | E_var:     1.0546 | E_err:   0.016046
[2025-10-02 15:04:24] [Iter  210/2250] R1[268/300]  | LR: 0.005695 | E: -26.865166 | E_var:     1.0867 | E_err:   0.016289
[2025-10-02 15:04:26] [Iter  211/2250] R1[270/300]  | LR: 0.005612 | E: -26.903150 | E_var:     0.7844 | E_err:   0.013839
[2025-10-02 15:04:29] [Iter  212/2250] R1[272/300]  | LR: 0.005534 | E: -26.899150 | E_var:     0.8957 | E_err:   0.014787
[2025-10-02 15:04:31] [Iter  213/2250] R1[274/300]  | LR: 0.005460 | E: -26.938060 | E_var:     0.9218 | E_err:   0.015002
[2025-10-02 15:04:33] [Iter  214/2250] R1[276/300]  | LR: 0.005393 | E: -26.901664 | E_var:     0.9035 | E_err:   0.014852
[2025-10-02 15:04:36] [Iter  215/2250] R1[278/300]  | LR: 0.005330 | E: -26.911781 | E_var:     0.9172 | E_err:   0.014964
[2025-10-02 15:04:38] [Iter  216/2250] R1[280/300]  | LR: 0.005273 | E: -26.868715 | E_var:     1.8114 | E_err:   0.021030
[2025-10-02 15:04:41] [Iter  217/2250] R1[282/300]  | LR: 0.005221 | E: -26.910371 | E_var:     1.3124 | E_err:   0.017900
[2025-10-02 15:04:43] [Iter  218/2250] R1[284/300]  | LR: 0.005175 | E: -26.908419 | E_var:     0.8062 | E_err:   0.014029
[2025-10-02 15:04:45] [Iter  219/2250] R1[286/300]  | LR: 0.005134 | E: -26.940118 | E_var:     0.8225 | E_err:   0.014171
[2025-10-02 15:04:48] [Iter  220/2250] R1[288/300]  | LR: 0.005099 | E: -26.924690 | E_var:     0.7584 | E_err:   0.013608
[2025-10-02 15:04:50] [Iter  221/2250] R1[290/300]  | LR: 0.005068 | E: -26.904201 | E_var:     0.9273 | E_err:   0.015047
[2025-10-02 15:04:53] [Iter  222/2250] R1[292/300]  | LR: 0.005044 | E: -26.921373 | E_var:     0.8802 | E_err:   0.014660
[2025-10-02 15:04:55] [Iter  223/2250] R1[294/300]  | LR: 0.005025 | E: -26.926062 | E_var:     0.8647 | E_err:   0.014530
[2025-10-02 15:04:57] [Iter  224/2250] R1[296/300]  | LR: 0.005011 | E: -26.941670 | E_var:     0.7432 | E_err:   0.013470
[2025-10-02 15:05:00] [Iter  225/2250] R1[298/300]  | LR: 0.005003 | E: -26.924524 | E_var:     1.0265 | E_err:   0.015830
[2025-10-02 15:05:00] 🔄 RESTART #2 | Period: 600
[2025-10-02 15:05:02] [Iter  226/2250] R2[0/600]    | LR: 0.030000 | E: -26.922575 | E_var:     1.7715 | E_err:   0.020797
[2025-10-02 15:05:05] [Iter  227/2250] R2[2/600]    | LR: 0.029999 | E: -26.948194 | E_var:     0.7515 | E_err:   0.013545
[2025-10-02 15:05:07] [Iter  228/2250] R2[4/600]    | LR: 0.029997 | E: -26.918324 | E_var:     0.8470 | E_err:   0.014381
[2025-10-02 15:05:10] [Iter  229/2250] R2[6/600]    | LR: 0.029994 | E: -26.921825 | E_var:     0.9069 | E_err:   0.014880
[2025-10-02 15:05:12] [Iter  230/2250] R2[8/600]    | LR: 0.029989 | E: -26.951600 | E_var:     0.8019 | E_err:   0.013992
[2025-10-02 15:05:14] [Iter  231/2250] R2[10/600]   | LR: 0.029983 | E: -26.942723 | E_var:     0.9176 | E_err:   0.014968
[2025-10-02 15:05:17] [Iter  232/2250] R2[12/600]   | LR: 0.029975 | E: -26.958347 | E_var:     0.8471 | E_err:   0.014381
[2025-10-02 15:05:19] [Iter  233/2250] R2[14/600]   | LR: 0.029966 | E: -26.950710 | E_var:     0.9199 | E_err:   0.014986
[2025-10-02 15:05:22] [Iter  234/2250] R2[16/600]   | LR: 0.029956 | E: -26.937313 | E_var:     0.9277 | E_err:   0.015050
[2025-10-02 15:05:24] [Iter  235/2250] R2[18/600]   | LR: 0.029945 | E: -26.963832 | E_var:     0.9224 | E_err:   0.015006
[2025-10-02 15:05:26] [Iter  236/2250] R2[20/600]   | LR: 0.029932 | E: -26.969743 | E_var:     1.1439 | E_err:   0.016711
[2025-10-02 15:05:29] [Iter  237/2250] R2[22/600]   | LR: 0.029917 | E: -26.951414 | E_var:     0.9645 | E_err:   0.015345
[2025-10-02 15:05:31] [Iter  238/2250] R2[24/600]   | LR: 0.029901 | E: -26.947087 | E_var:     0.8175 | E_err:   0.014127
[2025-10-02 15:05:34] [Iter  239/2250] R2[26/600]   | LR: 0.029884 | E: -26.966613 | E_var:     0.8630 | E_err:   0.014515
[2025-10-02 15:05:36] [Iter  240/2250] R2[28/600]   | LR: 0.029866 | E: -26.934005 | E_var:     1.3408 | E_err:   0.018093
[2025-10-02 15:05:38] [Iter  241/2250] R2[30/600]   | LR: 0.029846 | E: -26.947648 | E_var:     1.3537 | E_err:   0.018180
[2025-10-02 15:05:41] [Iter  242/2250] R2[32/600]   | LR: 0.029825 | E: -26.957678 | E_var:     0.7548 | E_err:   0.013574
[2025-10-02 15:05:43] [Iter  243/2250] R2[34/600]   | LR: 0.029802 | E: -26.976097 | E_var:     0.6639 | E_err:   0.012731
[2025-10-02 15:05:46] [Iter  244/2250] R2[36/600]   | LR: 0.029779 | E: -26.980661 | E_var:     0.7101 | E_err:   0.013166
[2025-10-02 15:05:48] [Iter  245/2250] R2[38/600]   | LR: 0.029753 | E: -26.958045 | E_var:     1.0137 | E_err:   0.015732
[2025-10-02 15:05:51] [Iter  246/2250] R2[40/600]   | LR: 0.029727 | E: -26.980515 | E_var:     0.7919 | E_err:   0.013905
[2025-10-02 15:05:53] [Iter  247/2250] R2[42/600]   | LR: 0.029699 | E: -26.955794 | E_var:     0.6986 | E_err:   0.013059
[2025-10-02 15:05:55] [Iter  248/2250] R2[44/600]   | LR: 0.029670 | E: -26.950258 | E_var:     1.2802 | E_err:   0.017679
[2025-10-02 15:05:58] [Iter  249/2250] R2[46/600]   | LR: 0.029639 | E: -26.977015 | E_var:     0.6962 | E_err:   0.013038
[2025-10-02 15:06:00] [Iter  250/2250] R2[48/600]   | LR: 0.029607 | E: -26.965125 | E_var:     0.9207 | E_err:   0.014993
[2025-10-02 15:06:03] [Iter  251/2250] R2[50/600]   | LR: 0.029574 | E: -26.958380 | E_var:     0.7877 | E_err:   0.013868
[2025-10-02 15:06:05] [Iter  252/2250] R2[52/600]   | LR: 0.029540 | E: -26.981434 | E_var:     0.7042 | E_err:   0.013112
[2025-10-02 15:06:08] [Iter  253/2250] R2[54/600]   | LR: 0.029504 | E: -26.984670 | E_var:     1.5440 | E_err:   0.019415
[2025-10-02 15:06:10] [Iter  254/2250] R2[56/600]   | LR: 0.029466 | E: -26.987513 | E_var:     0.6866 | E_err:   0.012947
[2025-10-02 15:06:12] [Iter  255/2250] R2[58/600]   | LR: 0.029428 | E: -26.995794 | E_var:     0.6533 | E_err:   0.012629
[2025-10-02 15:06:15] [Iter  256/2250] R2[60/600]   | LR: 0.029388 | E: -26.992594 | E_var:     0.6852 | E_err:   0.012934
[2025-10-02 15:06:17] [Iter  257/2250] R2[62/600]   | LR: 0.029347 | E: -26.976966 | E_var:     0.7700 | E_err:   0.013711
[2025-10-02 15:06:20] [Iter  258/2250] R2[64/600]   | LR: 0.029305 | E: -27.009792 | E_var:     0.9555 | E_err:   0.015273
[2025-10-02 15:06:22] [Iter  259/2250] R2[66/600]   | LR: 0.029261 | E: -26.987284 | E_var:     0.9861 | E_err:   0.015516
[2025-10-02 15:06:24] [Iter  260/2250] R2[68/600]   | LR: 0.029216 | E: -26.992205 | E_var:     0.7327 | E_err:   0.013375
[2025-10-02 15:06:27] [Iter  261/2250] R2[70/600]   | LR: 0.029170 | E: -26.996871 | E_var:     0.6156 | E_err:   0.012259
[2025-10-02 15:06:29] [Iter  262/2250] R2[72/600]   | LR: 0.029122 | E: -26.979736 | E_var:     0.7502 | E_err:   0.013534
[2025-10-02 15:06:32] [Iter  263/2250] R2[74/600]   | LR: 0.029073 | E: -26.993536 | E_var:     0.6648 | E_err:   0.012740
[2025-10-02 15:06:34] [Iter  264/2250] R2[76/600]   | LR: 0.029023 | E: -27.010200 | E_var:     0.7371 | E_err:   0.013414
[2025-10-02 15:06:36] [Iter  265/2250] R2[78/600]   | LR: 0.028972 | E: -26.987095 | E_var:     0.5353 | E_err:   0.011432
[2025-10-02 15:06:39] [Iter  266/2250] R2[80/600]   | LR: 0.028919 | E: -27.001716 | E_var:     0.7128 | E_err:   0.013192
[2025-10-02 15:06:41] [Iter  267/2250] R2[82/600]   | LR: 0.028865 | E: -27.009254 | E_var:     0.7893 | E_err:   0.013881
[2025-10-02 15:06:44] [Iter  268/2250] R2[84/600]   | LR: 0.028810 | E: -27.005181 | E_var:     0.7149 | E_err:   0.013212
[2025-10-02 15:06:46] [Iter  269/2250] R2[86/600]   | LR: 0.028754 | E: -26.988262 | E_var:     0.9666 | E_err:   0.015362
[2025-10-02 15:06:49] [Iter  270/2250] R2[88/600]   | LR: 0.028696 | E: -26.986579 | E_var:     0.6189 | E_err:   0.012293
[2025-10-02 15:06:51] [Iter  271/2250] R2[90/600]   | LR: 0.028638 | E: -27.016845 | E_var:     0.7000 | E_err:   0.013073
[2025-10-02 15:06:53] [Iter  272/2250] R2[92/600]   | LR: 0.028578 | E: -27.007520 | E_var:     0.7863 | E_err:   0.013855
[2025-10-02 15:06:56] [Iter  273/2250] R2[94/600]   | LR: 0.028516 | E: -27.011614 | E_var:     0.6724 | E_err:   0.012813
[2025-10-02 15:06:58] [Iter  274/2250] R2[96/600]   | LR: 0.028454 | E: -26.999247 | E_var:     0.5432 | E_err:   0.011516
[2025-10-02 15:07:01] [Iter  275/2250] R2[98/600]   | LR: 0.028390 | E: -27.012720 | E_var:     0.7334 | E_err:   0.013381
[2025-10-02 15:07:03] [Iter  276/2250] R2[100/600]  | LR: 0.028325 | E: -26.998440 | E_var:     1.0338 | E_err:   0.015887
[2025-10-02 15:07:06] [Iter  277/2250] R2[102/600]  | LR: 0.028259 | E: -27.009904 | E_var:     0.6958 | E_err:   0.013033
[2025-10-02 15:07:08] [Iter  278/2250] R2[104/600]  | LR: 0.028192 | E: -27.009837 | E_var:     0.7157 | E_err:   0.013218
[2025-10-02 15:07:10] [Iter  279/2250] R2[106/600]  | LR: 0.028124 | E: -27.016727 | E_var:     0.6470 | E_err:   0.012568
[2025-10-02 15:07:13] [Iter  280/2250] R2[108/600]  | LR: 0.028054 | E: -27.028983 | E_var:     0.6079 | E_err:   0.012182
[2025-10-02 15:07:15] [Iter  281/2250] R2[110/600]  | LR: 0.027983 | E: -27.023449 | E_var:     1.0884 | E_err:   0.016301
[2025-10-02 15:07:18] [Iter  282/2250] R2[112/600]  | LR: 0.027912 | E: -27.009467 | E_var:     0.6919 | E_err:   0.012997
[2025-10-02 15:07:20] [Iter  283/2250] R2[114/600]  | LR: 0.027839 | E: -27.016905 | E_var:     0.5728 | E_err:   0.011825
[2025-10-02 15:07:22] [Iter  284/2250] R2[116/600]  | LR: 0.027764 | E: -27.034244 | E_var:     0.5652 | E_err:   0.011747
[2025-10-02 15:07:25] [Iter  285/2250] R2[118/600]  | LR: 0.027689 | E: -26.999593 | E_var:     0.5698 | E_err:   0.011795
[2025-10-02 15:07:27] [Iter  286/2250] R2[120/600]  | LR: 0.027613 | E: -27.031007 | E_var:     0.7450 | E_err:   0.013486
[2025-10-02 15:07:30] [Iter  287/2250] R2[122/600]  | LR: 0.027535 | E: -27.010265 | E_var:     0.6548 | E_err:   0.012643
[2025-10-02 15:07:32] [Iter  288/2250] R2[124/600]  | LR: 0.027457 | E: -27.001992 | E_var:     0.5387 | E_err:   0.011468
[2025-10-02 15:07:35] [Iter  289/2250] R2[126/600]  | LR: 0.027377 | E: -27.016611 | E_var:     0.5762 | E_err:   0.011861
[2025-10-02 15:07:37] [Iter  290/2250] R2[128/600]  | LR: 0.027296 | E: -27.027601 | E_var:     0.5801 | E_err:   0.011901
[2025-10-02 15:07:39] [Iter  291/2250] R2[130/600]  | LR: 0.027214 | E: -27.009567 | E_var:     0.5387 | E_err:   0.011468
[2025-10-02 15:07:42] [Iter  292/2250] R2[132/600]  | LR: 0.027131 | E: -27.001375 | E_var:     0.5504 | E_err:   0.011592
[2025-10-02 15:07:44] [Iter  293/2250] R2[134/600]  | LR: 0.027047 | E: -27.017203 | E_var:     1.0742 | E_err:   0.016194
[2025-10-02 15:07:47] [Iter  294/2250] R2[136/600]  | LR: 0.026962 | E: -27.022138 | E_var:     0.5615 | E_err:   0.011708
[2025-10-02 15:07:49] [Iter  295/2250] R2[138/600]  | LR: 0.026876 | E: -27.013557 | E_var:     0.6631 | E_err:   0.012724
[2025-10-02 15:07:51] [Iter  296/2250] R2[140/600]  | LR: 0.026789 | E: -27.044134 | E_var:     0.5499 | E_err:   0.011586
[2025-10-02 15:07:54] [Iter  297/2250] R2[142/600]  | LR: 0.026701 | E: -27.020300 | E_var:     0.5038 | E_err:   0.011091
[2025-10-02 15:07:56] [Iter  298/2250] R2[144/600]  | LR: 0.026612 | E: -27.026970 | E_var:     0.6012 | E_err:   0.012115
[2025-10-02 15:07:59] [Iter  299/2250] R2[146/600]  | LR: 0.026522 | E: -27.028378 | E_var:     0.8150 | E_err:   0.014106
[2025-10-02 15:08:01] [Iter  300/2250] R2[148/600]  | LR: 0.026431 | E: -27.035893 | E_var:     0.6014 | E_err:   0.012118
[2025-10-02 15:08:03] [Iter  301/2250] R2[150/600]  | LR: 0.026339 | E: -27.022920 | E_var:     0.5220 | E_err:   0.011289
[2025-10-02 15:08:06] [Iter  302/2250] R2[152/600]  | LR: 0.026246 | E: -27.038803 | E_var:     0.4937 | E_err:   0.010978
[2025-10-02 15:08:08] [Iter  303/2250] R2[154/600]  | LR: 0.026152 | E: -27.022157 | E_var:     0.5510 | E_err:   0.011598
[2025-10-02 15:08:11] [Iter  304/2250] R2[156/600]  | LR: 0.026057 | E: -27.026452 | E_var:     0.5580 | E_err:   0.011672
[2025-10-02 15:08:13] [Iter  305/2250] R2[158/600]  | LR: 0.025961 | E: -27.029605 | E_var:     0.4969 | E_err:   0.011014
[2025-10-02 15:08:16] [Iter  306/2250] R2[160/600]  | LR: 0.025864 | E: -27.044384 | E_var:     0.5530 | E_err:   0.011620
[2025-10-02 15:08:18] [Iter  307/2250] R2[162/600]  | LR: 0.025766 | E: -27.026342 | E_var:     0.5179 | E_err:   0.011245
[2025-10-02 15:08:20] [Iter  308/2250] R2[164/600]  | LR: 0.025668 | E: -27.044836 | E_var:     0.5001 | E_err:   0.011050
[2025-10-02 15:08:23] [Iter  309/2250] R2[166/600]  | LR: 0.025568 | E: -27.050396 | E_var:     0.5410 | E_err:   0.011493
[2025-10-02 15:08:25] [Iter  310/2250] R2[168/600]  | LR: 0.025468 | E: -27.029729 | E_var:     0.4632 | E_err:   0.010634
[2025-10-02 15:08:28] [Iter  311/2250] R2[170/600]  | LR: 0.025367 | E: -27.029530 | E_var:     0.6979 | E_err:   0.013053
[2025-10-02 15:08:30] [Iter  312/2250] R2[172/600]  | LR: 0.025264 | E: -27.024605 | E_var:     0.6571 | E_err:   0.012666
[2025-10-02 15:08:32] [Iter  313/2250] R2[174/600]  | LR: 0.025161 | E: -27.032120 | E_var:     0.5813 | E_err:   0.011913
[2025-10-02 15:08:35] [Iter  314/2250] R2[176/600]  | LR: 0.025057 | E: -27.007667 | E_var:     0.5450 | E_err:   0.011535
[2025-10-02 15:08:37] [Iter  315/2250] R2[178/600]  | LR: 0.024953 | E: -27.029745 | E_var:     0.4791 | E_err:   0.010815
[2025-10-02 15:08:40] [Iter  316/2250] R2[180/600]  | LR: 0.024847 | E: -27.044472 | E_var:     0.5572 | E_err:   0.011663
[2025-10-02 15:08:42] [Iter  317/2250] R2[182/600]  | LR: 0.024741 | E: -27.030178 | E_var:     0.5710 | E_err:   0.011807
[2025-10-02 15:08:45] [Iter  318/2250] R2[184/600]  | LR: 0.024634 | E: -27.027044 | E_var:     0.6454 | E_err:   0.012553
[2025-10-02 15:08:47] [Iter  319/2250] R2[186/600]  | LR: 0.024526 | E: -27.041339 | E_var:     0.8597 | E_err:   0.014488
[2025-10-02 15:08:49] [Iter  320/2250] R2[188/600]  | LR: 0.024417 | E: -27.035410 | E_var:     0.5503 | E_err:   0.011590
[2025-10-02 15:08:52] [Iter  321/2250] R2[190/600]  | LR: 0.024308 | E: -27.043031 | E_var:     0.6981 | E_err:   0.013055
[2025-10-02 15:08:54] [Iter  322/2250] R2[192/600]  | LR: 0.024198 | E: -27.028322 | E_var:     0.4942 | E_err:   0.010984
[2025-10-02 15:08:57] [Iter  323/2250] R2[194/600]  | LR: 0.024087 | E: -27.023234 | E_var:     0.4893 | E_err:   0.010930
[2025-10-02 15:08:59] [Iter  324/2250] R2[196/600]  | LR: 0.023975 | E: -27.033531 | E_var:     0.4174 | E_err:   0.010094
[2025-10-02 15:09:01] [Iter  325/2250] R2[198/600]  | LR: 0.023863 | E: -27.040667 | E_var:     0.4760 | E_err:   0.010780
[2025-10-02 15:09:04] [Iter  326/2250] R2[200/600]  | LR: 0.023750 | E: -27.031880 | E_var:     0.5387 | E_err:   0.011468
[2025-10-02 15:09:06] [Iter  327/2250] R2[202/600]  | LR: 0.023636 | E: -27.041974 | E_var:     0.4541 | E_err:   0.010529
[2025-10-02 15:09:09] [Iter  328/2250] R2[204/600]  | LR: 0.023522 | E: -27.036016 | E_var:     0.4280 | E_err:   0.010222
[2025-10-02 15:09:11] [Iter  329/2250] R2[206/600]  | LR: 0.023407 | E: -27.053299 | E_var:     0.4142 | E_err:   0.010056
[2025-10-02 15:09:13] [Iter  330/2250] R2[208/600]  | LR: 0.023291 | E: -27.059132 | E_var:     0.4734 | E_err:   0.010750
[2025-10-02 15:09:16] [Iter  331/2250] R2[210/600]  | LR: 0.023175 | E: -27.055717 | E_var:     0.4831 | E_err:   0.010861
[2025-10-02 15:09:18] [Iter  332/2250] R2[212/600]  | LR: 0.023058 | E: -27.041813 | E_var:     0.4384 | E_err:   0.010346
[2025-10-02 15:09:21] [Iter  333/2250] R2[214/600]  | LR: 0.022940 | E: -27.050765 | E_var:     0.4409 | E_err:   0.010376
[2025-10-02 15:09:23] [Iter  334/2250] R2[216/600]  | LR: 0.022822 | E: -27.038345 | E_var:     0.8233 | E_err:   0.014178
[2025-10-02 15:09:26] [Iter  335/2250] R2[218/600]  | LR: 0.022704 | E: -27.031683 | E_var:     0.4994 | E_err:   0.011042
[2025-10-02 15:09:28] [Iter  336/2250] R2[220/600]  | LR: 0.022584 | E: -27.031791 | E_var:     0.6504 | E_err:   0.012601
[2025-10-02 15:09:30] [Iter  337/2250] R2[222/600]  | LR: 0.022464 | E: -27.048525 | E_var:     0.5295 | E_err:   0.011370
[2025-10-02 15:09:33] [Iter  338/2250] R2[224/600]  | LR: 0.022344 | E: -27.061879 | E_var:     1.1649 | E_err:   0.016864
[2025-10-02 15:09:35] [Iter  339/2250] R2[226/600]  | LR: 0.022223 | E: -27.033680 | E_var:     0.6247 | E_err:   0.012350
[2025-10-02 15:09:38] [Iter  340/2250] R2[228/600]  | LR: 0.022102 | E: -27.039875 | E_var:     0.4334 | E_err:   0.010287
[2025-10-02 15:09:40] [Iter  341/2250] R2[230/600]  | LR: 0.021980 | E: -27.061144 | E_var:     0.4298 | E_err:   0.010244
[2025-10-02 15:09:42] [Iter  342/2250] R2[232/600]  | LR: 0.021857 | E: -27.039126 | E_var:     0.5107 | E_err:   0.011166
[2025-10-02 15:09:45] [Iter  343/2250] R2[234/600]  | LR: 0.021734 | E: -27.041994 | E_var:     0.4993 | E_err:   0.011041
[2025-10-02 15:09:47] [Iter  344/2250] R2[236/600]  | LR: 0.021611 | E: -27.031543 | E_var:     0.5070 | E_err:   0.011125
[2025-10-02 15:09:50] [Iter  345/2250] R2[238/600]  | LR: 0.021487 | E: -27.066930 | E_var:     0.3675 | E_err:   0.009472
[2025-10-02 15:09:52] [Iter  346/2250] R2[240/600]  | LR: 0.021363 | E: -27.046999 | E_var:     0.5875 | E_err:   0.011976
[2025-10-02 15:09:55] [Iter  347/2250] R2[242/600]  | LR: 0.021238 | E: -27.061376 | E_var:     0.5515 | E_err:   0.011604
[2025-10-02 15:09:57] [Iter  348/2250] R2[244/600]  | LR: 0.021113 | E: -27.057340 | E_var:     0.4726 | E_err:   0.010742
[2025-10-02 15:09:59] [Iter  349/2250] R2[246/600]  | LR: 0.020987 | E: -27.035439 | E_var:     0.5144 | E_err:   0.011206
[2025-10-02 15:10:02] [Iter  350/2250] R2[248/600]  | LR: 0.020861 | E: -27.067187 | E_var:     0.5145 | E_err:   0.011207
[2025-10-02 15:10:04] [Iter  351/2250] R2[250/600]  | LR: 0.020735 | E: -27.052464 | E_var:     0.5002 | E_err:   0.011050
[2025-10-02 15:10:07] [Iter  352/2250] R2[252/600]  | LR: 0.020609 | E: -27.036291 | E_var:     0.6059 | E_err:   0.012162
[2025-10-02 15:10:09] [Iter  353/2250] R2[254/600]  | LR: 0.020482 | E: -27.048574 | E_var:     0.4060 | E_err:   0.009956
[2025-10-02 15:10:11] [Iter  354/2250] R2[256/600]  | LR: 0.020354 | E: -27.065095 | E_var:     0.4056 | E_err:   0.009951
[2025-10-02 15:10:14] [Iter  355/2250] R2[258/600]  | LR: 0.020227 | E: -27.042416 | E_var:     0.5178 | E_err:   0.011244
[2025-10-02 15:10:16] [Iter  356/2250] R2[260/600]  | LR: 0.020099 | E: -27.059560 | E_var:     0.4867 | E_err:   0.010900
[2025-10-02 15:10:19] [Iter  357/2250] R2[262/600]  | LR: 0.019971 | E: -27.048327 | E_var:     0.4857 | E_err:   0.010890
[2025-10-02 15:10:21] [Iter  358/2250] R2[264/600]  | LR: 0.019842 | E: -27.039630 | E_var:     0.4767 | E_err:   0.010788
[2025-10-02 15:10:24] [Iter  359/2250] R2[266/600]  | LR: 0.019714 | E: -27.063919 | E_var:     0.4843 | E_err:   0.010873
[2025-10-02 15:10:26] [Iter  360/2250] R2[268/600]  | LR: 0.019585 | E: -27.060667 | E_var:     0.5228 | E_err:   0.011298
[2025-10-02 15:10:28] [Iter  361/2250] R2[270/600]  | LR: 0.019455 | E: -27.069357 | E_var:     0.5848 | E_err:   0.011949
[2025-10-02 15:10:31] [Iter  362/2250] R2[272/600]  | LR: 0.019326 | E: -27.050905 | E_var:     0.4650 | E_err:   0.010655
[2025-10-02 15:10:33] [Iter  363/2250] R2[274/600]  | LR: 0.019196 | E: -27.050203 | E_var:     0.5477 | E_err:   0.011563
[2025-10-02 15:10:36] [Iter  364/2250] R2[276/600]  | LR: 0.019067 | E: -27.055078 | E_var:     0.4322 | E_err:   0.010272
[2025-10-02 15:10:38] [Iter  365/2250] R2[278/600]  | LR: 0.018937 | E: -27.042437 | E_var:     0.4624 | E_err:   0.010626
[2025-10-02 15:10:40] [Iter  366/2250] R2[280/600]  | LR: 0.018807 | E: -27.054964 | E_var:     0.5339 | E_err:   0.011417
[2025-10-02 15:10:43] [Iter  367/2250] R2[282/600]  | LR: 0.018676 | E: -27.057991 | E_var:     0.5082 | E_err:   0.011139
[2025-10-02 15:10:45] [Iter  368/2250] R2[284/600]  | LR: 0.018546 | E: -27.054835 | E_var:     0.7993 | E_err:   0.013969
[2025-10-02 15:10:48] [Iter  369/2250] R2[286/600]  | LR: 0.018415 | E: -27.061528 | E_var:     0.4248 | E_err:   0.010184
[2025-10-02 15:10:50] [Iter  370/2250] R2[288/600]  | LR: 0.018285 | E: -27.036290 | E_var:     0.4505 | E_err:   0.010488
[2025-10-02 15:10:52] [Iter  371/2250] R2[290/600]  | LR: 0.018154 | E: -27.065104 | E_var:     0.3627 | E_err:   0.009410
[2025-10-02 15:10:55] [Iter  372/2250] R2[292/600]  | LR: 0.018023 | E: -27.059997 | E_var:     0.4843 | E_err:   0.010874
[2025-10-02 15:10:57] [Iter  373/2250] R2[294/600]  | LR: 0.017893 | E: -27.052839 | E_var:     0.4264 | E_err:   0.010203
[2025-10-02 15:11:00] [Iter  374/2250] R2[296/600]  | LR: 0.017762 | E: -27.060968 | E_var:     0.4311 | E_err:   0.010259
[2025-10-02 15:11:02] [Iter  375/2250] R2[298/600]  | LR: 0.017631 | E: -27.051627 | E_var:     0.4027 | E_err:   0.009916
[2025-10-02 15:11:04] [Iter  376/2250] R2[300/600]  | LR: 0.017500 | E: -27.056772 | E_var:     0.4851 | E_err:   0.010883
[2025-10-02 15:11:07] [Iter  377/2250] R2[302/600]  | LR: 0.017369 | E: -27.048274 | E_var:     0.4828 | E_err:   0.010857
[2025-10-02 15:11:09] [Iter  378/2250] R2[304/600]  | LR: 0.017238 | E: -27.066699 | E_var:     0.4426 | E_err:   0.010395
[2025-10-02 15:11:12] [Iter  379/2250] R2[306/600]  | LR: 0.017107 | E: -27.043977 | E_var:     0.5719 | E_err:   0.011816
[2025-10-02 15:11:14] [Iter  380/2250] R2[308/600]  | LR: 0.016977 | E: -27.044192 | E_var:     0.3679 | E_err:   0.009478
[2025-10-02 15:11:16] [Iter  381/2250] R2[310/600]  | LR: 0.016846 | E: -27.048458 | E_var:     0.4648 | E_err:   0.010652
[2025-10-02 15:11:19] [Iter  382/2250] R2[312/600]  | LR: 0.016715 | E: -27.053103 | E_var:     0.5067 | E_err:   0.011122
[2025-10-02 15:11:21] [Iter  383/2250] R2[314/600]  | LR: 0.016585 | E: -27.044645 | E_var:     0.3574 | E_err:   0.009341
[2025-10-02 15:11:24] [Iter  384/2250] R2[316/600]  | LR: 0.016454 | E: -27.063359 | E_var:     0.4350 | E_err:   0.010306
[2025-10-02 15:11:26] [Iter  385/2250] R2[318/600]  | LR: 0.016324 | E: -27.062268 | E_var:     0.4082 | E_err:   0.009983
[2025-10-02 15:11:28] [Iter  386/2250] R2[320/600]  | LR: 0.016193 | E: -27.084860 | E_var:     0.4476 | E_err:   0.010453
[2025-10-02 15:11:31] [Iter  387/2250] R2[322/600]  | LR: 0.016063 | E: -27.058216 | E_var:     0.5224 | E_err:   0.011294
[2025-10-02 15:11:33] [Iter  388/2250] R2[324/600]  | LR: 0.015933 | E: -27.068470 | E_var:     0.4172 | E_err:   0.010092
[2025-10-02 15:11:36] [Iter  389/2250] R2[326/600]  | LR: 0.015804 | E: -27.066415 | E_var:     0.3619 | E_err:   0.009399
[2025-10-02 15:11:38] [Iter  390/2250] R2[328/600]  | LR: 0.015674 | E: -27.061300 | E_var:     0.4489 | E_err:   0.010469
[2025-10-02 15:11:40] [Iter  391/2250] R2[330/600]  | LR: 0.015545 | E: -27.066540 | E_var:     0.3866 | E_err:   0.009716
[2025-10-02 15:11:43] [Iter  392/2250] R2[332/600]  | LR: 0.015415 | E: -27.053484 | E_var:     0.4590 | E_err:   0.010586
[2025-10-02 15:11:45] [Iter  393/2250] R2[334/600]  | LR: 0.015286 | E: -27.058652 | E_var:     0.4325 | E_err:   0.010276
[2025-10-02 15:11:48] [Iter  394/2250] R2[336/600]  | LR: 0.015158 | E: -27.058503 | E_var:     0.4498 | E_err:   0.010479
[2025-10-02 15:11:50] [Iter  395/2250] R2[338/600]  | LR: 0.015029 | E: -27.052829 | E_var:     0.3820 | E_err:   0.009658
[2025-10-02 15:11:52] [Iter  396/2250] R2[340/600]  | LR: 0.014901 | E: -27.043297 | E_var:     0.4303 | E_err:   0.010250
[2025-10-02 15:11:55] [Iter  397/2250] R2[342/600]  | LR: 0.014773 | E: -27.067673 | E_var:     0.3624 | E_err:   0.009406
[2025-10-02 15:11:57] [Iter  398/2250] R2[344/600]  | LR: 0.014646 | E: -27.069470 | E_var:     0.4941 | E_err:   0.010983
[2025-10-02 15:12:00] [Iter  399/2250] R2[346/600]  | LR: 0.014518 | E: -27.078515 | E_var:     0.4735 | E_err:   0.010752
[2025-10-02 15:12:02] [Iter  400/2250] R2[348/600]  | LR: 0.014391 | E: -27.080022 | E_var:     0.4022 | E_err:   0.009909
[2025-10-02 15:12:02] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-02 15:12:04] [Iter  401/2250] R2[350/600]  | LR: 0.014265 | E: -27.061795 | E_var:     0.5438 | E_err:   0.011523
[2025-10-02 15:12:07] [Iter  402/2250] R2[352/600]  | LR: 0.014139 | E: -27.063397 | E_var:     0.4272 | E_err:   0.010212
[2025-10-02 15:12:09] [Iter  403/2250] R2[354/600]  | LR: 0.014013 | E: -27.068048 | E_var:     0.4401 | E_err:   0.010365
[2025-10-02 15:12:12] [Iter  404/2250] R2[356/600]  | LR: 0.013887 | E: -27.061005 | E_var:     0.4889 | E_err:   0.010925
[2025-10-02 15:12:14] [Iter  405/2250] R2[358/600]  | LR: 0.013762 | E: -27.057213 | E_var:     0.4437 | E_err:   0.010408
[2025-10-02 15:12:16] [Iter  406/2250] R2[360/600]  | LR: 0.013637 | E: -27.061880 | E_var:     0.4271 | E_err:   0.010211
[2025-10-02 15:12:19] [Iter  407/2250] R2[362/600]  | LR: 0.013513 | E: -27.059524 | E_var:     0.4326 | E_err:   0.010277
[2025-10-02 15:12:21] [Iter  408/2250] R2[364/600]  | LR: 0.013389 | E: -27.055241 | E_var:     0.4329 | E_err:   0.010281
[2025-10-02 15:12:24] [Iter  409/2250] R2[366/600]  | LR: 0.013266 | E: -27.076308 | E_var:     0.4344 | E_err:   0.010298
[2025-10-02 15:12:26] [Iter  410/2250] R2[368/600]  | LR: 0.013143 | E: -27.061993 | E_var:     0.4624 | E_err:   0.010625
[2025-10-02 15:12:28] [Iter  411/2250] R2[370/600]  | LR: 0.013020 | E: -27.068064 | E_var:     0.6669 | E_err:   0.012760
[2025-10-02 15:12:31] [Iter  412/2250] R2[372/600]  | LR: 0.012898 | E: -27.065155 | E_var:     0.7838 | E_err:   0.013833
[2025-10-02 15:12:33] [Iter  413/2250] R2[374/600]  | LR: 0.012777 | E: -27.075485 | E_var:     0.3763 | E_err:   0.009585
[2025-10-02 15:12:36] [Iter  414/2250] R2[376/600]  | LR: 0.012656 | E: -27.065231 | E_var:     0.3631 | E_err:   0.009416
[2025-10-02 15:12:38] [Iter  415/2250] R2[378/600]  | LR: 0.012536 | E: -27.066814 | E_var:     0.3827 | E_err:   0.009666
[2025-10-02 15:12:40] [Iter  416/2250] R2[380/600]  | LR: 0.012416 | E: -27.069024 | E_var:     0.4039 | E_err:   0.009930
[2025-10-02 15:12:43] [Iter  417/2250] R2[382/600]  | LR: 0.012296 | E: -27.060988 | E_var:     0.4953 | E_err:   0.010997
[2025-10-02 15:12:45] [Iter  418/2250] R2[384/600]  | LR: 0.012178 | E: -27.069626 | E_var:     0.3691 | E_err:   0.009493
[2025-10-02 15:12:48] [Iter  419/2250] R2[386/600]  | LR: 0.012060 | E: -27.069579 | E_var:     0.4324 | E_err:   0.010275
[2025-10-02 15:12:50] [Iter  420/2250] R2[388/600]  | LR: 0.011942 | E: -27.060564 | E_var:     0.3730 | E_err:   0.009542
[2025-10-02 15:12:53] [Iter  421/2250] R2[390/600]  | LR: 0.011825 | E: -27.060256 | E_var:     0.3714 | E_err:   0.009522
[2025-10-02 15:12:55] [Iter  422/2250] R2[392/600]  | LR: 0.011709 | E: -27.065144 | E_var:     0.4233 | E_err:   0.010165
[2025-10-02 15:12:57] [Iter  423/2250] R2[394/600]  | LR: 0.011593 | E: -27.050320 | E_var:     0.3948 | E_err:   0.009818
[2025-10-02 15:13:00] [Iter  424/2250] R2[396/600]  | LR: 0.011478 | E: -27.066807 | E_var:     0.3875 | E_err:   0.009726
[2025-10-02 15:13:02] [Iter  425/2250] R2[398/600]  | LR: 0.011364 | E: -27.065121 | E_var:     0.5216 | E_err:   0.011285
[2025-10-02 15:13:05] [Iter  426/2250] R2[400/600]  | LR: 0.011250 | E: -27.055820 | E_var:     0.4987 | E_err:   0.011034
[2025-10-02 15:13:07] [Iter  427/2250] R2[402/600]  | LR: 0.011137 | E: -27.073171 | E_var:     0.3676 | E_err:   0.009473
[2025-10-02 15:13:09] [Iter  428/2250] R2[404/600]  | LR: 0.011025 | E: -27.049513 | E_var:     0.3574 | E_err:   0.009341
[2025-10-02 15:13:12] [Iter  429/2250] R2[406/600]  | LR: 0.010913 | E: -27.065624 | E_var:     0.3859 | E_err:   0.009706
[2025-10-02 15:13:14] [Iter  430/2250] R2[408/600]  | LR: 0.010802 | E: -27.056897 | E_var:     0.3817 | E_err:   0.009654
[2025-10-02 15:13:17] [Iter  431/2250] R2[410/600]  | LR: 0.010692 | E: -27.066847 | E_var:     0.3444 | E_err:   0.009169
[2025-10-02 15:13:19] [Iter  432/2250] R2[412/600]  | LR: 0.010583 | E: -27.081750 | E_var:     0.5619 | E_err:   0.011713
[2025-10-02 15:13:21] [Iter  433/2250] R2[414/600]  | LR: 0.010474 | E: -27.050157 | E_var:     0.3264 | E_err:   0.008927
[2025-10-02 15:13:24] [Iter  434/2250] R2[416/600]  | LR: 0.010366 | E: -27.064840 | E_var:     0.4372 | E_err:   0.010332
[2025-10-02 15:13:26] [Iter  435/2250] R2[418/600]  | LR: 0.010259 | E: -27.075519 | E_var:     0.4083 | E_err:   0.009984
[2025-10-02 15:13:29] [Iter  436/2250] R2[420/600]  | LR: 0.010153 | E: -27.066413 | E_var:     0.4490 | E_err:   0.010469
[2025-10-02 15:13:31] [Iter  437/2250] R2[422/600]  | LR: 0.010047 | E: -27.064498 | E_var:     0.4242 | E_err:   0.010177
[2025-10-02 15:13:33] [Iter  438/2250] R2[424/600]  | LR: 0.009943 | E: -27.050272 | E_var:     0.4006 | E_err:   0.009890
[2025-10-02 15:13:36] [Iter  439/2250] R2[426/600]  | LR: 0.009839 | E: -27.050659 | E_var:     0.3508 | E_err:   0.009255
[2025-10-02 15:13:38] [Iter  440/2250] R2[428/600]  | LR: 0.009736 | E: -27.063558 | E_var:     0.4015 | E_err:   0.009900
[2025-10-02 15:13:41] [Iter  441/2250] R2[430/600]  | LR: 0.009633 | E: -27.058108 | E_var:     0.3565 | E_err:   0.009329
[2025-10-02 15:13:43] [Iter  442/2250] R2[432/600]  | LR: 0.009532 | E: -27.066628 | E_var:     0.4570 | E_err:   0.010563
[2025-10-02 15:13:45] [Iter  443/2250] R2[434/600]  | LR: 0.009432 | E: -27.056655 | E_var:     0.4348 | E_err:   0.010303
[2025-10-02 15:13:48] [Iter  444/2250] R2[436/600]  | LR: 0.009332 | E: -27.071796 | E_var:     0.3479 | E_err:   0.009217
[2025-10-02 15:13:50] [Iter  445/2250] R2[438/600]  | LR: 0.009234 | E: -27.078385 | E_var:     0.3318 | E_err:   0.009000
[2025-10-02 15:13:53] [Iter  446/2250] R2[440/600]  | LR: 0.009136 | E: -27.085746 | E_var:     0.5342 | E_err:   0.011421
[2025-10-02 15:13:55] [Iter  447/2250] R2[442/600]  | LR: 0.009039 | E: -27.070198 | E_var:     0.3441 | E_err:   0.009165
[2025-10-02 15:13:57] [Iter  448/2250] R2[444/600]  | LR: 0.008943 | E: -27.056631 | E_var:     0.4052 | E_err:   0.009946
[2025-10-02 15:14:00] [Iter  449/2250] R2[446/600]  | LR: 0.008848 | E: -27.065918 | E_var:     0.3550 | E_err:   0.009309
[2025-10-02 15:14:02] [Iter  450/2250] R2[448/600]  | LR: 0.008754 | E: -27.079392 | E_var:     0.3746 | E_err:   0.009563
[2025-10-02 15:14:05] [Iter  451/2250] R2[450/600]  | LR: 0.008661 | E: -27.064558 | E_var:     0.3981 | E_err:   0.009858
[2025-10-02 15:14:07] [Iter  452/2250] R2[452/600]  | LR: 0.008569 | E: -27.069147 | E_var:     0.3933 | E_err:   0.009800
[2025-10-02 15:14:09] [Iter  453/2250] R2[454/600]  | LR: 0.008478 | E: -27.055492 | E_var:     0.4235 | E_err:   0.010168
[2025-10-02 15:14:12] [Iter  454/2250] R2[456/600]  | LR: 0.008388 | E: -27.064095 | E_var:     0.4007 | E_err:   0.009891
[2025-10-02 15:14:14] [Iter  455/2250] R2[458/600]  | LR: 0.008299 | E: -27.061203 | E_var:     0.3963 | E_err:   0.009836
[2025-10-02 15:14:17] [Iter  456/2250] R2[460/600]  | LR: 0.008211 | E: -27.063308 | E_var:     0.4169 | E_err:   0.010088
[2025-10-02 15:14:19] [Iter  457/2250] R2[462/600]  | LR: 0.008124 | E: -27.067035 | E_var:     0.3504 | E_err:   0.009249
[2025-10-02 15:14:21] [Iter  458/2250] R2[464/600]  | LR: 0.008038 | E: -27.072808 | E_var:     0.3232 | E_err:   0.008884
[2025-10-02 15:14:24] [Iter  459/2250] R2[466/600]  | LR: 0.007953 | E: -27.069578 | E_var:     0.4346 | E_err:   0.010301
[2025-10-02 15:14:26] [Iter  460/2250] R2[468/600]  | LR: 0.007869 | E: -27.062170 | E_var:     0.3299 | E_err:   0.008974
[2025-10-02 15:14:29] [Iter  461/2250] R2[470/600]  | LR: 0.007786 | E: -27.061034 | E_var:     0.3210 | E_err:   0.008853
[2025-10-02 15:14:31] [Iter  462/2250] R2[472/600]  | LR: 0.007704 | E: -27.075967 | E_var:     0.3356 | E_err:   0.009051
[2025-10-02 15:14:33] [Iter  463/2250] R2[474/600]  | LR: 0.007623 | E: -27.064924 | E_var:     0.4535 | E_err:   0.010522
[2025-10-02 15:14:36] [Iter  464/2250] R2[476/600]  | LR: 0.007543 | E: -27.067957 | E_var:     0.3130 | E_err:   0.008742
[2025-10-02 15:14:38] [Iter  465/2250] R2[478/600]  | LR: 0.007465 | E: -27.064819 | E_var:     0.5970 | E_err:   0.012073
[2025-10-02 15:14:41] [Iter  466/2250] R2[480/600]  | LR: 0.007387 | E: -27.055303 | E_var:     0.4146 | E_err:   0.010061
[2025-10-02 15:14:43] [Iter  467/2250] R2[482/600]  | LR: 0.007311 | E: -27.074917 | E_var:     0.4112 | E_err:   0.010019
[2025-10-02 15:14:45] [Iter  468/2250] R2[484/600]  | LR: 0.007236 | E: -27.062369 | E_var:     0.4290 | E_err:   0.010234
[2025-10-02 15:14:48] [Iter  469/2250] R2[486/600]  | LR: 0.007161 | E: -27.067644 | E_var:     0.4276 | E_err:   0.010218
[2025-10-02 15:14:50] [Iter  470/2250] R2[488/600]  | LR: 0.007088 | E: -27.073687 | E_var:     0.4104 | E_err:   0.010009
[2025-10-02 15:14:53] [Iter  471/2250] R2[490/600]  | LR: 0.007017 | E: -27.064997 | E_var:     0.3181 | E_err:   0.008813
[2025-10-02 15:14:55] [Iter  472/2250] R2[492/600]  | LR: 0.006946 | E: -27.079581 | E_var:     0.3925 | E_err:   0.009790
[2025-10-02 15:14:57] [Iter  473/2250] R2[494/600]  | LR: 0.006876 | E: -27.067296 | E_var:     0.5041 | E_err:   0.011094
[2025-10-02 15:15:00] [Iter  474/2250] R2[496/600]  | LR: 0.006808 | E: -27.060400 | E_var:     0.5597 | E_err:   0.011689
[2025-10-02 15:15:02] [Iter  475/2250] R2[498/600]  | LR: 0.006741 | E: -27.095973 | E_var:     0.3177 | E_err:   0.008807
[2025-10-02 15:15:05] [Iter  476/2250] R2[500/600]  | LR: 0.006675 | E: -27.062324 | E_var:     0.3793 | E_err:   0.009622
[2025-10-02 15:15:07] [Iter  477/2250] R2[502/600]  | LR: 0.006610 | E: -27.079557 | E_var:     0.3264 | E_err:   0.008927
[2025-10-02 15:15:09] [Iter  478/2250] R2[504/600]  | LR: 0.006546 | E: -27.073425 | E_var:     0.3664 | E_err:   0.009458
[2025-10-02 15:15:12] [Iter  479/2250] R2[506/600]  | LR: 0.006484 | E: -27.076791 | E_var:     0.3541 | E_err:   0.009298
[2025-10-02 15:15:14] [Iter  480/2250] R2[508/600]  | LR: 0.006422 | E: -27.066856 | E_var:     0.3862 | E_err:   0.009710
[2025-10-02 15:15:17] [Iter  481/2250] R2[510/600]  | LR: 0.006362 | E: -27.084600 | E_var:     0.4078 | E_err:   0.009978
[2025-10-02 15:15:19] [Iter  482/2250] R2[512/600]  | LR: 0.006304 | E: -27.078458 | E_var:     0.3771 | E_err:   0.009594
[2025-10-02 15:15:22] [Iter  483/2250] R2[514/600]  | LR: 0.006246 | E: -27.064488 | E_var:     0.3280 | E_err:   0.008949
[2025-10-02 15:15:24] [Iter  484/2250] R2[516/600]  | LR: 0.006190 | E: -27.092919 | E_var:     0.4560 | E_err:   0.010552
[2025-10-02 15:15:26] [Iter  485/2250] R2[518/600]  | LR: 0.006135 | E: -27.071893 | E_var:     0.3154 | E_err:   0.008775
[2025-10-02 15:15:29] [Iter  486/2250] R2[520/600]  | LR: 0.006081 | E: -27.058822 | E_var:     0.3422 | E_err:   0.009141
[2025-10-02 15:15:31] [Iter  487/2250] R2[522/600]  | LR: 0.006028 | E: -27.076074 | E_var:     0.3587 | E_err:   0.009358
[2025-10-02 15:15:34] [Iter  488/2250] R2[524/600]  | LR: 0.005977 | E: -27.081582 | E_var:     0.3457 | E_err:   0.009187
[2025-10-02 15:15:36] [Iter  489/2250] R2[526/600]  | LR: 0.005927 | E: -27.068959 | E_var:     0.4041 | E_err:   0.009933
[2025-10-02 15:15:38] [Iter  490/2250] R2[528/600]  | LR: 0.005878 | E: -27.063918 | E_var:     0.5528 | E_err:   0.011617
[2025-10-02 15:15:41] [Iter  491/2250] R2[530/600]  | LR: 0.005830 | E: -27.083243 | E_var:     0.3889 | E_err:   0.009745
[2025-10-02 15:15:43] [Iter  492/2250] R2[532/600]  | LR: 0.005784 | E: -27.087157 | E_var:     0.3373 | E_err:   0.009075
[2025-10-02 15:15:46] [Iter  493/2250] R2[534/600]  | LR: 0.005739 | E: -27.079647 | E_var:     0.5402 | E_err:   0.011484
[2025-10-02 15:15:48] [Iter  494/2250] R2[536/600]  | LR: 0.005695 | E: -27.062841 | E_var:     0.3640 | E_err:   0.009427
[2025-10-02 15:15:50] [Iter  495/2250] R2[538/600]  | LR: 0.005653 | E: -27.088847 | E_var:     0.3281 | E_err:   0.008949
[2025-10-02 15:15:53] [Iter  496/2250] R2[540/600]  | LR: 0.005612 | E: -27.082643 | E_var:     0.4924 | E_err:   0.010964
[2025-10-02 15:15:55] [Iter  497/2250] R2[542/600]  | LR: 0.005572 | E: -27.075976 | E_var:     0.3241 | E_err:   0.008895
[2025-10-02 15:15:58] [Iter  498/2250] R2[544/600]  | LR: 0.005534 | E: -27.080939 | E_var:     0.3573 | E_err:   0.009340
[2025-10-02 15:16:00] [Iter  499/2250] R2[546/600]  | LR: 0.005496 | E: -27.089985 | E_var:     0.3453 | E_err:   0.009182
[2025-10-02 15:16:02] [Iter  500/2250] R2[548/600]  | LR: 0.005460 | E: -27.077999 | E_var:     0.3188 | E_err:   0.008822
[2025-10-02 15:16:05] [Iter  501/2250] R2[550/600]  | LR: 0.005426 | E: -27.077044 | E_var:     0.3315 | E_err:   0.008996
[2025-10-02 15:16:07] [Iter  502/2250] R2[552/600]  | LR: 0.005393 | E: -27.061070 | E_var:     0.3752 | E_err:   0.009571
[2025-10-02 15:16:10] [Iter  503/2250] R2[554/600]  | LR: 0.005361 | E: -27.064218 | E_var:     0.3554 | E_err:   0.009315
[2025-10-02 15:16:12] [Iter  504/2250] R2[556/600]  | LR: 0.005330 | E: -27.083121 | E_var:     0.3228 | E_err:   0.008877
[2025-10-02 15:16:14] [Iter  505/2250] R2[558/600]  | LR: 0.005301 | E: -27.080001 | E_var:     0.3629 | E_err:   0.009413
[2025-10-02 15:16:17] [Iter  506/2250] R2[560/600]  | LR: 0.005273 | E: -27.080336 | E_var:     0.3291 | E_err:   0.008964
[2025-10-02 15:16:19] [Iter  507/2250] R2[562/600]  | LR: 0.005247 | E: -27.089746 | E_var:     0.3147 | E_err:   0.008766
[2025-10-02 15:16:22] [Iter  508/2250] R2[564/600]  | LR: 0.005221 | E: -27.067558 | E_var:     0.2960 | E_err:   0.008501
[2025-10-02 15:16:24] [Iter  509/2250] R2[566/600]  | LR: 0.005198 | E: -27.055820 | E_var:     0.5132 | E_err:   0.011194
[2025-10-02 15:16:26] [Iter  510/2250] R2[568/600]  | LR: 0.005175 | E: -27.068403 | E_var:     0.4200 | E_err:   0.010126
[2025-10-02 15:16:29] [Iter  511/2250] R2[570/600]  | LR: 0.005154 | E: -27.080049 | E_var:     0.3427 | E_err:   0.009147
[2025-10-02 15:16:31] [Iter  512/2250] R2[572/600]  | LR: 0.005134 | E: -27.058094 | E_var:     0.4055 | E_err:   0.009950
[2025-10-02 15:16:34] [Iter  513/2250] R2[574/600]  | LR: 0.005116 | E: -27.079192 | E_var:     0.4116 | E_err:   0.010024
[2025-10-02 15:16:36] [Iter  514/2250] R2[576/600]  | LR: 0.005099 | E: -27.084327 | E_var:     0.3660 | E_err:   0.009453
[2025-10-02 15:16:38] [Iter  515/2250] R2[578/600]  | LR: 0.005083 | E: -27.073045 | E_var:     0.3814 | E_err:   0.009650
[2025-10-02 15:16:41] [Iter  516/2250] R2[580/600]  | LR: 0.005068 | E: -27.070580 | E_var:     0.3122 | E_err:   0.008731
[2025-10-02 15:16:43] [Iter  517/2250] R2[582/600]  | LR: 0.005055 | E: -27.059914 | E_var:     0.3300 | E_err:   0.008975
[2025-10-02 15:16:46] [Iter  518/2250] R2[584/600]  | LR: 0.005044 | E: -27.080939 | E_var:     0.3534 | E_err:   0.009289
[2025-10-02 15:16:48] [Iter  519/2250] R2[586/600]  | LR: 0.005034 | E: -27.094733 | E_var:     0.3863 | E_err:   0.009711
[2025-10-02 15:16:50] [Iter  520/2250] R2[588/600]  | LR: 0.005025 | E: -27.061704 | E_var:     0.3603 | E_err:   0.009378
[2025-10-02 15:16:53] [Iter  521/2250] R2[590/600]  | LR: 0.005017 | E: -27.077088 | E_var:     0.3295 | E_err:   0.008969
[2025-10-02 15:16:55] [Iter  522/2250] R2[592/600]  | LR: 0.005011 | E: -27.087043 | E_var:     0.3968 | E_err:   0.009843
[2025-10-02 15:16:58] [Iter  523/2250] R2[594/600]  | LR: 0.005006 | E: -27.092150 | E_var:     0.3496 | E_err:   0.009238
[2025-10-02 15:17:00] [Iter  524/2250] R2[596/600]  | LR: 0.005003 | E: -27.072691 | E_var:     0.3843 | E_err:   0.009686
[2025-10-02 15:17:02] [Iter  525/2250] R2[598/600]  | LR: 0.005001 | E: -27.077816 | E_var:     0.3673 | E_err:   0.009470
[2025-10-02 15:17:02] 🔄 RESTART #3 | Period: 1200
[2025-10-02 15:17:05] [Iter  526/2250] R3[0/1200]   | LR: 0.030000 | E: -27.072465 | E_var:     0.2928 | E_err:   0.008455
[2025-10-02 15:17:07] [Iter  527/2250] R3[2/1200]   | LR: 0.030000 | E: -27.094525 | E_var:     0.2994 | E_err:   0.008549
[2025-10-02 15:17:10] [Iter  528/2250] R3[4/1200]   | LR: 0.029999 | E: -27.093347 | E_var:     0.3245 | E_err:   0.008901
[2025-10-02 15:17:12] [Iter  529/2250] R3[6/1200]   | LR: 0.029998 | E: -27.083183 | E_var:     0.2649 | E_err:   0.008042
[2025-10-02 15:17:14] [Iter  530/2250] R3[8/1200]   | LR: 0.029997 | E: -27.063615 | E_var:     0.3625 | E_err:   0.009407
[2025-10-02 15:17:17] [Iter  531/2250] R3[10/1200]  | LR: 0.029996 | E: -27.070019 | E_var:     0.3100 | E_err:   0.008699
[2025-10-02 15:17:19] [Iter  532/2250] R3[12/1200]  | LR: 0.029994 | E: -27.068949 | E_var:     0.3677 | E_err:   0.009475
[2025-10-02 15:17:22] [Iter  533/2250] R3[14/1200]  | LR: 0.029992 | E: -27.088502 | E_var:     0.4040 | E_err:   0.009931
[2025-10-02 15:17:24] [Iter  534/2250] R3[16/1200]  | LR: 0.029989 | E: -27.086390 | E_var:     0.3141 | E_err:   0.008757
[2025-10-02 15:17:26] [Iter  535/2250] R3[18/1200]  | LR: 0.029986 | E: -27.072701 | E_var:     0.4570 | E_err:   0.010562
[2025-10-02 15:17:29] [Iter  536/2250] R3[20/1200]  | LR: 0.029983 | E: -27.087916 | E_var:     0.3412 | E_err:   0.009127
[2025-10-02 15:17:31] [Iter  537/2250] R3[22/1200]  | LR: 0.029979 | E: -27.079619 | E_var:     0.3316 | E_err:   0.008998
[2025-10-02 15:17:34] [Iter  538/2250] R3[24/1200]  | LR: 0.029975 | E: -27.085621 | E_var:     0.4279 | E_err:   0.010221
[2025-10-02 15:17:36] [Iter  539/2250] R3[26/1200]  | LR: 0.029971 | E: -27.092348 | E_var:     0.4135 | E_err:   0.010048
[2025-10-02 15:17:39] [Iter  540/2250] R3[28/1200]  | LR: 0.029966 | E: -27.080929 | E_var:     0.3362 | E_err:   0.009059
[2025-10-02 15:17:41] [Iter  541/2250] R3[30/1200]  | LR: 0.029961 | E: -27.073162 | E_var:     0.3746 | E_err:   0.009564
[2025-10-02 15:17:43] [Iter  542/2250] R3[32/1200]  | LR: 0.029956 | E: -27.068544 | E_var:     0.3919 | E_err:   0.009781
[2025-10-02 15:17:46] [Iter  543/2250] R3[34/1200]  | LR: 0.029951 | E: -27.083572 | E_var:     0.2885 | E_err:   0.008392
[2025-10-02 15:17:48] [Iter  544/2250] R3[36/1200]  | LR: 0.029945 | E: -27.080108 | E_var:     0.5147 | E_err:   0.011210
[2025-10-02 15:17:51] [Iter  545/2250] R3[38/1200]  | LR: 0.029938 | E: -27.074959 | E_var:     0.3736 | E_err:   0.009551
[2025-10-02 15:17:53] [Iter  546/2250] R3[40/1200]  | LR: 0.029932 | E: -27.089988 | E_var:     0.3562 | E_err:   0.009325
[2025-10-02 15:17:55] [Iter  547/2250] R3[42/1200]  | LR: 0.029925 | E: -27.079919 | E_var:     0.3983 | E_err:   0.009861
[2025-10-02 15:17:58] [Iter  548/2250] R3[44/1200]  | LR: 0.029917 | E: -27.062379 | E_var:     0.3605 | E_err:   0.009381
[2025-10-02 15:18:00] [Iter  549/2250] R3[46/1200]  | LR: 0.029909 | E: -27.088666 | E_var:     0.3236 | E_err:   0.008888
[2025-10-02 15:18:03] [Iter  550/2250] R3[48/1200]  | LR: 0.029901 | E: -27.083060 | E_var:     0.3987 | E_err:   0.009866
[2025-10-02 15:18:05] [Iter  551/2250] R3[50/1200]  | LR: 0.029893 | E: -27.080786 | E_var:     0.3758 | E_err:   0.009579
[2025-10-02 15:18:07] [Iter  552/2250] R3[52/1200]  | LR: 0.029884 | E: -27.083294 | E_var:     0.2895 | E_err:   0.008407
[2025-10-02 15:18:10] [Iter  553/2250] R3[54/1200]  | LR: 0.029875 | E: -27.062319 | E_var:     0.4094 | E_err:   0.009998
[2025-10-02 15:18:12] [Iter  554/2250] R3[56/1200]  | LR: 0.029866 | E: -27.066040 | E_var:     0.3577 | E_err:   0.009345
[2025-10-02 15:18:15] [Iter  555/2250] R3[58/1200]  | LR: 0.029856 | E: -27.085350 | E_var:     0.2900 | E_err:   0.008414
[2025-10-02 15:18:17] [Iter  556/2250] R3[60/1200]  | LR: 0.029846 | E: -27.098350 | E_var:     0.3548 | E_err:   0.009307
[2025-10-02 15:18:19] [Iter  557/2250] R3[62/1200]  | LR: 0.029836 | E: -27.079145 | E_var:     0.2905 | E_err:   0.008422
[2025-10-02 15:18:22] [Iter  558/2250] R3[64/1200]  | LR: 0.029825 | E: -27.077622 | E_var:     0.3456 | E_err:   0.009186
[2025-10-02 15:18:24] [Iter  559/2250] R3[66/1200]  | LR: 0.029814 | E: -27.073592 | E_var:     0.3026 | E_err:   0.008594
[2025-10-02 15:18:27] [Iter  560/2250] R3[68/1200]  | LR: 0.029802 | E: -27.083358 | E_var:     0.3385 | E_err:   0.009091
[2025-10-02 15:18:29] [Iter  561/2250] R3[70/1200]  | LR: 0.029791 | E: -27.095875 | E_var:     0.3155 | E_err:   0.008776
[2025-10-02 15:18:31] [Iter  562/2250] R3[72/1200]  | LR: 0.029779 | E: -27.074273 | E_var:     0.2865 | E_err:   0.008363
[2025-10-02 15:18:34] [Iter  563/2250] R3[74/1200]  | LR: 0.029766 | E: -27.084159 | E_var:     0.3484 | E_err:   0.009222
[2025-10-02 15:18:36] [Iter  564/2250] R3[76/1200]  | LR: 0.029753 | E: -27.087786 | E_var:     0.2884 | E_err:   0.008391
[2025-10-02 15:18:39] [Iter  565/2250] R3[78/1200]  | LR: 0.029740 | E: -27.066954 | E_var:     0.3815 | E_err:   0.009650
[2025-10-02 15:18:41] [Iter  566/2250] R3[80/1200]  | LR: 0.029727 | E: -27.080474 | E_var:     0.3385 | E_err:   0.009091
[2025-10-02 15:18:43] [Iter  567/2250] R3[82/1200]  | LR: 0.029713 | E: -27.085322 | E_var:     0.2849 | E_err:   0.008339
[2025-10-02 15:18:46] [Iter  568/2250] R3[84/1200]  | LR: 0.029699 | E: -27.088068 | E_var:     0.2868 | E_err:   0.008367
[2025-10-02 15:18:48] [Iter  569/2250] R3[86/1200]  | LR: 0.029685 | E: -27.087397 | E_var:     0.3751 | E_err:   0.009570
[2025-10-02 15:18:51] [Iter  570/2250] R3[88/1200]  | LR: 0.029670 | E: -27.079735 | E_var:     0.3218 | E_err:   0.008863
[2025-10-02 15:18:53] [Iter  571/2250] R3[90/1200]  | LR: 0.029655 | E: -27.091076 | E_var:     0.3167 | E_err:   0.008794
[2025-10-02 15:18:55] [Iter  572/2250] R3[92/1200]  | LR: 0.029639 | E: -27.091976 | E_var:     0.3383 | E_err:   0.009088
[2025-10-02 15:18:58] [Iter  573/2250] R3[94/1200]  | LR: 0.029623 | E: -27.063449 | E_var:     0.4330 | E_err:   0.010282
[2025-10-02 15:19:00] [Iter  574/2250] R3[96/1200]  | LR: 0.029607 | E: -27.089458 | E_var:     0.3601 | E_err:   0.009377
[2025-10-02 15:19:03] [Iter  575/2250] R3[98/1200]  | LR: 0.029591 | E: -27.085205 | E_var:     0.4252 | E_err:   0.010189
[2025-10-02 15:19:05] [Iter  576/2250] R3[100/1200] | LR: 0.029574 | E: -27.081498 | E_var:     0.3220 | E_err:   0.008866
[2025-10-02 15:19:07] [Iter  577/2250] R3[102/1200] | LR: 0.029557 | E: -27.061987 | E_var:     0.4085 | E_err:   0.009987
[2025-10-02 15:19:10] [Iter  578/2250] R3[104/1200] | LR: 0.029540 | E: -27.081808 | E_var:     0.3676 | E_err:   0.009474
[2025-10-02 15:19:12] [Iter  579/2250] R3[106/1200] | LR: 0.029522 | E: -27.091190 | E_var:     0.4946 | E_err:   0.010989
[2025-10-02 15:19:15] [Iter  580/2250] R3[108/1200] | LR: 0.029504 | E: -27.086089 | E_var:     0.3398 | E_err:   0.009109
[2025-10-02 15:19:17] [Iter  581/2250] R3[110/1200] | LR: 0.029485 | E: -27.083584 | E_var:     0.5191 | E_err:   0.011257
[2025-10-02 15:19:19] [Iter  582/2250] R3[112/1200] | LR: 0.029466 | E: -27.092822 | E_var:     0.3489 | E_err:   0.009229
[2025-10-02 15:19:22] [Iter  583/2250] R3[114/1200] | LR: 0.029447 | E: -27.079361 | E_var:     0.3175 | E_err:   0.008804
[2025-10-02 15:19:24] [Iter  584/2250] R3[116/1200] | LR: 0.029428 | E: -27.083389 | E_var:     0.2809 | E_err:   0.008281
[2025-10-02 15:19:27] [Iter  585/2250] R3[118/1200] | LR: 0.029408 | E: -27.089625 | E_var:     0.3665 | E_err:   0.009460
[2025-10-02 15:19:29] [Iter  586/2250] R3[120/1200] | LR: 0.029388 | E: -27.081610 | E_var:     0.3515 | E_err:   0.009263
[2025-10-02 15:19:32] [Iter  587/2250] R3[122/1200] | LR: 0.029368 | E: -27.099108 | E_var:     0.3157 | E_err:   0.008779
[2025-10-02 15:19:34] [Iter  588/2250] R3[124/1200] | LR: 0.029347 | E: -27.081712 | E_var:     0.3727 | E_err:   0.009539
[2025-10-02 15:19:36] [Iter  589/2250] R3[126/1200] | LR: 0.029326 | E: -27.080945 | E_var:     0.3381 | E_err:   0.009085
[2025-10-02 15:19:39] [Iter  590/2250] R3[128/1200] | LR: 0.029305 | E: -27.095866 | E_var:     0.3107 | E_err:   0.008709
[2025-10-02 15:19:41] [Iter  591/2250] R3[130/1200] | LR: 0.029283 | E: -27.084218 | E_var:     0.3483 | E_err:   0.009221
[2025-10-02 15:19:44] [Iter  592/2250] R3[132/1200] | LR: 0.029261 | E: -27.079521 | E_var:     0.2772 | E_err:   0.008227
[2025-10-02 15:19:46] [Iter  593/2250] R3[134/1200] | LR: 0.029239 | E: -27.089408 | E_var:     0.3099 | E_err:   0.008698
[2025-10-02 15:19:48] [Iter  594/2250] R3[136/1200] | LR: 0.029216 | E: -27.088753 | E_var:     0.2594 | E_err:   0.007958
[2025-10-02 15:19:51] [Iter  595/2250] R3[138/1200] | LR: 0.029193 | E: -27.086643 | E_var:     0.3114 | E_err:   0.008720
[2025-10-02 15:19:53] [Iter  596/2250] R3[140/1200] | LR: 0.029170 | E: -27.086505 | E_var:     0.3339 | E_err:   0.009029
[2025-10-02 15:19:56] [Iter  597/2250] R3[142/1200] | LR: 0.029146 | E: -27.079230 | E_var:     0.2856 | E_err:   0.008350
[2025-10-02 15:19:58] [Iter  598/2250] R3[144/1200] | LR: 0.029122 | E: -27.077256 | E_var:     0.5624 | E_err:   0.011718
[2025-10-02 15:20:00] [Iter  599/2250] R3[146/1200] | LR: 0.029098 | E: -27.067915 | E_var:     0.3937 | E_err:   0.009804
[2025-10-02 15:20:03] [Iter  600/2250] R3[148/1200] | LR: 0.029073 | E: -27.091467 | E_var:     0.3336 | E_err:   0.009024
[2025-10-02 15:20:03] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-02 15:20:05] [Iter  601/2250] R3[150/1200] | LR: 0.029048 | E: -27.082165 | E_var:     0.4757 | E_err:   0.010776
[2025-10-02 15:20:08] [Iter  602/2250] R3[152/1200] | LR: 0.029023 | E: -27.105519 | E_var:     0.3137 | E_err:   0.008751
[2025-10-02 15:20:10] [Iter  603/2250] R3[154/1200] | LR: 0.028998 | E: -27.093543 | E_var:     0.3577 | E_err:   0.009345
[2025-10-02 15:20:12] [Iter  604/2250] R3[156/1200] | LR: 0.028972 | E: -27.091553 | E_var:     0.3527 | E_err:   0.009280
[2025-10-02 15:20:15] [Iter  605/2250] R3[158/1200] | LR: 0.028946 | E: -27.083766 | E_var:     0.3180 | E_err:   0.008812
[2025-10-02 15:20:17] [Iter  606/2250] R3[160/1200] | LR: 0.028919 | E: -27.094733 | E_var:     0.3077 | E_err:   0.008668
[2025-10-02 15:20:20] [Iter  607/2250] R3[162/1200] | LR: 0.028893 | E: -27.086386 | E_var:     0.3420 | E_err:   0.009138
[2025-10-02 15:20:22] [Iter  608/2250] R3[164/1200] | LR: 0.028865 | E: -27.093330 | E_var:     0.2685 | E_err:   0.008096
[2025-10-02 15:20:24] [Iter  609/2250] R3[166/1200] | LR: 0.028838 | E: -27.097867 | E_var:     0.2915 | E_err:   0.008436
[2025-10-02 15:20:27] [Iter  610/2250] R3[168/1200] | LR: 0.028810 | E: -27.089551 | E_var:     0.2754 | E_err:   0.008200
[2025-10-02 15:20:29] [Iter  611/2250] R3[170/1200] | LR: 0.028782 | E: -27.091337 | E_var:     0.2934 | E_err:   0.008464
[2025-10-02 15:20:32] [Iter  612/2250] R3[172/1200] | LR: 0.028754 | E: -27.087037 | E_var:     0.2787 | E_err:   0.008248
[2025-10-02 15:20:34] [Iter  613/2250] R3[174/1200] | LR: 0.028725 | E: -27.086688 | E_var:     0.3065 | E_err:   0.008650
[2025-10-02 15:20:36] [Iter  614/2250] R3[176/1200] | LR: 0.028696 | E: -27.077455 | E_var:     0.3269 | E_err:   0.008934
[2025-10-02 15:20:39] [Iter  615/2250] R3[178/1200] | LR: 0.028667 | E: -27.087766 | E_var:     0.3406 | E_err:   0.009119
[2025-10-02 15:20:41] [Iter  616/2250] R3[180/1200] | LR: 0.028638 | E: -27.071637 | E_var:     0.2775 | E_err:   0.008232
[2025-10-02 15:20:44] [Iter  617/2250] R3[182/1200] | LR: 0.028608 | E: -27.093271 | E_var:     0.3369 | E_err:   0.009069
[2025-10-02 15:20:46] [Iter  618/2250] R3[184/1200] | LR: 0.028578 | E: -27.097851 | E_var:     0.3478 | E_err:   0.009215
[2025-10-02 15:20:49] [Iter  619/2250] R3[186/1200] | LR: 0.028547 | E: -27.099906 | E_var:     0.2870 | E_err:   0.008371
[2025-10-02 15:20:51] [Iter  620/2250] R3[188/1200] | LR: 0.028516 | E: -27.078266 | E_var:     0.2856 | E_err:   0.008350
[2025-10-02 15:20:53] [Iter  621/2250] R3[190/1200] | LR: 0.028485 | E: -27.091575 | E_var:     0.5230 | E_err:   0.011300
[2025-10-02 15:20:56] [Iter  622/2250] R3[192/1200] | LR: 0.028454 | E: -27.083936 | E_var:     0.3343 | E_err:   0.009034
[2025-10-02 15:20:58] [Iter  623/2250] R3[194/1200] | LR: 0.028422 | E: -27.089149 | E_var:     0.3100 | E_err:   0.008699
[2025-10-02 15:21:01] [Iter  624/2250] R3[196/1200] | LR: 0.028390 | E: -27.099537 | E_var:     0.2640 | E_err:   0.008029
[2025-10-02 15:21:03] [Iter  625/2250] R3[198/1200] | LR: 0.028358 | E: -27.097742 | E_var:     0.5136 | E_err:   0.011198
[2025-10-02 15:21:05] [Iter  626/2250] R3[200/1200] | LR: 0.028325 | E: -27.080249 | E_var:     0.3716 | E_err:   0.009525
[2025-10-02 15:21:08] [Iter  627/2250] R3[202/1200] | LR: 0.028292 | E: -27.095315 | E_var:     0.2897 | E_err:   0.008410
[2025-10-02 15:21:10] [Iter  628/2250] R3[204/1200] | LR: 0.028259 | E: -27.118137 | E_var:     0.3090 | E_err:   0.008686
[2025-10-02 15:21:13] [Iter  629/2250] R3[206/1200] | LR: 0.028226 | E: -27.082397 | E_var:     0.2533 | E_err:   0.007864
[2025-10-02 15:21:15] [Iter  630/2250] R3[208/1200] | LR: 0.028192 | E: -27.078669 | E_var:     0.2888 | E_err:   0.008397
[2025-10-02 15:21:17] [Iter  631/2250] R3[210/1200] | LR: 0.028158 | E: -27.092802 | E_var:     0.3304 | E_err:   0.008981
[2025-10-02 15:21:20] [Iter  632/2250] R3[212/1200] | LR: 0.028124 | E: -27.100667 | E_var:     0.3138 | E_err:   0.008753
[2025-10-02 15:21:22] [Iter  633/2250] R3[214/1200] | LR: 0.028089 | E: -27.084356 | E_var:     0.3290 | E_err:   0.008963
[2025-10-02 15:21:25] [Iter  634/2250] R3[216/1200] | LR: 0.028054 | E: -27.088042 | E_var:     0.4045 | E_err:   0.009938
[2025-10-02 15:21:27] [Iter  635/2250] R3[218/1200] | LR: 0.028019 | E: -27.096418 | E_var:     0.3656 | E_err:   0.009448
[2025-10-02 15:21:29] [Iter  636/2250] R3[220/1200] | LR: 0.027983 | E: -27.098627 | E_var:     0.2844 | E_err:   0.008332
[2025-10-02 15:21:32] [Iter  637/2250] R3[222/1200] | LR: 0.027948 | E: -27.079308 | E_var:     0.3211 | E_err:   0.008854
[2025-10-02 15:21:34] [Iter  638/2250] R3[224/1200] | LR: 0.027912 | E: -27.093087 | E_var:     0.3337 | E_err:   0.009026
[2025-10-02 15:21:37] [Iter  639/2250] R3[226/1200] | LR: 0.027875 | E: -27.089510 | E_var:     0.3031 | E_err:   0.008603
[2025-10-02 15:21:39] [Iter  640/2250] R3[228/1200] | LR: 0.027839 | E: -27.080666 | E_var:     0.2892 | E_err:   0.008402
[2025-10-02 15:21:41] [Iter  641/2250] R3[230/1200] | LR: 0.027802 | E: -27.084076 | E_var:     0.2735 | E_err:   0.008172
[2025-10-02 15:21:44] [Iter  642/2250] R3[232/1200] | LR: 0.027764 | E: -27.104333 | E_var:     0.3160 | E_err:   0.008784
[2025-10-02 15:21:46] [Iter  643/2250] R3[234/1200] | LR: 0.027727 | E: -27.089471 | E_var:     0.2675 | E_err:   0.008082
[2025-10-02 15:21:49] [Iter  644/2250] R3[236/1200] | LR: 0.027689 | E: -27.101603 | E_var:     0.2862 | E_err:   0.008359
[2025-10-02 15:21:51] [Iter  645/2250] R3[238/1200] | LR: 0.027651 | E: -27.095277 | E_var:     0.2909 | E_err:   0.008427
[2025-10-02 15:21:53] [Iter  646/2250] R3[240/1200] | LR: 0.027613 | E: -27.089584 | E_var:     0.2922 | E_err:   0.008446
[2025-10-02 15:21:56] [Iter  647/2250] R3[242/1200] | LR: 0.027574 | E: -27.090944 | E_var:     0.2630 | E_err:   0.008012
[2025-10-02 15:21:58] [Iter  648/2250] R3[244/1200] | LR: 0.027535 | E: -27.092395 | E_var:     0.2825 | E_err:   0.008305
[2025-10-02 15:22:01] [Iter  649/2250] R3[246/1200] | LR: 0.027496 | E: -27.111220 | E_var:     0.3019 | E_err:   0.008585
[2025-10-02 15:22:03] [Iter  650/2250] R3[248/1200] | LR: 0.027457 | E: -27.082942 | E_var:     0.2809 | E_err:   0.008281
[2025-10-02 15:22:05] [Iter  651/2250] R3[250/1200] | LR: 0.027417 | E: -27.095760 | E_var:     0.2434 | E_err:   0.007709
[2025-10-02 15:22:08] [Iter  652/2250] R3[252/1200] | LR: 0.027377 | E: -27.090723 | E_var:     0.2677 | E_err:   0.008084
[2025-10-02 15:22:10] [Iter  653/2250] R3[254/1200] | LR: 0.027337 | E: -27.081774 | E_var:     0.2791 | E_err:   0.008254
[2025-10-02 15:22:13] [Iter  654/2250] R3[256/1200] | LR: 0.027296 | E: -27.093707 | E_var:     0.2992 | E_err:   0.008546
[2025-10-02 15:22:15] [Iter  655/2250] R3[258/1200] | LR: 0.027255 | E: -27.097747 | E_var:     0.2903 | E_err:   0.008418
[2025-10-02 15:22:17] [Iter  656/2250] R3[260/1200] | LR: 0.027214 | E: -27.108740 | E_var:     0.3513 | E_err:   0.009261
[2025-10-02 15:22:20] [Iter  657/2250] R3[262/1200] | LR: 0.027173 | E: -27.098168 | E_var:     0.2780 | E_err:   0.008239
[2025-10-02 15:22:22] [Iter  658/2250] R3[264/1200] | LR: 0.027131 | E: -27.096104 | E_var:     0.2744 | E_err:   0.008185
[2025-10-02 15:22:25] [Iter  659/2250] R3[266/1200] | LR: 0.027090 | E: -27.077149 | E_var:     0.3285 | E_err:   0.008956
[2025-10-02 15:22:27] [Iter  660/2250] R3[268/1200] | LR: 0.027047 | E: -27.083480 | E_var:     0.4005 | E_err:   0.009889
[2025-10-02 15:22:29] [Iter  661/2250] R3[270/1200] | LR: 0.027005 | E: -27.097273 | E_var:     0.3331 | E_err:   0.009018
[2025-10-02 15:22:32] [Iter  662/2250] R3[272/1200] | LR: 0.026962 | E: -27.090020 | E_var:     0.2736 | E_err:   0.008173
[2025-10-02 15:22:34] [Iter  663/2250] R3[274/1200] | LR: 0.026920 | E: -27.098432 | E_var:     0.3490 | E_err:   0.009231
[2025-10-02 15:22:37] [Iter  664/2250] R3[276/1200] | LR: 0.026876 | E: -27.106895 | E_var:     0.2902 | E_err:   0.008417
[2025-10-02 15:22:39] [Iter  665/2250] R3[278/1200] | LR: 0.026833 | E: -27.089973 | E_var:     0.3473 | E_err:   0.009208
[2025-10-02 15:22:41] [Iter  666/2250] R3[280/1200] | LR: 0.026789 | E: -27.093823 | E_var:     0.3727 | E_err:   0.009539
[2025-10-02 15:22:44] [Iter  667/2250] R3[282/1200] | LR: 0.026745 | E: -27.095048 | E_var:     0.3193 | E_err:   0.008830
[2025-10-02 15:22:46] [Iter  668/2250] R3[284/1200] | LR: 0.026701 | E: -27.092604 | E_var:     0.4107 | E_err:   0.010013
[2025-10-02 15:22:49] [Iter  669/2250] R3[286/1200] | LR: 0.026657 | E: -27.093931 | E_var:     0.4501 | E_err:   0.010483
[2025-10-02 15:22:51] [Iter  670/2250] R3[288/1200] | LR: 0.026612 | E: -27.098418 | E_var:     0.3241 | E_err:   0.008895
[2025-10-02 15:22:53] [Iter  671/2250] R3[290/1200] | LR: 0.026567 | E: -27.087968 | E_var:     0.2720 | E_err:   0.008149
[2025-10-02 15:22:56] [Iter  672/2250] R3[292/1200] | LR: 0.026522 | E: -27.092516 | E_var:     0.2955 | E_err:   0.008494
[2025-10-02 15:22:58] [Iter  673/2250] R3[294/1200] | LR: 0.026477 | E: -27.095032 | E_var:     0.3053 | E_err:   0.008633
[2025-10-02 15:23:01] [Iter  674/2250] R3[296/1200] | LR: 0.026431 | E: -27.095072 | E_var:     0.2427 | E_err:   0.007697
[2025-10-02 15:23:03] [Iter  675/2250] R3[298/1200] | LR: 0.026385 | E: -27.109229 | E_var:     0.2935 | E_err:   0.008465
[2025-10-02 15:23:05] [Iter  676/2250] R3[300/1200] | LR: 0.026339 | E: -27.091460 | E_var:     0.2821 | E_err:   0.008300
[2025-10-02 15:23:08] [Iter  677/2250] R3[302/1200] | LR: 0.026292 | E: -27.098257 | E_var:     0.2952 | E_err:   0.008489
[2025-10-02 15:23:10] [Iter  678/2250] R3[304/1200] | LR: 0.026246 | E: -27.083568 | E_var:     0.3850 | E_err:   0.009695
[2025-10-02 15:23:13] [Iter  679/2250] R3[306/1200] | LR: 0.026199 | E: -27.094436 | E_var:     0.3025 | E_err:   0.008593
[2025-10-02 15:23:15] [Iter  680/2250] R3[308/1200] | LR: 0.026152 | E: -27.082138 | E_var:     0.2918 | E_err:   0.008440
[2025-10-02 15:23:18] [Iter  681/2250] R3[310/1200] | LR: 0.026104 | E: -27.086260 | E_var:     0.2774 | E_err:   0.008229
[2025-10-02 15:23:20] [Iter  682/2250] R3[312/1200] | LR: 0.026057 | E: -27.095253 | E_var:     0.3516 | E_err:   0.009266
[2025-10-02 15:23:22] [Iter  683/2250] R3[314/1200] | LR: 0.026009 | E: -27.091630 | E_var:     0.2610 | E_err:   0.007982
[2025-10-02 15:23:25] [Iter  684/2250] R3[316/1200] | LR: 0.025961 | E: -27.076988 | E_var:     0.3525 | E_err:   0.009277
[2025-10-02 15:23:27] [Iter  685/2250] R3[318/1200] | LR: 0.025913 | E: -27.104761 | E_var:     0.3006 | E_err:   0.008566
[2025-10-02 15:23:30] [Iter  686/2250] R3[320/1200] | LR: 0.025864 | E: -27.086139 | E_var:     0.3888 | E_err:   0.009743
[2025-10-02 15:23:32] [Iter  687/2250] R3[322/1200] | LR: 0.025815 | E: -27.091634 | E_var:     0.3059 | E_err:   0.008641
[2025-10-02 15:23:34] [Iter  688/2250] R3[324/1200] | LR: 0.025766 | E: -27.092561 | E_var:     0.2727 | E_err:   0.008160
[2025-10-02 15:23:37] [Iter  689/2250] R3[326/1200] | LR: 0.025717 | E: -27.098503 | E_var:     0.4102 | E_err:   0.010008
[2025-10-02 15:23:39] [Iter  690/2250] R3[328/1200] | LR: 0.025668 | E: -27.095390 | E_var:     0.2801 | E_err:   0.008269
[2025-10-02 15:23:42] [Iter  691/2250] R3[330/1200] | LR: 0.025618 | E: -27.103610 | E_var:     0.2680 | E_err:   0.008088
[2025-10-02 15:23:44] [Iter  692/2250] R3[332/1200] | LR: 0.025568 | E: -27.097115 | E_var:     0.2818 | E_err:   0.008294
[2025-10-02 15:23:46] [Iter  693/2250] R3[334/1200] | LR: 0.025518 | E: -27.082781 | E_var:     0.3016 | E_err:   0.008580
[2025-10-02 15:23:49] [Iter  694/2250] R3[336/1200] | LR: 0.025468 | E: -27.077499 | E_var:     0.2982 | E_err:   0.008532
[2025-10-02 15:23:51] [Iter  695/2250] R3[338/1200] | LR: 0.025417 | E: -27.101770 | E_var:     0.2633 | E_err:   0.008017
[2025-10-02 15:23:54] [Iter  696/2250] R3[340/1200] | LR: 0.025367 | E: -27.081284 | E_var:     0.2765 | E_err:   0.008215
[2025-10-02 15:23:56] [Iter  697/2250] R3[342/1200] | LR: 0.025316 | E: -27.095620 | E_var:     0.2526 | E_err:   0.007853
[2025-10-02 15:23:58] [Iter  698/2250] R3[344/1200] | LR: 0.025264 | E: -27.091398 | E_var:     0.3383 | E_err:   0.009088
[2025-10-02 15:24:01] [Iter  699/2250] R3[346/1200] | LR: 0.025213 | E: -27.089807 | E_var:     0.2810 | E_err:   0.008283
[2025-10-02 15:24:03] [Iter  700/2250] R3[348/1200] | LR: 0.025161 | E: -27.092811 | E_var:     0.2565 | E_err:   0.007914
[2025-10-02 15:24:06] [Iter  701/2250] R3[350/1200] | LR: 0.025110 | E: -27.089220 | E_var:     0.3227 | E_err:   0.008876
[2025-10-02 15:24:08] [Iter  702/2250] R3[352/1200] | LR: 0.025057 | E: -27.081728 | E_var:     0.3235 | E_err:   0.008886
[2025-10-02 15:24:10] [Iter  703/2250] R3[354/1200] | LR: 0.025005 | E: -27.098531 | E_var:     0.3153 | E_err:   0.008774
[2025-10-02 15:24:13] [Iter  704/2250] R3[356/1200] | LR: 0.024953 | E: -27.099844 | E_var:     0.2664 | E_err:   0.008065
[2025-10-02 15:24:15] [Iter  705/2250] R3[358/1200] | LR: 0.024900 | E: -27.101628 | E_var:     0.3193 | E_err:   0.008829
[2025-10-02 15:24:18] [Iter  706/2250] R3[360/1200] | LR: 0.024847 | E: -27.080948 | E_var:     0.2685 | E_err:   0.008096
[2025-10-02 15:24:20] [Iter  707/2250] R3[362/1200] | LR: 0.024794 | E: -27.100285 | E_var:     0.2831 | E_err:   0.008314
[2025-10-02 15:24:22] [Iter  708/2250] R3[364/1200] | LR: 0.024741 | E: -27.084872 | E_var:     0.2452 | E_err:   0.007736
[2025-10-02 15:24:25] [Iter  709/2250] R3[366/1200] | LR: 0.024688 | E: -27.079830 | E_var:     0.3333 | E_err:   0.009020
[2025-10-02 15:24:27] [Iter  710/2250] R3[368/1200] | LR: 0.024634 | E: -27.095398 | E_var:     0.3146 | E_err:   0.008764
[2025-10-02 15:24:30] [Iter  711/2250] R3[370/1200] | LR: 0.024580 | E: -27.095809 | E_var:     0.3268 | E_err:   0.008932
[2025-10-02 15:24:32] [Iter  712/2250] R3[372/1200] | LR: 0.024526 | E: -27.106346 | E_var:     0.3033 | E_err:   0.008605
[2025-10-02 15:24:34] [Iter  713/2250] R3[374/1200] | LR: 0.024472 | E: -27.093195 | E_var:     0.2837 | E_err:   0.008322
[2025-10-02 15:24:37] [Iter  714/2250] R3[376/1200] | LR: 0.024417 | E: -27.096753 | E_var:     0.2607 | E_err:   0.007977
[2025-10-02 15:24:39] [Iter  715/2250] R3[378/1200] | LR: 0.024363 | E: -27.093669 | E_var:     0.2602 | E_err:   0.007970
[2025-10-02 15:24:42] [Iter  716/2250] R3[380/1200] | LR: 0.024308 | E: -27.084993 | E_var:     0.3584 | E_err:   0.009354
[2025-10-02 15:24:44] [Iter  717/2250] R3[382/1200] | LR: 0.024253 | E: -27.099715 | E_var:     0.3444 | E_err:   0.009169
[2025-10-02 15:24:47] [Iter  718/2250] R3[384/1200] | LR: 0.024198 | E: -27.097325 | E_var:     0.2686 | E_err:   0.008098
[2025-10-02 15:24:49] [Iter  719/2250] R3[386/1200] | LR: 0.024142 | E: -27.106306 | E_var:     0.2964 | E_err:   0.008506
[2025-10-02 15:24:51] [Iter  720/2250] R3[388/1200] | LR: 0.024087 | E: -27.087117 | E_var:     0.2777 | E_err:   0.008234
[2025-10-02 15:24:54] [Iter  721/2250] R3[390/1200] | LR: 0.024031 | E: -27.091087 | E_var:     0.2800 | E_err:   0.008268
[2025-10-02 15:24:56] [Iter  722/2250] R3[392/1200] | LR: 0.023975 | E: -27.100075 | E_var:     0.2589 | E_err:   0.007950
[2025-10-02 15:24:59] [Iter  723/2250] R3[394/1200] | LR: 0.023919 | E: -27.088449 | E_var:     0.3676 | E_err:   0.009474
[2025-10-02 15:25:01] [Iter  724/2250] R3[396/1200] | LR: 0.023863 | E: -27.095565 | E_var:     0.3907 | E_err:   0.009767
[2025-10-02 15:25:03] [Iter  725/2250] R3[398/1200] | LR: 0.023807 | E: -27.100509 | E_var:     0.2721 | E_err:   0.008150
[2025-10-02 15:25:06] [Iter  726/2250] R3[400/1200] | LR: 0.023750 | E: -27.109339 | E_var:     0.2423 | E_err:   0.007691
[2025-10-02 15:25:08] [Iter  727/2250] R3[402/1200] | LR: 0.023693 | E: -27.075546 | E_var:     0.3333 | E_err:   0.009021
[2025-10-02 15:25:11] [Iter  728/2250] R3[404/1200] | LR: 0.023636 | E: -27.107993 | E_var:     0.2486 | E_err:   0.007790
[2025-10-02 15:25:13] [Iter  729/2250] R3[406/1200] | LR: 0.023579 | E: -27.091874 | E_var:     0.2842 | E_err:   0.008330
[2025-10-02 15:25:15] [Iter  730/2250] R3[408/1200] | LR: 0.023522 | E: -27.097336 | E_var:     0.2500 | E_err:   0.007813
[2025-10-02 15:25:18] [Iter  731/2250] R3[410/1200] | LR: 0.023464 | E: -27.106087 | E_var:     0.4502 | E_err:   0.010484
[2025-10-02 15:25:20] [Iter  732/2250] R3[412/1200] | LR: 0.023407 | E: -27.104188 | E_var:     0.2981 | E_err:   0.008530
[2025-10-02 15:25:23] [Iter  733/2250] R3[414/1200] | LR: 0.023349 | E: -27.114221 | E_var:     0.2850 | E_err:   0.008342
[2025-10-02 15:25:25] [Iter  734/2250] R3[416/1200] | LR: 0.023291 | E: -27.082605 | E_var:     0.3303 | E_err:   0.008980
[2025-10-02 15:25:27] [Iter  735/2250] R3[418/1200] | LR: 0.023233 | E: -27.099496 | E_var:     0.3536 | E_err:   0.009292
[2025-10-02 15:25:30] [Iter  736/2250] R3[420/1200] | LR: 0.023175 | E: -27.093579 | E_var:     0.2733 | E_err:   0.008168
[2025-10-02 15:25:32] [Iter  737/2250] R3[422/1200] | LR: 0.023116 | E: -27.080966 | E_var:     0.2946 | E_err:   0.008480
[2025-10-02 15:25:35] [Iter  738/2250] R3[424/1200] | LR: 0.023058 | E: -27.084181 | E_var:     0.2293 | E_err:   0.007483
[2025-10-02 15:25:37] [Iter  739/2250] R3[426/1200] | LR: 0.022999 | E: -27.100842 | E_var:     0.3185 | E_err:   0.008818
[2025-10-02 15:25:39] [Iter  740/2250] R3[428/1200] | LR: 0.022940 | E: -27.092968 | E_var:     0.2395 | E_err:   0.007647
[2025-10-02 15:25:42] [Iter  741/2250] R3[430/1200] | LR: 0.022881 | E: -27.099193 | E_var:     0.3454 | E_err:   0.009183
[2025-10-02 15:25:44] [Iter  742/2250] R3[432/1200] | LR: 0.022822 | E: -27.097384 | E_var:     0.3233 | E_err:   0.008884
[2025-10-02 15:25:47] [Iter  743/2250] R3[434/1200] | LR: 0.022763 | E: -27.091652 | E_var:     0.2628 | E_err:   0.008009
[2025-10-02 15:25:49] [Iter  744/2250] R3[436/1200] | LR: 0.022704 | E: -27.095437 | E_var:     0.3327 | E_err:   0.009013
[2025-10-02 15:25:51] [Iter  745/2250] R3[438/1200] | LR: 0.022644 | E: -27.091097 | E_var:     0.2623 | E_err:   0.008002
[2025-10-02 15:25:54] [Iter  746/2250] R3[440/1200] | LR: 0.022584 | E: -27.098018 | E_var:     0.3787 | E_err:   0.009616
[2025-10-02 15:25:56] [Iter  747/2250] R3[442/1200] | LR: 0.022524 | E: -27.102101 | E_var:     0.2831 | E_err:   0.008314
[2025-10-02 15:25:59] [Iter  748/2250] R3[444/1200] | LR: 0.022464 | E: -27.097715 | E_var:     0.2644 | E_err:   0.008034
[2025-10-02 15:26:01] [Iter  749/2250] R3[446/1200] | LR: 0.022404 | E: -27.101029 | E_var:     0.3019 | E_err:   0.008586
[2025-10-02 15:26:03] [Iter  750/2250] R3[448/1200] | LR: 0.022344 | E: -27.098335 | E_var:     0.2910 | E_err:   0.008429
[2025-10-02 15:26:06] [Iter  751/2250] R3[450/1200] | LR: 0.022284 | E: -27.103793 | E_var:     0.3150 | E_err:   0.008769
[2025-10-02 15:26:08] [Iter  752/2250] R3[452/1200] | LR: 0.022223 | E: -27.088116 | E_var:     0.4174 | E_err:   0.010095
[2025-10-02 15:26:11] [Iter  753/2250] R3[454/1200] | LR: 0.022162 | E: -27.092368 | E_var:     0.2303 | E_err:   0.007499
[2025-10-02 15:26:13] [Iter  754/2250] R3[456/1200] | LR: 0.022102 | E: -27.085941 | E_var:     0.2450 | E_err:   0.007734
[2025-10-02 15:26:15] [Iter  755/2250] R3[458/1200] | LR: 0.022041 | E: -27.089638 | E_var:     0.2826 | E_err:   0.008307
[2025-10-02 15:26:18] [Iter  756/2250] R3[460/1200] | LR: 0.021980 | E: -27.102484 | E_var:     0.3098 | E_err:   0.008697
[2025-10-02 15:26:20] [Iter  757/2250] R3[462/1200] | LR: 0.021918 | E: -27.098684 | E_var:     0.2546 | E_err:   0.007884
[2025-10-02 15:26:23] [Iter  758/2250] R3[464/1200] | LR: 0.021857 | E: -27.088089 | E_var:     0.2965 | E_err:   0.008508
[2025-10-02 15:26:25] [Iter  759/2250] R3[466/1200] | LR: 0.021796 | E: -27.093697 | E_var:     0.2719 | E_err:   0.008148
[2025-10-02 15:26:28] [Iter  760/2250] R3[468/1200] | LR: 0.021734 | E: -27.100162 | E_var:     0.2684 | E_err:   0.008095
[2025-10-02 15:26:30] [Iter  761/2250] R3[470/1200] | LR: 0.021673 | E: -27.097647 | E_var:     0.2437 | E_err:   0.007714
[2025-10-02 15:26:32] [Iter  762/2250] R3[472/1200] | LR: 0.021611 | E: -27.103703 | E_var:     0.2917 | E_err:   0.008439
[2025-10-02 15:26:35] [Iter  763/2250] R3[474/1200] | LR: 0.021549 | E: -27.096114 | E_var:     0.4129 | E_err:   0.010041
[2025-10-02 15:26:37] [Iter  764/2250] R3[476/1200] | LR: 0.021487 | E: -27.096144 | E_var:     0.2509 | E_err:   0.007827
[2025-10-02 15:26:40] [Iter  765/2250] R3[478/1200] | LR: 0.021425 | E: -27.114705 | E_var:     0.2866 | E_err:   0.008364
[2025-10-02 15:26:42] [Iter  766/2250] R3[480/1200] | LR: 0.021363 | E: -27.097264 | E_var:     0.9157 | E_err:   0.014952
[2025-10-02 15:26:44] [Iter  767/2250] R3[482/1200] | LR: 0.021300 | E: -27.081508 | E_var:     0.3348 | E_err:   0.009041
[2025-10-02 15:26:47] [Iter  768/2250] R3[484/1200] | LR: 0.021238 | E: -27.102826 | E_var:     0.2606 | E_err:   0.007977
[2025-10-02 15:26:49] [Iter  769/2250] R3[486/1200] | LR: 0.021176 | E: -27.091951 | E_var:     0.3233 | E_err:   0.008884
[2025-10-02 15:26:52] [Iter  770/2250] R3[488/1200] | LR: 0.021113 | E: -27.086575 | E_var:     0.2724 | E_err:   0.008154
[2025-10-02 15:26:54] [Iter  771/2250] R3[490/1200] | LR: 0.021050 | E: -27.084267 | E_var:     0.2886 | E_err:   0.008394
[2025-10-02 15:26:56] [Iter  772/2250] R3[492/1200] | LR: 0.020987 | E: -27.086465 | E_var:     0.2992 | E_err:   0.008547
[2025-10-02 15:26:59] [Iter  773/2250] R3[494/1200] | LR: 0.020924 | E: -27.091330 | E_var:     0.2378 | E_err:   0.007619
[2025-10-02 15:27:01] [Iter  774/2250] R3[496/1200] | LR: 0.020861 | E: -27.092397 | E_var:     0.2358 | E_err:   0.007587
[2025-10-02 15:27:04] [Iter  775/2250] R3[498/1200] | LR: 0.020798 | E: -27.098023 | E_var:     0.2904 | E_err:   0.008420
[2025-10-02 15:27:06] [Iter  776/2250] R3[500/1200] | LR: 0.020735 | E: -27.099780 | E_var:     0.2587 | E_err:   0.007948
[2025-10-02 15:27:08] [Iter  777/2250] R3[502/1200] | LR: 0.020672 | E: -27.098396 | E_var:     0.2217 | E_err:   0.007357
[2025-10-02 15:27:11] [Iter  778/2250] R3[504/1200] | LR: 0.020609 | E: -27.102725 | E_var:     0.3310 | E_err:   0.008990
[2025-10-02 15:27:13] [Iter  779/2250] R3[506/1200] | LR: 0.020545 | E: -27.096405 | E_var:     0.3135 | E_err:   0.008748
[2025-10-02 15:27:16] [Iter  780/2250] R3[508/1200] | LR: 0.020482 | E: -27.107046 | E_var:     0.2600 | E_err:   0.007967
[2025-10-02 15:27:18] [Iter  781/2250] R3[510/1200] | LR: 0.020418 | E: -27.100292 | E_var:     0.3325 | E_err:   0.009009
[2025-10-02 15:27:20] [Iter  782/2250] R3[512/1200] | LR: 0.020354 | E: -27.109305 | E_var:     0.2524 | E_err:   0.007850
[2025-10-02 15:27:23] [Iter  783/2250] R3[514/1200] | LR: 0.020291 | E: -27.100432 | E_var:     0.2509 | E_err:   0.007827
[2025-10-02 15:27:25] [Iter  784/2250] R3[516/1200] | LR: 0.020227 | E: -27.102221 | E_var:     0.3106 | E_err:   0.008709
[2025-10-02 15:27:28] [Iter  785/2250] R3[518/1200] | LR: 0.020163 | E: -27.110354 | E_var:     0.2708 | E_err:   0.008131
[2025-10-02 15:27:30] [Iter  786/2250] R3[520/1200] | LR: 0.020099 | E: -27.102108 | E_var:     0.2501 | E_err:   0.007815
[2025-10-02 15:27:32] [Iter  787/2250] R3[522/1200] | LR: 0.020035 | E: -27.096756 | E_var:     0.2881 | E_err:   0.008386
[2025-10-02 15:27:35] [Iter  788/2250] R3[524/1200] | LR: 0.019971 | E: -27.115820 | E_var:     0.3214 | E_err:   0.008858
[2025-10-02 15:27:37] [Iter  789/2250] R3[526/1200] | LR: 0.019907 | E: -27.095197 | E_var:     0.2663 | E_err:   0.008063
[2025-10-02 15:27:40] [Iter  790/2250] R3[528/1200] | LR: 0.019842 | E: -27.094891 | E_var:     0.2607 | E_err:   0.007977
[2025-10-02 15:27:42] [Iter  791/2250] R3[530/1200] | LR: 0.019778 | E: -27.100368 | E_var:     0.2612 | E_err:   0.007985
[2025-10-02 15:27:44] [Iter  792/2250] R3[532/1200] | LR: 0.019714 | E: -27.097126 | E_var:     0.3550 | E_err:   0.009310
[2025-10-02 15:27:47] [Iter  793/2250] R3[534/1200] | LR: 0.019649 | E: -27.102141 | E_var:     0.2606 | E_err:   0.007976
[2025-10-02 15:27:49] [Iter  794/2250] R3[536/1200] | LR: 0.019585 | E: -27.094285 | E_var:     0.2778 | E_err:   0.008235
[2025-10-02 15:27:52] [Iter  795/2250] R3[538/1200] | LR: 0.019520 | E: -27.090572 | E_var:     0.2452 | E_err:   0.007738
[2025-10-02 15:27:54] [Iter  796/2250] R3[540/1200] | LR: 0.019455 | E: -27.089921 | E_var:     0.3037 | E_err:   0.008610
[2025-10-02 15:27:56] [Iter  797/2250] R3[542/1200] | LR: 0.019391 | E: -27.113959 | E_var:     0.3808 | E_err:   0.009642
[2025-10-02 15:27:59] [Iter  798/2250] R3[544/1200] | LR: 0.019326 | E: -27.093550 | E_var:     0.2937 | E_err:   0.008468
[2025-10-02 15:28:01] [Iter  799/2250] R3[546/1200] | LR: 0.019261 | E: -27.103387 | E_var:     0.2256 | E_err:   0.007422
[2025-10-02 15:28:04] [Iter  800/2250] R3[548/1200] | LR: 0.019196 | E: -27.095050 | E_var:     0.2547 | E_err:   0.007886
[2025-10-02 15:28:04] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-02 15:28:06] [Iter  801/2250] R3[550/1200] | LR: 0.019132 | E: -27.098639 | E_var:     0.2600 | E_err:   0.007967
[2025-10-02 15:28:09] [Iter  802/2250] R3[552/1200] | LR: 0.019067 | E: -27.109141 | E_var:     0.2523 | E_err:   0.007849
[2025-10-02 15:28:11] [Iter  803/2250] R3[554/1200] | LR: 0.019002 | E: -27.102959 | E_var:     0.3204 | E_err:   0.008845
[2025-10-02 15:28:13] [Iter  804/2250] R3[556/1200] | LR: 0.018937 | E: -27.097788 | E_var:     0.2606 | E_err:   0.007976
[2025-10-02 15:28:16] [Iter  805/2250] R3[558/1200] | LR: 0.018872 | E: -27.094076 | E_var:     0.2823 | E_err:   0.008302
[2025-10-02 15:28:18] [Iter  806/2250] R3[560/1200] | LR: 0.018807 | E: -27.100157 | E_var:     0.2395 | E_err:   0.007646
[2025-10-02 15:28:20] [Iter  807/2250] R3[562/1200] | LR: 0.018741 | E: -27.094570 | E_var:     0.2632 | E_err:   0.008016
[2025-10-02 15:28:23] [Iter  808/2250] R3[564/1200] | LR: 0.018676 | E: -27.110693 | E_var:     0.3332 | E_err:   0.009020
[2025-10-02 15:28:25] [Iter  809/2250] R3[566/1200] | LR: 0.018611 | E: -27.110031 | E_var:     0.3288 | E_err:   0.008960
[2025-10-02 15:28:28] [Iter  810/2250] R3[568/1200] | LR: 0.018546 | E: -27.093602 | E_var:     0.2503 | E_err:   0.007817
[2025-10-02 15:28:30] [Iter  811/2250] R3[570/1200] | LR: 0.018481 | E: -27.104480 | E_var:     0.2272 | E_err:   0.007447
[2025-10-02 15:28:32] [Iter  812/2250] R3[572/1200] | LR: 0.018415 | E: -27.096285 | E_var:     0.2274 | E_err:   0.007451
[2025-10-02 15:28:35] [Iter  813/2250] R3[574/1200] | LR: 0.018350 | E: -27.105231 | E_var:     0.2902 | E_err:   0.008417
[2025-10-02 15:28:37] [Iter  814/2250] R3[576/1200] | LR: 0.018285 | E: -27.100742 | E_var:     0.2548 | E_err:   0.007887
[2025-10-02 15:28:40] [Iter  815/2250] R3[578/1200] | LR: 0.018220 | E: -27.096586 | E_var:     0.2989 | E_err:   0.008543
[2025-10-02 15:28:42] [Iter  816/2250] R3[580/1200] | LR: 0.018154 | E: -27.097497 | E_var:     0.2357 | E_err:   0.007586
[2025-10-02 15:28:44] [Iter  817/2250] R3[582/1200] | LR: 0.018089 | E: -27.104281 | E_var:     0.2750 | E_err:   0.008194
[2025-10-02 15:28:47] [Iter  818/2250] R3[584/1200] | LR: 0.018023 | E: -27.098831 | E_var:     0.3008 | E_err:   0.008569
[2025-10-02 15:28:49] [Iter  819/2250] R3[586/1200] | LR: 0.017958 | E: -27.085990 | E_var:     0.2658 | E_err:   0.008056
[2025-10-02 15:28:52] [Iter  820/2250] R3[588/1200] | LR: 0.017893 | E: -27.092978 | E_var:     0.3221 | E_err:   0.008868
[2025-10-02 15:28:54] [Iter  821/2250] R3[590/1200] | LR: 0.017827 | E: -27.103937 | E_var:     0.2610 | E_err:   0.007982
[2025-10-02 15:28:57] [Iter  822/2250] R3[592/1200] | LR: 0.017762 | E: -27.092156 | E_var:     0.2500 | E_err:   0.007812
[2025-10-02 15:28:59] [Iter  823/2250] R3[594/1200] | LR: 0.017696 | E: -27.101114 | E_var:     0.2782 | E_err:   0.008242
[2025-10-02 15:29:01] [Iter  824/2250] R3[596/1200] | LR: 0.017631 | E: -27.100743 | E_var:     0.2256 | E_err:   0.007422
[2025-10-02 15:29:04] [Iter  825/2250] R3[598/1200] | LR: 0.017565 | E: -27.103318 | E_var:     0.4542 | E_err:   0.010530
[2025-10-02 15:29:06] [Iter  826/2250] R3[600/1200] | LR: 0.017500 | E: -27.103758 | E_var:     0.2628 | E_err:   0.008010
[2025-10-02 15:29:08] [Iter  827/2250] R3[602/1200] | LR: 0.017435 | E: -27.090741 | E_var:     0.2725 | E_err:   0.008156
[2025-10-02 15:29:11] [Iter  828/2250] R3[604/1200] | LR: 0.017369 | E: -27.088907 | E_var:     0.2597 | E_err:   0.007963
[2025-10-02 15:29:13] [Iter  829/2250] R3[606/1200] | LR: 0.017304 | E: -27.106917 | E_var:     0.2210 | E_err:   0.007346
[2025-10-02 15:29:16] [Iter  830/2250] R3[608/1200] | LR: 0.017238 | E: -27.109482 | E_var:     0.2579 | E_err:   0.007935
[2025-10-02 15:29:18] [Iter  831/2250] R3[610/1200] | LR: 0.017173 | E: -27.101634 | E_var:     0.2543 | E_err:   0.007879
[2025-10-02 15:29:21] [Iter  832/2250] R3[612/1200] | LR: 0.017107 | E: -27.100216 | E_var:     0.3023 | E_err:   0.008591
[2025-10-02 15:29:23] [Iter  833/2250] R3[614/1200] | LR: 0.017042 | E: -27.101569 | E_var:     0.2383 | E_err:   0.007627
[2025-10-02 15:29:25] [Iter  834/2250] R3[616/1200] | LR: 0.016977 | E: -27.102954 | E_var:     0.2964 | E_err:   0.008507
[2025-10-02 15:29:28] [Iter  835/2250] R3[618/1200] | LR: 0.016911 | E: -27.099571 | E_var:     0.2511 | E_err:   0.007830
[2025-10-02 15:29:30] [Iter  836/2250] R3[620/1200] | LR: 0.016846 | E: -27.098140 | E_var:     0.2519 | E_err:   0.007843
[2025-10-02 15:29:33] [Iter  837/2250] R3[622/1200] | LR: 0.016780 | E: -27.107479 | E_var:     0.2557 | E_err:   0.007902
[2025-10-02 15:29:35] [Iter  838/2250] R3[624/1200] | LR: 0.016715 | E: -27.088434 | E_var:     0.3137 | E_err:   0.008751
[2025-10-02 15:29:37] [Iter  839/2250] R3[626/1200] | LR: 0.016650 | E: -27.105961 | E_var:     0.2765 | E_err:   0.008216
[2025-10-02 15:29:40] [Iter  840/2250] R3[628/1200] | LR: 0.016585 | E: -27.095513 | E_var:     0.2837 | E_err:   0.008322
[2025-10-02 15:29:42] [Iter  841/2250] R3[630/1200] | LR: 0.016519 | E: -27.088257 | E_var:     0.2587 | E_err:   0.007947
[2025-10-02 15:29:45] [Iter  842/2250] R3[632/1200] | LR: 0.016454 | E: -27.103918 | E_var:     0.2424 | E_err:   0.007692
[2025-10-02 15:29:47] [Iter  843/2250] R3[634/1200] | LR: 0.016389 | E: -27.092855 | E_var:     0.2326 | E_err:   0.007536
[2025-10-02 15:29:49] [Iter  844/2250] R3[636/1200] | LR: 0.016324 | E: -27.103767 | E_var:     0.2890 | E_err:   0.008400
[2025-10-02 15:29:52] [Iter  845/2250] R3[638/1200] | LR: 0.016259 | E: -27.099294 | E_var:     0.2953 | E_err:   0.008491
[2025-10-02 15:29:54] [Iter  846/2250] R3[640/1200] | LR: 0.016193 | E: -27.112653 | E_var:     0.2423 | E_err:   0.007691
[2025-10-02 15:29:57] [Iter  847/2250] R3[642/1200] | LR: 0.016128 | E: -27.106341 | E_var:     0.3664 | E_err:   0.009458
[2025-10-02 15:29:59] [Iter  848/2250] R3[644/1200] | LR: 0.016063 | E: -27.102290 | E_var:     0.2812 | E_err:   0.008285
[2025-10-02 15:30:01] [Iter  849/2250] R3[646/1200] | LR: 0.015998 | E: -27.106388 | E_var:     0.6112 | E_err:   0.012215
[2025-10-02 15:30:04] [Iter  850/2250] R3[648/1200] | LR: 0.015933 | E: -27.108416 | E_var:     0.3202 | E_err:   0.008842
[2025-10-02 15:30:06] [Iter  851/2250] R3[650/1200] | LR: 0.015868 | E: -27.107388 | E_var:     0.2617 | E_err:   0.007994
[2025-10-02 15:30:09] [Iter  852/2250] R3[652/1200] | LR: 0.015804 | E: -27.095544 | E_var:     0.2635 | E_err:   0.008020
[2025-10-02 15:30:11] [Iter  853/2250] R3[654/1200] | LR: 0.015739 | E: -27.090133 | E_var:     0.2614 | E_err:   0.007988
[2025-10-02 15:30:13] [Iter  854/2250] R3[656/1200] | LR: 0.015674 | E: -27.114722 | E_var:     0.2268 | E_err:   0.007442
[2025-10-02 15:30:16] [Iter  855/2250] R3[658/1200] | LR: 0.015609 | E: -27.107921 | E_var:     0.2519 | E_err:   0.007843
[2025-10-02 15:30:18] [Iter  856/2250] R3[660/1200] | LR: 0.015545 | E: -27.094847 | E_var:     0.2742 | E_err:   0.008181
[2025-10-02 15:30:21] [Iter  857/2250] R3[662/1200] | LR: 0.015480 | E: -27.110684 | E_var:     0.6201 | E_err:   0.012304
[2025-10-02 15:30:23] [Iter  858/2250] R3[664/1200] | LR: 0.015415 | E: -27.094377 | E_var:     0.2532 | E_err:   0.007863
[2025-10-02 15:30:25] [Iter  859/2250] R3[666/1200] | LR: 0.015351 | E: -27.092515 | E_var:     0.3351 | E_err:   0.009045
[2025-10-02 15:30:28] [Iter  860/2250] R3[668/1200] | LR: 0.015286 | E: -27.117970 | E_var:     0.2708 | E_err:   0.008131
[2025-10-02 15:30:30] [Iter  861/2250] R3[670/1200] | LR: 0.015222 | E: -27.098977 | E_var:     0.2302 | E_err:   0.007496
[2025-10-02 15:30:33] [Iter  862/2250] R3[672/1200] | LR: 0.015158 | E: -27.100247 | E_var:     0.3678 | E_err:   0.009476
[2025-10-02 15:30:35] [Iter  863/2250] R3[674/1200] | LR: 0.015093 | E: -27.098665 | E_var:     0.2794 | E_err:   0.008260
[2025-10-02 15:30:37] [Iter  864/2250] R3[676/1200] | LR: 0.015029 | E: -27.102835 | E_var:     0.2498 | E_err:   0.007809
[2025-10-02 15:30:40] [Iter  865/2250] R3[678/1200] | LR: 0.014965 | E: -27.094449 | E_var:     0.2258 | E_err:   0.007425
[2025-10-02 15:30:42] [Iter  866/2250] R3[680/1200] | LR: 0.014901 | E: -27.093404 | E_var:     0.2716 | E_err:   0.008142
[2025-10-02 15:30:45] [Iter  867/2250] R3[682/1200] | LR: 0.014837 | E: -27.121419 | E_var:     0.3283 | E_err:   0.008953
[2025-10-02 15:30:47] [Iter  868/2250] R3[684/1200] | LR: 0.014773 | E: -27.110260 | E_var:     0.2598 | E_err:   0.007964
[2025-10-02 15:30:49] [Iter  869/2250] R3[686/1200] | LR: 0.014709 | E: -27.102271 | E_var:     0.2758 | E_err:   0.008206
[2025-10-02 15:30:52] [Iter  870/2250] R3[688/1200] | LR: 0.014646 | E: -27.096505 | E_var:     0.2982 | E_err:   0.008532
[2025-10-02 15:30:54] [Iter  871/2250] R3[690/1200] | LR: 0.014582 | E: -27.105580 | E_var:     0.2437 | E_err:   0.007713
[2025-10-02 15:30:57] [Iter  872/2250] R3[692/1200] | LR: 0.014518 | E: -27.108033 | E_var:     0.2215 | E_err:   0.007354
[2025-10-02 15:30:59] [Iter  873/2250] R3[694/1200] | LR: 0.014455 | E: -27.118233 | E_var:     0.2563 | E_err:   0.007910
[2025-10-02 15:31:01] [Iter  874/2250] R3[696/1200] | LR: 0.014391 | E: -27.097420 | E_var:     0.2566 | E_err:   0.007915
[2025-10-02 15:31:04] [Iter  875/2250] R3[698/1200] | LR: 0.014328 | E: -27.109380 | E_var:     0.3072 | E_err:   0.008660
[2025-10-02 15:31:06] [Iter  876/2250] R3[700/1200] | LR: 0.014265 | E: -27.097976 | E_var:     0.2390 | E_err:   0.007638
[2025-10-02 15:31:09] [Iter  877/2250] R3[702/1200] | LR: 0.014202 | E: -27.106264 | E_var:     0.2413 | E_err:   0.007675
[2025-10-02 15:31:11] [Iter  878/2250] R3[704/1200] | LR: 0.014139 | E: -27.113925 | E_var:     0.2447 | E_err:   0.007729
[2025-10-02 15:31:13] [Iter  879/2250] R3[706/1200] | LR: 0.014076 | E: -27.096193 | E_var:     0.3803 | E_err:   0.009636
[2025-10-02 15:31:16] [Iter  880/2250] R3[708/1200] | LR: 0.014013 | E: -27.098359 | E_var:     0.2549 | E_err:   0.007889
[2025-10-02 15:31:18] [Iter  881/2250] R3[710/1200] | LR: 0.013950 | E: -27.101510 | E_var:     0.2295 | E_err:   0.007485
[2025-10-02 15:31:21] [Iter  882/2250] R3[712/1200] | LR: 0.013887 | E: -27.102290 | E_var:     0.2766 | E_err:   0.008217
[2025-10-02 15:31:23] [Iter  883/2250] R3[714/1200] | LR: 0.013824 | E: -27.107577 | E_var:     0.2362 | E_err:   0.007593
[2025-10-02 15:31:25] [Iter  884/2250] R3[716/1200] | LR: 0.013762 | E: -27.098618 | E_var:     0.2833 | E_err:   0.008317
[2025-10-02 15:31:28] [Iter  885/2250] R3[718/1200] | LR: 0.013700 | E: -27.101279 | E_var:     0.2479 | E_err:   0.007780
[2025-10-02 15:31:30] [Iter  886/2250] R3[720/1200] | LR: 0.013637 | E: -27.094904 | E_var:     0.2687 | E_err:   0.008100
[2025-10-02 15:31:33] [Iter  887/2250] R3[722/1200] | LR: 0.013575 | E: -27.108751 | E_var:     0.2718 | E_err:   0.008146
[2025-10-02 15:31:35] [Iter  888/2250] R3[724/1200] | LR: 0.013513 | E: -27.108465 | E_var:     0.2183 | E_err:   0.007300
[2025-10-02 15:31:37] [Iter  889/2250] R3[726/1200] | LR: 0.013451 | E: -27.110320 | E_var:     0.2412 | E_err:   0.007674
[2025-10-02 15:31:40] [Iter  890/2250] R3[728/1200] | LR: 0.013389 | E: -27.107024 | E_var:     0.2188 | E_err:   0.007308
[2025-10-02 15:31:42] [Iter  891/2250] R3[730/1200] | LR: 0.013327 | E: -27.100173 | E_var:     0.2938 | E_err:   0.008469
[2025-10-02 15:31:45] [Iter  892/2250] R3[732/1200] | LR: 0.013266 | E: -27.107462 | E_var:     0.4330 | E_err:   0.010281
[2025-10-02 15:31:47] [Iter  893/2250] R3[734/1200] | LR: 0.013204 | E: -27.110911 | E_var:     0.3261 | E_err:   0.008922
[2025-10-02 15:31:49] [Iter  894/2250] R3[736/1200] | LR: 0.013143 | E: -27.084279 | E_var:     0.2587 | E_err:   0.007948
[2025-10-02 15:31:52] [Iter  895/2250] R3[738/1200] | LR: 0.013082 | E: -27.115150 | E_var:     0.3066 | E_err:   0.008651
[2025-10-02 15:31:54] [Iter  896/2250] R3[740/1200] | LR: 0.013020 | E: -27.103338 | E_var:     0.3318 | E_err:   0.009001
[2025-10-02 15:31:57] [Iter  897/2250] R3[742/1200] | LR: 0.012959 | E: -27.097194 | E_var:     0.2955 | E_err:   0.008493
[2025-10-02 15:31:59] [Iter  898/2250] R3[744/1200] | LR: 0.012898 | E: -27.111133 | E_var:     0.2107 | E_err:   0.007172
[2025-10-02 15:32:01] [Iter  899/2250] R3[746/1200] | LR: 0.012838 | E: -27.101978 | E_var:     0.2709 | E_err:   0.008132
[2025-10-02 15:32:04] [Iter  900/2250] R3[748/1200] | LR: 0.012777 | E: -27.112041 | E_var:     0.2345 | E_err:   0.007567
[2025-10-02 15:32:06] [Iter  901/2250] R3[750/1200] | LR: 0.012716 | E: -27.098438 | E_var:     0.2837 | E_err:   0.008322
[2025-10-02 15:32:09] [Iter  902/2250] R3[752/1200] | LR: 0.012656 | E: -27.103648 | E_var:     0.2375 | E_err:   0.007615
[2025-10-02 15:32:11] [Iter  903/2250] R3[754/1200] | LR: 0.012596 | E: -27.098643 | E_var:     0.2045 | E_err:   0.007066
[2025-10-02 15:32:13] [Iter  904/2250] R3[756/1200] | LR: 0.012536 | E: -27.114498 | E_var:     0.2550 | E_err:   0.007890
[2025-10-02 15:32:16] [Iter  905/2250] R3[758/1200] | LR: 0.012476 | E: -27.096964 | E_var:     0.2382 | E_err:   0.007626
[2025-10-02 15:32:18] [Iter  906/2250] R3[760/1200] | LR: 0.012416 | E: -27.104903 | E_var:     0.2865 | E_err:   0.008364
[2025-10-02 15:32:21] [Iter  907/2250] R3[762/1200] | LR: 0.012356 | E: -27.108144 | E_var:     0.2244 | E_err:   0.007402
[2025-10-02 15:32:23] [Iter  908/2250] R3[764/1200] | LR: 0.012296 | E: -27.109249 | E_var:     0.2310 | E_err:   0.007509
[2025-10-02 15:32:25] [Iter  909/2250] R3[766/1200] | LR: 0.012237 | E: -27.104223 | E_var:     0.2688 | E_err:   0.008100
[2025-10-02 15:32:28] [Iter  910/2250] R3[768/1200] | LR: 0.012178 | E: -27.099377 | E_var:     0.2848 | E_err:   0.008339
[2025-10-02 15:32:30] [Iter  911/2250] R3[770/1200] | LR: 0.012119 | E: -27.109228 | E_var:     0.2093 | E_err:   0.007149
[2025-10-02 15:32:33] [Iter  912/2250] R3[772/1200] | LR: 0.012060 | E: -27.098090 | E_var:     0.2783 | E_err:   0.008243
[2025-10-02 15:32:35] [Iter  913/2250] R3[774/1200] | LR: 0.012001 | E: -27.101963 | E_var:     0.3348 | E_err:   0.009041
[2025-10-02 15:32:37] [Iter  914/2250] R3[776/1200] | LR: 0.011942 | E: -27.091354 | E_var:     0.2991 | E_err:   0.008546
[2025-10-02 15:32:40] [Iter  915/2250] R3[778/1200] | LR: 0.011884 | E: -27.096079 | E_var:     0.3780 | E_err:   0.009607
[2025-10-02 15:32:42] [Iter  916/2250] R3[780/1200] | LR: 0.011825 | E: -27.115137 | E_var:     0.2742 | E_err:   0.008182
[2025-10-02 15:32:45] [Iter  917/2250] R3[782/1200] | LR: 0.011767 | E: -27.099885 | E_var:     0.3699 | E_err:   0.009504
[2025-10-02 15:32:47] [Iter  918/2250] R3[784/1200] | LR: 0.011709 | E: -27.096408 | E_var:     0.2793 | E_err:   0.008258
[2025-10-02 15:32:49] [Iter  919/2250] R3[786/1200] | LR: 0.011651 | E: -27.102018 | E_var:     0.2153 | E_err:   0.007251
[2025-10-02 15:32:52] [Iter  920/2250] R3[788/1200] | LR: 0.011593 | E: -27.086334 | E_var:     0.4577 | E_err:   0.010571
[2025-10-02 15:32:54] [Iter  921/2250] R3[790/1200] | LR: 0.011536 | E: -27.116186 | E_var:     0.2645 | E_err:   0.008036
[2025-10-02 15:32:57] [Iter  922/2250] R3[792/1200] | LR: 0.011478 | E: -27.116475 | E_var:     0.2625 | E_err:   0.008005
[2025-10-02 15:32:59] [Iter  923/2250] R3[794/1200] | LR: 0.011421 | E: -27.129200 | E_var:     0.2554 | E_err:   0.007896
[2025-10-02 15:33:02] [Iter  924/2250] R3[796/1200] | LR: 0.011364 | E: -27.102833 | E_var:     0.2989 | E_err:   0.008543
[2025-10-02 15:33:04] [Iter  925/2250] R3[798/1200] | LR: 0.011307 | E: -27.108938 | E_var:     0.2503 | E_err:   0.007817
[2025-10-02 15:33:06] [Iter  926/2250] R3[800/1200] | LR: 0.011250 | E: -27.110935 | E_var:     0.2596 | E_err:   0.007961
[2025-10-02 15:33:09] [Iter  927/2250] R3[802/1200] | LR: 0.011193 | E: -27.106976 | E_var:     0.2683 | E_err:   0.008094
[2025-10-02 15:33:11] [Iter  928/2250] R3[804/1200] | LR: 0.011137 | E: -27.113767 | E_var:     0.2613 | E_err:   0.007987
[2025-10-02 15:33:14] [Iter  929/2250] R3[806/1200] | LR: 0.011081 | E: -27.106460 | E_var:     0.2503 | E_err:   0.007818
[2025-10-02 15:33:16] [Iter  930/2250] R3[808/1200] | LR: 0.011025 | E: -27.112121 | E_var:     0.3669 | E_err:   0.009464
[2025-10-02 15:33:18] [Iter  931/2250] R3[810/1200] | LR: 0.010969 | E: -27.111673 | E_var:     0.3194 | E_err:   0.008831
[2025-10-02 15:33:21] [Iter  932/2250] R3[812/1200] | LR: 0.010913 | E: -27.102138 | E_var:     0.3013 | E_err:   0.008576
[2025-10-02 15:33:23] [Iter  933/2250] R3[814/1200] | LR: 0.010858 | E: -27.100592 | E_var:     0.2306 | E_err:   0.007504
[2025-10-02 15:33:26] [Iter  934/2250] R3[816/1200] | LR: 0.010802 | E: -27.109542 | E_var:     0.2545 | E_err:   0.007882
[2025-10-02 15:33:28] [Iter  935/2250] R3[818/1200] | LR: 0.010747 | E: -27.099728 | E_var:     0.2594 | E_err:   0.007957
[2025-10-02 15:33:30] [Iter  936/2250] R3[820/1200] | LR: 0.010692 | E: -27.107087 | E_var:     0.3008 | E_err:   0.008570
[2025-10-02 15:33:33] [Iter  937/2250] R3[822/1200] | LR: 0.010637 | E: -27.096754 | E_var:     0.2236 | E_err:   0.007389
[2025-10-02 15:33:35] [Iter  938/2250] R3[824/1200] | LR: 0.010583 | E: -27.097906 | E_var:     0.2800 | E_err:   0.008267
[2025-10-02 15:33:38] [Iter  939/2250] R3[826/1200] | LR: 0.010528 | E: -27.104412 | E_var:     0.4241 | E_err:   0.010175
[2025-10-02 15:33:40] [Iter  940/2250] R3[828/1200] | LR: 0.010474 | E: -27.112812 | E_var:     0.2189 | E_err:   0.007311
[2025-10-02 15:33:42] [Iter  941/2250] R3[830/1200] | LR: 0.010420 | E: -27.104836 | E_var:     0.2358 | E_err:   0.007588
[2025-10-02 15:33:46] [Iter  942/2250] R3[832/1200] | LR: 0.010366 | E: -27.102988 | E_var:     0.2700 | E_err:   0.008118
[2025-10-02 15:33:48] [Iter  943/2250] R3[834/1200] | LR: 0.010312 | E: -27.110401 | E_var:     0.2350 | E_err:   0.007574
[2025-10-02 15:33:51] [Iter  944/2250] R3[836/1200] | LR: 0.010259 | E: -27.114742 | E_var:     0.3953 | E_err:   0.009823
[2025-10-02 15:33:53] [Iter  945/2250] R3[838/1200] | LR: 0.010206 | E: -27.113982 | E_var:     0.2932 | E_err:   0.008461
[2025-10-02 15:33:56] [Iter  946/2250] R3[840/1200] | LR: 0.010153 | E: -27.105068 | E_var:     0.3039 | E_err:   0.008614
[2025-10-02 15:33:58] [Iter  947/2250] R3[842/1200] | LR: 0.010100 | E: -27.111824 | E_var:     0.2140 | E_err:   0.007228
[2025-10-02 15:34:00] [Iter  948/2250] R3[844/1200] | LR: 0.010047 | E: -27.106376 | E_var:     0.2428 | E_err:   0.007700
[2025-10-02 15:34:03] [Iter  949/2250] R3[846/1200] | LR: 0.009995 | E: -27.107198 | E_var:     0.2629 | E_err:   0.008011
[2025-10-02 15:34:05] [Iter  950/2250] R3[848/1200] | LR: 0.009943 | E: -27.101769 | E_var:     0.2148 | E_err:   0.007241
[2025-10-02 15:34:08] [Iter  951/2250] R3[850/1200] | LR: 0.009890 | E: -27.114515 | E_var:     0.2134 | E_err:   0.007218
[2025-10-02 15:34:10] [Iter  952/2250] R3[852/1200] | LR: 0.009839 | E: -27.094749 | E_var:     0.2753 | E_err:   0.008198
[2025-10-02 15:34:12] [Iter  953/2250] R3[854/1200] | LR: 0.009787 | E: -27.109309 | E_var:     0.2379 | E_err:   0.007621
[2025-10-02 15:34:15] [Iter  954/2250] R3[856/1200] | LR: 0.009736 | E: -27.113427 | E_var:     0.2690 | E_err:   0.008104
[2025-10-02 15:34:17] [Iter  955/2250] R3[858/1200] | LR: 0.009684 | E: -27.095970 | E_var:     0.2355 | E_err:   0.007583
[2025-10-02 15:34:20] [Iter  956/2250] R3[860/1200] | LR: 0.009633 | E: -27.116527 | E_var:     0.2350 | E_err:   0.007575
[2025-10-02 15:34:22] [Iter  957/2250] R3[862/1200] | LR: 0.009583 | E: -27.106891 | E_var:     0.3048 | E_err:   0.008627
[2025-10-02 15:34:24] [Iter  958/2250] R3[864/1200] | LR: 0.009532 | E: -27.109199 | E_var:     0.2933 | E_err:   0.008463
[2025-10-02 15:34:27] [Iter  959/2250] R3[866/1200] | LR: 0.009482 | E: -27.092424 | E_var:     0.3180 | E_err:   0.008811
[2025-10-02 15:34:30] [Iter  960/2250] R3[868/1200] | LR: 0.009432 | E: -27.118986 | E_var:     0.2796 | E_err:   0.008262
[2025-10-02 15:34:33] [Iter  961/2250] R3[870/1200] | LR: 0.009382 | E: -27.095326 | E_var:     0.2449 | E_err:   0.007732
[2025-10-02 15:34:35] [Iter  962/2250] R3[872/1200] | LR: 0.009332 | E: -27.106521 | E_var:     0.3161 | E_err:   0.008785
[2025-10-02 15:34:38] [Iter  963/2250] R3[874/1200] | LR: 0.009283 | E: -27.116522 | E_var:     0.2564 | E_err:   0.007912
[2025-10-02 15:34:40] [Iter  964/2250] R3[876/1200] | LR: 0.009234 | E: -27.099567 | E_var:     0.2747 | E_err:   0.008190
[2025-10-02 15:34:42] [Iter  965/2250] R3[878/1200] | LR: 0.009185 | E: -27.118334 | E_var:     0.2352 | E_err:   0.007577
[2025-10-02 15:34:45] [Iter  966/2250] R3[880/1200] | LR: 0.009136 | E: -27.110633 | E_var:     0.1966 | E_err:   0.006928
[2025-10-02 15:34:47] [Iter  967/2250] R3[882/1200] | LR: 0.009087 | E: -27.115297 | E_var:     0.2186 | E_err:   0.007306
[2025-10-02 15:34:50] [Iter  968/2250] R3[884/1200] | LR: 0.009039 | E: -27.100090 | E_var:     0.2307 | E_err:   0.007504
[2025-10-02 15:34:52] [Iter  969/2250] R3[886/1200] | LR: 0.008991 | E: -27.092887 | E_var:     0.2580 | E_err:   0.007936
[2025-10-02 15:34:54] [Iter  970/2250] R3[888/1200] | LR: 0.008943 | E: -27.100359 | E_var:     0.3103 | E_err:   0.008704
[2025-10-02 15:34:57] [Iter  971/2250] R3[890/1200] | LR: 0.008896 | E: -27.105416 | E_var:     0.2534 | E_err:   0.007865
[2025-10-02 15:34:59] [Iter  972/2250] R3[892/1200] | LR: 0.008848 | E: -27.122112 | E_var:     0.3310 | E_err:   0.008989
[2025-10-02 15:35:02] [Iter  973/2250] R3[894/1200] | LR: 0.008801 | E: -27.104383 | E_var:     0.2837 | E_err:   0.008322
[2025-10-02 15:35:04] [Iter  974/2250] R3[896/1200] | LR: 0.008754 | E: -27.107996 | E_var:     0.3150 | E_err:   0.008769
[2025-10-02 15:35:06] [Iter  975/2250] R3[898/1200] | LR: 0.008708 | E: -27.093024 | E_var:     0.2873 | E_err:   0.008375
[2025-10-02 15:35:09] [Iter  976/2250] R3[900/1200] | LR: 0.008661 | E: -27.114821 | E_var:     0.3035 | E_err:   0.008608
[2025-10-02 15:35:11] [Iter  977/2250] R3[902/1200] | LR: 0.008615 | E: -27.111521 | E_var:     0.2797 | E_err:   0.008264
[2025-10-02 15:35:14] [Iter  978/2250] R3[904/1200] | LR: 0.008569 | E: -27.121755 | E_var:     0.2728 | E_err:   0.008162
[2025-10-02 15:35:16] [Iter  979/2250] R3[906/1200] | LR: 0.008523 | E: -27.098859 | E_var:     0.2974 | E_err:   0.008521
[2025-10-02 15:35:18] [Iter  980/2250] R3[908/1200] | LR: 0.008478 | E: -27.099904 | E_var:     0.2313 | E_err:   0.007515
[2025-10-02 15:35:21] [Iter  981/2250] R3[910/1200] | LR: 0.008433 | E: -27.097063 | E_var:     0.2189 | E_err:   0.007310
[2025-10-02 15:35:23] [Iter  982/2250] R3[912/1200] | LR: 0.008388 | E: -27.108199 | E_var:     0.2431 | E_err:   0.007704
[2025-10-02 15:35:26] [Iter  983/2250] R3[914/1200] | LR: 0.008343 | E: -27.106841 | E_var:     0.2338 | E_err:   0.007556
[2025-10-02 15:35:28] [Iter  984/2250] R3[916/1200] | LR: 0.008299 | E: -27.109185 | E_var:     0.2335 | E_err:   0.007550
[2025-10-02 15:35:30] [Iter  985/2250] R3[918/1200] | LR: 0.008255 | E: -27.113059 | E_var:     0.2353 | E_err:   0.007579
[2025-10-02 15:35:33] [Iter  986/2250] R3[920/1200] | LR: 0.008211 | E: -27.094560 | E_var:     0.3093 | E_err:   0.008690
[2025-10-02 15:35:35] [Iter  987/2250] R3[922/1200] | LR: 0.008167 | E: -27.107486 | E_var:     0.2670 | E_err:   0.008074
[2025-10-02 15:35:38] [Iter  988/2250] R3[924/1200] | LR: 0.008124 | E: -27.094843 | E_var:     0.2623 | E_err:   0.008002
[2025-10-02 15:35:40] [Iter  989/2250] R3[926/1200] | LR: 0.008080 | E: -27.085976 | E_var:     0.2796 | E_err:   0.008262
[2025-10-02 15:35:42] [Iter  990/2250] R3[928/1200] | LR: 0.008038 | E: -27.122209 | E_var:     0.2911 | E_err:   0.008430
[2025-10-02 15:35:45] [Iter  991/2250] R3[930/1200] | LR: 0.007995 | E: -27.106131 | E_var:     0.2306 | E_err:   0.007503
[2025-10-02 15:35:47] [Iter  992/2250] R3[932/1200] | LR: 0.007953 | E: -27.106670 | E_var:     0.2175 | E_err:   0.007287
[2025-10-02 15:35:50] [Iter  993/2250] R3[934/1200] | LR: 0.007910 | E: -27.117541 | E_var:     0.2980 | E_err:   0.008529
[2025-10-02 15:35:52] [Iter  994/2250] R3[936/1200] | LR: 0.007869 | E: -27.109326 | E_var:     0.2065 | E_err:   0.007100
[2025-10-02 15:35:55] [Iter  995/2250] R3[938/1200] | LR: 0.007827 | E: -27.107122 | E_var:     0.2308 | E_err:   0.007506
[2025-10-02 15:35:57] [Iter  996/2250] R3[940/1200] | LR: 0.007786 | E: -27.102831 | E_var:     0.2344 | E_err:   0.007565
[2025-10-02 15:35:59] [Iter  997/2250] R3[942/1200] | LR: 0.007745 | E: -27.106976 | E_var:     0.3040 | E_err:   0.008616
[2025-10-02 15:36:02] [Iter  998/2250] R3[944/1200] | LR: 0.007704 | E: -27.103118 | E_var:     0.2422 | E_err:   0.007690
[2025-10-02 15:36:04] [Iter  999/2250] R3[946/1200] | LR: 0.007663 | E: -27.122441 | E_var:     0.2278 | E_err:   0.007458
[2025-10-02 15:36:07] [Iter 1000/2250] R3[948/1200] | LR: 0.007623 | E: -27.115893 | E_var:     0.2913 | E_err:   0.008433
[2025-10-02 15:36:07] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-02 15:36:09] [Iter 1001/2250] R3[950/1200] | LR: 0.007583 | E: -27.112668 | E_var:     0.2165 | E_err:   0.007270
[2025-10-02 15:36:12] [Iter 1002/2250] R3[952/1200] | LR: 0.007543 | E: -27.113802 | E_var:     0.2223 | E_err:   0.007367
[2025-10-02 15:36:14] [Iter 1003/2250] R3[954/1200] | LR: 0.007504 | E: -27.107400 | E_var:     0.3380 | E_err:   0.009084
[2025-10-02 15:36:16] [Iter 1004/2250] R3[956/1200] | LR: 0.007465 | E: -27.107314 | E_var:     0.2630 | E_err:   0.008013
[2025-10-02 15:36:19] [Iter 1005/2250] R3[958/1200] | LR: 0.007426 | E: -27.122245 | E_var:     0.2813 | E_err:   0.008288
[2025-10-02 15:36:21] [Iter 1006/2250] R3[960/1200] | LR: 0.007387 | E: -27.108987 | E_var:     0.2221 | E_err:   0.007364
[2025-10-02 15:36:24] [Iter 1007/2250] R3[962/1200] | LR: 0.007349 | E: -27.109086 | E_var:     0.3044 | E_err:   0.008620
[2025-10-02 15:36:26] [Iter 1008/2250] R3[964/1200] | LR: 0.007311 | E: -27.108345 | E_var:     0.2402 | E_err:   0.007657
[2025-10-02 15:36:28] [Iter 1009/2250] R3[966/1200] | LR: 0.007273 | E: -27.100141 | E_var:     0.2809 | E_err:   0.008281
[2025-10-02 15:36:31] [Iter 1010/2250] R3[968/1200] | LR: 0.007236 | E: -27.103325 | E_var:     0.2460 | E_err:   0.007750
[2025-10-02 15:36:33] [Iter 1011/2250] R3[970/1200] | LR: 0.007198 | E: -27.114458 | E_var:     0.3352 | E_err:   0.009046
[2025-10-02 15:36:36] [Iter 1012/2250] R3[972/1200] | LR: 0.007161 | E: -27.113560 | E_var:     0.2794 | E_err:   0.008259
[2025-10-02 15:36:38] [Iter 1013/2250] R3[974/1200] | LR: 0.007125 | E: -27.100168 | E_var:     0.2384 | E_err:   0.007629
[2025-10-02 15:36:40] [Iter 1014/2250] R3[976/1200] | LR: 0.007088 | E: -27.105303 | E_var:     0.2535 | E_err:   0.007867
[2025-10-02 15:36:43] [Iter 1015/2250] R3[978/1200] | LR: 0.007052 | E: -27.105565 | E_var:     0.2107 | E_err:   0.007172
[2025-10-02 15:36:45] [Iter 1016/2250] R3[980/1200] | LR: 0.007017 | E: -27.108556 | E_var:     0.4110 | E_err:   0.010017
[2025-10-02 15:36:48] [Iter 1017/2250] R3[982/1200] | LR: 0.006981 | E: -27.113598 | E_var:     0.2018 | E_err:   0.007019
[2025-10-02 15:36:50] [Iter 1018/2250] R3[984/1200] | LR: 0.006946 | E: -27.095658 | E_var:     0.2961 | E_err:   0.008502
[2025-10-02 15:36:52] [Iter 1019/2250] R3[986/1200] | LR: 0.006911 | E: -27.100821 | E_var:     0.2609 | E_err:   0.007980
[2025-10-02 15:36:55] [Iter 1020/2250] R3[988/1200] | LR: 0.006876 | E: -27.102464 | E_var:     0.2770 | E_err:   0.008223
[2025-10-02 15:36:57] [Iter 1021/2250] R3[990/1200] | LR: 0.006842 | E: -27.109627 | E_var:     0.2169 | E_err:   0.007277
[2025-10-02 15:37:00] [Iter 1022/2250] R3[992/1200] | LR: 0.006808 | E: -27.111946 | E_var:     0.2249 | E_err:   0.007410
[2025-10-02 15:37:02] [Iter 1023/2250] R3[994/1200] | LR: 0.006774 | E: -27.108683 | E_var:     0.2155 | E_err:   0.007253
[2025-10-02 15:37:04] [Iter 1024/2250] R3[996/1200] | LR: 0.006741 | E: -27.104856 | E_var:     0.2105 | E_err:   0.007169
[2025-10-02 15:37:07] [Iter 1025/2250] R3[998/1200] | LR: 0.006708 | E: -27.116300 | E_var:     0.2669 | E_err:   0.008072
[2025-10-02 15:37:09] [Iter 1026/2250] R3[1000/1200] | LR: 0.006675 | E: -27.109229 | E_var:     0.2904 | E_err:   0.008421
[2025-10-02 15:37:12] [Iter 1027/2250] R3[1002/1200] | LR: 0.006642 | E: -27.105841 | E_var:     0.2102 | E_err:   0.007164
[2025-10-02 15:37:14] [Iter 1028/2250] R3[1004/1200] | LR: 0.006610 | E: -27.114022 | E_var:     0.2773 | E_err:   0.008228
[2025-10-02 15:37:16] [Iter 1029/2250] R3[1006/1200] | LR: 0.006578 | E: -27.115476 | E_var:     0.2844 | E_err:   0.008332
[2025-10-02 15:37:19] [Iter 1030/2250] R3[1008/1200] | LR: 0.006546 | E: -27.106698 | E_var:     0.2173 | E_err:   0.007284
[2025-10-02 15:37:21] [Iter 1031/2250] R3[1010/1200] | LR: 0.006515 | E: -27.098891 | E_var:     0.2180 | E_err:   0.007295
[2025-10-02 15:37:24] [Iter 1032/2250] R3[1012/1200] | LR: 0.006484 | E: -27.101152 | E_var:     0.2584 | E_err:   0.007942
[2025-10-02 15:37:26] [Iter 1033/2250] R3[1014/1200] | LR: 0.006453 | E: -27.099395 | E_var:     0.3209 | E_err:   0.008852
[2025-10-02 15:37:28] [Iter 1034/2250] R3[1016/1200] | LR: 0.006422 | E: -27.113635 | E_var:     0.2880 | E_err:   0.008385
[2025-10-02 15:37:31] [Iter 1035/2250] R3[1018/1200] | LR: 0.006392 | E: -27.120556 | E_var:     0.2360 | E_err:   0.007591
[2025-10-02 15:37:33] [Iter 1036/2250] R3[1020/1200] | LR: 0.006362 | E: -27.125767 | E_var:     0.2812 | E_err:   0.008286
[2025-10-02 15:37:36] [Iter 1037/2250] R3[1022/1200] | LR: 0.006333 | E: -27.102532 | E_var:     0.5275 | E_err:   0.011348
[2025-10-02 15:37:38] [Iter 1038/2250] R3[1024/1200] | LR: 0.006304 | E: -27.113768 | E_var:     0.2453 | E_err:   0.007739
[2025-10-02 15:37:40] [Iter 1039/2250] R3[1026/1200] | LR: 0.006275 | E: -27.111091 | E_var:     0.2071 | E_err:   0.007111
[2025-10-02 15:37:43] [Iter 1040/2250] R3[1028/1200] | LR: 0.006246 | E: -27.105304 | E_var:     0.2177 | E_err:   0.007290
[2025-10-02 15:37:45] [Iter 1041/2250] R3[1030/1200] | LR: 0.006218 | E: -27.097567 | E_var:     0.2958 | E_err:   0.008498
[2025-10-02 15:37:48] [Iter 1042/2250] R3[1032/1200] | LR: 0.006190 | E: -27.105223 | E_var:     0.1935 | E_err:   0.006873
[2025-10-02 15:37:50] [Iter 1043/2250] R3[1034/1200] | LR: 0.006162 | E: -27.107713 | E_var:     0.2888 | E_err:   0.008396
[2025-10-02 15:37:52] [Iter 1044/2250] R3[1036/1200] | LR: 0.006135 | E: -27.115500 | E_var:     0.2086 | E_err:   0.007137
[2025-10-02 15:37:55] [Iter 1045/2250] R3[1038/1200] | LR: 0.006107 | E: -27.105170 | E_var:     0.2365 | E_err:   0.007599
[2025-10-02 15:37:57] [Iter 1046/2250] R3[1040/1200] | LR: 0.006081 | E: -27.114072 | E_var:     0.2347 | E_err:   0.007570
[2025-10-02 15:38:00] [Iter 1047/2250] R3[1042/1200] | LR: 0.006054 | E: -27.107204 | E_var:     0.2205 | E_err:   0.007338
[2025-10-02 15:38:02] [Iter 1048/2250] R3[1044/1200] | LR: 0.006028 | E: -27.101777 | E_var:     0.3107 | E_err:   0.008709
[2025-10-02 15:38:04] [Iter 1049/2250] R3[1046/1200] | LR: 0.006002 | E: -27.123542 | E_var:     0.2819 | E_err:   0.008296
[2025-10-02 15:38:07] [Iter 1050/2250] R3[1048/1200] | LR: 0.005977 | E: -27.098063 | E_var:     0.2383 | E_err:   0.007628
[2025-10-02 15:38:09] [Iter 1051/2250] R3[1050/1200] | LR: 0.005952 | E: -27.109927 | E_var:     0.3105 | E_err:   0.008706
[2025-10-02 15:38:12] [Iter 1052/2250] R3[1052/1200] | LR: 0.005927 | E: -27.104712 | E_var:     0.2105 | E_err:   0.007168
[2025-10-02 15:38:14] [Iter 1053/2250] R3[1054/1200] | LR: 0.005902 | E: -27.107652 | E_var:     0.2765 | E_err:   0.008216
[2025-10-02 15:38:16] [Iter 1054/2250] R3[1056/1200] | LR: 0.005878 | E: -27.107638 | E_var:     0.2273 | E_err:   0.007450
[2025-10-02 15:38:19] [Iter 1055/2250] R3[1058/1200] | LR: 0.005854 | E: -27.115787 | E_var:     0.2899 | E_err:   0.008413
[2025-10-02 15:38:21] [Iter 1056/2250] R3[1060/1200] | LR: 0.005830 | E: -27.107460 | E_var:     0.2346 | E_err:   0.007569
[2025-10-02 15:38:24] [Iter 1057/2250] R3[1062/1200] | LR: 0.005807 | E: -27.113336 | E_var:     0.2108 | E_err:   0.007173
[2025-10-02 15:38:26] [Iter 1058/2250] R3[1064/1200] | LR: 0.005784 | E: -27.106372 | E_var:     0.2468 | E_err:   0.007762
[2025-10-02 15:38:28] [Iter 1059/2250] R3[1066/1200] | LR: 0.005761 | E: -27.103148 | E_var:     0.2219 | E_err:   0.007360
[2025-10-02 15:38:31] [Iter 1060/2250] R3[1068/1200] | LR: 0.005739 | E: -27.104550 | E_var:     0.2323 | E_err:   0.007531
[2025-10-02 15:38:33] [Iter 1061/2250] R3[1070/1200] | LR: 0.005717 | E: -27.109102 | E_var:     0.2999 | E_err:   0.008557
[2025-10-02 15:38:36] [Iter 1062/2250] R3[1072/1200] | LR: 0.005695 | E: -27.105997 | E_var:     0.2832 | E_err:   0.008315
[2025-10-02 15:38:38] [Iter 1063/2250] R3[1074/1200] | LR: 0.005674 | E: -27.107164 | E_var:     0.2800 | E_err:   0.008269
[2025-10-02 15:38:40] [Iter 1064/2250] R3[1076/1200] | LR: 0.005653 | E: -27.111990 | E_var:     0.2712 | E_err:   0.008137
[2025-10-02 15:38:43] [Iter 1065/2250] R3[1078/1200] | LR: 0.005632 | E: -27.108415 | E_var:     0.3168 | E_err:   0.008794
[2025-10-02 15:38:45] [Iter 1066/2250] R3[1080/1200] | LR: 0.005612 | E: -27.104443 | E_var:     0.2098 | E_err:   0.007156
[2025-10-02 15:38:48] [Iter 1067/2250] R3[1082/1200] | LR: 0.005592 | E: -27.114610 | E_var:     0.2316 | E_err:   0.007519
[2025-10-02 15:38:50] [Iter 1068/2250] R3[1084/1200] | LR: 0.005572 | E: -27.103684 | E_var:     0.2903 | E_err:   0.008418
[2025-10-02 15:38:52] [Iter 1069/2250] R3[1086/1200] | LR: 0.005553 | E: -27.111564 | E_var:     0.2390 | E_err:   0.007638
[2025-10-02 15:38:55] [Iter 1070/2250] R3[1088/1200] | LR: 0.005534 | E: -27.104047 | E_var:     0.3577 | E_err:   0.009345
[2025-10-02 15:38:59] [Iter 1071/2250] R3[1090/1200] | LR: 0.005515 | E: -27.122514 | E_var:     0.1999 | E_err:   0.006987
[2025-10-02 15:39:01] [Iter 1072/2250] R3[1092/1200] | LR: 0.005496 | E: -27.120894 | E_var:     0.2199 | E_err:   0.007327
[2025-10-02 15:39:04] [Iter 1073/2250] R3[1094/1200] | LR: 0.005478 | E: -27.110503 | E_var:     0.2564 | E_err:   0.007913
[2025-10-02 15:39:06] [Iter 1074/2250] R3[1096/1200] | LR: 0.005460 | E: -27.113246 | E_var:     0.2175 | E_err:   0.007288
[2025-10-02 15:39:08] [Iter 1075/2250] R3[1098/1200] | LR: 0.005443 | E: -27.102253 | E_var:     0.3410 | E_err:   0.009124
[2025-10-02 15:39:11] [Iter 1076/2250] R3[1100/1200] | LR: 0.005426 | E: -27.106898 | E_var:     0.2552 | E_err:   0.007894
[2025-10-02 15:39:13] [Iter 1077/2250] R3[1102/1200] | LR: 0.005409 | E: -27.115259 | E_var:     0.2221 | E_err:   0.007364
[2025-10-02 15:39:16] [Iter 1078/2250] R3[1104/1200] | LR: 0.005393 | E: -27.106449 | E_var:     0.2805 | E_err:   0.008275
[2025-10-02 15:39:18] [Iter 1079/2250] R3[1106/1200] | LR: 0.005377 | E: -27.111702 | E_var:     0.2074 | E_err:   0.007115
[2025-10-02 15:39:20] [Iter 1080/2250] R3[1108/1200] | LR: 0.005361 | E: -27.106138 | E_var:     0.2401 | E_err:   0.007656
[2025-10-02 15:39:23] [Iter 1081/2250] R3[1110/1200] | LR: 0.005345 | E: -27.122548 | E_var:     0.1938 | E_err:   0.006879
[2025-10-02 15:39:25] [Iter 1082/2250] R3[1112/1200] | LR: 0.005330 | E: -27.098569 | E_var:     0.2667 | E_err:   0.008069
[2025-10-02 15:39:27] [Iter 1083/2250] R3[1114/1200] | LR: 0.005315 | E: -27.110657 | E_var:     0.2422 | E_err:   0.007689
[2025-10-02 15:39:30] [Iter 1084/2250] R3[1116/1200] | LR: 0.005301 | E: -27.101793 | E_var:     0.2161 | E_err:   0.007264
[2025-10-02 15:39:32] [Iter 1085/2250] R3[1118/1200] | LR: 0.005287 | E: -27.104041 | E_var:     0.2260 | E_err:   0.007428
[2025-10-02 15:39:35] [Iter 1086/2250] R3[1120/1200] | LR: 0.005273 | E: -27.121120 | E_var:     0.2918 | E_err:   0.008441
[2025-10-02 15:39:37] [Iter 1087/2250] R3[1122/1200] | LR: 0.005260 | E: -27.099837 | E_var:     0.3094 | E_err:   0.008691
[2025-10-02 15:39:40] [Iter 1088/2250] R3[1124/1200] | LR: 0.005247 | E: -27.109273 | E_var:     0.2859 | E_err:   0.008355
[2025-10-02 15:39:42] [Iter 1089/2250] R3[1126/1200] | LR: 0.005234 | E: -27.105996 | E_var:     0.2091 | E_err:   0.007146
[2025-10-02 15:39:44] [Iter 1090/2250] R3[1128/1200] | LR: 0.005221 | E: -27.116767 | E_var:     0.2357 | E_err:   0.007586
[2025-10-02 15:39:47] [Iter 1091/2250] R3[1130/1200] | LR: 0.005209 | E: -27.115527 | E_var:     0.2440 | E_err:   0.007718
[2025-10-02 15:39:49] [Iter 1092/2250] R3[1132/1200] | LR: 0.005198 | E: -27.122635 | E_var:     0.2833 | E_err:   0.008317
[2025-10-02 15:39:52] [Iter 1093/2250] R3[1134/1200] | LR: 0.005186 | E: -27.098779 | E_var:     0.2085 | E_err:   0.007134
[2025-10-02 15:39:54] [Iter 1094/2250] R3[1136/1200] | LR: 0.005175 | E: -27.119260 | E_var:     0.1974 | E_err:   0.006943
[2025-10-02 15:39:56] [Iter 1095/2250] R3[1138/1200] | LR: 0.005164 | E: -27.112476 | E_var:     0.2183 | E_err:   0.007301
[2025-10-02 15:39:59] [Iter 1096/2250] R3[1140/1200] | LR: 0.005154 | E: -27.110538 | E_var:     0.2201 | E_err:   0.007330
[2025-10-02 15:40:01] [Iter 1097/2250] R3[1142/1200] | LR: 0.005144 | E: -27.117531 | E_var:     0.2457 | E_err:   0.007745
[2025-10-02 15:40:04] [Iter 1098/2250] R3[1144/1200] | LR: 0.005134 | E: -27.102275 | E_var:     0.2173 | E_err:   0.007284
[2025-10-02 15:40:06] [Iter 1099/2250] R3[1146/1200] | LR: 0.005125 | E: -27.106755 | E_var:     0.2315 | E_err:   0.007519
[2025-10-02 15:40:09] [Iter 1100/2250] R3[1148/1200] | LR: 0.005116 | E: -27.119465 | E_var:     0.2372 | E_err:   0.007610
[2025-10-02 15:40:12] [Iter 1101/2250] R3[1150/1200] | LR: 0.005107 | E: -27.114014 | E_var:     0.2451 | E_err:   0.007735
[2025-10-02 15:40:14] [Iter 1102/2250] R3[1152/1200] | LR: 0.005099 | E: -27.092952 | E_var:     0.1861 | E_err:   0.006741
[2025-10-02 15:40:17] [Iter 1103/2250] R3[1154/1200] | LR: 0.005091 | E: -27.124381 | E_var:     0.2456 | E_err:   0.007744
[2025-10-02 15:40:19] [Iter 1104/2250] R3[1156/1200] | LR: 0.005083 | E: -27.109903 | E_var:     0.3109 | E_err:   0.008713
[2025-10-02 15:40:21] [Iter 1105/2250] R3[1158/1200] | LR: 0.005075 | E: -27.113780 | E_var:     0.1994 | E_err:   0.006977
[2025-10-02 15:40:24] [Iter 1106/2250] R3[1160/1200] | LR: 0.005068 | E: -27.097095 | E_var:     0.2898 | E_err:   0.008412
[2025-10-02 15:40:26] [Iter 1107/2250] R3[1162/1200] | LR: 0.005062 | E: -27.095529 | E_var:     0.3935 | E_err:   0.009802
[2025-10-02 15:40:29] [Iter 1108/2250] R3[1164/1200] | LR: 0.005055 | E: -27.105326 | E_var:     0.2860 | E_err:   0.008357
[2025-10-02 15:40:31] [Iter 1109/2250] R3[1166/1200] | LR: 0.005049 | E: -27.107100 | E_var:     0.2264 | E_err:   0.007434
[2025-10-02 15:40:33] [Iter 1110/2250] R3[1168/1200] | LR: 0.005044 | E: -27.102734 | E_var:     0.2525 | E_err:   0.007851
[2025-10-02 15:40:36] [Iter 1111/2250] R3[1170/1200] | LR: 0.005039 | E: -27.109845 | E_var:     0.2202 | E_err:   0.007332
[2025-10-02 15:40:38] [Iter 1112/2250] R3[1172/1200] | LR: 0.005034 | E: -27.109268 | E_var:     0.2743 | E_err:   0.008183
[2025-10-02 15:40:41] [Iter 1113/2250] R3[1174/1200] | LR: 0.005029 | E: -27.089377 | E_var:     0.2007 | E_err:   0.006999
[2025-10-02 15:40:43] [Iter 1114/2250] R3[1176/1200] | LR: 0.005025 | E: -27.115374 | E_var:     0.2898 | E_err:   0.008411
[2025-10-02 15:40:45] [Iter 1115/2250] R3[1178/1200] | LR: 0.005021 | E: -27.112550 | E_var:     0.1985 | E_err:   0.006961
[2025-10-02 15:40:48] [Iter 1116/2250] R3[1180/1200] | LR: 0.005017 | E: -27.114112 | E_var:     0.2281 | E_err:   0.007462
[2025-10-02 15:40:50] [Iter 1117/2250] R3[1182/1200] | LR: 0.005014 | E: -27.111708 | E_var:     0.2469 | E_err:   0.007763
[2025-10-02 15:40:53] [Iter 1118/2250] R3[1184/1200] | LR: 0.005011 | E: -27.121551 | E_var:     0.2130 | E_err:   0.007211
[2025-10-02 15:40:55] [Iter 1119/2250] R3[1186/1200] | LR: 0.005008 | E: -27.113568 | E_var:     0.2271 | E_err:   0.007447
[2025-10-02 15:40:57] [Iter 1120/2250] R3[1188/1200] | LR: 0.005006 | E: -27.117296 | E_var:     0.2456 | E_err:   0.007743
[2025-10-02 15:41:00] [Iter 1121/2250] R3[1190/1200] | LR: 0.005004 | E: -27.099900 | E_var:     0.2723 | E_err:   0.008153
[2025-10-02 15:41:02] [Iter 1122/2250] R3[1192/1200] | LR: 0.005003 | E: -27.130511 | E_var:     0.3348 | E_err:   0.009041
[2025-10-02 15:41:05] [Iter 1123/2250] R3[1194/1200] | LR: 0.005002 | E: -27.109990 | E_var:     0.2353 | E_err:   0.007579
[2025-10-02 15:41:07] [Iter 1124/2250] R3[1196/1200] | LR: 0.005001 | E: -27.111119 | E_var:     0.2590 | E_err:   0.007952
[2025-10-02 15:41:09] [Iter 1125/2250] R3[1198/1200] | LR: 0.005000 | E: -27.123958 | E_var:     0.2627 | E_err:   0.008009
[2025-10-02 15:41:09] 🔄 RESTART #4 | Period: 2400
[2025-10-02 15:41:12] [Iter 1126/2250] R4[0/2400]   | LR: 0.030000 | E: -27.111663 | E_var:     0.2760 | E_err:   0.008209
[2025-10-02 15:41:14] [Iter 1127/2250] R4[2/2400]   | LR: 0.030000 | E: -27.107169 | E_var:     0.2274 | E_err:   0.007451
[2025-10-02 15:41:17] [Iter 1128/2250] R4[4/2400]   | LR: 0.030000 | E: -27.115750 | E_var:     0.2168 | E_err:   0.007275
[2025-10-02 15:41:19] [Iter 1129/2250] R4[6/2400]   | LR: 0.030000 | E: -27.102126 | E_var:     0.4576 | E_err:   0.010569
[2025-10-02 15:41:21] [Iter 1130/2250] R4[8/2400]   | LR: 0.029999 | E: -27.111830 | E_var:     0.2165 | E_err:   0.007270
[2025-10-02 15:41:24] [Iter 1131/2250] R4[10/2400]  | LR: 0.029999 | E: -27.100260 | E_var:     0.2599 | E_err:   0.007966
[2025-10-02 15:41:27] [Iter 1132/2250] R4[12/2400]  | LR: 0.029998 | E: -27.101694 | E_var:     0.2283 | E_err:   0.007466
[2025-10-02 15:41:29] [Iter 1133/2250] R4[14/2400]  | LR: 0.029998 | E: -27.107112 | E_var:     0.2445 | E_err:   0.007726
[2025-10-02 15:41:32] [Iter 1134/2250] R4[16/2400]  | LR: 0.029997 | E: -27.102561 | E_var:     0.2369 | E_err:   0.007605
[2025-10-02 15:41:34] [Iter 1135/2250] R4[18/2400]  | LR: 0.029997 | E: -27.109190 | E_var:     0.1761 | E_err:   0.006557
[2025-10-02 15:41:37] [Iter 1136/2250] R4[20/2400]  | LR: 0.029996 | E: -27.109732 | E_var:     0.2203 | E_err:   0.007334
[2025-10-02 15:41:39] [Iter 1137/2250] R4[22/2400]  | LR: 0.029995 | E: -27.102554 | E_var:     0.2440 | E_err:   0.007718
[2025-10-02 15:41:41] [Iter 1138/2250] R4[24/2400]  | LR: 0.029994 | E: -27.110479 | E_var:     0.2008 | E_err:   0.007001
[2025-10-02 15:41:44] [Iter 1139/2250] R4[26/2400]  | LR: 0.029993 | E: -27.121081 | E_var:     0.2302 | E_err:   0.007497
[2025-10-02 15:41:46] [Iter 1140/2250] R4[28/2400]  | LR: 0.029992 | E: -27.105250 | E_var:     0.2200 | E_err:   0.007329
[2025-10-02 15:41:49] [Iter 1141/2250] R4[30/2400]  | LR: 0.029990 | E: -27.097018 | E_var:     0.2932 | E_err:   0.008460
[2025-10-02 15:41:51] [Iter 1142/2250] R4[32/2400]  | LR: 0.029989 | E: -27.113755 | E_var:     0.2163 | E_err:   0.007267
[2025-10-02 15:41:53] [Iter 1143/2250] R4[34/2400]  | LR: 0.029988 | E: -27.112793 | E_var:     0.2251 | E_err:   0.007413
[2025-10-02 15:41:56] [Iter 1144/2250] R4[36/2400]  | LR: 0.029986 | E: -27.108214 | E_var:     0.3371 | E_err:   0.009072
[2025-10-02 15:41:58] [Iter 1145/2250] R4[38/2400]  | LR: 0.029985 | E: -27.113993 | E_var:     0.2168 | E_err:   0.007276
[2025-10-02 15:42:01] [Iter 1146/2250] R4[40/2400]  | LR: 0.029983 | E: -27.101320 | E_var:     0.2702 | E_err:   0.008122
[2025-10-02 15:42:03] [Iter 1147/2250] R4[42/2400]  | LR: 0.029981 | E: -27.106388 | E_var:     0.2285 | E_err:   0.007469
[2025-10-02 15:42:05] [Iter 1148/2250] R4[44/2400]  | LR: 0.029979 | E: -27.110909 | E_var:     0.2275 | E_err:   0.007453
[2025-10-02 15:42:08] [Iter 1149/2250] R4[46/2400]  | LR: 0.029977 | E: -27.120064 | E_var:     0.2728 | E_err:   0.008161
[2025-10-02 15:42:10] [Iter 1150/2250] R4[48/2400]  | LR: 0.029975 | E: -27.105192 | E_var:     0.3235 | E_err:   0.008887
[2025-10-02 15:42:13] [Iter 1151/2250] R4[50/2400]  | LR: 0.029973 | E: -27.104221 | E_var:     0.2203 | E_err:   0.007335
[2025-10-02 15:42:15] [Iter 1152/2250] R4[52/2400]  | LR: 0.029971 | E: -27.108608 | E_var:     0.2595 | E_err:   0.007960
[2025-10-02 15:42:17] [Iter 1153/2250] R4[54/2400]  | LR: 0.029969 | E: -27.123484 | E_var:     0.2005 | E_err:   0.006996
[2025-10-02 15:42:20] [Iter 1154/2250] R4[56/2400]  | LR: 0.029966 | E: -27.120314 | E_var:     0.1977 | E_err:   0.006947
[2025-10-02 15:42:22] [Iter 1155/2250] R4[58/2400]  | LR: 0.029964 | E: -27.115212 | E_var:     0.1697 | E_err:   0.006438
[2025-10-02 15:42:25] [Iter 1156/2250] R4[60/2400]  | LR: 0.029961 | E: -27.118848 | E_var:     0.2573 | E_err:   0.007926
[2025-10-02 15:42:27] [Iter 1157/2250] R4[62/2400]  | LR: 0.029959 | E: -27.113926 | E_var:     0.2081 | E_err:   0.007128
[2025-10-02 15:42:29] [Iter 1158/2250] R4[64/2400]  | LR: 0.029956 | E: -27.117228 | E_var:     0.2943 | E_err:   0.008477
[2025-10-02 15:42:32] [Iter 1159/2250] R4[66/2400]  | LR: 0.029953 | E: -27.119139 | E_var:     0.1909 | E_err:   0.006828
[2025-10-02 15:42:34] [Iter 1160/2250] R4[68/2400]  | LR: 0.029951 | E: -27.109393 | E_var:     0.2132 | E_err:   0.007214
[2025-10-02 15:42:37] [Iter 1161/2250] R4[70/2400]  | LR: 0.029948 | E: -27.102184 | E_var:     0.2744 | E_err:   0.008184
[2025-10-02 15:42:39] [Iter 1162/2250] R4[72/2400]  | LR: 0.029945 | E: -27.116386 | E_var:     0.1906 | E_err:   0.006822
[2025-10-02 15:42:41] [Iter 1163/2250] R4[74/2400]  | LR: 0.029941 | E: -27.115487 | E_var:     0.2780 | E_err:   0.008238
[2025-10-02 15:42:44] [Iter 1164/2250] R4[76/2400]  | LR: 0.029938 | E: -27.108628 | E_var:     0.2963 | E_err:   0.008505
[2025-10-02 15:42:46] [Iter 1165/2250] R4[78/2400]  | LR: 0.029935 | E: -27.106260 | E_var:     0.1778 | E_err:   0.006588
[2025-10-02 15:42:49] [Iter 1166/2250] R4[80/2400]  | LR: 0.029932 | E: -27.113161 | E_var:     0.2612 | E_err:   0.007985
[2025-10-02 15:42:51] [Iter 1167/2250] R4[82/2400]  | LR: 0.029928 | E: -27.100414 | E_var:     0.2139 | E_err:   0.007226
[2025-10-02 15:42:53] [Iter 1168/2250] R4[84/2400]  | LR: 0.029925 | E: -27.113843 | E_var:     0.2045 | E_err:   0.007066
[2025-10-02 15:42:56] [Iter 1169/2250] R4[86/2400]  | LR: 0.029921 | E: -27.104764 | E_var:     0.2358 | E_err:   0.007587
[2025-10-02 15:42:58] [Iter 1170/2250] R4[88/2400]  | LR: 0.029917 | E: -27.118755 | E_var:     0.2789 | E_err:   0.008252
[2025-10-02 15:43:01] [Iter 1171/2250] R4[90/2400]  | LR: 0.029913 | E: -27.112674 | E_var:     0.2330 | E_err:   0.007542
[2025-10-02 15:43:03] [Iter 1172/2250] R4[92/2400]  | LR: 0.029909 | E: -27.099997 | E_var:     0.2082 | E_err:   0.007129
[2025-10-02 15:43:05] [Iter 1173/2250] R4[94/2400]  | LR: 0.029905 | E: -27.102149 | E_var:     0.2923 | E_err:   0.008447
[2025-10-02 15:43:08] [Iter 1174/2250] R4[96/2400]  | LR: 0.029901 | E: -27.119352 | E_var:     0.2029 | E_err:   0.007039
[2025-10-02 15:43:10] [Iter 1175/2250] R4[98/2400]  | LR: 0.029897 | E: -27.121283 | E_var:     0.2037 | E_err:   0.007052
[2025-10-02 15:43:13] [Iter 1176/2250] R4[100/2400] | LR: 0.029893 | E: -27.114046 | E_var:     0.2203 | E_err:   0.007333
[2025-10-02 15:43:15] [Iter 1177/2250] R4[102/2400] | LR: 0.029889 | E: -27.102612 | E_var:     0.2388 | E_err:   0.007636
[2025-10-02 15:43:17] [Iter 1178/2250] R4[104/2400] | LR: 0.029884 | E: -27.116941 | E_var:     0.2247 | E_err:   0.007406
[2025-10-02 15:43:20] [Iter 1179/2250] R4[106/2400] | LR: 0.029880 | E: -27.111151 | E_var:     0.2160 | E_err:   0.007263
[2025-10-02 15:43:22] [Iter 1180/2250] R4[108/2400] | LR: 0.029875 | E: -27.125214 | E_var:     0.2052 | E_err:   0.007078
[2025-10-02 15:43:25] [Iter 1181/2250] R4[110/2400] | LR: 0.029871 | E: -27.122302 | E_var:     0.2394 | E_err:   0.007645
[2025-10-02 15:43:27] [Iter 1182/2250] R4[112/2400] | LR: 0.029866 | E: -27.105738 | E_var:     0.2175 | E_err:   0.007288
[2025-10-02 15:43:29] [Iter 1183/2250] R4[114/2400] | LR: 0.029861 | E: -27.101956 | E_var:     0.2209 | E_err:   0.007343
[2025-10-02 15:43:32] [Iter 1184/2250] R4[116/2400] | LR: 0.029856 | E: -27.118910 | E_var:     0.2311 | E_err:   0.007511
[2025-10-02 15:43:34] [Iter 1185/2250] R4[118/2400] | LR: 0.029851 | E: -27.097192 | E_var:     0.3926 | E_err:   0.009790
[2025-10-02 15:43:37] [Iter 1186/2250] R4[120/2400] | LR: 0.029846 | E: -27.121295 | E_var:     0.3373 | E_err:   0.009075
[2025-10-02 15:43:39] [Iter 1187/2250] R4[122/2400] | LR: 0.029841 | E: -27.112152 | E_var:     0.1990 | E_err:   0.006970
[2025-10-02 15:43:41] [Iter 1188/2250] R4[124/2400] | LR: 0.029836 | E: -27.120060 | E_var:     0.4631 | E_err:   0.010633
[2025-10-02 15:43:44] [Iter 1189/2250] R4[126/2400] | LR: 0.029830 | E: -27.109758 | E_var:     0.2578 | E_err:   0.007934
[2025-10-02 15:43:46] [Iter 1190/2250] R4[128/2400] | LR: 0.029825 | E: -27.110458 | E_var:     0.3681 | E_err:   0.009480
[2025-10-02 15:43:48] [Iter 1191/2250] R4[130/2400] | LR: 0.029819 | E: -27.108220 | E_var:     0.4006 | E_err:   0.009889
[2025-10-02 15:43:51] [Iter 1192/2250] R4[132/2400] | LR: 0.029814 | E: -27.111236 | E_var:     0.2260 | E_err:   0.007428
[2025-10-02 15:43:53] [Iter 1193/2250] R4[134/2400] | LR: 0.029808 | E: -27.105253 | E_var:     0.1947 | E_err:   0.006895
[2025-10-02 15:43:56] [Iter 1194/2250] R4[136/2400] | LR: 0.029802 | E: -27.120349 | E_var:     0.2034 | E_err:   0.007047
[2025-10-02 15:43:58] [Iter 1195/2250] R4[138/2400] | LR: 0.029797 | E: -27.116574 | E_var:     0.3934 | E_err:   0.009801
[2025-10-02 15:44:00] [Iter 1196/2250] R4[140/2400] | LR: 0.029791 | E: -27.123620 | E_var:     0.2057 | E_err:   0.007087
[2025-10-02 15:44:03] [Iter 1197/2250] R4[142/2400] | LR: 0.029785 | E: -27.100746 | E_var:     0.2717 | E_err:   0.008145
[2025-10-02 15:44:05] [Iter 1198/2250] R4[144/2400] | LR: 0.029779 | E: -27.113078 | E_var:     0.2145 | E_err:   0.007236
[2025-10-02 15:44:08] [Iter 1199/2250] R4[146/2400] | LR: 0.029772 | E: -27.108787 | E_var:     0.2224 | E_err:   0.007368
[2025-10-02 15:44:10] [Iter 1200/2250] R4[148/2400] | LR: 0.029766 | E: -27.104231 | E_var:     0.1884 | E_err:   0.006782
[2025-10-02 15:44:10] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-02 15:44:12] [Iter 1201/2250] R4[150/2400] | LR: 0.029760 | E: -27.114421 | E_var:     0.2285 | E_err:   0.007470
[2025-10-02 15:44:15] [Iter 1202/2250] R4[152/2400] | LR: 0.029753 | E: -27.105107 | E_var:     0.2764 | E_err:   0.008215
[2025-10-02 15:44:17] [Iter 1203/2250] R4[154/2400] | LR: 0.029747 | E: -27.105113 | E_var:     0.2260 | E_err:   0.007428
[2025-10-02 15:44:20] [Iter 1204/2250] R4[156/2400] | LR: 0.029740 | E: -27.112282 | E_var:     0.2449 | E_err:   0.007733
[2025-10-02 15:44:22] [Iter 1205/2250] R4[158/2400] | LR: 0.029734 | E: -27.118379 | E_var:     0.2766 | E_err:   0.008218
[2025-10-02 15:44:24] [Iter 1206/2250] R4[160/2400] | LR: 0.029727 | E: -27.104460 | E_var:     0.2063 | E_err:   0.007097
[2025-10-02 15:44:27] [Iter 1207/2250] R4[162/2400] | LR: 0.029720 | E: -27.118937 | E_var:     0.2516 | E_err:   0.007837
[2025-10-02 15:44:29] [Iter 1208/2250] R4[164/2400] | LR: 0.029713 | E: -27.115444 | E_var:     0.2415 | E_err:   0.007678
[2025-10-02 15:44:32] [Iter 1209/2250] R4[166/2400] | LR: 0.029706 | E: -27.110090 | E_var:     0.2973 | E_err:   0.008520
[2025-10-02 15:44:34] [Iter 1210/2250] R4[168/2400] | LR: 0.029699 | E: -27.109365 | E_var:     0.3101 | E_err:   0.008701
[2025-10-02 15:44:36] [Iter 1211/2250] R4[170/2400] | LR: 0.029692 | E: -27.119980 | E_var:     0.1661 | E_err:   0.006368
[2025-10-02 15:44:39] [Iter 1212/2250] R4[172/2400] | LR: 0.029685 | E: -27.119463 | E_var:     0.2227 | E_err:   0.007374
[2025-10-02 15:44:41] [Iter 1213/2250] R4[174/2400] | LR: 0.029677 | E: -27.112238 | E_var:     0.2435 | E_err:   0.007710
[2025-10-02 15:44:44] [Iter 1214/2250] R4[176/2400] | LR: 0.029670 | E: -27.118742 | E_var:     0.2766 | E_err:   0.008218
[2025-10-02 15:44:46] [Iter 1215/2250] R4[178/2400] | LR: 0.029662 | E: -27.105739 | E_var:     0.2510 | E_err:   0.007829
[2025-10-02 15:44:48] [Iter 1216/2250] R4[180/2400] | LR: 0.029655 | E: -27.128924 | E_var:     0.1940 | E_err:   0.006882
[2025-10-02 15:44:51] [Iter 1217/2250] R4[182/2400] | LR: 0.029647 | E: -27.121452 | E_var:     0.1768 | E_err:   0.006569
[2025-10-02 15:44:53] [Iter 1218/2250] R4[184/2400] | LR: 0.029639 | E: -27.117143 | E_var:     0.2337 | E_err:   0.007554
[2025-10-02 15:44:56] [Iter 1219/2250] R4[186/2400] | LR: 0.029631 | E: -27.105631 | E_var:     0.2561 | E_err:   0.007907
[2025-10-02 15:44:58] [Iter 1220/2250] R4[188/2400] | LR: 0.029623 | E: -27.115356 | E_var:     0.2659 | E_err:   0.008058
[2025-10-02 15:45:00] [Iter 1221/2250] R4[190/2400] | LR: 0.029615 | E: -27.102857 | E_var:     0.2728 | E_err:   0.008161
[2025-10-02 15:45:03] [Iter 1222/2250] R4[192/2400] | LR: 0.029607 | E: -27.116691 | E_var:     0.2041 | E_err:   0.007059
[2025-10-02 15:45:05] [Iter 1223/2250] R4[194/2400] | LR: 0.029599 | E: -27.123520 | E_var:     0.4989 | E_err:   0.011037
[2025-10-02 15:45:08] [Iter 1224/2250] R4[196/2400] | LR: 0.029591 | E: -27.108776 | E_var:     0.2140 | E_err:   0.007228
[2025-10-02 15:45:10] [Iter 1225/2250] R4[198/2400] | LR: 0.029583 | E: -27.109324 | E_var:     0.2161 | E_err:   0.007263
[2025-10-02 15:45:12] [Iter 1226/2250] R4[200/2400] | LR: 0.029574 | E: -27.115671 | E_var:     0.2274 | E_err:   0.007451
[2025-10-02 15:45:15] [Iter 1227/2250] R4[202/2400] | LR: 0.029566 | E: -27.111869 | E_var:     0.2015 | E_err:   0.007013
[2025-10-02 15:45:17] [Iter 1228/2250] R4[204/2400] | LR: 0.029557 | E: -27.101720 | E_var:     0.1723 | E_err:   0.006486
[2025-10-02 15:45:20] [Iter 1229/2250] R4[206/2400] | LR: 0.029548 | E: -27.106415 | E_var:     0.1933 | E_err:   0.006869
[2025-10-02 15:45:22] [Iter 1230/2250] R4[208/2400] | LR: 0.029540 | E: -27.100821 | E_var:     0.2271 | E_err:   0.007446
[2025-10-02 15:45:24] [Iter 1231/2250] R4[210/2400] | LR: 0.029531 | E: -27.117545 | E_var:     0.1910 | E_err:   0.006829
[2025-10-02 15:45:27] [Iter 1232/2250] R4[212/2400] | LR: 0.029522 | E: -27.098231 | E_var:     0.2791 | E_err:   0.008254
[2025-10-02 15:45:29] [Iter 1233/2250] R4[214/2400] | LR: 0.029513 | E: -27.126975 | E_var:     0.2790 | E_err:   0.008253
[2025-10-02 15:45:32] [Iter 1234/2250] R4[216/2400] | LR: 0.029504 | E: -27.130006 | E_var:     0.2488 | E_err:   0.007793
[2025-10-02 15:45:34] [Iter 1235/2250] R4[218/2400] | LR: 0.029494 | E: -27.104302 | E_var:     0.2320 | E_err:   0.007526
[2025-10-02 15:45:36] [Iter 1236/2250] R4[220/2400] | LR: 0.029485 | E: -27.107679 | E_var:     0.3468 | E_err:   0.009202
[2025-10-02 15:45:39] [Iter 1237/2250] R4[222/2400] | LR: 0.029476 | E: -27.108257 | E_var:     0.2158 | E_err:   0.007259
[2025-10-02 15:45:41] [Iter 1238/2250] R4[224/2400] | LR: 0.029466 | E: -27.106381 | E_var:     0.1935 | E_err:   0.006873
[2025-10-02 15:45:44] [Iter 1239/2250] R4[226/2400] | LR: 0.029457 | E: -27.115801 | E_var:     0.2227 | E_err:   0.007374
[2025-10-02 15:45:46] [Iter 1240/2250] R4[228/2400] | LR: 0.029447 | E: -27.111863 | E_var:     0.1948 | E_err:   0.006896
[2025-10-02 15:45:48] [Iter 1241/2250] R4[230/2400] | LR: 0.029438 | E: -27.113987 | E_var:     0.2182 | E_err:   0.007299
[2025-10-02 15:45:51] [Iter 1242/2250] R4[232/2400] | LR: 0.029428 | E: -27.125941 | E_var:     0.1849 | E_err:   0.006720
[2025-10-02 15:45:53] [Iter 1243/2250] R4[234/2400] | LR: 0.029418 | E: -27.115310 | E_var:     0.2250 | E_err:   0.007411
[2025-10-02 15:45:56] [Iter 1244/2250] R4[236/2400] | LR: 0.029408 | E: -27.118505 | E_var:     0.1916 | E_err:   0.006839
[2025-10-02 15:45:58] [Iter 1245/2250] R4[238/2400] | LR: 0.029398 | E: -27.121525 | E_var:     0.2291 | E_err:   0.007480
[2025-10-02 15:46:00] [Iter 1246/2250] R4[240/2400] | LR: 0.029388 | E: -27.117404 | E_var:     0.1874 | E_err:   0.006763
[2025-10-02 15:46:03] [Iter 1247/2250] R4[242/2400] | LR: 0.029378 | E: -27.121479 | E_var:     0.1941 | E_err:   0.006883
[2025-10-02 15:46:05] [Iter 1248/2250] R4[244/2400] | LR: 0.029368 | E: -27.098089 | E_var:     0.3073 | E_err:   0.008661
[2025-10-02 15:46:08] [Iter 1249/2250] R4[246/2400] | LR: 0.029358 | E: -27.102342 | E_var:     0.1995 | E_err:   0.006979
[2025-10-02 15:46:10] [Iter 1250/2250] R4[248/2400] | LR: 0.029347 | E: -27.116188 | E_var:     0.2687 | E_err:   0.008099
[2025-10-02 15:46:12] [Iter 1251/2250] R4[250/2400] | LR: 0.029337 | E: -27.114542 | E_var:     0.2580 | E_err:   0.007937
[2025-10-02 15:46:15] [Iter 1252/2250] R4[252/2400] | LR: 0.029326 | E: -27.109996 | E_var:     0.2105 | E_err:   0.007169
[2025-10-02 15:46:17] [Iter 1253/2250] R4[254/2400] | LR: 0.029315 | E: -27.124553 | E_var:     0.3218 | E_err:   0.008863
[2025-10-02 15:46:19] [Iter 1254/2250] R4[256/2400] | LR: 0.029305 | E: -27.119409 | E_var:     0.2259 | E_err:   0.007426
[2025-10-02 15:46:22] [Iter 1255/2250] R4[258/2400] | LR: 0.029294 | E: -27.116042 | E_var:     0.2388 | E_err:   0.007636
[2025-10-02 15:46:24] [Iter 1256/2250] R4[260/2400] | LR: 0.029283 | E: -27.115548 | E_var:     0.1984 | E_err:   0.006960
[2025-10-02 15:46:27] [Iter 1257/2250] R4[262/2400] | LR: 0.029272 | E: -27.115166 | E_var:     0.2294 | E_err:   0.007484
[2025-10-02 15:46:29] [Iter 1258/2250] R4[264/2400] | LR: 0.029261 | E: -27.116930 | E_var:     0.2198 | E_err:   0.007325
[2025-10-02 15:46:31] [Iter 1259/2250] R4[266/2400] | LR: 0.029250 | E: -27.120301 | E_var:     0.2591 | E_err:   0.007954
[2025-10-02 15:46:34] [Iter 1260/2250] R4[268/2400] | LR: 0.029239 | E: -27.122157 | E_var:     0.1811 | E_err:   0.006649
[2025-10-02 15:46:36] [Iter 1261/2250] R4[270/2400] | LR: 0.029227 | E: -27.119375 | E_var:     0.3075 | E_err:   0.008665
[2025-10-02 15:46:39] [Iter 1262/2250] R4[272/2400] | LR: 0.029216 | E: -27.107463 | E_var:     0.2314 | E_err:   0.007515
[2025-10-02 15:46:41] [Iter 1263/2250] R4[274/2400] | LR: 0.029205 | E: -27.108883 | E_var:     0.2046 | E_err:   0.007068
[2025-10-02 15:46:43] [Iter 1264/2250] R4[276/2400] | LR: 0.029193 | E: -27.127865 | E_var:     0.1791 | E_err:   0.006612
[2025-10-02 15:46:46] [Iter 1265/2250] R4[278/2400] | LR: 0.029181 | E: -27.111853 | E_var:     0.1903 | E_err:   0.006817
[2025-10-02 15:46:48] [Iter 1266/2250] R4[280/2400] | LR: 0.029170 | E: -27.113938 | E_var:     0.2436 | E_err:   0.007713
[2025-10-02 15:46:51] [Iter 1267/2250] R4[282/2400] | LR: 0.029158 | E: -27.126542 | E_var:     0.2284 | E_err:   0.007468
[2025-10-02 15:46:53] [Iter 1268/2250] R4[284/2400] | LR: 0.029146 | E: -27.114133 | E_var:     0.2182 | E_err:   0.007299
[2025-10-02 15:46:55] [Iter 1269/2250] R4[286/2400] | LR: 0.029134 | E: -27.108418 | E_var:     0.2030 | E_err:   0.007041
[2025-10-02 15:46:58] [Iter 1270/2250] R4[288/2400] | LR: 0.029122 | E: -27.112511 | E_var:     0.2067 | E_err:   0.007103
[2025-10-02 15:47:00] [Iter 1271/2250] R4[290/2400] | LR: 0.029110 | E: -27.110727 | E_var:     0.2154 | E_err:   0.007251
[2025-10-02 15:47:03] [Iter 1272/2250] R4[292/2400] | LR: 0.029098 | E: -27.104213 | E_var:     0.2651 | E_err:   0.008045
[2025-10-02 15:47:05] [Iter 1273/2250] R4[294/2400] | LR: 0.029086 | E: -27.126432 | E_var:     0.2344 | E_err:   0.007564
[2025-10-02 15:47:07] [Iter 1274/2250] R4[296/2400] | LR: 0.029073 | E: -27.114982 | E_var:     0.2580 | E_err:   0.007937
[2025-10-02 15:47:10] [Iter 1275/2250] R4[298/2400] | LR: 0.029061 | E: -27.116815 | E_var:     0.2189 | E_err:   0.007311
[2025-10-02 15:47:12] [Iter 1276/2250] R4[300/2400] | LR: 0.029048 | E: -27.101624 | E_var:     0.2444 | E_err:   0.007725
[2025-10-02 15:47:15] [Iter 1277/2250] R4[302/2400] | LR: 0.029036 | E: -27.110043 | E_var:     0.2676 | E_err:   0.008083
[2025-10-02 15:47:17] [Iter 1278/2250] R4[304/2400] | LR: 0.029023 | E: -27.131888 | E_var:     0.2946 | E_err:   0.008481
[2025-10-02 15:47:19] [Iter 1279/2250] R4[306/2400] | LR: 0.029011 | E: -27.111923 | E_var:     0.4337 | E_err:   0.010290
[2025-10-02 15:47:22] [Iter 1280/2250] R4[308/2400] | LR: 0.028998 | E: -27.115159 | E_var:     0.2094 | E_err:   0.007151
[2025-10-02 15:47:24] [Iter 1281/2250] R4[310/2400] | LR: 0.028985 | E: -27.132051 | E_var:     0.2255 | E_err:   0.007419
[2025-10-02 15:47:27] [Iter 1282/2250] R4[312/2400] | LR: 0.028972 | E: -27.114815 | E_var:     0.2029 | E_err:   0.007039
[2025-10-02 15:47:29] [Iter 1283/2250] R4[314/2400] | LR: 0.028959 | E: -27.121475 | E_var:     0.1935 | E_err:   0.006874
[2025-10-02 15:47:31] [Iter 1284/2250] R4[316/2400] | LR: 0.028946 | E: -27.121221 | E_var:     0.2312 | E_err:   0.007512
[2025-10-02 15:47:34] [Iter 1285/2250] R4[318/2400] | LR: 0.028933 | E: -27.112181 | E_var:     0.1857 | E_err:   0.006733
[2025-10-02 15:47:36] [Iter 1286/2250] R4[320/2400] | LR: 0.028919 | E: -27.126273 | E_var:     0.2206 | E_err:   0.007339
[2025-10-02 15:47:39] [Iter 1287/2250] R4[322/2400] | LR: 0.028906 | E: -27.108604 | E_var:     0.2564 | E_err:   0.007911
[2025-10-02 15:47:41] [Iter 1288/2250] R4[324/2400] | LR: 0.028893 | E: -27.118819 | E_var:     0.2052 | E_err:   0.007077
[2025-10-02 15:47:43] [Iter 1289/2250] R4[326/2400] | LR: 0.028879 | E: -27.112367 | E_var:     0.2542 | E_err:   0.007878
[2025-10-02 15:47:46] [Iter 1290/2250] R4[328/2400] | LR: 0.028865 | E: -27.111435 | E_var:     0.2241 | E_err:   0.007397
[2025-10-02 15:47:48] [Iter 1291/2250] R4[330/2400] | LR: 0.028852 | E: -27.112388 | E_var:     0.2221 | E_err:   0.007364
[2025-10-02 15:47:50] [Iter 1292/2250] R4[332/2400] | LR: 0.028838 | E: -27.125405 | E_var:     0.1975 | E_err:   0.006943
[2025-10-02 15:47:53] [Iter 1293/2250] R4[334/2400] | LR: 0.028824 | E: -27.105879 | E_var:     0.1783 | E_err:   0.006598
[2025-10-02 15:47:55] [Iter 1294/2250] R4[336/2400] | LR: 0.028810 | E: -27.104750 | E_var:     0.2886 | E_err:   0.008395
[2025-10-02 15:47:58] [Iter 1295/2250] R4[338/2400] | LR: 0.028796 | E: -27.116046 | E_var:     0.1847 | E_err:   0.006715
[2025-10-02 15:48:00] [Iter 1296/2250] R4[340/2400] | LR: 0.028782 | E: -27.116127 | E_var:     0.2084 | E_err:   0.007134
[2025-10-02 15:48:02] [Iter 1297/2250] R4[342/2400] | LR: 0.028768 | E: -27.109385 | E_var:     0.3281 | E_err:   0.008950
[2025-10-02 15:48:05] [Iter 1298/2250] R4[344/2400] | LR: 0.028754 | E: -27.104540 | E_var:     0.1936 | E_err:   0.006876
[2025-10-02 15:48:07] [Iter 1299/2250] R4[346/2400] | LR: 0.028740 | E: -27.110729 | E_var:     0.1798 | E_err:   0.006625
[2025-10-02 15:48:10] [Iter 1300/2250] R4[348/2400] | LR: 0.028725 | E: -27.107831 | E_var:     0.1947 | E_err:   0.006895
[2025-10-02 15:48:12] [Iter 1301/2250] R4[350/2400] | LR: 0.028711 | E: -27.112769 | E_var:     0.2037 | E_err:   0.007051
[2025-10-02 15:48:14] [Iter 1302/2250] R4[352/2400] | LR: 0.028696 | E: -27.117714 | E_var:     0.1936 | E_err:   0.006876
[2025-10-02 15:48:17] [Iter 1303/2250] R4[354/2400] | LR: 0.028682 | E: -27.101557 | E_var:     0.2957 | E_err:   0.008496
[2025-10-02 15:48:19] [Iter 1304/2250] R4[356/2400] | LR: 0.028667 | E: -27.114716 | E_var:     0.1766 | E_err:   0.006565
[2025-10-02 15:48:22] [Iter 1305/2250] R4[358/2400] | LR: 0.028652 | E: -27.120072 | E_var:     0.1865 | E_err:   0.006748
[2025-10-02 15:48:24] [Iter 1306/2250] R4[360/2400] | LR: 0.028638 | E: -27.120944 | E_var:     0.3336 | E_err:   0.009025
[2025-10-02 15:48:26] [Iter 1307/2250] R4[362/2400] | LR: 0.028623 | E: -27.118004 | E_var:     0.2108 | E_err:   0.007175
[2025-10-02 15:48:29] [Iter 1308/2250] R4[364/2400] | LR: 0.028608 | E: -27.109230 | E_var:     0.2144 | E_err:   0.007234
[2025-10-02 15:48:31] [Iter 1309/2250] R4[366/2400] | LR: 0.028593 | E: -27.111595 | E_var:     0.2488 | E_err:   0.007794
[2025-10-02 15:48:34] [Iter 1310/2250] R4[368/2400] | LR: 0.028578 | E: -27.113764 | E_var:     0.1889 | E_err:   0.006791
[2025-10-02 15:48:36] [Iter 1311/2250] R4[370/2400] | LR: 0.028562 | E: -27.121325 | E_var:     0.3101 | E_err:   0.008701
[2025-10-02 15:48:38] [Iter 1312/2250] R4[372/2400] | LR: 0.028547 | E: -27.119792 | E_var:     0.2105 | E_err:   0.007168
[2025-10-02 15:48:41] [Iter 1313/2250] R4[374/2400] | LR: 0.028532 | E: -27.117043 | E_var:     0.1779 | E_err:   0.006591
[2025-10-02 15:48:43] [Iter 1314/2250] R4[376/2400] | LR: 0.028516 | E: -27.109573 | E_var:     0.2800 | E_err:   0.008269
[2025-10-02 15:48:46] [Iter 1315/2250] R4[378/2400] | LR: 0.028501 | E: -27.116790 | E_var:     0.1797 | E_err:   0.006624
[2025-10-02 15:48:48] [Iter 1316/2250] R4[380/2400] | LR: 0.028485 | E: -27.117265 | E_var:     0.2716 | E_err:   0.008144
[2025-10-02 15:48:50] [Iter 1317/2250] R4[382/2400] | LR: 0.028470 | E: -27.110736 | E_var:     0.1894 | E_err:   0.006799
[2025-10-02 15:48:53] [Iter 1318/2250] R4[384/2400] | LR: 0.028454 | E: -27.105732 | E_var:     0.1805 | E_err:   0.006639
[2025-10-02 15:48:55] [Iter 1319/2250] R4[386/2400] | LR: 0.028438 | E: -27.115389 | E_var:     0.2034 | E_err:   0.007046
[2025-10-02 15:48:58] [Iter 1320/2250] R4[388/2400] | LR: 0.028422 | E: -27.106538 | E_var:     0.2088 | E_err:   0.007140
[2025-10-02 15:49:00] [Iter 1321/2250] R4[390/2400] | LR: 0.028406 | E: -27.116456 | E_var:     0.2847 | E_err:   0.008337
[2025-10-02 15:49:02] [Iter 1322/2250] R4[392/2400] | LR: 0.028390 | E: -27.105158 | E_var:     0.1819 | E_err:   0.006665
[2025-10-02 15:49:05] [Iter 1323/2250] R4[394/2400] | LR: 0.028374 | E: -27.118967 | E_var:     0.1630 | E_err:   0.006309
[2025-10-02 15:49:07] [Iter 1324/2250] R4[396/2400] | LR: 0.028358 | E: -27.125436 | E_var:     0.2223 | E_err:   0.007367
[2025-10-02 15:49:10] [Iter 1325/2250] R4[398/2400] | LR: 0.028342 | E: -27.121158 | E_var:     0.2161 | E_err:   0.007264
[2025-10-02 15:49:12] [Iter 1326/2250] R4[400/2400] | LR: 0.028325 | E: -27.117894 | E_var:     0.2159 | E_err:   0.007260
[2025-10-02 15:49:15] [Iter 1327/2250] R4[402/2400] | LR: 0.028309 | E: -27.128434 | E_var:     0.1742 | E_err:   0.006522
[2025-10-02 15:49:17] [Iter 1328/2250] R4[404/2400] | LR: 0.028292 | E: -27.122738 | E_var:     0.1910 | E_err:   0.006829
[2025-10-02 15:49:19] [Iter 1329/2250] R4[406/2400] | LR: 0.028276 | E: -27.117732 | E_var:     0.1865 | E_err:   0.006748
[2025-10-02 15:49:22] [Iter 1330/2250] R4[408/2400] | LR: 0.028259 | E: -27.115841 | E_var:     0.1985 | E_err:   0.006961
[2025-10-02 15:49:24] [Iter 1331/2250] R4[410/2400] | LR: 0.028243 | E: -27.113539 | E_var:     0.1734 | E_err:   0.006506
[2025-10-02 15:49:27] [Iter 1332/2250] R4[412/2400] | LR: 0.028226 | E: -27.119386 | E_var:     0.3038 | E_err:   0.008612
[2025-10-02 15:49:29] [Iter 1333/2250] R4[414/2400] | LR: 0.028209 | E: -27.122969 | E_var:     0.1822 | E_err:   0.006669
[2025-10-02 15:49:31] [Iter 1334/2250] R4[416/2400] | LR: 0.028192 | E: -27.120219 | E_var:     0.3415 | E_err:   0.009131
[2025-10-02 15:49:34] [Iter 1335/2250] R4[418/2400] | LR: 0.028175 | E: -27.123545 | E_var:     0.2273 | E_err:   0.007450
[2025-10-02 15:49:36] [Iter 1336/2250] R4[420/2400] | LR: 0.028158 | E: -27.115775 | E_var:     0.1884 | E_err:   0.006783
[2025-10-02 15:49:39] [Iter 1337/2250] R4[422/2400] | LR: 0.028141 | E: -27.118139 | E_var:     0.2346 | E_err:   0.007569
[2025-10-02 15:49:41] [Iter 1338/2250] R4[424/2400] | LR: 0.028124 | E: -27.114598 | E_var:     0.1808 | E_err:   0.006643
[2025-10-02 15:49:43] [Iter 1339/2250] R4[426/2400] | LR: 0.028106 | E: -27.117005 | E_var:     0.1816 | E_err:   0.006659
[2025-10-02 15:49:46] [Iter 1340/2250] R4[428/2400] | LR: 0.028089 | E: -27.119724 | E_var:     0.2347 | E_err:   0.007570
[2025-10-02 15:49:48] [Iter 1341/2250] R4[430/2400] | LR: 0.028072 | E: -27.115298 | E_var:     0.1835 | E_err:   0.006692
[2025-10-02 15:49:51] [Iter 1342/2250] R4[432/2400] | LR: 0.028054 | E: -27.104297 | E_var:     0.2038 | E_err:   0.007054
[2025-10-02 15:49:53] [Iter 1343/2250] R4[434/2400] | LR: 0.028037 | E: -27.120972 | E_var:     0.2163 | E_err:   0.007266
[2025-10-02 15:49:55] [Iter 1344/2250] R4[436/2400] | LR: 0.028019 | E: -27.114660 | E_var:     0.2205 | E_err:   0.007337
[2025-10-02 15:49:58] [Iter 1345/2250] R4[438/2400] | LR: 0.028001 | E: -27.125434 | E_var:     0.2198 | E_err:   0.007326
[2025-10-02 15:50:00] [Iter 1346/2250] R4[440/2400] | LR: 0.027983 | E: -27.127378 | E_var:     0.2079 | E_err:   0.007125
[2025-10-02 15:50:03] [Iter 1347/2250] R4[442/2400] | LR: 0.027966 | E: -27.109925 | E_var:     0.2352 | E_err:   0.007577
[2025-10-02 15:50:05] [Iter 1348/2250] R4[444/2400] | LR: 0.027948 | E: -27.113313 | E_var:     0.1995 | E_err:   0.006979
[2025-10-02 15:50:07] [Iter 1349/2250] R4[446/2400] | LR: 0.027930 | E: -27.110571 | E_var:     0.2275 | E_err:   0.007453
[2025-10-02 15:50:10] [Iter 1350/2250] R4[448/2400] | LR: 0.027912 | E: -27.114097 | E_var:     0.1973 | E_err:   0.006941
[2025-10-02 15:50:12] [Iter 1351/2250] R4[450/2400] | LR: 0.027893 | E: -27.114537 | E_var:     0.1878 | E_err:   0.006771
[2025-10-02 15:50:14] [Iter 1352/2250] R4[452/2400] | LR: 0.027875 | E: -27.116704 | E_var:     0.2493 | E_err:   0.007802
[2025-10-02 15:50:17] [Iter 1353/2250] R4[454/2400] | LR: 0.027857 | E: -27.117177 | E_var:     0.2061 | E_err:   0.007094
[2025-10-02 15:50:19] [Iter 1354/2250] R4[456/2400] | LR: 0.027839 | E: -27.125212 | E_var:     0.2283 | E_err:   0.007465
[2025-10-02 15:50:22] [Iter 1355/2250] R4[458/2400] | LR: 0.027820 | E: -27.087667 | E_var:     0.3365 | E_err:   0.009064
[2025-10-02 15:50:24] [Iter 1356/2250] R4[460/2400] | LR: 0.027802 | E: -27.107591 | E_var:     0.2290 | E_err:   0.007477
[2025-10-02 15:50:26] [Iter 1357/2250] R4[462/2400] | LR: 0.027783 | E: -27.107199 | E_var:     0.1938 | E_err:   0.006879
[2025-10-02 15:50:29] [Iter 1358/2250] R4[464/2400] | LR: 0.027764 | E: -27.120365 | E_var:     0.2134 | E_err:   0.007218
[2025-10-02 15:50:31] [Iter 1359/2250] R4[466/2400] | LR: 0.027746 | E: -27.111211 | E_var:     0.3126 | E_err:   0.008735
[2025-10-02 15:50:34] [Iter 1360/2250] R4[468/2400] | LR: 0.027727 | E: -27.105762 | E_var:     0.1775 | E_err:   0.006583
[2025-10-02 15:50:36] [Iter 1361/2250] R4[470/2400] | LR: 0.027708 | E: -27.110927 | E_var:     0.2089 | E_err:   0.007141
[2025-10-02 15:50:38] [Iter 1362/2250] R4[472/2400] | LR: 0.027689 | E: -27.121061 | E_var:     0.2177 | E_err:   0.007290
[2025-10-02 15:50:41] [Iter 1363/2250] R4[474/2400] | LR: 0.027670 | E: -27.128184 | E_var:     1.7514 | E_err:   0.020678
[2025-10-02 15:50:43] [Iter 1364/2250] R4[476/2400] | LR: 0.027651 | E: -27.115682 | E_var:     0.2735 | E_err:   0.008172
[2025-10-02 15:50:46] [Iter 1365/2250] R4[478/2400] | LR: 0.027632 | E: -27.121342 | E_var:     0.1725 | E_err:   0.006490
[2025-10-02 15:50:48] [Iter 1366/2250] R4[480/2400] | LR: 0.027613 | E: -27.103046 | E_var:     0.2403 | E_err:   0.007659
[2025-10-02 15:50:50] [Iter 1367/2250] R4[482/2400] | LR: 0.027593 | E: -27.119371 | E_var:     0.2340 | E_err:   0.007559
[2025-10-02 15:50:53] [Iter 1368/2250] R4[484/2400] | LR: 0.027574 | E: -27.116545 | E_var:     0.1869 | E_err:   0.006756
[2025-10-02 15:50:55] [Iter 1369/2250] R4[486/2400] | LR: 0.027555 | E: -27.120390 | E_var:     0.1876 | E_err:   0.006768
[2025-10-02 15:50:58] [Iter 1370/2250] R4[488/2400] | LR: 0.027535 | E: -27.118378 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 15:51:00] [Iter 1371/2250] R4[490/2400] | LR: 0.027516 | E: -27.120291 | E_var:     0.3457 | E_err:   0.009186
[2025-10-02 15:51:02] [Iter 1372/2250] R4[492/2400] | LR: 0.027496 | E: -27.116604 | E_var:     0.1989 | E_err:   0.006968
[2025-10-02 15:51:05] [Iter 1373/2250] R4[494/2400] | LR: 0.027476 | E: -27.121073 | E_var:     0.1879 | E_err:   0.006772
[2025-10-02 15:51:07] [Iter 1374/2250] R4[496/2400] | LR: 0.027457 | E: -27.112024 | E_var:     0.1924 | E_err:   0.006853
[2025-10-02 15:51:10] [Iter 1375/2250] R4[498/2400] | LR: 0.027437 | E: -27.101363 | E_var:     0.2581 | E_err:   0.007938
[2025-10-02 15:51:12] [Iter 1376/2250] R4[500/2400] | LR: 0.027417 | E: -27.131585 | E_var:     0.1866 | E_err:   0.006750
[2025-10-02 15:51:14] [Iter 1377/2250] R4[502/2400] | LR: 0.027397 | E: -27.115525 | E_var:     0.1902 | E_err:   0.006814
[2025-10-02 15:51:17] [Iter 1378/2250] R4[504/2400] | LR: 0.027377 | E: -27.103838 | E_var:     0.2203 | E_err:   0.007334
[2025-10-02 15:51:19] [Iter 1379/2250] R4[506/2400] | LR: 0.027357 | E: -27.124679 | E_var:     0.2189 | E_err:   0.007311
[2025-10-02 15:51:22] [Iter 1380/2250] R4[508/2400] | LR: 0.027337 | E: -27.125354 | E_var:     0.2490 | E_err:   0.007797
[2025-10-02 15:51:24] [Iter 1381/2250] R4[510/2400] | LR: 0.027316 | E: -27.109475 | E_var:     0.1685 | E_err:   0.006413
[2025-10-02 15:51:26] [Iter 1382/2250] R4[512/2400] | LR: 0.027296 | E: -27.122411 | E_var:     0.2054 | E_err:   0.007081
[2025-10-02 15:51:29] [Iter 1383/2250] R4[514/2400] | LR: 0.027276 | E: -27.113341 | E_var:     0.1930 | E_err:   0.006863
[2025-10-02 15:51:31] [Iter 1384/2250] R4[516/2400] | LR: 0.027255 | E: -27.124962 | E_var:     0.2379 | E_err:   0.007622
[2025-10-02 15:51:34] [Iter 1385/2250] R4[518/2400] | LR: 0.027235 | E: -27.104751 | E_var:     0.3050 | E_err:   0.008629
[2025-10-02 15:51:36] [Iter 1386/2250] R4[520/2400] | LR: 0.027214 | E: -27.123437 | E_var:     0.2333 | E_err:   0.007547
[2025-10-02 15:51:38] [Iter 1387/2250] R4[522/2400] | LR: 0.027194 | E: -27.113522 | E_var:     0.2529 | E_err:   0.007858
[2025-10-02 15:51:41] [Iter 1388/2250] R4[524/2400] | LR: 0.027173 | E: -27.123067 | E_var:     0.1792 | E_err:   0.006614
[2025-10-02 15:51:43] [Iter 1389/2250] R4[526/2400] | LR: 0.027152 | E: -27.107281 | E_var:     0.2426 | E_err:   0.007696
[2025-10-02 15:51:46] [Iter 1390/2250] R4[528/2400] | LR: 0.027131 | E: -27.129485 | E_var:     0.2980 | E_err:   0.008530
[2025-10-02 15:51:48] [Iter 1391/2250] R4[530/2400] | LR: 0.027111 | E: -27.116079 | E_var:     0.2144 | E_err:   0.007235
[2025-10-02 15:51:50] [Iter 1392/2250] R4[532/2400] | LR: 0.027090 | E: -27.116993 | E_var:     0.1658 | E_err:   0.006363
[2025-10-02 15:51:53] [Iter 1393/2250] R4[534/2400] | LR: 0.027069 | E: -27.122186 | E_var:     0.1951 | E_err:   0.006901
[2025-10-02 15:51:55] [Iter 1394/2250] R4[536/2400] | LR: 0.027047 | E: -27.119496 | E_var:     0.2614 | E_err:   0.007988
[2025-10-02 15:51:57] [Iter 1395/2250] R4[538/2400] | LR: 0.027026 | E: -27.108222 | E_var:     0.2523 | E_err:   0.007849
[2025-10-02 15:52:00] [Iter 1396/2250] R4[540/2400] | LR: 0.027005 | E: -27.135021 | E_var:     0.1729 | E_err:   0.006498
[2025-10-02 15:52:02] [Iter 1397/2250] R4[542/2400] | LR: 0.026984 | E: -27.119556 | E_var:     0.2333 | E_err:   0.007547
[2025-10-02 15:52:05] [Iter 1398/2250] R4[544/2400] | LR: 0.026962 | E: -27.130403 | E_var:     0.1706 | E_err:   0.006454
[2025-10-02 15:52:07] [Iter 1399/2250] R4[546/2400] | LR: 0.026941 | E: -27.122622 | E_var:     0.2337 | E_err:   0.007553
[2025-10-02 15:52:09] [Iter 1400/2250] R4[548/2400] | LR: 0.026920 | E: -27.125695 | E_var:     0.2217 | E_err:   0.007357
[2025-10-02 15:52:09] ✓ Checkpoint saved: checkpoint_iter_001400.pkl
[2025-10-02 15:52:12] [Iter 1401/2250] R4[550/2400] | LR: 0.026898 | E: -27.112351 | E_var:     0.2436 | E_err:   0.007711
[2025-10-02 15:52:14] [Iter 1402/2250] R4[552/2400] | LR: 0.026876 | E: -27.125511 | E_var:     0.1958 | E_err:   0.006915
[2025-10-02 15:52:17] [Iter 1403/2250] R4[554/2400] | LR: 0.026855 | E: -27.115566 | E_var:     0.2232 | E_err:   0.007382
[2025-10-02 15:52:19] [Iter 1404/2250] R4[556/2400] | LR: 0.026833 | E: -27.117012 | E_var:     0.2384 | E_err:   0.007629
[2025-10-02 15:52:21] [Iter 1405/2250] R4[558/2400] | LR: 0.026811 | E: -27.135678 | E_var:     0.4348 | E_err:   0.010303
[2025-10-02 15:52:24] [Iter 1406/2250] R4[560/2400] | LR: 0.026789 | E: -27.114308 | E_var:     0.2362 | E_err:   0.007594
[2025-10-02 15:52:26] [Iter 1407/2250] R4[562/2400] | LR: 0.026767 | E: -27.118599 | E_var:     0.2045 | E_err:   0.007066
[2025-10-02 15:52:29] [Iter 1408/2250] R4[564/2400] | LR: 0.026745 | E: -27.122876 | E_var:     0.2285 | E_err:   0.007469
[2025-10-02 15:52:31] [Iter 1409/2250] R4[566/2400] | LR: 0.026723 | E: -27.110215 | E_var:     0.1935 | E_err:   0.006873
[2025-10-02 15:52:33] [Iter 1410/2250] R4[568/2400] | LR: 0.026701 | E: -27.113476 | E_var:     0.1850 | E_err:   0.006721
[2025-10-02 15:52:36] [Iter 1411/2250] R4[570/2400] | LR: 0.026679 | E: -27.129373 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 15:52:38] [Iter 1412/2250] R4[572/2400] | LR: 0.026657 | E: -27.126730 | E_var:     0.2335 | E_err:   0.007550
[2025-10-02 15:52:41] [Iter 1413/2250] R4[574/2400] | LR: 0.026634 | E: -27.122063 | E_var:     0.1942 | E_err:   0.006887
[2025-10-02 15:52:43] [Iter 1414/2250] R4[576/2400] | LR: 0.026612 | E: -27.123521 | E_var:     0.2375 | E_err:   0.007614
[2025-10-02 15:52:45] [Iter 1415/2250] R4[578/2400] | LR: 0.026590 | E: -27.113882 | E_var:     0.1700 | E_err:   0.006443
[2025-10-02 15:52:48] [Iter 1416/2250] R4[580/2400] | LR: 0.026567 | E: -27.125031 | E_var:     0.2003 | E_err:   0.006993
[2025-10-02 15:52:50] [Iter 1417/2250] R4[582/2400] | LR: 0.026545 | E: -27.118968 | E_var:     0.2266 | E_err:   0.007439
[2025-10-02 15:52:53] [Iter 1418/2250] R4[584/2400] | LR: 0.026522 | E: -27.117427 | E_var:     0.2119 | E_err:   0.007192
[2025-10-02 15:52:55] [Iter 1419/2250] R4[586/2400] | LR: 0.026499 | E: -27.122044 | E_var:     0.2237 | E_err:   0.007389
[2025-10-02 15:52:57] [Iter 1420/2250] R4[588/2400] | LR: 0.026477 | E: -27.108145 | E_var:     0.2005 | E_err:   0.006997
[2025-10-02 15:53:00] [Iter 1421/2250] R4[590/2400] | LR: 0.026454 | E: -27.118885 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 15:53:02] [Iter 1422/2250] R4[592/2400] | LR: 0.026431 | E: -27.124029 | E_var:     0.1775 | E_err:   0.006583
[2025-10-02 15:53:05] [Iter 1423/2250] R4[594/2400] | LR: 0.026408 | E: -27.119232 | E_var:     0.1813 | E_err:   0.006654
[2025-10-02 15:53:07] [Iter 1424/2250] R4[596/2400] | LR: 0.026385 | E: -27.128695 | E_var:     0.1908 | E_err:   0.006825
[2025-10-02 15:53:09] [Iter 1425/2250] R4[598/2400] | LR: 0.026362 | E: -27.121553 | E_var:     0.1891 | E_err:   0.006794
[2025-10-02 15:53:12] [Iter 1426/2250] R4[600/2400] | LR: 0.026339 | E: -27.117582 | E_var:     0.1728 | E_err:   0.006495
[2025-10-02 15:53:14] [Iter 1427/2250] R4[602/2400] | LR: 0.026316 | E: -27.111874 | E_var:     0.2145 | E_err:   0.007237
[2025-10-02 15:53:17] [Iter 1428/2250] R4[604/2400] | LR: 0.026292 | E: -27.106675 | E_var:     0.2608 | E_err:   0.007979
[2025-10-02 15:53:19] [Iter 1429/2250] R4[606/2400] | LR: 0.026269 | E: -27.118588 | E_var:     0.1881 | E_err:   0.006777
[2025-10-02 15:53:21] [Iter 1430/2250] R4[608/2400] | LR: 0.026246 | E: -27.114500 | E_var:     0.2761 | E_err:   0.008211
[2025-10-02 15:53:24] [Iter 1431/2250] R4[610/2400] | LR: 0.026222 | E: -27.118842 | E_var:     0.1643 | E_err:   0.006333
[2025-10-02 15:53:26] [Iter 1432/2250] R4[612/2400] | LR: 0.026199 | E: -27.120549 | E_var:     0.2594 | E_err:   0.007957
[2025-10-02 15:53:29] [Iter 1433/2250] R4[614/2400] | LR: 0.026175 | E: -27.121976 | E_var:     0.2880 | E_err:   0.008385
[2025-10-02 15:53:31] [Iter 1434/2250] R4[616/2400] | LR: 0.026152 | E: -27.116629 | E_var:     0.1905 | E_err:   0.006819
[2025-10-02 15:53:33] [Iter 1435/2250] R4[618/2400] | LR: 0.026128 | E: -27.123656 | E_var:     0.1969 | E_err:   0.006933
[2025-10-02 15:53:36] [Iter 1436/2250] R4[620/2400] | LR: 0.026104 | E: -27.109748 | E_var:     0.2355 | E_err:   0.007583
[2025-10-02 15:53:38] [Iter 1437/2250] R4[622/2400] | LR: 0.026081 | E: -27.118428 | E_var:     0.1896 | E_err:   0.006803
[2025-10-02 15:53:41] [Iter 1438/2250] R4[624/2400] | LR: 0.026057 | E: -27.127184 | E_var:     0.1955 | E_err:   0.006908
[2025-10-02 15:53:43] [Iter 1439/2250] R4[626/2400] | LR: 0.026033 | E: -27.117278 | E_var:     0.2015 | E_err:   0.007014
[2025-10-02 15:53:45] [Iter 1440/2250] R4[628/2400] | LR: 0.026009 | E: -27.121921 | E_var:     0.1726 | E_err:   0.006491
[2025-10-02 15:53:48] [Iter 1441/2250] R4[630/2400] | LR: 0.025985 | E: -27.105634 | E_var:     0.2169 | E_err:   0.007277
[2025-10-02 15:53:50] [Iter 1442/2250] R4[632/2400] | LR: 0.025961 | E: -27.112804 | E_var:     0.2150 | E_err:   0.007246
[2025-10-02 15:53:53] [Iter 1443/2250] R4[634/2400] | LR: 0.025937 | E: -27.114021 | E_var:     0.2356 | E_err:   0.007584
[2025-10-02 15:53:55] [Iter 1444/2250] R4[636/2400] | LR: 0.025913 | E: -27.105499 | E_var:     0.1650 | E_err:   0.006348
[2025-10-02 15:53:57] [Iter 1445/2250] R4[638/2400] | LR: 0.025888 | E: -27.126231 | E_var:     0.2057 | E_err:   0.007087
[2025-10-02 15:54:00] [Iter 1446/2250] R4[640/2400] | LR: 0.025864 | E: -27.116777 | E_var:     0.1910 | E_err:   0.006829
[2025-10-02 15:54:02] [Iter 1447/2250] R4[642/2400] | LR: 0.025840 | E: -27.118505 | E_var:     0.1969 | E_err:   0.006933
[2025-10-02 15:54:05] [Iter 1448/2250] R4[644/2400] | LR: 0.025815 | E: -27.114007 | E_var:     0.2305 | E_err:   0.007502
[2025-10-02 15:54:07] [Iter 1449/2250] R4[646/2400] | LR: 0.025791 | E: -27.121617 | E_var:     0.1930 | E_err:   0.006865
[2025-10-02 15:54:09] [Iter 1450/2250] R4[648/2400] | LR: 0.025766 | E: -27.120520 | E_var:     0.3981 | E_err:   0.009859
[2025-10-02 15:54:12] [Iter 1451/2250] R4[650/2400] | LR: 0.025742 | E: -27.111865 | E_var:     0.1866 | E_err:   0.006750
[2025-10-02 15:54:14] [Iter 1452/2250] R4[652/2400] | LR: 0.025717 | E: -27.117982 | E_var:     0.2265 | E_err:   0.007437
[2025-10-02 15:54:17] [Iter 1453/2250] R4[654/2400] | LR: 0.025693 | E: -27.098853 | E_var:     0.2825 | E_err:   0.008305
[2025-10-02 15:54:19] [Iter 1454/2250] R4[656/2400] | LR: 0.025668 | E: -27.103422 | E_var:     0.3858 | E_err:   0.009705
[2025-10-02 15:54:21] [Iter 1455/2250] R4[658/2400] | LR: 0.025643 | E: -27.126291 | E_var:     0.2479 | E_err:   0.007780
[2025-10-02 15:54:24] [Iter 1456/2250] R4[660/2400] | LR: 0.025618 | E: -27.113736 | E_var:     0.2017 | E_err:   0.007017
[2025-10-02 15:54:26] [Iter 1457/2250] R4[662/2400] | LR: 0.025593 | E: -27.117403 | E_var:     0.1872 | E_err:   0.006761
[2025-10-02 15:54:29] [Iter 1458/2250] R4[664/2400] | LR: 0.025568 | E: -27.093538 | E_var:     0.4387 | E_err:   0.010349
[2025-10-02 15:54:31] [Iter 1459/2250] R4[666/2400] | LR: 0.025543 | E: -27.122225 | E_var:     0.2333 | E_err:   0.007548
[2025-10-02 15:54:33] [Iter 1460/2250] R4[668/2400] | LR: 0.025518 | E: -27.122218 | E_var:     0.2079 | E_err:   0.007124
[2025-10-02 15:54:36] [Iter 1461/2250] R4[670/2400] | LR: 0.025493 | E: -27.115217 | E_var:     0.1559 | E_err:   0.006170
[2025-10-02 15:54:38] [Iter 1462/2250] R4[672/2400] | LR: 0.025468 | E: -27.117192 | E_var:     0.3253 | E_err:   0.008912
[2025-10-02 15:54:41] [Iter 1463/2250] R4[674/2400] | LR: 0.025443 | E: -27.136774 | E_var:     0.1991 | E_err:   0.006972
[2025-10-02 15:54:43] [Iter 1464/2250] R4[676/2400] | LR: 0.025417 | E: -27.115094 | E_var:     0.2028 | E_err:   0.007036
[2025-10-02 15:54:45] [Iter 1465/2250] R4[678/2400] | LR: 0.025392 | E: -27.124981 | E_var:     0.2221 | E_err:   0.007363
[2025-10-02 15:54:48] [Iter 1466/2250] R4[680/2400] | LR: 0.025367 | E: -27.115747 | E_var:     0.2061 | E_err:   0.007094
[2025-10-02 15:54:50] [Iter 1467/2250] R4[682/2400] | LR: 0.025341 | E: -27.121574 | E_var:     0.2559 | E_err:   0.007904
[2025-10-02 15:54:53] [Iter 1468/2250] R4[684/2400] | LR: 0.025316 | E: -27.113004 | E_var:     0.1948 | E_err:   0.006897
[2025-10-02 15:54:55] [Iter 1469/2250] R4[686/2400] | LR: 0.025290 | E: -27.116831 | E_var:     0.2128 | E_err:   0.007208
[2025-10-02 15:54:57] [Iter 1470/2250] R4[688/2400] | LR: 0.025264 | E: -27.116913 | E_var:     0.2708 | E_err:   0.008131
[2025-10-02 15:55:00] [Iter 1471/2250] R4[690/2400] | LR: 0.025239 | E: -27.105772 | E_var:     0.2265 | E_err:   0.007436
[2025-10-02 15:55:02] [Iter 1472/2250] R4[692/2400] | LR: 0.025213 | E: -27.121376 | E_var:     0.1954 | E_err:   0.006908
[2025-10-02 15:55:04] [Iter 1473/2250] R4[694/2400] | LR: 0.025187 | E: -27.123248 | E_var:     0.2273 | E_err:   0.007450
[2025-10-02 15:55:07] [Iter 1474/2250] R4[696/2400] | LR: 0.025161 | E: -27.124195 | E_var:     0.2138 | E_err:   0.007224
[2025-10-02 15:55:09] [Iter 1475/2250] R4[698/2400] | LR: 0.025135 | E: -27.113167 | E_var:     0.2037 | E_err:   0.007051
[2025-10-02 15:55:12] [Iter 1476/2250] R4[700/2400] | LR: 0.025110 | E: -27.125032 | E_var:     0.1903 | E_err:   0.006816
[2025-10-02 15:55:14] [Iter 1477/2250] R4[702/2400] | LR: 0.025084 | E: -27.122865 | E_var:     0.2044 | E_err:   0.007064
[2025-10-02 15:55:16] [Iter 1478/2250] R4[704/2400] | LR: 0.025057 | E: -27.119945 | E_var:     0.2221 | E_err:   0.007363
[2025-10-02 15:55:19] [Iter 1479/2250] R4[706/2400] | LR: 0.025031 | E: -27.123300 | E_var:     0.2119 | E_err:   0.007192
[2025-10-02 15:55:21] [Iter 1480/2250] R4[708/2400] | LR: 0.025005 | E: -27.121592 | E_var:     0.2512 | E_err:   0.007831
[2025-10-02 15:55:24] [Iter 1481/2250] R4[710/2400] | LR: 0.024979 | E: -27.113301 | E_var:     0.2244 | E_err:   0.007403
[2025-10-02 15:55:26] [Iter 1482/2250] R4[712/2400] | LR: 0.024953 | E: -27.111459 | E_var:     0.2220 | E_err:   0.007361
[2025-10-02 15:55:28] [Iter 1483/2250] R4[714/2400] | LR: 0.024927 | E: -27.122841 | E_var:     0.1824 | E_err:   0.006673
[2025-10-02 15:55:31] [Iter 1484/2250] R4[716/2400] | LR: 0.024900 | E: -27.116671 | E_var:     0.3214 | E_err:   0.008858
[2025-10-02 15:55:33] [Iter 1485/2250] R4[718/2400] | LR: 0.024874 | E: -27.128173 | E_var:     0.3074 | E_err:   0.008663
[2025-10-02 15:55:36] [Iter 1486/2250] R4[720/2400] | LR: 0.024847 | E: -27.115023 | E_var:     0.1769 | E_err:   0.006572
[2025-10-02 15:55:38] [Iter 1487/2250] R4[722/2400] | LR: 0.024821 | E: -27.121736 | E_var:     0.1864 | E_err:   0.006745
[2025-10-02 15:55:40] [Iter 1488/2250] R4[724/2400] | LR: 0.024794 | E: -27.126347 | E_var:     0.1678 | E_err:   0.006400
[2025-10-02 15:55:43] [Iter 1489/2250] R4[726/2400] | LR: 0.024768 | E: -27.110017 | E_var:     0.2608 | E_err:   0.007979
[2025-10-02 15:55:45] [Iter 1490/2250] R4[728/2400] | LR: 0.024741 | E: -27.106256 | E_var:     0.1882 | E_err:   0.006779
[2025-10-02 15:55:48] [Iter 1491/2250] R4[730/2400] | LR: 0.024714 | E: -27.128763 | E_var:     0.2027 | E_err:   0.007034
[2025-10-02 15:55:50] [Iter 1492/2250] R4[732/2400] | LR: 0.024688 | E: -27.114105 | E_var:     0.2187 | E_err:   0.007307
[2025-10-02 15:55:52] [Iter 1493/2250] R4[734/2400] | LR: 0.024661 | E: -27.115023 | E_var:     0.2223 | E_err:   0.007366
[2025-10-02 15:55:55] [Iter 1494/2250] R4[736/2400] | LR: 0.024634 | E: -27.132997 | E_var:     0.1800 | E_err:   0.006628
[2025-10-02 15:55:57] [Iter 1495/2250] R4[738/2400] | LR: 0.024607 | E: -27.099189 | E_var:     0.2068 | E_err:   0.007106
[2025-10-02 15:56:00] [Iter 1496/2250] R4[740/2400] | LR: 0.024580 | E: -27.113602 | E_var:     0.2461 | E_err:   0.007752
[2025-10-02 15:56:02] [Iter 1497/2250] R4[742/2400] | LR: 0.024553 | E: -27.124178 | E_var:     0.1819 | E_err:   0.006663
[2025-10-02 15:56:04] [Iter 1498/2250] R4[744/2400] | LR: 0.024526 | E: -27.113383 | E_var:     0.1691 | E_err:   0.006425
[2025-10-02 15:56:07] [Iter 1499/2250] R4[746/2400] | LR: 0.024499 | E: -27.115931 | E_var:     0.2277 | E_err:   0.007456
[2025-10-02 15:56:09] [Iter 1500/2250] R4[748/2400] | LR: 0.024472 | E: -27.123273 | E_var:     0.1964 | E_err:   0.006925
[2025-10-02 15:56:12] [Iter 1501/2250] R4[750/2400] | LR: 0.024445 | E: -27.131752 | E_var:     0.2178 | E_err:   0.007292
[2025-10-02 15:56:14] [Iter 1502/2250] R4[752/2400] | LR: 0.024417 | E: -27.123705 | E_var:     0.2137 | E_err:   0.007224
[2025-10-02 15:56:16] [Iter 1503/2250] R4[754/2400] | LR: 0.024390 | E: -27.115542 | E_var:     0.2060 | E_err:   0.007091
[2025-10-02 15:56:19] [Iter 1504/2250] R4[756/2400] | LR: 0.024363 | E: -27.130530 | E_var:     0.3691 | E_err:   0.009493
[2025-10-02 15:56:21] [Iter 1505/2250] R4[758/2400] | LR: 0.024335 | E: -27.111411 | E_var:     0.2038 | E_err:   0.007053
[2025-10-02 15:56:24] [Iter 1506/2250] R4[760/2400] | LR: 0.024308 | E: -27.117924 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 15:56:26] [Iter 1507/2250] R4[762/2400] | LR: 0.024281 | E: -27.129400 | E_var:     0.1875 | E_err:   0.006766
[2025-10-02 15:56:28] [Iter 1508/2250] R4[764/2400] | LR: 0.024253 | E: -27.127806 | E_var:     0.1753 | E_err:   0.006543
[2025-10-02 15:56:31] [Iter 1509/2250] R4[766/2400] | LR: 0.024225 | E: -27.122551 | E_var:     0.2220 | E_err:   0.007362
[2025-10-02 15:56:33] [Iter 1510/2250] R4[768/2400] | LR: 0.024198 | E: -27.122123 | E_var:     0.2172 | E_err:   0.007282
[2025-10-02 15:56:36] [Iter 1511/2250] R4[770/2400] | LR: 0.024170 | E: -27.121020 | E_var:     0.1979 | E_err:   0.006950
[2025-10-02 15:56:38] [Iter 1512/2250] R4[772/2400] | LR: 0.024142 | E: -27.117921 | E_var:     0.1908 | E_err:   0.006826
[2025-10-02 15:56:40] [Iter 1513/2250] R4[774/2400] | LR: 0.024115 | E: -27.124294 | E_var:     0.2047 | E_err:   0.007069
[2025-10-02 15:56:43] [Iter 1514/2250] R4[776/2400] | LR: 0.024087 | E: -27.110205 | E_var:     0.2269 | E_err:   0.007443
[2025-10-02 15:56:45] [Iter 1515/2250] R4[778/2400] | LR: 0.024059 | E: -27.131761 | E_var:     0.2104 | E_err:   0.007168
[2025-10-02 15:56:48] [Iter 1516/2250] R4[780/2400] | LR: 0.024031 | E: -27.120368 | E_var:     0.1790 | E_err:   0.006611
[2025-10-02 15:56:50] [Iter 1517/2250] R4[782/2400] | LR: 0.024003 | E: -27.111892 | E_var:     0.2287 | E_err:   0.007472
[2025-10-02 15:56:52] [Iter 1518/2250] R4[784/2400] | LR: 0.023975 | E: -27.120283 | E_var:     0.2075 | E_err:   0.007117
[2025-10-02 15:56:55] [Iter 1519/2250] R4[786/2400] | LR: 0.023947 | E: -27.106667 | E_var:     0.2130 | E_err:   0.007212
[2025-10-02 15:56:57] [Iter 1520/2250] R4[788/2400] | LR: 0.023919 | E: -27.103320 | E_var:     0.2064 | E_err:   0.007098
[2025-10-02 15:57:00] [Iter 1521/2250] R4[790/2400] | LR: 0.023891 | E: -27.119282 | E_var:     0.2239 | E_err:   0.007393
[2025-10-02 15:57:02] [Iter 1522/2250] R4[792/2400] | LR: 0.023863 | E: -27.102924 | E_var:     0.2710 | E_err:   0.008134
[2025-10-02 15:57:04] [Iter 1523/2250] R4[794/2400] | LR: 0.023835 | E: -27.118469 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 15:57:07] [Iter 1524/2250] R4[796/2400] | LR: 0.023807 | E: -27.120343 | E_var:     0.1704 | E_err:   0.006449
[2025-10-02 15:57:09] [Iter 1525/2250] R4[798/2400] | LR: 0.023778 | E: -27.115599 | E_var:     0.2077 | E_err:   0.007121
[2025-10-02 15:57:12] [Iter 1526/2250] R4[800/2400] | LR: 0.023750 | E: -27.114764 | E_var:     0.2198 | E_err:   0.007325
[2025-10-02 15:57:14] [Iter 1527/2250] R4[802/2400] | LR: 0.023722 | E: -27.127294 | E_var:     0.1665 | E_err:   0.006376
[2025-10-02 15:57:16] [Iter 1528/2250] R4[804/2400] | LR: 0.023693 | E: -27.119370 | E_var:     0.3083 | E_err:   0.008675
[2025-10-02 15:57:19] [Iter 1529/2250] R4[806/2400] | LR: 0.023665 | E: -27.119472 | E_var:     0.2458 | E_err:   0.007747
[2025-10-02 15:57:21] [Iter 1530/2250] R4[808/2400] | LR: 0.023636 | E: -27.126326 | E_var:     0.1853 | E_err:   0.006725
[2025-10-02 15:57:23] [Iter 1531/2250] R4[810/2400] | LR: 0.023608 | E: -27.105056 | E_var:     0.2260 | E_err:   0.007427
[2025-10-02 15:57:26] [Iter 1532/2250] R4[812/2400] | LR: 0.023579 | E: -27.109055 | E_var:     0.2048 | E_err:   0.007071
[2025-10-02 15:57:28] [Iter 1533/2250] R4[814/2400] | LR: 0.023551 | E: -27.122351 | E_var:     0.2611 | E_err:   0.007984
[2025-10-02 15:57:31] [Iter 1534/2250] R4[816/2400] | LR: 0.023522 | E: -27.108420 | E_var:     0.2203 | E_err:   0.007334
[2025-10-02 15:57:33] [Iter 1535/2250] R4[818/2400] | LR: 0.023493 | E: -27.115450 | E_var:     0.1992 | E_err:   0.006974
[2025-10-02 15:57:35] [Iter 1536/2250] R4[820/2400] | LR: 0.023464 | E: -27.121330 | E_var:     0.1554 | E_err:   0.006160
[2025-10-02 15:57:38] [Iter 1537/2250] R4[822/2400] | LR: 0.023436 | E: -27.121905 | E_var:     0.1684 | E_err:   0.006413
[2025-10-02 15:57:40] [Iter 1538/2250] R4[824/2400] | LR: 0.023407 | E: -27.115604 | E_var:     0.1676 | E_err:   0.006398
[2025-10-02 15:57:43] [Iter 1539/2250] R4[826/2400] | LR: 0.023378 | E: -27.126244 | E_var:     0.1841 | E_err:   0.006705
[2025-10-02 15:57:45] [Iter 1540/2250] R4[828/2400] | LR: 0.023349 | E: -27.124825 | E_var:     0.1957 | E_err:   0.006912
[2025-10-02 15:57:47] [Iter 1541/2250] R4[830/2400] | LR: 0.023320 | E: -27.131754 | E_var:     0.2092 | E_err:   0.007147
[2025-10-02 15:57:50] [Iter 1542/2250] R4[832/2400] | LR: 0.023291 | E: -27.120577 | E_var:     0.1880 | E_err:   0.006774
[2025-10-02 15:57:52] [Iter 1543/2250] R4[834/2400] | LR: 0.023262 | E: -27.134554 | E_var:     0.2258 | E_err:   0.007425
[2025-10-02 15:57:55] [Iter 1544/2250] R4[836/2400] | LR: 0.023233 | E: -27.126109 | E_var:     0.2566 | E_err:   0.007915
[2025-10-02 15:57:57] [Iter 1545/2250] R4[838/2400] | LR: 0.023204 | E: -27.113756 | E_var:     0.2197 | E_err:   0.007324
[2025-10-02 15:57:59] [Iter 1546/2250] R4[840/2400] | LR: 0.023175 | E: -27.141926 | E_var:     0.6801 | E_err:   0.012886
[2025-10-02 15:58:02] [Iter 1547/2250] R4[842/2400] | LR: 0.023146 | E: -27.110159 | E_var:     0.1627 | E_err:   0.006303
[2025-10-02 15:58:04] [Iter 1548/2250] R4[844/2400] | LR: 0.023116 | E: -27.123717 | E_var:     0.2703 | E_err:   0.008123
[2025-10-02 15:58:07] [Iter 1549/2250] R4[846/2400] | LR: 0.023087 | E: -27.116083 | E_var:     0.1964 | E_err:   0.006925
[2025-10-02 15:58:09] [Iter 1550/2250] R4[848/2400] | LR: 0.023058 | E: -27.120715 | E_var:     0.2608 | E_err:   0.007980
[2025-10-02 15:58:11] [Iter 1551/2250] R4[850/2400] | LR: 0.023029 | E: -27.130884 | E_var:     0.2516 | E_err:   0.007837
[2025-10-02 15:58:14] [Iter 1552/2250] R4[852/2400] | LR: 0.022999 | E: -27.124868 | E_var:     0.1696 | E_err:   0.006434
[2025-10-02 15:58:16] [Iter 1553/2250] R4[854/2400] | LR: 0.022970 | E: -27.113934 | E_var:     0.2296 | E_err:   0.007487
[2025-10-02 15:58:19] [Iter 1554/2250] R4[856/2400] | LR: 0.022940 | E: -27.116966 | E_var:     0.2235 | E_err:   0.007386
[2025-10-02 15:58:21] [Iter 1555/2250] R4[858/2400] | LR: 0.022911 | E: -27.113279 | E_var:     0.3563 | E_err:   0.009326
[2025-10-02 15:58:23] [Iter 1556/2250] R4[860/2400] | LR: 0.022881 | E: -27.122176 | E_var:     0.2797 | E_err:   0.008263
[2025-10-02 15:58:26] [Iter 1557/2250] R4[862/2400] | LR: 0.022852 | E: -27.119983 | E_var:     0.2057 | E_err:   0.007087
[2025-10-02 15:58:28] [Iter 1558/2250] R4[864/2400] | LR: 0.022822 | E: -27.114832 | E_var:     0.1998 | E_err:   0.006985
[2025-10-02 15:58:31] [Iter 1559/2250] R4[866/2400] | LR: 0.022793 | E: -27.127642 | E_var:     0.2466 | E_err:   0.007758
[2025-10-02 15:58:33] [Iter 1560/2250] R4[868/2400] | LR: 0.022763 | E: -27.121790 | E_var:     0.2292 | E_err:   0.007480
[2025-10-02 15:58:35] [Iter 1561/2250] R4[870/2400] | LR: 0.022733 | E: -27.109182 | E_var:     0.1751 | E_err:   0.006538
[2025-10-02 15:58:38] [Iter 1562/2250] R4[872/2400] | LR: 0.022704 | E: -27.129226 | E_var:     0.2777 | E_err:   0.008234
[2025-10-02 15:58:40] [Iter 1563/2250] R4[874/2400] | LR: 0.022674 | E: -27.115313 | E_var:     0.1994 | E_err:   0.006977
[2025-10-02 15:58:43] [Iter 1564/2250] R4[876/2400] | LR: 0.022644 | E: -27.121253 | E_var:     0.2288 | E_err:   0.007473
[2025-10-02 15:58:45] [Iter 1565/2250] R4[878/2400] | LR: 0.022614 | E: -27.125817 | E_var:     0.2022 | E_err:   0.007027
[2025-10-02 15:58:47] [Iter 1566/2250] R4[880/2400] | LR: 0.022584 | E: -27.112942 | E_var:     0.1546 | E_err:   0.006144
[2025-10-02 15:58:50] [Iter 1567/2250] R4[882/2400] | LR: 0.022554 | E: -27.116665 | E_var:     0.1699 | E_err:   0.006440
[2025-10-02 15:58:52] [Iter 1568/2250] R4[884/2400] | LR: 0.022524 | E: -27.124499 | E_var:     0.2374 | E_err:   0.007613
[2025-10-02 15:58:55] [Iter 1569/2250] R4[886/2400] | LR: 0.022494 | E: -27.122753 | E_var:     0.1802 | E_err:   0.006633
[2025-10-02 15:58:57] [Iter 1570/2250] R4[888/2400] | LR: 0.022464 | E: -27.131623 | E_var:     0.2248 | E_err:   0.007408
[2025-10-02 15:58:59] [Iter 1571/2250] R4[890/2400] | LR: 0.022434 | E: -27.116456 | E_var:     0.2528 | E_err:   0.007856
[2025-10-02 15:59:02] [Iter 1572/2250] R4[892/2400] | LR: 0.022404 | E: -27.117208 | E_var:     0.1790 | E_err:   0.006611
[2025-10-02 15:59:04] [Iter 1573/2250] R4[894/2400] | LR: 0.022374 | E: -27.121049 | E_var:     0.2078 | E_err:   0.007123
[2025-10-02 15:59:07] [Iter 1574/2250] R4[896/2400] | LR: 0.022344 | E: -27.117482 | E_var:     0.1967 | E_err:   0.006930
[2025-10-02 15:59:09] [Iter 1575/2250] R4[898/2400] | LR: 0.022314 | E: -27.119111 | E_var:     0.1776 | E_err:   0.006585
[2025-10-02 15:59:11] [Iter 1576/2250] R4[900/2400] | LR: 0.022284 | E: -27.115951 | E_var:     0.2237 | E_err:   0.007390
[2025-10-02 15:59:14] [Iter 1577/2250] R4[902/2400] | LR: 0.022253 | E: -27.120622 | E_var:     0.2006 | E_err:   0.006998
[2025-10-02 15:59:16] [Iter 1578/2250] R4[904/2400] | LR: 0.022223 | E: -27.122206 | E_var:     0.1762 | E_err:   0.006558
[2025-10-02 15:59:19] [Iter 1579/2250] R4[906/2400] | LR: 0.022193 | E: -27.123229 | E_var:     0.1832 | E_err:   0.006688
[2025-10-02 15:59:21] [Iter 1580/2250] R4[908/2400] | LR: 0.022162 | E: -27.126319 | E_var:     0.3276 | E_err:   0.008944
[2025-10-02 15:59:23] [Iter 1581/2250] R4[910/2400] | LR: 0.022132 | E: -27.121107 | E_var:     0.1868 | E_err:   0.006754
[2025-10-02 15:59:26] [Iter 1582/2250] R4[912/2400] | LR: 0.022102 | E: -27.124883 | E_var:     0.1723 | E_err:   0.006486
[2025-10-02 15:59:28] [Iter 1583/2250] R4[914/2400] | LR: 0.022071 | E: -27.119001 | E_var:     0.2110 | E_err:   0.007178
[2025-10-02 15:59:31] [Iter 1584/2250] R4[916/2400] | LR: 0.022041 | E: -27.125643 | E_var:     0.2223 | E_err:   0.007368
[2025-10-02 15:59:33] [Iter 1585/2250] R4[918/2400] | LR: 0.022010 | E: -27.116923 | E_var:     0.2271 | E_err:   0.007447
[2025-10-02 15:59:35] [Iter 1586/2250] R4[920/2400] | LR: 0.021980 | E: -27.111876 | E_var:     0.2110 | E_err:   0.007178
[2025-10-02 15:59:38] [Iter 1587/2250] R4[922/2400] | LR: 0.021949 | E: -27.121928 | E_var:     0.1746 | E_err:   0.006530
[2025-10-02 15:59:40] [Iter 1588/2250] R4[924/2400] | LR: 0.021918 | E: -27.119471 | E_var:     0.1886 | E_err:   0.006786
[2025-10-02 15:59:43] [Iter 1589/2250] R4[926/2400] | LR: 0.021888 | E: -27.118281 | E_var:     0.1845 | E_err:   0.006712
[2025-10-02 15:59:45] [Iter 1590/2250] R4[928/2400] | LR: 0.021857 | E: -27.113822 | E_var:     0.2578 | E_err:   0.007934
[2025-10-02 15:59:47] [Iter 1591/2250] R4[930/2400] | LR: 0.021826 | E: -27.099159 | E_var:     0.1805 | E_err:   0.006639
[2025-10-02 15:59:50] [Iter 1592/2250] R4[932/2400] | LR: 0.021796 | E: -27.114661 | E_var:     0.2028 | E_err:   0.007036
[2025-10-02 15:59:52] [Iter 1593/2250] R4[934/2400] | LR: 0.021765 | E: -27.120163 | E_var:     0.1881 | E_err:   0.006777
[2025-10-02 15:59:55] [Iter 1594/2250] R4[936/2400] | LR: 0.021734 | E: -27.110126 | E_var:     0.1782 | E_err:   0.006596
[2025-10-02 15:59:57] [Iter 1595/2250] R4[938/2400] | LR: 0.021703 | E: -27.129099 | E_var:     0.2306 | E_err:   0.007503
[2025-10-02 15:59:59] [Iter 1596/2250] R4[940/2400] | LR: 0.021673 | E: -27.122870 | E_var:     0.1797 | E_err:   0.006623
[2025-10-02 16:00:02] [Iter 1597/2250] R4[942/2400] | LR: 0.021642 | E: -27.114857 | E_var:     0.1960 | E_err:   0.006918
[2025-10-02 16:00:04] [Iter 1598/2250] R4[944/2400] | LR: 0.021611 | E: -27.119740 | E_var:     0.1962 | E_err:   0.006921
[2025-10-02 16:00:07] [Iter 1599/2250] R4[946/2400] | LR: 0.021580 | E: -27.127157 | E_var:     0.1860 | E_err:   0.006738
[2025-10-02 16:00:10] [Iter 1600/2250] R4[948/2400] | LR: 0.021549 | E: -27.114448 | E_var:     0.2310 | E_err:   0.007511
[2025-10-02 16:00:10] ✓ Checkpoint saved: checkpoint_iter_001600.pkl
[2025-10-02 16:00:12] [Iter 1601/2250] R4[950/2400] | LR: 0.021518 | E: -27.116072 | E_var:     0.1704 | E_err:   0.006450
[2025-10-02 16:00:14] [Iter 1602/2250] R4[952/2400] | LR: 0.021487 | E: -27.113455 | E_var:     0.1634 | E_err:   0.006316
[2025-10-02 16:00:17] [Iter 1603/2250] R4[954/2400] | LR: 0.021456 | E: -27.122677 | E_var:     0.2246 | E_err:   0.007405
[2025-10-02 16:00:19] [Iter 1604/2250] R4[956/2400] | LR: 0.021425 | E: -27.109741 | E_var:     0.1868 | E_err:   0.006754
[2025-10-02 16:00:21] [Iter 1605/2250] R4[958/2400] | LR: 0.021394 | E: -27.125202 | E_var:     0.2339 | E_err:   0.007557
[2025-10-02 16:00:24] [Iter 1606/2250] R4[960/2400] | LR: 0.021363 | E: -27.124828 | E_var:     0.1808 | E_err:   0.006644
[2025-10-02 16:00:26] [Iter 1607/2250] R4[962/2400] | LR: 0.021332 | E: -27.136228 | E_var:     0.1996 | E_err:   0.006981
[2025-10-02 16:00:29] [Iter 1608/2250] R4[964/2400] | LR: 0.021300 | E: -27.130021 | E_var:     0.2909 | E_err:   0.008428
[2025-10-02 16:00:31] [Iter 1609/2250] R4[966/2400] | LR: 0.021269 | E: -27.114894 | E_var:     0.2543 | E_err:   0.007879
[2025-10-02 16:00:33] [Iter 1610/2250] R4[968/2400] | LR: 0.021238 | E: -27.115888 | E_var:     0.1808 | E_err:   0.006643
[2025-10-02 16:00:36] [Iter 1611/2250] R4[970/2400] | LR: 0.021207 | E: -27.113080 | E_var:     0.2087 | E_err:   0.007138
[2025-10-02 16:00:38] [Iter 1612/2250] R4[972/2400] | LR: 0.021176 | E: -27.113740 | E_var:     0.1814 | E_err:   0.006654
[2025-10-02 16:00:41] [Iter 1613/2250] R4[974/2400] | LR: 0.021144 | E: -27.119676 | E_var:     0.1833 | E_err:   0.006689
[2025-10-02 16:00:43] [Iter 1614/2250] R4[976/2400] | LR: 0.021113 | E: -27.129541 | E_var:     0.1780 | E_err:   0.006593
[2025-10-02 16:00:45] [Iter 1615/2250] R4[978/2400] | LR: 0.021082 | E: -27.124773 | E_var:     0.1781 | E_err:   0.006594
[2025-10-02 16:00:48] [Iter 1616/2250] R4[980/2400] | LR: 0.021050 | E: -27.113707 | E_var:     0.2197 | E_err:   0.007324
[2025-10-02 16:00:50] [Iter 1617/2250] R4[982/2400] | LR: 0.021019 | E: -27.115710 | E_var:     0.1989 | E_err:   0.006969
[2025-10-02 16:00:53] [Iter 1618/2250] R4[984/2400] | LR: 0.020987 | E: -27.108387 | E_var:     0.2137 | E_err:   0.007222
[2025-10-02 16:00:55] [Iter 1619/2250] R4[986/2400] | LR: 0.020956 | E: -27.121047 | E_var:     0.2687 | E_err:   0.008099
[2025-10-02 16:00:58] [Iter 1620/2250] R4[988/2400] | LR: 0.020924 | E: -27.114266 | E_var:     0.1965 | E_err:   0.006926
[2025-10-02 16:01:00] [Iter 1621/2250] R4[990/2400] | LR: 0.020893 | E: -27.120869 | E_var:     0.2250 | E_err:   0.007412
[2025-10-02 16:01:02] [Iter 1622/2250] R4[992/2400] | LR: 0.020861 | E: -27.125759 | E_var:     0.1944 | E_err:   0.006890
[2025-10-02 16:01:05] [Iter 1623/2250] R4[994/2400] | LR: 0.020830 | E: -27.131185 | E_var:     0.2283 | E_err:   0.007466
[2025-10-02 16:01:07] [Iter 1624/2250] R4[996/2400] | LR: 0.020798 | E: -27.120436 | E_var:     0.2012 | E_err:   0.007008
[2025-10-02 16:01:10] [Iter 1625/2250] R4[998/2400] | LR: 0.020767 | E: -27.123312 | E_var:     0.2133 | E_err:   0.007216
[2025-10-02 16:01:12] [Iter 1626/2250] R4[1000/2400] | LR: 0.020735 | E: -27.117811 | E_var:     0.1700 | E_err:   0.006442
[2025-10-02 16:01:14] [Iter 1627/2250] R4[1002/2400] | LR: 0.020704 | E: -27.126050 | E_var:     0.1841 | E_err:   0.006705
[2025-10-02 16:01:17] [Iter 1628/2250] R4[1004/2400] | LR: 0.020672 | E: -27.115436 | E_var:     0.1644 | E_err:   0.006335
[2025-10-02 16:01:19] [Iter 1629/2250] R4[1006/2400] | LR: 0.020640 | E: -27.108871 | E_var:     0.2011 | E_err:   0.007007
[2025-10-02 16:01:22] [Iter 1630/2250] R4[1008/2400] | LR: 0.020609 | E: -27.111898 | E_var:     0.1936 | E_err:   0.006875
[2025-10-02 16:01:24] [Iter 1631/2250] R4[1010/2400] | LR: 0.020577 | E: -27.124859 | E_var:     0.1820 | E_err:   0.006666
[2025-10-02 16:01:26] [Iter 1632/2250] R4[1012/2400] | LR: 0.020545 | E: -27.120327 | E_var:     0.1860 | E_err:   0.006739
[2025-10-02 16:01:29] [Iter 1633/2250] R4[1014/2400] | LR: 0.020513 | E: -27.108377 | E_var:     0.2537 | E_err:   0.007870
[2025-10-02 16:01:31] [Iter 1634/2250] R4[1016/2400] | LR: 0.020482 | E: -27.117720 | E_var:     0.2191 | E_err:   0.007314
[2025-10-02 16:01:34] [Iter 1635/2250] R4[1018/2400] | LR: 0.020450 | E: -27.121109 | E_var:     0.2082 | E_err:   0.007130
[2025-10-02 16:01:36] [Iter 1636/2250] R4[1020/2400] | LR: 0.020418 | E: -27.127842 | E_var:     0.2510 | E_err:   0.007828
[2025-10-02 16:01:38] [Iter 1637/2250] R4[1022/2400] | LR: 0.020386 | E: -27.121648 | E_var:     0.1794 | E_err:   0.006619
[2025-10-02 16:01:41] [Iter 1638/2250] R4[1024/2400] | LR: 0.020354 | E: -27.121735 | E_var:     0.1981 | E_err:   0.006955
[2025-10-02 16:01:43] [Iter 1639/2250] R4[1026/2400] | LR: 0.020323 | E: -27.128265 | E_var:     0.1758 | E_err:   0.006551
[2025-10-02 16:01:46] [Iter 1640/2250] R4[1028/2400] | LR: 0.020291 | E: -27.124074 | E_var:     0.1895 | E_err:   0.006803
[2025-10-02 16:01:48] [Iter 1641/2250] R4[1030/2400] | LR: 0.020259 | E: -27.137495 | E_var:     0.1833 | E_err:   0.006690
[2025-10-02 16:01:50] [Iter 1642/2250] R4[1032/2400] | LR: 0.020227 | E: -27.113315 | E_var:     0.2246 | E_err:   0.007406
[2025-10-02 16:01:53] [Iter 1643/2250] R4[1034/2400] | LR: 0.020195 | E: -27.129552 | E_var:     0.2408 | E_err:   0.007668
[2025-10-02 16:01:55] [Iter 1644/2250] R4[1036/2400] | LR: 0.020163 | E: -27.119360 | E_var:     0.1938 | E_err:   0.006878
[2025-10-02 16:01:58] [Iter 1645/2250] R4[1038/2400] | LR: 0.020131 | E: -27.114202 | E_var:     0.2425 | E_err:   0.007694
[2025-10-02 16:02:00] [Iter 1646/2250] R4[1040/2400] | LR: 0.020099 | E: -27.122402 | E_var:     0.1571 | E_err:   0.006194
[2025-10-02 16:02:02] [Iter 1647/2250] R4[1042/2400] | LR: 0.020067 | E: -27.127080 | E_var:     0.1835 | E_err:   0.006693
[2025-10-02 16:02:05] [Iter 1648/2250] R4[1044/2400] | LR: 0.020035 | E: -27.114243 | E_var:     0.1917 | E_err:   0.006842
[2025-10-02 16:02:07] [Iter 1649/2250] R4[1046/2400] | LR: 0.020003 | E: -27.116909 | E_var:     0.2444 | E_err:   0.007725
[2025-10-02 16:02:10] [Iter 1650/2250] R4[1048/2400] | LR: 0.019971 | E: -27.110203 | E_var:     0.1852 | E_err:   0.006724
[2025-10-02 16:02:12] [Iter 1651/2250] R4[1050/2400] | LR: 0.019939 | E: -27.122201 | E_var:     0.1816 | E_err:   0.006659
[2025-10-02 16:02:15] [Iter 1652/2250] R4[1052/2400] | LR: 0.019907 | E: -27.112260 | E_var:     0.3518 | E_err:   0.009268
[2025-10-02 16:02:17] [Iter 1653/2250] R4[1054/2400] | LR: 0.019874 | E: -27.132196 | E_var:     0.1817 | E_err:   0.006661
[2025-10-02 16:02:19] [Iter 1654/2250] R4[1056/2400] | LR: 0.019842 | E: -27.114848 | E_var:     0.2091 | E_err:   0.007145
[2025-10-02 16:02:22] [Iter 1655/2250] R4[1058/2400] | LR: 0.019810 | E: -27.121276 | E_var:     0.1548 | E_err:   0.006148
[2025-10-02 16:02:24] [Iter 1656/2250] R4[1060/2400] | LR: 0.019778 | E: -27.125270 | E_var:     0.1579 | E_err:   0.006210
[2025-10-02 16:02:27] [Iter 1657/2250] R4[1062/2400] | LR: 0.019746 | E: -27.118084 | E_var:     0.1770 | E_err:   0.006574
[2025-10-02 16:02:29] [Iter 1658/2250] R4[1064/2400] | LR: 0.019714 | E: -27.122992 | E_var:     0.1840 | E_err:   0.006702
[2025-10-02 16:02:31] [Iter 1659/2250] R4[1066/2400] | LR: 0.019681 | E: -27.123135 | E_var:     0.1886 | E_err:   0.006786
[2025-10-02 16:02:34] [Iter 1660/2250] R4[1068/2400] | LR: 0.019649 | E: -27.110055 | E_var:     0.2036 | E_err:   0.007051
[2025-10-02 16:02:36] [Iter 1661/2250] R4[1070/2400] | LR: 0.019617 | E: -27.111731 | E_var:     0.2500 | E_err:   0.007813
[2025-10-02 16:02:39] [Iter 1662/2250] R4[1072/2400] | LR: 0.019585 | E: -27.118285 | E_var:     0.1909 | E_err:   0.006827
[2025-10-02 16:02:41] [Iter 1663/2250] R4[1074/2400] | LR: 0.019552 | E: -27.126177 | E_var:     0.2153 | E_err:   0.007250
[2025-10-02 16:02:43] [Iter 1664/2250] R4[1076/2400] | LR: 0.019520 | E: -27.118373 | E_var:     0.1795 | E_err:   0.006620
[2025-10-02 16:02:46] [Iter 1665/2250] R4[1078/2400] | LR: 0.019488 | E: -27.108332 | E_var:     0.1688 | E_err:   0.006420
[2025-10-02 16:02:48] [Iter 1666/2250] R4[1080/2400] | LR: 0.019455 | E: -27.122868 | E_var:     0.1644 | E_err:   0.006335
[2025-10-02 16:02:51] [Iter 1667/2250] R4[1082/2400] | LR: 0.019423 | E: -27.120629 | E_var:     0.1831 | E_err:   0.006686
[2025-10-02 16:02:53] [Iter 1668/2250] R4[1084/2400] | LR: 0.019391 | E: -27.125016 | E_var:     0.2153 | E_err:   0.007250
[2025-10-02 16:02:55] [Iter 1669/2250] R4[1086/2400] | LR: 0.019358 | E: -27.121651 | E_var:     0.2326 | E_err:   0.007535
[2025-10-02 16:02:58] [Iter 1670/2250] R4[1088/2400] | LR: 0.019326 | E: -27.119935 | E_var:     0.2115 | E_err:   0.007186
[2025-10-02 16:03:00] [Iter 1671/2250] R4[1090/2400] | LR: 0.019294 | E: -27.118941 | E_var:     0.2112 | E_err:   0.007181
[2025-10-02 16:03:03] [Iter 1672/2250] R4[1092/2400] | LR: 0.019261 | E: -27.123824 | E_var:     0.1703 | E_err:   0.006449
[2025-10-02 16:03:05] [Iter 1673/2250] R4[1094/2400] | LR: 0.019229 | E: -27.117183 | E_var:     0.1844 | E_err:   0.006709
[2025-10-02 16:03:08] [Iter 1674/2250] R4[1096/2400] | LR: 0.019196 | E: -27.119737 | E_var:     0.1686 | E_err:   0.006415
[2025-10-02 16:03:10] [Iter 1675/2250] R4[1098/2400] | LR: 0.019164 | E: -27.113710 | E_var:     0.3250 | E_err:   0.008907
[2025-10-02 16:03:12] [Iter 1676/2250] R4[1100/2400] | LR: 0.019132 | E: -27.127092 | E_var:     0.1817 | E_err:   0.006660
[2025-10-02 16:03:15] [Iter 1677/2250] R4[1102/2400] | LR: 0.019099 | E: -27.120250 | E_var:     0.1831 | E_err:   0.006687
[2025-10-02 16:03:17] [Iter 1678/2250] R4[1104/2400] | LR: 0.019067 | E: -27.124759 | E_var:     0.1715 | E_err:   0.006471
[2025-10-02 16:03:20] [Iter 1679/2250] R4[1106/2400] | LR: 0.019034 | E: -27.132830 | E_var:     0.2412 | E_err:   0.007674
[2025-10-02 16:03:22] [Iter 1680/2250] R4[1108/2400] | LR: 0.019002 | E: -27.119610 | E_var:     0.2034 | E_err:   0.007046
[2025-10-02 16:03:24] [Iter 1681/2250] R4[1110/2400] | LR: 0.018969 | E: -27.112774 | E_var:     0.2140 | E_err:   0.007229
[2025-10-02 16:03:27] [Iter 1682/2250] R4[1112/2400] | LR: 0.018937 | E: -27.119982 | E_var:     0.2934 | E_err:   0.008463
[2025-10-02 16:03:29] [Iter 1683/2250] R4[1114/2400] | LR: 0.018904 | E: -27.125786 | E_var:     0.1837 | E_err:   0.006697
[2025-10-02 16:03:32] [Iter 1684/2250] R4[1116/2400] | LR: 0.018872 | E: -27.132647 | E_var:     0.1710 | E_err:   0.006460
[2025-10-02 16:03:34] [Iter 1685/2250] R4[1118/2400] | LR: 0.018839 | E: -27.123049 | E_var:     0.2215 | E_err:   0.007354
[2025-10-02 16:03:36] [Iter 1686/2250] R4[1120/2400] | LR: 0.018807 | E: -27.126550 | E_var:     0.1632 | E_err:   0.006312
[2025-10-02 16:03:39] [Iter 1687/2250] R4[1122/2400] | LR: 0.018774 | E: -27.119206 | E_var:     0.1772 | E_err:   0.006577
[2025-10-02 16:03:41] [Iter 1688/2250] R4[1124/2400] | LR: 0.018741 | E: -27.112764 | E_var:     0.2246 | E_err:   0.007404
[2025-10-02 16:03:44] [Iter 1689/2250] R4[1126/2400] | LR: 0.018709 | E: -27.128313 | E_var:     0.2364 | E_err:   0.007598
[2025-10-02 16:03:46] [Iter 1690/2250] R4[1128/2400] | LR: 0.018676 | E: -27.108210 | E_var:     0.2933 | E_err:   0.008462
[2025-10-02 16:03:48] [Iter 1691/2250] R4[1130/2400] | LR: 0.018644 | E: -27.107083 | E_var:     0.2150 | E_err:   0.007245
[2025-10-02 16:03:51] [Iter 1692/2250] R4[1132/2400] | LR: 0.018611 | E: -27.107863 | E_var:     0.2326 | E_err:   0.007536
[2025-10-02 16:03:53] [Iter 1693/2250] R4[1134/2400] | LR: 0.018579 | E: -27.123063 | E_var:     0.1876 | E_err:   0.006768
[2025-10-02 16:03:56] [Iter 1694/2250] R4[1136/2400] | LR: 0.018546 | E: -27.128878 | E_var:     0.2409 | E_err:   0.007668
[2025-10-02 16:03:58] [Iter 1695/2250] R4[1138/2400] | LR: 0.018513 | E: -27.126661 | E_var:     0.1566 | E_err:   0.006184
[2025-10-02 16:04:01] [Iter 1696/2250] R4[1140/2400] | LR: 0.018481 | E: -27.128582 | E_var:     0.2289 | E_err:   0.007476
[2025-10-02 16:04:03] [Iter 1697/2250] R4[1142/2400] | LR: 0.018448 | E: -27.124534 | E_var:     0.1718 | E_err:   0.006476
[2025-10-02 16:04:05] [Iter 1698/2250] R4[1144/2400] | LR: 0.018415 | E: -27.123826 | E_var:     0.1558 | E_err:   0.006167
[2025-10-02 16:04:08] [Iter 1699/2250] R4[1146/2400] | LR: 0.018383 | E: -27.124906 | E_var:     0.1814 | E_err:   0.006655
[2025-10-02 16:04:10] [Iter 1700/2250] R4[1148/2400] | LR: 0.018350 | E: -27.121437 | E_var:     0.1623 | E_err:   0.006294
[2025-10-02 16:04:13] [Iter 1701/2250] R4[1150/2400] | LR: 0.018318 | E: -27.132152 | E_var:     0.2087 | E_err:   0.007137
[2025-10-02 16:04:15] [Iter 1702/2250] R4[1152/2400] | LR: 0.018285 | E: -27.126741 | E_var:     0.1631 | E_err:   0.006311
[2025-10-02 16:04:17] [Iter 1703/2250] R4[1154/2400] | LR: 0.018252 | E: -27.136480 | E_var:     0.2169 | E_err:   0.007277
[2025-10-02 16:04:20] [Iter 1704/2250] R4[1156/2400] | LR: 0.018220 | E: -27.124973 | E_var:     0.2312 | E_err:   0.007513
[2025-10-02 16:04:22] [Iter 1705/2250] R4[1158/2400] | LR: 0.018187 | E: -27.109541 | E_var:     0.2751 | E_err:   0.008196
[2025-10-02 16:04:25] [Iter 1706/2250] R4[1160/2400] | LR: 0.018154 | E: -27.123487 | E_var:     0.2053 | E_err:   0.007080
[2025-10-02 16:04:27] [Iter 1707/2250] R4[1162/2400] | LR: 0.018122 | E: -27.132161 | E_var:     0.2003 | E_err:   0.006993
[2025-10-02 16:04:29] [Iter 1708/2250] R4[1164/2400] | LR: 0.018089 | E: -27.124692 | E_var:     0.2232 | E_err:   0.007382
[2025-10-02 16:04:32] [Iter 1709/2250] R4[1166/2400] | LR: 0.018056 | E: -27.125129 | E_var:     0.1742 | E_err:   0.006521
[2025-10-02 16:04:34] [Iter 1710/2250] R4[1168/2400] | LR: 0.018023 | E: -27.119554 | E_var:     0.1720 | E_err:   0.006480
[2025-10-02 16:04:37] [Iter 1711/2250] R4[1170/2400] | LR: 0.017991 | E: -27.116061 | E_var:     0.1893 | E_err:   0.006798
[2025-10-02 16:04:39] [Iter 1712/2250] R4[1172/2400] | LR: 0.017958 | E: -27.120227 | E_var:     0.1762 | E_err:   0.006558
[2025-10-02 16:04:42] [Iter 1713/2250] R4[1174/2400] | LR: 0.017925 | E: -27.121971 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 16:04:44] [Iter 1714/2250] R4[1176/2400] | LR: 0.017893 | E: -27.116219 | E_var:     0.1812 | E_err:   0.006652
[2025-10-02 16:04:46] [Iter 1715/2250] R4[1178/2400] | LR: 0.017860 | E: -27.131154 | E_var:     0.2324 | E_err:   0.007533
[2025-10-02 16:04:49] [Iter 1716/2250] R4[1180/2400] | LR: 0.017827 | E: -27.128919 | E_var:     0.1748 | E_err:   0.006532
[2025-10-02 16:04:51] [Iter 1717/2250] R4[1182/2400] | LR: 0.017794 | E: -27.111284 | E_var:     0.2105 | E_err:   0.007169
[2025-10-02 16:04:54] [Iter 1718/2250] R4[1184/2400] | LR: 0.017762 | E: -27.116156 | E_var:     0.3087 | E_err:   0.008681
[2025-10-02 16:04:56] [Iter 1719/2250] R4[1186/2400] | LR: 0.017729 | E: -27.118164 | E_var:     0.2024 | E_err:   0.007030
[2025-10-02 16:04:58] [Iter 1720/2250] R4[1188/2400] | LR: 0.017696 | E: -27.123275 | E_var:     0.2200 | E_err:   0.007328
[2025-10-02 16:05:01] [Iter 1721/2250] R4[1190/2400] | LR: 0.017664 | E: -27.112666 | E_var:     0.1604 | E_err:   0.006257
[2025-10-02 16:05:03] [Iter 1722/2250] R4[1192/2400] | LR: 0.017631 | E: -27.116452 | E_var:     0.1531 | E_err:   0.006113
[2025-10-02 16:05:06] [Iter 1723/2250] R4[1194/2400] | LR: 0.017598 | E: -27.129553 | E_var:     0.2199 | E_err:   0.007326
[2025-10-02 16:05:08] [Iter 1724/2250] R4[1196/2400] | LR: 0.017565 | E: -27.130445 | E_var:     0.1653 | E_err:   0.006353
[2025-10-02 16:05:10] [Iter 1725/2250] R4[1198/2400] | LR: 0.017533 | E: -27.122699 | E_var:     0.1933 | E_err:   0.006870
[2025-10-02 16:05:13] [Iter 1726/2250] R4[1200/2400] | LR: 0.017500 | E: -27.112903 | E_var:     0.3185 | E_err:   0.008818
[2025-10-02 16:05:15] [Iter 1727/2250] R4[1202/2400] | LR: 0.017467 | E: -27.120113 | E_var:     0.1793 | E_err:   0.006616
[2025-10-02 16:05:18] [Iter 1728/2250] R4[1204/2400] | LR: 0.017435 | E: -27.131445 | E_var:     0.1857 | E_err:   0.006734
[2025-10-02 16:05:20] [Iter 1729/2250] R4[1206/2400] | LR: 0.017402 | E: -27.126013 | E_var:     0.1893 | E_err:   0.006799
[2025-10-02 16:05:23] [Iter 1730/2250] R4[1208/2400] | LR: 0.017369 | E: -27.124691 | E_var:     0.1544 | E_err:   0.006140
[2025-10-02 16:05:25] [Iter 1731/2250] R4[1210/2400] | LR: 0.017336 | E: -27.122714 | E_var:     0.2909 | E_err:   0.008427
[2025-10-02 16:05:27] [Iter 1732/2250] R4[1212/2400] | LR: 0.017304 | E: -27.127287 | E_var:     0.1932 | E_err:   0.006867
[2025-10-02 16:05:30] [Iter 1733/2250] R4[1214/2400] | LR: 0.017271 | E: -27.119961 | E_var:     0.1984 | E_err:   0.006959
[2025-10-02 16:05:32] [Iter 1734/2250] R4[1216/2400] | LR: 0.017238 | E: -27.135387 | E_var:     0.1733 | E_err:   0.006505
[2025-10-02 16:05:35] [Iter 1735/2250] R4[1218/2400] | LR: 0.017206 | E: -27.135296 | E_var:     0.2350 | E_err:   0.007575
[2025-10-02 16:05:37] [Iter 1736/2250] R4[1220/2400] | LR: 0.017173 | E: -27.126620 | E_var:     0.1740 | E_err:   0.006517
[2025-10-02 16:05:39] [Iter 1737/2250] R4[1222/2400] | LR: 0.017140 | E: -27.120927 | E_var:     0.1812 | E_err:   0.006651
[2025-10-02 16:05:42] [Iter 1738/2250] R4[1224/2400] | LR: 0.017107 | E: -27.128869 | E_var:     0.3277 | E_err:   0.008945
[2025-10-02 16:05:44] [Iter 1739/2250] R4[1226/2400] | LR: 0.017075 | E: -27.118079 | E_var:     0.1877 | E_err:   0.006770
[2025-10-02 16:05:47] [Iter 1740/2250] R4[1228/2400] | LR: 0.017042 | E: -27.124404 | E_var:     0.1975 | E_err:   0.006943
[2025-10-02 16:05:49] [Iter 1741/2250] R4[1230/2400] | LR: 0.017009 | E: -27.119038 | E_var:     0.2601 | E_err:   0.007969
[2025-10-02 16:05:51] [Iter 1742/2250] R4[1232/2400] | LR: 0.016977 | E: -27.131327 | E_var:     0.2102 | E_err:   0.007164
[2025-10-02 16:05:54] [Iter 1743/2250] R4[1234/2400] | LR: 0.016944 | E: -27.121793 | E_var:     0.2212 | E_err:   0.007349
[2025-10-02 16:05:56] [Iter 1744/2250] R4[1236/2400] | LR: 0.016911 | E: -27.126649 | E_var:     0.1751 | E_err:   0.006539
[2025-10-02 16:05:59] [Iter 1745/2250] R4[1238/2400] | LR: 0.016878 | E: -27.120587 | E_var:     0.1811 | E_err:   0.006649
[2025-10-02 16:06:01] [Iter 1746/2250] R4[1240/2400] | LR: 0.016846 | E: -27.114938 | E_var:     0.1920 | E_err:   0.006847
[2025-10-02 16:06:03] [Iter 1747/2250] R4[1242/2400] | LR: 0.016813 | E: -27.117580 | E_var:     0.1512 | E_err:   0.006077
[2025-10-02 16:06:06] [Iter 1748/2250] R4[1244/2400] | LR: 0.016780 | E: -27.116476 | E_var:     0.1822 | E_err:   0.006669
[2025-10-02 16:06:08] [Iter 1749/2250] R4[1246/2400] | LR: 0.016748 | E: -27.122566 | E_var:     0.1655 | E_err:   0.006356
[2025-10-02 16:06:11] [Iter 1750/2250] R4[1248/2400] | LR: 0.016715 | E: -27.114663 | E_var:     0.1733 | E_err:   0.006505
[2025-10-02 16:06:13] [Iter 1751/2250] R4[1250/2400] | LR: 0.016682 | E: -27.122470 | E_var:     0.2389 | E_err:   0.007637
[2025-10-02 16:06:16] [Iter 1752/2250] R4[1252/2400] | LR: 0.016650 | E: -27.121466 | E_var:     0.1829 | E_err:   0.006683
[2025-10-02 16:06:18] [Iter 1753/2250] R4[1254/2400] | LR: 0.016617 | E: -27.122307 | E_var:     0.2475 | E_err:   0.007774
[2025-10-02 16:06:20] [Iter 1754/2250] R4[1256/2400] | LR: 0.016585 | E: -27.116928 | E_var:     0.2059 | E_err:   0.007091
[2025-10-02 16:06:23] [Iter 1755/2250] R4[1258/2400] | LR: 0.016552 | E: -27.124238 | E_var:     0.2084 | E_err:   0.007133
[2025-10-02 16:06:25] [Iter 1756/2250] R4[1260/2400] | LR: 0.016519 | E: -27.122207 | E_var:     0.2736 | E_err:   0.008173
[2025-10-02 16:06:28] [Iter 1757/2250] R4[1262/2400] | LR: 0.016487 | E: -27.120330 | E_var:     0.1618 | E_err:   0.006285
[2025-10-02 16:06:30] [Iter 1758/2250] R4[1264/2400] | LR: 0.016454 | E: -27.126781 | E_var:     0.1713 | E_err:   0.006467
[2025-10-02 16:06:32] [Iter 1759/2250] R4[1266/2400] | LR: 0.016421 | E: -27.124453 | E_var:     0.2347 | E_err:   0.007570
[2025-10-02 16:06:35] [Iter 1760/2250] R4[1268/2400] | LR: 0.016389 | E: -27.124542 | E_var:     0.1674 | E_err:   0.006393
[2025-10-02 16:06:37] [Iter 1761/2250] R4[1270/2400] | LR: 0.016356 | E: -27.123913 | E_var:     0.1664 | E_err:   0.006374
[2025-10-02 16:06:40] [Iter 1762/2250] R4[1272/2400] | LR: 0.016324 | E: -27.127378 | E_var:     0.2463 | E_err:   0.007755
[2025-10-02 16:06:42] [Iter 1763/2250] R4[1274/2400] | LR: 0.016291 | E: -27.119016 | E_var:     0.1575 | E_err:   0.006201
[2025-10-02 16:06:44] [Iter 1764/2250] R4[1276/2400] | LR: 0.016259 | E: -27.124549 | E_var:     0.1789 | E_err:   0.006608
[2025-10-02 16:06:47] [Iter 1765/2250] R4[1278/2400] | LR: 0.016226 | E: -27.110451 | E_var:     0.1779 | E_err:   0.006590
[2025-10-02 16:06:49] [Iter 1766/2250] R4[1280/2400] | LR: 0.016193 | E: -27.130137 | E_var:     0.2455 | E_err:   0.007741
[2025-10-02 16:06:52] [Iter 1767/2250] R4[1282/2400] | LR: 0.016161 | E: -27.121869 | E_var:     0.1574 | E_err:   0.006198
[2025-10-02 16:06:54] [Iter 1768/2250] R4[1284/2400] | LR: 0.016128 | E: -27.125180 | E_var:     0.1853 | E_err:   0.006726
[2025-10-02 16:06:56] [Iter 1769/2250] R4[1286/2400] | LR: 0.016096 | E: -27.129771 | E_var:     0.1732 | E_err:   0.006502
[2025-10-02 16:06:59] [Iter 1770/2250] R4[1288/2400] | LR: 0.016063 | E: -27.120515 | E_var:     0.1849 | E_err:   0.006719
[2025-10-02 16:07:01] [Iter 1771/2250] R4[1290/2400] | LR: 0.016031 | E: -27.129210 | E_var:     0.1642 | E_err:   0.006332
[2025-10-02 16:07:04] [Iter 1772/2250] R4[1292/2400] | LR: 0.015998 | E: -27.119725 | E_var:     0.1866 | E_err:   0.006750
[2025-10-02 16:07:06] [Iter 1773/2250] R4[1294/2400] | LR: 0.015966 | E: -27.114649 | E_var:     0.1872 | E_err:   0.006760
[2025-10-02 16:07:09] [Iter 1774/2250] R4[1296/2400] | LR: 0.015933 | E: -27.130972 | E_var:     0.1490 | E_err:   0.006031
[2025-10-02 16:07:11] [Iter 1775/2250] R4[1298/2400] | LR: 0.015901 | E: -27.130620 | E_var:     0.1918 | E_err:   0.006844
[2025-10-02 16:07:13] [Iter 1776/2250] R4[1300/2400] | LR: 0.015868 | E: -27.118994 | E_var:     0.1829 | E_err:   0.006682
[2025-10-02 16:07:16] [Iter 1777/2250] R4[1302/2400] | LR: 0.015836 | E: -27.106699 | E_var:     0.3147 | E_err:   0.008765
[2025-10-02 16:07:18] [Iter 1778/2250] R4[1304/2400] | LR: 0.015804 | E: -27.126192 | E_var:     0.2068 | E_err:   0.007106
[2025-10-02 16:07:21] [Iter 1779/2250] R4[1306/2400] | LR: 0.015771 | E: -27.121187 | E_var:     0.1945 | E_err:   0.006891
[2025-10-02 16:07:23] [Iter 1780/2250] R4[1308/2400] | LR: 0.015739 | E: -27.124396 | E_var:     0.1608 | E_err:   0.006265
[2025-10-02 16:07:26] [Iter 1781/2250] R4[1310/2400] | LR: 0.015706 | E: -27.116944 | E_var:     0.1640 | E_err:   0.006327
[2025-10-02 16:07:28] [Iter 1782/2250] R4[1312/2400] | LR: 0.015674 | E: -27.119074 | E_var:     0.2280 | E_err:   0.007461
[2025-10-02 16:07:30] [Iter 1783/2250] R4[1314/2400] | LR: 0.015642 | E: -27.125567 | E_var:     0.2041 | E_err:   0.007060
[2025-10-02 16:07:33] [Iter 1784/2250] R4[1316/2400] | LR: 0.015609 | E: -27.129403 | E_var:     0.1947 | E_err:   0.006895
[2025-10-02 16:07:35] [Iter 1785/2250] R4[1318/2400] | LR: 0.015577 | E: -27.122775 | E_var:     0.1680 | E_err:   0.006405
[2025-10-02 16:07:38] [Iter 1786/2250] R4[1320/2400] | LR: 0.015545 | E: -27.113177 | E_var:     0.1810 | E_err:   0.006648
[2025-10-02 16:07:40] [Iter 1787/2250] R4[1322/2400] | LR: 0.015512 | E: -27.113630 | E_var:     0.2001 | E_err:   0.006990
[2025-10-02 16:07:42] [Iter 1788/2250] R4[1324/2400] | LR: 0.015480 | E: -27.124035 | E_var:     0.1824 | E_err:   0.006673
[2025-10-02 16:07:45] [Iter 1789/2250] R4[1326/2400] | LR: 0.015448 | E: -27.120865 | E_var:     0.1816 | E_err:   0.006659
[2025-10-02 16:07:47] [Iter 1790/2250] R4[1328/2400] | LR: 0.015415 | E: -27.122258 | E_var:     0.1794 | E_err:   0.006618
[2025-10-02 16:07:50] [Iter 1791/2250] R4[1330/2400] | LR: 0.015383 | E: -27.137540 | E_var:     0.1896 | E_err:   0.006803
[2025-10-02 16:07:52] [Iter 1792/2250] R4[1332/2400] | LR: 0.015351 | E: -27.119405 | E_var:     0.1770 | E_err:   0.006573
[2025-10-02 16:07:54] [Iter 1793/2250] R4[1334/2400] | LR: 0.015319 | E: -27.123717 | E_var:     0.1664 | E_err:   0.006373
[2025-10-02 16:07:57] [Iter 1794/2250] R4[1336/2400] | LR: 0.015286 | E: -27.133310 | E_var:     0.2026 | E_err:   0.007034
[2025-10-02 16:07:59] [Iter 1795/2250] R4[1338/2400] | LR: 0.015254 | E: -27.121021 | E_var:     0.2003 | E_err:   0.006993
[2025-10-02 16:08:02] [Iter 1796/2250] R4[1340/2400] | LR: 0.015222 | E: -27.120714 | E_var:     0.2160 | E_err:   0.007262
[2025-10-02 16:08:04] [Iter 1797/2250] R4[1342/2400] | LR: 0.015190 | E: -27.120069 | E_var:     0.1617 | E_err:   0.006283
[2025-10-02 16:08:06] [Iter 1798/2250] R4[1344/2400] | LR: 0.015158 | E: -27.126503 | E_var:     0.2478 | E_err:   0.007778
[2025-10-02 16:08:09] [Iter 1799/2250] R4[1346/2400] | LR: 0.015126 | E: -27.120454 | E_var:     0.2097 | E_err:   0.007156
[2025-10-02 16:08:11] [Iter 1800/2250] R4[1348/2400] | LR: 0.015093 | E: -27.116221 | E_var:     0.2285 | E_err:   0.007469
[2025-10-02 16:08:11] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-02 16:08:14] [Iter 1801/2250] R4[1350/2400] | LR: 0.015061 | E: -27.122838 | E_var:     0.2207 | E_err:   0.007340
[2025-10-02 16:08:16] [Iter 1802/2250] R4[1352/2400] | LR: 0.015029 | E: -27.131650 | E_var:     0.2028 | E_err:   0.007036
[2025-10-02 16:08:19] [Iter 1803/2250] R4[1354/2400] | LR: 0.014997 | E: -27.134647 | E_var:     0.1669 | E_err:   0.006383
[2025-10-02 16:08:21] [Iter 1804/2250] R4[1356/2400] | LR: 0.014965 | E: -27.109534 | E_var:     0.1847 | E_err:   0.006716
[2025-10-02 16:08:23] [Iter 1805/2250] R4[1358/2400] | LR: 0.014933 | E: -27.128240 | E_var:     0.1705 | E_err:   0.006451
[2025-10-02 16:08:26] [Iter 1806/2250] R4[1360/2400] | LR: 0.014901 | E: -27.123221 | E_var:     0.3144 | E_err:   0.008761
[2025-10-02 16:08:28] [Iter 1807/2250] R4[1362/2400] | LR: 0.014869 | E: -27.131715 | E_var:     0.1875 | E_err:   0.006766
[2025-10-02 16:08:31] [Iter 1808/2250] R4[1364/2400] | LR: 0.014837 | E: -27.119963 | E_var:     0.2042 | E_err:   0.007061
[2025-10-02 16:08:33] [Iter 1809/2250] R4[1366/2400] | LR: 0.014805 | E: -27.118214 | E_var:     0.1638 | E_err:   0.006323
[2025-10-02 16:08:35] [Iter 1810/2250] R4[1368/2400] | LR: 0.014773 | E: -27.133485 | E_var:     0.4222 | E_err:   0.010153
[2025-10-02 16:08:38] [Iter 1811/2250] R4[1370/2400] | LR: 0.014741 | E: -27.126528 | E_var:     0.2258 | E_err:   0.007425
[2025-10-02 16:08:40] [Iter 1812/2250] R4[1372/2400] | LR: 0.014709 | E: -27.123143 | E_var:     0.1749 | E_err:   0.006535
[2025-10-02 16:08:43] [Iter 1813/2250] R4[1374/2400] | LR: 0.014677 | E: -27.108537 | E_var:     0.1855 | E_err:   0.006730
[2025-10-02 16:08:45] [Iter 1814/2250] R4[1376/2400] | LR: 0.014646 | E: -27.124506 | E_var:     0.1864 | E_err:   0.006746
[2025-10-02 16:08:47] [Iter 1815/2250] R4[1378/2400] | LR: 0.014614 | E: -27.122371 | E_var:     0.2266 | E_err:   0.007437
[2025-10-02 16:08:50] [Iter 1816/2250] R4[1380/2400] | LR: 0.014582 | E: -27.117991 | E_var:     0.2372 | E_err:   0.007610
[2025-10-02 16:08:52] [Iter 1817/2250] R4[1382/2400] | LR: 0.014550 | E: -27.121315 | E_var:     0.1802 | E_err:   0.006633
[2025-10-02 16:08:55] [Iter 1818/2250] R4[1384/2400] | LR: 0.014518 | E: -27.128195 | E_var:     0.2415 | E_err:   0.007679
[2025-10-02 16:08:57] [Iter 1819/2250] R4[1386/2400] | LR: 0.014487 | E: -27.118856 | E_var:     0.1652 | E_err:   0.006350
[2025-10-02 16:09:00] [Iter 1820/2250] R4[1388/2400] | LR: 0.014455 | E: -27.109453 | E_var:     0.2010 | E_err:   0.007005
[2025-10-02 16:09:02] [Iter 1821/2250] R4[1390/2400] | LR: 0.014423 | E: -27.119933 | E_var:     0.2809 | E_err:   0.008281
[2025-10-02 16:09:04] [Iter 1822/2250] R4[1392/2400] | LR: 0.014391 | E: -27.117087 | E_var:     0.2454 | E_err:   0.007741
[2025-10-02 16:09:07] [Iter 1823/2250] R4[1394/2400] | LR: 0.014360 | E: -27.121374 | E_var:     0.1852 | E_err:   0.006724
[2025-10-02 16:09:09] [Iter 1824/2250] R4[1396/2400] | LR: 0.014328 | E: -27.124850 | E_var:     0.1837 | E_err:   0.006697
[2025-10-02 16:09:12] [Iter 1825/2250] R4[1398/2400] | LR: 0.014296 | E: -27.135203 | E_var:     0.1744 | E_err:   0.006526
[2025-10-02 16:09:14] [Iter 1826/2250] R4[1400/2400] | LR: 0.014265 | E: -27.120728 | E_var:     0.2240 | E_err:   0.007395
[2025-10-02 16:09:16] [Iter 1827/2250] R4[1402/2400] | LR: 0.014233 | E: -27.116033 | E_var:     0.1759 | E_err:   0.006552
[2025-10-02 16:09:19] [Iter 1828/2250] R4[1404/2400] | LR: 0.014202 | E: -27.121124 | E_var:     0.1810 | E_err:   0.006648
[2025-10-02 16:09:21] [Iter 1829/2250] R4[1406/2400] | LR: 0.014170 | E: -27.130289 | E_var:     0.1646 | E_err:   0.006339
[2025-10-02 16:09:24] [Iter 1830/2250] R4[1408/2400] | LR: 0.014139 | E: -27.128590 | E_var:     0.4268 | E_err:   0.010208
[2025-10-02 16:09:26] [Iter 1831/2250] R4[1410/2400] | LR: 0.014107 | E: -27.120983 | E_var:     0.3282 | E_err:   0.008951
[2025-10-02 16:09:28] [Iter 1832/2250] R4[1412/2400] | LR: 0.014076 | E: -27.129258 | E_var:     0.1875 | E_err:   0.006767
[2025-10-02 16:09:31] [Iter 1833/2250] R4[1414/2400] | LR: 0.014044 | E: -27.117202 | E_var:     0.1762 | E_err:   0.006559
[2025-10-02 16:09:33] [Iter 1834/2250] R4[1416/2400] | LR: 0.014013 | E: -27.124625 | E_var:     0.1486 | E_err:   0.006024
[2025-10-02 16:09:36] [Iter 1835/2250] R4[1418/2400] | LR: 0.013981 | E: -27.137665 | E_var:     0.1811 | E_err:   0.006650
[2025-10-02 16:09:38] [Iter 1836/2250] R4[1420/2400] | LR: 0.013950 | E: -27.126441 | E_var:     0.2121 | E_err:   0.007196
[2025-10-02 16:09:40] [Iter 1837/2250] R4[1422/2400] | LR: 0.013918 | E: -27.132525 | E_var:     0.1629 | E_err:   0.006305
[2025-10-02 16:09:43] [Iter 1838/2250] R4[1424/2400] | LR: 0.013887 | E: -27.134359 | E_var:     0.2228 | E_err:   0.007376
[2025-10-02 16:09:45] [Iter 1839/2250] R4[1426/2400] | LR: 0.013856 | E: -27.124051 | E_var:     0.1786 | E_err:   0.006604
[2025-10-02 16:09:48] [Iter 1840/2250] R4[1428/2400] | LR: 0.013824 | E: -27.121183 | E_var:     0.1726 | E_err:   0.006492
[2025-10-02 16:09:50] [Iter 1841/2250] R4[1430/2400] | LR: 0.013793 | E: -27.127965 | E_var:     0.1805 | E_err:   0.006639
[2025-10-02 16:09:52] [Iter 1842/2250] R4[1432/2400] | LR: 0.013762 | E: -27.120373 | E_var:     0.2869 | E_err:   0.008369
[2025-10-02 16:09:55] [Iter 1843/2250] R4[1434/2400] | LR: 0.013731 | E: -27.118178 | E_var:     0.2804 | E_err:   0.008274
[2025-10-02 16:09:57] [Iter 1844/2250] R4[1436/2400] | LR: 0.013700 | E: -27.119926 | E_var:     0.1527 | E_err:   0.006106
[2025-10-02 16:10:00] [Iter 1845/2250] R4[1438/2400] | LR: 0.013668 | E: -27.115929 | E_var:     0.1713 | E_err:   0.006467
[2025-10-02 16:10:02] [Iter 1846/2250] R4[1440/2400] | LR: 0.013637 | E: -27.129562 | E_var:     0.1955 | E_err:   0.006908
[2025-10-02 16:10:05] [Iter 1847/2250] R4[1442/2400] | LR: 0.013606 | E: -27.127483 | E_var:     0.1900 | E_err:   0.006811
[2025-10-02 16:10:07] [Iter 1848/2250] R4[1444/2400] | LR: 0.013575 | E: -27.134760 | E_var:     0.1559 | E_err:   0.006170
[2025-10-02 16:10:09] [Iter 1849/2250] R4[1446/2400] | LR: 0.013544 | E: -27.122900 | E_var:     0.2387 | E_err:   0.007634
[2025-10-02 16:10:12] [Iter 1850/2250] R4[1448/2400] | LR: 0.013513 | E: -27.131466 | E_var:     0.1907 | E_err:   0.006822
[2025-10-02 16:10:14] [Iter 1851/2250] R4[1450/2400] | LR: 0.013482 | E: -27.127144 | E_var:     0.1679 | E_err:   0.006403
[2025-10-02 16:10:17] [Iter 1852/2250] R4[1452/2400] | LR: 0.013451 | E: -27.128810 | E_var:     0.2292 | E_err:   0.007480
[2025-10-02 16:10:19] [Iter 1853/2250] R4[1454/2400] | LR: 0.013420 | E: -27.136676 | E_var:     0.3756 | E_err:   0.009576
[2025-10-02 16:10:21] [Iter 1854/2250] R4[1456/2400] | LR: 0.013389 | E: -27.129367 | E_var:     0.1613 | E_err:   0.006276
[2025-10-02 16:10:24] [Iter 1855/2250] R4[1458/2400] | LR: 0.013358 | E: -27.123145 | E_var:     0.1898 | E_err:   0.006807
[2025-10-02 16:10:26] [Iter 1856/2250] R4[1460/2400] | LR: 0.013327 | E: -27.116787 | E_var:     0.1685 | E_err:   0.006414
[2025-10-02 16:10:29] [Iter 1857/2250] R4[1462/2400] | LR: 0.013297 | E: -27.118228 | E_var:     0.2330 | E_err:   0.007542
[2025-10-02 16:10:31] [Iter 1858/2250] R4[1464/2400] | LR: 0.013266 | E: -27.130865 | E_var:     0.2269 | E_err:   0.007444
[2025-10-02 16:10:33] [Iter 1859/2250] R4[1466/2400] | LR: 0.013235 | E: -27.123022 | E_var:     0.2750 | E_err:   0.008194
[2025-10-02 16:10:36] [Iter 1860/2250] R4[1468/2400] | LR: 0.013204 | E: -27.132690 | E_var:     0.1567 | E_err:   0.006186
[2025-10-02 16:10:38] [Iter 1861/2250] R4[1470/2400] | LR: 0.013174 | E: -27.125640 | E_var:     0.1721 | E_err:   0.006481
[2025-10-02 16:10:41] [Iter 1862/2250] R4[1472/2400] | LR: 0.013143 | E: -27.125381 | E_var:     0.1855 | E_err:   0.006729
[2025-10-02 16:10:43] [Iter 1863/2250] R4[1474/2400] | LR: 0.013112 | E: -27.117324 | E_var:     0.2463 | E_err:   0.007755
[2025-10-02 16:10:45] [Iter 1864/2250] R4[1476/2400] | LR: 0.013082 | E: -27.114352 | E_var:     0.1864 | E_err:   0.006746
[2025-10-02 16:10:48] [Iter 1865/2250] R4[1478/2400] | LR: 0.013051 | E: -27.127469 | E_var:     0.1624 | E_err:   0.006296
[2025-10-02 16:10:50] [Iter 1866/2250] R4[1480/2400] | LR: 0.013020 | E: -27.125103 | E_var:     0.4514 | E_err:   0.010498
[2025-10-02 16:10:53] [Iter 1867/2250] R4[1482/2400] | LR: 0.012990 | E: -27.123986 | E_var:     0.2237 | E_err:   0.007390
[2025-10-02 16:10:55] [Iter 1868/2250] R4[1484/2400] | LR: 0.012959 | E: -27.126179 | E_var:     0.1905 | E_err:   0.006820
[2025-10-02 16:10:58] [Iter 1869/2250] R4[1486/2400] | LR: 0.012929 | E: -27.130204 | E_var:     0.1866 | E_err:   0.006749
[2025-10-02 16:11:00] [Iter 1870/2250] R4[1488/2400] | LR: 0.012898 | E: -27.122179 | E_var:     0.2752 | E_err:   0.008197
[2025-10-02 16:11:02] [Iter 1871/2250] R4[1490/2400] | LR: 0.012868 | E: -27.123936 | E_var:     0.2027 | E_err:   0.007035
[2025-10-02 16:11:05] [Iter 1872/2250] R4[1492/2400] | LR: 0.012838 | E: -27.137049 | E_var:     0.1943 | E_err:   0.006887
[2025-10-02 16:11:07] [Iter 1873/2250] R4[1494/2400] | LR: 0.012807 | E: -27.109546 | E_var:     0.3790 | E_err:   0.009619
[2025-10-02 16:11:10] [Iter 1874/2250] R4[1496/2400] | LR: 0.012777 | E: -27.114758 | E_var:     0.1716 | E_err:   0.006473
[2025-10-02 16:11:12] [Iter 1875/2250] R4[1498/2400] | LR: 0.012747 | E: -27.128522 | E_var:     0.2045 | E_err:   0.007065
[2025-10-02 16:11:14] [Iter 1876/2250] R4[1500/2400] | LR: 0.012716 | E: -27.116703 | E_var:     0.1625 | E_err:   0.006299
[2025-10-02 16:11:17] [Iter 1877/2250] R4[1502/2400] | LR: 0.012686 | E: -27.130230 | E_var:     0.1746 | E_err:   0.006529
[2025-10-02 16:11:19] [Iter 1878/2250] R4[1504/2400] | LR: 0.012656 | E: -27.121355 | E_var:     0.1534 | E_err:   0.006121
[2025-10-02 16:11:22] [Iter 1879/2250] R4[1506/2400] | LR: 0.012626 | E: -27.130860 | E_var:     0.1532 | E_err:   0.006116
[2025-10-02 16:11:24] [Iter 1880/2250] R4[1508/2400] | LR: 0.012596 | E: -27.125039 | E_var:     0.2042 | E_err:   0.007061
[2025-10-02 16:11:27] [Iter 1881/2250] R4[1510/2400] | LR: 0.012566 | E: -27.121300 | E_var:     0.1681 | E_err:   0.006406
[2025-10-02 16:11:29] [Iter 1882/2250] R4[1512/2400] | LR: 0.012536 | E: -27.115517 | E_var:     0.1946 | E_err:   0.006894
[2025-10-02 16:11:31] [Iter 1883/2250] R4[1514/2400] | LR: 0.012506 | E: -27.131455 | E_var:     0.1545 | E_err:   0.006141
[2025-10-02 16:11:34] [Iter 1884/2250] R4[1516/2400] | LR: 0.012476 | E: -27.129099 | E_var:     0.1756 | E_err:   0.006548
[2025-10-02 16:11:36] [Iter 1885/2250] R4[1518/2400] | LR: 0.012446 | E: -27.124057 | E_var:     0.1784 | E_err:   0.006599
[2025-10-02 16:11:39] [Iter 1886/2250] R4[1520/2400] | LR: 0.012416 | E: -27.130203 | E_var:     0.1502 | E_err:   0.006055
[2025-10-02 16:11:41] [Iter 1887/2250] R4[1522/2400] | LR: 0.012386 | E: -27.127532 | E_var:     0.1968 | E_err:   0.006931
[2025-10-02 16:11:43] [Iter 1888/2250] R4[1524/2400] | LR: 0.012356 | E: -27.128426 | E_var:     0.1641 | E_err:   0.006330
[2025-10-02 16:11:46] [Iter 1889/2250] R4[1526/2400] | LR: 0.012326 | E: -27.115453 | E_var:     0.1574 | E_err:   0.006200
[2025-10-02 16:11:48] [Iter 1890/2250] R4[1528/2400] | LR: 0.012296 | E: -27.121430 | E_var:     0.1973 | E_err:   0.006940
[2025-10-02 16:11:51] [Iter 1891/2250] R4[1530/2400] | LR: 0.012267 | E: -27.119045 | E_var:     0.2129 | E_err:   0.007209
[2025-10-02 16:11:53] [Iter 1892/2250] R4[1532/2400] | LR: 0.012237 | E: -27.134279 | E_var:     0.2443 | E_err:   0.007722
[2025-10-02 16:11:55] [Iter 1893/2250] R4[1534/2400] | LR: 0.012207 | E: -27.116780 | E_var:     0.1772 | E_err:   0.006577
[2025-10-02 16:11:58] [Iter 1894/2250] R4[1536/2400] | LR: 0.012178 | E: -27.120609 | E_var:     0.1914 | E_err:   0.006836
[2025-10-02 16:12:00] [Iter 1895/2250] R4[1538/2400] | LR: 0.012148 | E: -27.123576 | E_var:     0.3857 | E_err:   0.009704
[2025-10-02 16:12:03] [Iter 1896/2250] R4[1540/2400] | LR: 0.012119 | E: -27.117998 | E_var:     0.1994 | E_err:   0.006977
[2025-10-02 16:12:05] [Iter 1897/2250] R4[1542/2400] | LR: 0.012089 | E: -27.118625 | E_var:     0.1748 | E_err:   0.006532
[2025-10-02 16:12:08] [Iter 1898/2250] R4[1544/2400] | LR: 0.012060 | E: -27.131586 | E_var:     0.1714 | E_err:   0.006469
[2025-10-02 16:12:10] [Iter 1899/2250] R4[1546/2400] | LR: 0.012030 | E: -27.130535 | E_var:     0.1992 | E_err:   0.006973
[2025-10-02 16:12:12] [Iter 1900/2250] R4[1548/2400] | LR: 0.012001 | E: -27.130722 | E_var:     0.1717 | E_err:   0.006474
[2025-10-02 16:12:15] [Iter 1901/2250] R4[1550/2400] | LR: 0.011971 | E: -27.131570 | E_var:     0.2312 | E_err:   0.007513
[2025-10-02 16:12:17] [Iter 1902/2250] R4[1552/2400] | LR: 0.011942 | E: -27.118749 | E_var:     0.1560 | E_err:   0.006172
[2025-10-02 16:12:20] [Iter 1903/2250] R4[1554/2400] | LR: 0.011913 | E: -27.119363 | E_var:     0.1783 | E_err:   0.006597
[2025-10-02 16:12:22] [Iter 1904/2250] R4[1556/2400] | LR: 0.011884 | E: -27.110426 | E_var:     0.1566 | E_err:   0.006183
[2025-10-02 16:12:24] [Iter 1905/2250] R4[1558/2400] | LR: 0.011854 | E: -27.133449 | E_var:     0.1826 | E_err:   0.006677
[2025-10-02 16:12:27] [Iter 1906/2250] R4[1560/2400] | LR: 0.011825 | E: -27.129157 | E_var:     0.2304 | E_err:   0.007500
[2025-10-02 16:12:29] [Iter 1907/2250] R4[1562/2400] | LR: 0.011796 | E: -27.125393 | E_var:     0.1881 | E_err:   0.006777
[2025-10-02 16:12:32] [Iter 1908/2250] R4[1564/2400] | LR: 0.011767 | E: -27.132613 | E_var:     0.1627 | E_err:   0.006303
[2025-10-02 16:12:34] [Iter 1909/2250] R4[1566/2400] | LR: 0.011738 | E: -27.125187 | E_var:     0.1782 | E_err:   0.006596
[2025-10-02 16:12:36] [Iter 1910/2250] R4[1568/2400] | LR: 0.011709 | E: -27.128914 | E_var:     0.1789 | E_err:   0.006609
[2025-10-02 16:12:39] [Iter 1911/2250] R4[1570/2400] | LR: 0.011680 | E: -27.124989 | E_var:     0.1873 | E_err:   0.006762
[2025-10-02 16:12:41] [Iter 1912/2250] R4[1572/2400] | LR: 0.011651 | E: -27.122508 | E_var:     0.2033 | E_err:   0.007046
[2025-10-02 16:12:44] [Iter 1913/2250] R4[1574/2400] | LR: 0.011622 | E: -27.119235 | E_var:     0.1692 | E_err:   0.006426
[2025-10-02 16:12:46] [Iter 1914/2250] R4[1576/2400] | LR: 0.011593 | E: -27.123765 | E_var:     0.1706 | E_err:   0.006454
[2025-10-02 16:12:49] [Iter 1915/2250] R4[1578/2400] | LR: 0.011564 | E: -27.124501 | E_var:     0.3028 | E_err:   0.008598
[2025-10-02 16:12:51] [Iter 1916/2250] R4[1580/2400] | LR: 0.011536 | E: -27.135032 | E_var:     0.5882 | E_err:   0.011984
[2025-10-02 16:12:53] [Iter 1917/2250] R4[1582/2400] | LR: 0.011507 | E: -27.121789 | E_var:     0.2255 | E_err:   0.007420
[2025-10-02 16:12:56] [Iter 1918/2250] R4[1584/2400] | LR: 0.011478 | E: -27.131163 | E_var:     0.1848 | E_err:   0.006717
[2025-10-02 16:12:58] [Iter 1919/2250] R4[1586/2400] | LR: 0.011449 | E: -27.131812 | E_var:     0.2774 | E_err:   0.008230
[2025-10-02 16:13:01] [Iter 1920/2250] R4[1588/2400] | LR: 0.011421 | E: -27.129435 | E_var:     0.1800 | E_err:   0.006629
[2025-10-02 16:13:03] [Iter 1921/2250] R4[1590/2400] | LR: 0.011392 | E: -27.125120 | E_var:     0.1939 | E_err:   0.006880
[2025-10-02 16:13:05] [Iter 1922/2250] R4[1592/2400] | LR: 0.011364 | E: -27.127529 | E_var:     0.1645 | E_err:   0.006337
[2025-10-02 16:13:08] [Iter 1923/2250] R4[1594/2400] | LR: 0.011335 | E: -27.123287 | E_var:     0.1476 | E_err:   0.006002
[2025-10-02 16:13:10] [Iter 1924/2250] R4[1596/2400] | LR: 0.011307 | E: -27.132985 | E_var:     0.2059 | E_err:   0.007090
[2025-10-02 16:13:13] [Iter 1925/2250] R4[1598/2400] | LR: 0.011278 | E: -27.129419 | E_var:     0.2747 | E_err:   0.008189
[2025-10-02 16:13:15] [Iter 1926/2250] R4[1600/2400] | LR: 0.011250 | E: -27.123917 | E_var:     0.2118 | E_err:   0.007191
[2025-10-02 16:13:17] [Iter 1927/2250] R4[1602/2400] | LR: 0.011222 | E: -27.131265 | E_var:     0.1783 | E_err:   0.006599
[2025-10-02 16:13:20] [Iter 1928/2250] R4[1604/2400] | LR: 0.011193 | E: -27.128547 | E_var:     0.1535 | E_err:   0.006121
[2025-10-02 16:13:22] [Iter 1929/2250] R4[1606/2400] | LR: 0.011165 | E: -27.120490 | E_var:     0.1599 | E_err:   0.006249
[2025-10-02 16:13:25] [Iter 1930/2250] R4[1608/2400] | LR: 0.011137 | E: -27.124951 | E_var:     0.1898 | E_err:   0.006808
[2025-10-02 16:13:27] [Iter 1931/2250] R4[1610/2400] | LR: 0.011109 | E: -27.121940 | E_var:     0.1368 | E_err:   0.005778
[2025-10-02 16:13:29] [Iter 1932/2250] R4[1612/2400] | LR: 0.011081 | E: -27.130838 | E_var:     0.2388 | E_err:   0.007635
[2025-10-02 16:13:32] [Iter 1933/2250] R4[1614/2400] | LR: 0.011053 | E: -27.120578 | E_var:     0.1867 | E_err:   0.006751
[2025-10-02 16:13:34] [Iter 1934/2250] R4[1616/2400] | LR: 0.011025 | E: -27.133617 | E_var:     0.1589 | E_err:   0.006228
[2025-10-02 16:13:37] [Iter 1935/2250] R4[1618/2400] | LR: 0.010997 | E: -27.129251 | E_var:     0.1780 | E_err:   0.006592
[2025-10-02 16:13:39] [Iter 1936/2250] R4[1620/2400] | LR: 0.010969 | E: -27.119797 | E_var:     0.1768 | E_err:   0.006570
[2025-10-02 16:13:41] [Iter 1937/2250] R4[1622/2400] | LR: 0.010941 | E: -27.119262 | E_var:     0.2908 | E_err:   0.008426
[2025-10-02 16:13:44] [Iter 1938/2250] R4[1624/2400] | LR: 0.010913 | E: -27.129803 | E_var:     0.2274 | E_err:   0.007451
[2025-10-02 16:13:46] [Iter 1939/2250] R4[1626/2400] | LR: 0.010885 | E: -27.112408 | E_var:     0.1966 | E_err:   0.006928
[2025-10-02 16:13:49] [Iter 1940/2250] R4[1628/2400] | LR: 0.010858 | E: -27.125266 | E_var:     0.1872 | E_err:   0.006760
[2025-10-02 16:13:51] [Iter 1941/2250] R4[1630/2400] | LR: 0.010830 | E: -27.120785 | E_var:     0.1987 | E_err:   0.006965
[2025-10-02 16:13:53] [Iter 1942/2250] R4[1632/2400] | LR: 0.010802 | E: -27.136732 | E_var:     0.1759 | E_err:   0.006552
[2025-10-02 16:13:56] [Iter 1943/2250] R4[1634/2400] | LR: 0.010775 | E: -27.111743 | E_var:     0.1517 | E_err:   0.006086
[2025-10-02 16:13:58] [Iter 1944/2250] R4[1636/2400] | LR: 0.010747 | E: -27.120906 | E_var:     0.1546 | E_err:   0.006144
[2025-10-02 16:14:01] [Iter 1945/2250] R4[1638/2400] | LR: 0.010719 | E: -27.125635 | E_var:     0.1607 | E_err:   0.006263
[2025-10-02 16:14:03] [Iter 1946/2250] R4[1640/2400] | LR: 0.010692 | E: -27.122126 | E_var:     0.2300 | E_err:   0.007493
[2025-10-02 16:14:06] [Iter 1947/2250] R4[1642/2400] | LR: 0.010665 | E: -27.137695 | E_var:     0.2108 | E_err:   0.007173
[2025-10-02 16:14:08] [Iter 1948/2250] R4[1644/2400] | LR: 0.010637 | E: -27.126366 | E_var:     0.2028 | E_err:   0.007037
[2025-10-02 16:14:10] [Iter 1949/2250] R4[1646/2400] | LR: 0.010610 | E: -27.122952 | E_var:     0.6484 | E_err:   0.012582
[2025-10-02 16:14:13] [Iter 1950/2250] R4[1648/2400] | LR: 0.010583 | E: -27.132958 | E_var:     0.1920 | E_err:   0.006846
[2025-10-02 16:14:15] [Iter 1951/2250] R4[1650/2400] | LR: 0.010555 | E: -27.137885 | E_var:     0.1826 | E_err:   0.006676
[2025-10-02 16:14:18] [Iter 1952/2250] R4[1652/2400] | LR: 0.010528 | E: -27.121671 | E_var:     0.1652 | E_err:   0.006351
[2025-10-02 16:14:20] [Iter 1953/2250] R4[1654/2400] | LR: 0.010501 | E: -27.122676 | E_var:     0.1679 | E_err:   0.006403
[2025-10-02 16:14:22] [Iter 1954/2250] R4[1656/2400] | LR: 0.010474 | E: -27.134841 | E_var:     0.1848 | E_err:   0.006717
[2025-10-02 16:14:25] [Iter 1955/2250] R4[1658/2400] | LR: 0.010447 | E: -27.121331 | E_var:     0.2199 | E_err:   0.007327
[2025-10-02 16:14:27] [Iter 1956/2250] R4[1660/2400] | LR: 0.010420 | E: -27.124285 | E_var:     0.2171 | E_err:   0.007280
[2025-10-02 16:14:30] [Iter 1957/2250] R4[1662/2400] | LR: 0.010393 | E: -27.129324 | E_var:     0.1762 | E_err:   0.006559
[2025-10-02 16:14:32] [Iter 1958/2250] R4[1664/2400] | LR: 0.010366 | E: -27.128247 | E_var:     0.2420 | E_err:   0.007687
[2025-10-02 16:14:35] [Iter 1959/2250] R4[1666/2400] | LR: 0.010339 | E: -27.124014 | E_var:     0.1988 | E_err:   0.006966
[2025-10-02 16:14:37] [Iter 1960/2250] R4[1668/2400] | LR: 0.010312 | E: -27.125784 | E_var:     0.2214 | E_err:   0.007351
[2025-10-02 16:14:39] [Iter 1961/2250] R4[1670/2400] | LR: 0.010286 | E: -27.124029 | E_var:     0.2257 | E_err:   0.007423
[2025-10-02 16:14:42] [Iter 1962/2250] R4[1672/2400] | LR: 0.010259 | E: -27.118462 | E_var:     0.2126 | E_err:   0.007204
[2025-10-02 16:14:44] [Iter 1963/2250] R4[1674/2400] | LR: 0.010232 | E: -27.119188 | E_var:     0.1814 | E_err:   0.006656
[2025-10-02 16:14:47] [Iter 1964/2250] R4[1676/2400] | LR: 0.010206 | E: -27.116609 | E_var:     0.1842 | E_err:   0.006705
[2025-10-02 16:14:49] [Iter 1965/2250] R4[1678/2400] | LR: 0.010179 | E: -27.121470 | E_var:     0.1987 | E_err:   0.006966
[2025-10-02 16:14:51] [Iter 1966/2250] R4[1680/2400] | LR: 0.010153 | E: -27.126191 | E_var:     0.1778 | E_err:   0.006588
[2025-10-02 16:14:54] [Iter 1967/2250] R4[1682/2400] | LR: 0.010126 | E: -27.137634 | E_var:     0.1784 | E_err:   0.006600
[2025-10-02 16:14:56] [Iter 1968/2250] R4[1684/2400] | LR: 0.010100 | E: -27.119372 | E_var:     0.2006 | E_err:   0.006998
[2025-10-02 16:14:59] [Iter 1969/2250] R4[1686/2400] | LR: 0.010073 | E: -27.128352 | E_var:     0.1817 | E_err:   0.006661
[2025-10-02 16:15:01] [Iter 1970/2250] R4[1688/2400] | LR: 0.010047 | E: -27.112211 | E_var:     0.1753 | E_err:   0.006542
[2025-10-02 16:15:04] [Iter 1971/2250] R4[1690/2400] | LR: 0.010021 | E: -27.124495 | E_var:     0.2076 | E_err:   0.007119
[2025-10-02 16:15:06] [Iter 1972/2250] R4[1692/2400] | LR: 0.009995 | E: -27.123398 | E_var:     0.2269 | E_err:   0.007443
[2025-10-02 16:15:08] [Iter 1973/2250] R4[1694/2400] | LR: 0.009969 | E: -27.132106 | E_var:     0.2031 | E_err:   0.007041
[2025-10-02 16:15:11] [Iter 1974/2250] R4[1696/2400] | LR: 0.009943 | E: -27.125639 | E_var:     0.1824 | E_err:   0.006673
[2025-10-02 16:15:13] [Iter 1975/2250] R4[1698/2400] | LR: 0.009916 | E: -27.116401 | E_var:     0.2473 | E_err:   0.007770
[2025-10-02 16:15:16] [Iter 1976/2250] R4[1700/2400] | LR: 0.009890 | E: -27.122643 | E_var:     0.3709 | E_err:   0.009516
[2025-10-02 16:15:18] [Iter 1977/2250] R4[1702/2400] | LR: 0.009865 | E: -27.123966 | E_var:     0.1764 | E_err:   0.006563
[2025-10-02 16:15:20] [Iter 1978/2250] R4[1704/2400] | LR: 0.009839 | E: -27.128247 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 16:15:23] [Iter 1979/2250] R4[1706/2400] | LR: 0.009813 | E: -27.127613 | E_var:     0.2634 | E_err:   0.008019
[2025-10-02 16:15:25] [Iter 1980/2250] R4[1708/2400] | LR: 0.009787 | E: -27.121021 | E_var:     0.2590 | E_err:   0.007952
[2025-10-02 16:15:28] [Iter 1981/2250] R4[1710/2400] | LR: 0.009761 | E: -27.122908 | E_var:     0.1467 | E_err:   0.005984
[2025-10-02 16:15:30] [Iter 1982/2250] R4[1712/2400] | LR: 0.009736 | E: -27.120020 | E_var:     0.1797 | E_err:   0.006623
[2025-10-02 16:15:32] [Iter 1983/2250] R4[1714/2400] | LR: 0.009710 | E: -27.123664 | E_var:     0.1647 | E_err:   0.006341
[2025-10-02 16:15:35] [Iter 1984/2250] R4[1716/2400] | LR: 0.009684 | E: -27.117923 | E_var:     0.1426 | E_err:   0.005901
[2025-10-02 16:15:37] [Iter 1985/2250] R4[1718/2400] | LR: 0.009659 | E: -27.119777 | E_var:     0.3898 | E_err:   0.009756
[2025-10-02 16:15:40] [Iter 1986/2250] R4[1720/2400] | LR: 0.009633 | E: -27.123991 | E_var:     0.1905 | E_err:   0.006820
[2025-10-02 16:15:42] [Iter 1987/2250] R4[1722/2400] | LR: 0.009608 | E: -27.130562 | E_var:     0.1619 | E_err:   0.006286
[2025-10-02 16:15:45] [Iter 1988/2250] R4[1724/2400] | LR: 0.009583 | E: -27.118897 | E_var:     0.1889 | E_err:   0.006791
[2025-10-02 16:15:47] [Iter 1989/2250] R4[1726/2400] | LR: 0.009557 | E: -27.121919 | E_var:     0.1421 | E_err:   0.005890
[2025-10-02 16:15:49] [Iter 1990/2250] R4[1728/2400] | LR: 0.009532 | E: -27.122422 | E_var:     0.1460 | E_err:   0.005971
[2025-10-02 16:15:52] [Iter 1991/2250] R4[1730/2400] | LR: 0.009507 | E: -27.127285 | E_var:     0.1819 | E_err:   0.006663
[2025-10-02 16:15:54] [Iter 1992/2250] R4[1732/2400] | LR: 0.009482 | E: -27.133473 | E_var:     0.1629 | E_err:   0.006307
[2025-10-02 16:15:57] [Iter 1993/2250] R4[1734/2400] | LR: 0.009457 | E: -27.128099 | E_var:     0.1533 | E_err:   0.006119
[2025-10-02 16:15:59] [Iter 1994/2250] R4[1736/2400] | LR: 0.009432 | E: -27.127308 | E_var:     0.1585 | E_err:   0.006221
[2025-10-02 16:16:01] [Iter 1995/2250] R4[1738/2400] | LR: 0.009407 | E: -27.113618 | E_var:     0.1661 | E_err:   0.006368
[2025-10-02 16:16:04] [Iter 1996/2250] R4[1740/2400] | LR: 0.009382 | E: -27.125672 | E_var:     0.1721 | E_err:   0.006482
[2025-10-02 16:16:06] [Iter 1997/2250] R4[1742/2400] | LR: 0.009357 | E: -27.128371 | E_var:     0.1719 | E_err:   0.006477
[2025-10-02 16:16:09] [Iter 1998/2250] R4[1744/2400] | LR: 0.009332 | E: -27.131415 | E_var:     0.1920 | E_err:   0.006847
[2025-10-02 16:16:11] [Iter 1999/2250] R4[1746/2400] | LR: 0.009307 | E: -27.133657 | E_var:     0.1901 | E_err:   0.006812
[2025-10-02 16:16:13] [Iter 2000/2250] R4[1748/2400] | LR: 0.009283 | E: -27.114722 | E_var:     0.1417 | E_err:   0.005882
[2025-10-02 16:16:13] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-10-02 16:16:16] [Iter 2001/2250] R4[1750/2400] | LR: 0.009258 | E: -27.117554 | E_var:     0.1558 | E_err:   0.006168
[2025-10-02 16:16:18] [Iter 2002/2250] R4[1752/2400] | LR: 0.009234 | E: -27.139756 | E_var:     0.1414 | E_err:   0.005875
[2025-10-02 16:16:21] [Iter 2003/2250] R4[1754/2400] | LR: 0.009209 | E: -27.127956 | E_var:     0.1630 | E_err:   0.006308
[2025-10-02 16:16:23] [Iter 2004/2250] R4[1756/2400] | LR: 0.009185 | E: -27.124407 | E_var:     0.3321 | E_err:   0.009004
[2025-10-02 16:16:25] [Iter 2005/2250] R4[1758/2400] | LR: 0.009160 | E: -27.129909 | E_var:     0.1783 | E_err:   0.006597
[2025-10-02 16:16:28] [Iter 2006/2250] R4[1760/2400] | LR: 0.009136 | E: -27.136761 | E_var:     0.2259 | E_err:   0.007427
[2025-10-02 16:16:30] [Iter 2007/2250] R4[1762/2400] | LR: 0.009112 | E: -27.131021 | E_var:     0.1492 | E_err:   0.006036
[2025-10-02 16:16:33] [Iter 2008/2250] R4[1764/2400] | LR: 0.009087 | E: -27.130413 | E_var:     0.1589 | E_err:   0.006229
[2025-10-02 16:16:35] [Iter 2009/2250] R4[1766/2400] | LR: 0.009063 | E: -27.128435 | E_var:     0.1608 | E_err:   0.006265
[2025-10-02 16:16:38] [Iter 2010/2250] R4[1768/2400] | LR: 0.009039 | E: -27.125355 | E_var:     0.1704 | E_err:   0.006449
[2025-10-02 16:16:40] [Iter 2011/2250] R4[1770/2400] | LR: 0.009015 | E: -27.120478 | E_var:     0.1853 | E_err:   0.006726
[2025-10-02 16:16:42] [Iter 2012/2250] R4[1772/2400] | LR: 0.008991 | E: -27.125705 | E_var:     0.2703 | E_err:   0.008124
[2025-10-02 16:16:45] [Iter 2013/2250] R4[1774/2400] | LR: 0.008967 | E: -27.123447 | E_var:     0.1981 | E_err:   0.006954
[2025-10-02 16:16:47] [Iter 2014/2250] R4[1776/2400] | LR: 0.008943 | E: -27.132625 | E_var:     0.2078 | E_err:   0.007122
[2025-10-02 16:16:50] [Iter 2015/2250] R4[1778/2400] | LR: 0.008919 | E: -27.133347 | E_var:     0.2164 | E_err:   0.007269
[2025-10-02 16:16:52] [Iter 2016/2250] R4[1780/2400] | LR: 0.008896 | E: -27.131590 | E_var:     0.1757 | E_err:   0.006549
[2025-10-02 16:16:54] [Iter 2017/2250] R4[1782/2400] | LR: 0.008872 | E: -27.123870 | E_var:     0.2896 | E_err:   0.008409
[2025-10-02 16:16:57] [Iter 2018/2250] R4[1784/2400] | LR: 0.008848 | E: -27.116885 | E_var:     0.1423 | E_err:   0.005895
[2025-10-02 16:16:59] [Iter 2019/2250] R4[1786/2400] | LR: 0.008825 | E: -27.126088 | E_var:     0.1810 | E_err:   0.006648
[2025-10-02 16:17:02] [Iter 2020/2250] R4[1788/2400] | LR: 0.008801 | E: -27.122212 | E_var:     0.2281 | E_err:   0.007462
[2025-10-02 16:17:04] [Iter 2021/2250] R4[1790/2400] | LR: 0.008778 | E: -27.132752 | E_var:     0.2195 | E_err:   0.007321
[2025-10-02 16:17:06] [Iter 2022/2250] R4[1792/2400] | LR: 0.008754 | E: -27.136718 | E_var:     0.1713 | E_err:   0.006467
[2025-10-02 16:17:09] [Iter 2023/2250] R4[1794/2400] | LR: 0.008731 | E: -27.129144 | E_var:     0.1808 | E_err:   0.006643
[2025-10-02 16:17:11] [Iter 2024/2250] R4[1796/2400] | LR: 0.008708 | E: -27.130742 | E_var:     0.1825 | E_err:   0.006675
[2025-10-02 16:17:14] [Iter 2025/2250] R4[1798/2400] | LR: 0.008684 | E: -27.132353 | E_var:     0.1467 | E_err:   0.005985
[2025-10-02 16:17:16] [Iter 2026/2250] R4[1800/2400] | LR: 0.008661 | E: -27.130492 | E_var:     0.1889 | E_err:   0.006791
[2025-10-02 16:17:18] [Iter 2027/2250] R4[1802/2400] | LR: 0.008638 | E: -27.128792 | E_var:     0.1818 | E_err:   0.006661
[2025-10-02 16:17:21] [Iter 2028/2250] R4[1804/2400] | LR: 0.008615 | E: -27.131859 | E_var:     0.1831 | E_err:   0.006687
[2025-10-02 16:17:23] [Iter 2029/2250] R4[1806/2400] | LR: 0.008592 | E: -27.114401 | E_var:     0.1952 | E_err:   0.006903
[2025-10-02 16:17:26] [Iter 2030/2250] R4[1808/2400] | LR: 0.008569 | E: -27.120487 | E_var:     0.1602 | E_err:   0.006253
[2025-10-02 16:17:28] [Iter 2031/2250] R4[1810/2400] | LR: 0.008546 | E: -27.137796 | E_var:     0.2017 | E_err:   0.007018
[2025-10-02 16:17:30] [Iter 2032/2250] R4[1812/2400] | LR: 0.008523 | E: -27.125164 | E_var:     0.2060 | E_err:   0.007092
[2025-10-02 16:17:33] [Iter 2033/2250] R4[1814/2400] | LR: 0.008501 | E: -27.133820 | E_var:     0.1578 | E_err:   0.006206
[2025-10-02 16:17:35] [Iter 2034/2250] R4[1816/2400] | LR: 0.008478 | E: -27.130863 | E_var:     0.1761 | E_err:   0.006557
[2025-10-02 16:17:38] [Iter 2035/2250] R4[1818/2400] | LR: 0.008455 | E: -27.118136 | E_var:     0.1555 | E_err:   0.006162
[2025-10-02 16:17:40] [Iter 2036/2250] R4[1820/2400] | LR: 0.008433 | E: -27.126713 | E_var:     0.1868 | E_err:   0.006752
[2025-10-02 16:17:43] [Iter 2037/2250] R4[1822/2400] | LR: 0.008410 | E: -27.121860 | E_var:     0.1815 | E_err:   0.006656
[2025-10-02 16:17:45] [Iter 2038/2250] R4[1824/2400] | LR: 0.008388 | E: -27.122368 | E_var:     0.2117 | E_err:   0.007188
[2025-10-02 16:17:47] [Iter 2039/2250] R4[1826/2400] | LR: 0.008366 | E: -27.127551 | E_var:     0.1718 | E_err:   0.006477
[2025-10-02 16:17:50] [Iter 2040/2250] R4[1828/2400] | LR: 0.008343 | E: -27.128362 | E_var:     0.1983 | E_err:   0.006958
[2025-10-02 16:17:52] [Iter 2041/2250] R4[1830/2400] | LR: 0.008321 | E: -27.117109 | E_var:     0.1636 | E_err:   0.006319
[2025-10-02 16:17:55] [Iter 2042/2250] R4[1832/2400] | LR: 0.008299 | E: -27.116517 | E_var:     0.3037 | E_err:   0.008610
[2025-10-02 16:17:57] [Iter 2043/2250] R4[1834/2400] | LR: 0.008277 | E: -27.117210 | E_var:     0.2130 | E_err:   0.007211
[2025-10-02 16:17:59] [Iter 2044/2250] R4[1836/2400] | LR: 0.008255 | E: -27.126921 | E_var:     0.1699 | E_err:   0.006440
[2025-10-02 16:18:02] [Iter 2045/2250] R4[1838/2400] | LR: 0.008233 | E: -27.126937 | E_var:     0.1726 | E_err:   0.006492
[2025-10-02 16:18:04] [Iter 2046/2250] R4[1840/2400] | LR: 0.008211 | E: -27.122774 | E_var:     0.1857 | E_err:   0.006733
[2025-10-02 16:18:07] [Iter 2047/2250] R4[1842/2400] | LR: 0.008189 | E: -27.135565 | E_var:     0.3055 | E_err:   0.008637
[2025-10-02 16:18:09] [Iter 2048/2250] R4[1844/2400] | LR: 0.008167 | E: -27.121152 | E_var:     0.2025 | E_err:   0.007031
[2025-10-02 16:18:11] [Iter 2049/2250] R4[1846/2400] | LR: 0.008145 | E: -27.136906 | E_var:     0.2221 | E_err:   0.007364
[2025-10-02 16:18:14] [Iter 2050/2250] R4[1848/2400] | LR: 0.008124 | E: -27.122656 | E_var:     0.1639 | E_err:   0.006327
[2025-10-02 16:18:16] [Iter 2051/2250] R4[1850/2400] | LR: 0.008102 | E: -27.131957 | E_var:     0.1640 | E_err:   0.006328
[2025-10-02 16:18:19] [Iter 2052/2250] R4[1852/2400] | LR: 0.008080 | E: -27.135310 | E_var:     0.2243 | E_err:   0.007400
[2025-10-02 16:18:21] [Iter 2053/2250] R4[1854/2400] | LR: 0.008059 | E: -27.135891 | E_var:     0.1552 | E_err:   0.006155
[2025-10-02 16:18:23] [Iter 2054/2250] R4[1856/2400] | LR: 0.008038 | E: -27.131614 | E_var:     0.2014 | E_err:   0.007012
[2025-10-02 16:18:26] [Iter 2055/2250] R4[1858/2400] | LR: 0.008016 | E: -27.124325 | E_var:     0.1837 | E_err:   0.006698
[2025-10-02 16:18:28] [Iter 2056/2250] R4[1860/2400] | LR: 0.007995 | E: -27.142059 | E_var:     0.1870 | E_err:   0.006756
[2025-10-02 16:18:31] [Iter 2057/2250] R4[1862/2400] | LR: 0.007974 | E: -27.114664 | E_var:     0.2125 | E_err:   0.007202
[2025-10-02 16:18:33] [Iter 2058/2250] R4[1864/2400] | LR: 0.007953 | E: -27.124995 | E_var:     0.1756 | E_err:   0.006547
[2025-10-02 16:18:36] [Iter 2059/2250] R4[1866/2400] | LR: 0.007931 | E: -27.119581 | E_var:     0.2239 | E_err:   0.007394
[2025-10-02 16:18:38] [Iter 2060/2250] R4[1868/2400] | LR: 0.007910 | E: -27.137523 | E_var:     0.2164 | E_err:   0.007269
[2025-10-02 16:18:40] [Iter 2061/2250] R4[1870/2400] | LR: 0.007889 | E: -27.114634 | E_var:     0.2057 | E_err:   0.007086
[2025-10-02 16:18:43] [Iter 2062/2250] R4[1872/2400] | LR: 0.007869 | E: -27.131186 | E_var:     0.1841 | E_err:   0.006704
[2025-10-02 16:18:45] [Iter 2063/2250] R4[1874/2400] | LR: 0.007848 | E: -27.112810 | E_var:     0.2607 | E_err:   0.007978
[2025-10-02 16:18:48] [Iter 2064/2250] R4[1876/2400] | LR: 0.007827 | E: -27.141691 | E_var:     0.2559 | E_err:   0.007905
[2025-10-02 16:18:50] [Iter 2065/2250] R4[1878/2400] | LR: 0.007806 | E: -27.134415 | E_var:     0.1511 | E_err:   0.006074
[2025-10-02 16:18:52] [Iter 2066/2250] R4[1880/2400] | LR: 0.007786 | E: -27.119816 | E_var:     0.1616 | E_err:   0.006282
[2025-10-02 16:18:55] [Iter 2067/2250] R4[1882/2400] | LR: 0.007765 | E: -27.110894 | E_var:     0.1840 | E_err:   0.006703
[2025-10-02 16:18:57] [Iter 2068/2250] R4[1884/2400] | LR: 0.007745 | E: -27.120614 | E_var:     0.1625 | E_err:   0.006300
[2025-10-02 16:19:00] [Iter 2069/2250] R4[1886/2400] | LR: 0.007724 | E: -27.133060 | E_var:     0.1649 | E_err:   0.006345
[2025-10-02 16:19:02] [Iter 2070/2250] R4[1888/2400] | LR: 0.007704 | E: -27.133668 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 16:19:04] [Iter 2071/2250] R4[1890/2400] | LR: 0.007684 | E: -27.131910 | E_var:     0.1559 | E_err:   0.006169
[2025-10-02 16:19:07] [Iter 2072/2250] R4[1892/2400] | LR: 0.007663 | E: -27.127664 | E_var:     0.1836 | E_err:   0.006694
[2025-10-02 16:19:09] [Iter 2073/2250] R4[1894/2400] | LR: 0.007643 | E: -27.125139 | E_var:     0.1877 | E_err:   0.006769
[2025-10-02 16:19:12] [Iter 2074/2250] R4[1896/2400] | LR: 0.007623 | E: -27.130949 | E_var:     0.1531 | E_err:   0.006113
[2025-10-02 16:19:14] [Iter 2075/2250] R4[1898/2400] | LR: 0.007603 | E: -27.127438 | E_var:     0.2193 | E_err:   0.007317
[2025-10-02 16:19:16] [Iter 2076/2250] R4[1900/2400] | LR: 0.007583 | E: -27.122014 | E_var:     0.2513 | E_err:   0.007833
[2025-10-02 16:19:19] [Iter 2077/2250] R4[1902/2400] | LR: 0.007563 | E: -27.121106 | E_var:     0.2502 | E_err:   0.007815
[2025-10-02 16:19:21] [Iter 2078/2250] R4[1904/2400] | LR: 0.007543 | E: -27.119986 | E_var:     0.2712 | E_err:   0.008136
[2025-10-02 16:19:24] [Iter 2079/2250] R4[1906/2400] | LR: 0.007524 | E: -27.121595 | E_var:     0.1890 | E_err:   0.006794
[2025-10-02 16:19:26] [Iter 2080/2250] R4[1908/2400] | LR: 0.007504 | E: -27.122759 | E_var:     0.2149 | E_err:   0.007244
[2025-10-02 16:19:29] [Iter 2081/2250] R4[1910/2400] | LR: 0.007484 | E: -27.129787 | E_var:     0.1771 | E_err:   0.006575
[2025-10-02 16:19:31] [Iter 2082/2250] R4[1912/2400] | LR: 0.007465 | E: -27.125582 | E_var:     0.1577 | E_err:   0.006204
[2025-10-02 16:19:33] [Iter 2083/2250] R4[1914/2400] | LR: 0.007445 | E: -27.140259 | E_var:     0.2049 | E_err:   0.007072
[2025-10-02 16:19:36] [Iter 2084/2250] R4[1916/2400] | LR: 0.007426 | E: -27.129477 | E_var:     0.1972 | E_err:   0.006939
[2025-10-02 16:19:38] [Iter 2085/2250] R4[1918/2400] | LR: 0.007407 | E: -27.121323 | E_var:     0.2038 | E_err:   0.007054
[2025-10-02 16:19:41] [Iter 2086/2250] R4[1920/2400] | LR: 0.007387 | E: -27.124064 | E_var:     0.1724 | E_err:   0.006488
[2025-10-02 16:19:43] [Iter 2087/2250] R4[1922/2400] | LR: 0.007368 | E: -27.128827 | E_var:     0.1933 | E_err:   0.006869
[2025-10-02 16:19:45] [Iter 2088/2250] R4[1924/2400] | LR: 0.007349 | E: -27.112419 | E_var:     0.1757 | E_err:   0.006549
[2025-10-02 16:19:48] [Iter 2089/2250] R4[1926/2400] | LR: 0.007330 | E: -27.118313 | E_var:     0.1776 | E_err:   0.006585
[2025-10-02 16:19:50] [Iter 2090/2250] R4[1928/2400] | LR: 0.007311 | E: -27.139729 | E_var:     0.1615 | E_err:   0.006280
[2025-10-02 16:19:53] [Iter 2091/2250] R4[1930/2400] | LR: 0.007292 | E: -27.126924 | E_var:     0.1751 | E_err:   0.006539
[2025-10-02 16:19:55] [Iter 2092/2250] R4[1932/2400] | LR: 0.007273 | E: -27.129453 | E_var:     0.2320 | E_err:   0.007526
[2025-10-02 16:19:57] [Iter 2093/2250] R4[1934/2400] | LR: 0.007254 | E: -27.115584 | E_var:     0.1997 | E_err:   0.006983
[2025-10-02 16:20:00] [Iter 2094/2250] R4[1936/2400] | LR: 0.007236 | E: -27.122436 | E_var:     0.1658 | E_err:   0.006362
[2025-10-02 16:20:02] [Iter 2095/2250] R4[1938/2400] | LR: 0.007217 | E: -27.116708 | E_var:     0.2439 | E_err:   0.007717
[2025-10-02 16:20:05] [Iter 2096/2250] R4[1940/2400] | LR: 0.007198 | E: -27.119018 | E_var:     0.1653 | E_err:   0.006352
[2025-10-02 16:20:07] [Iter 2097/2250] R4[1942/2400] | LR: 0.007180 | E: -27.131013 | E_var:     0.1725 | E_err:   0.006490
[2025-10-02 16:20:10] [Iter 2098/2250] R4[1944/2400] | LR: 0.007161 | E: -27.121999 | E_var:     0.1723 | E_err:   0.006487
[2025-10-02 16:20:12] [Iter 2099/2250] R4[1946/2400] | LR: 0.007143 | E: -27.114135 | E_var:     0.1622 | E_err:   0.006292
[2025-10-02 16:20:14] [Iter 2100/2250] R4[1948/2400] | LR: 0.007125 | E: -27.128118 | E_var:     0.2132 | E_err:   0.007214
[2025-10-02 16:20:17] [Iter 2101/2250] R4[1950/2400] | LR: 0.007107 | E: -27.135426 | E_var:     0.1582 | E_err:   0.006215
[2025-10-02 16:20:19] [Iter 2102/2250] R4[1952/2400] | LR: 0.007088 | E: -27.119794 | E_var:     0.1699 | E_err:   0.006441
[2025-10-02 16:20:22] [Iter 2103/2250] R4[1954/2400] | LR: 0.007070 | E: -27.121038 | E_var:     0.1764 | E_err:   0.006562
[2025-10-02 16:20:24] [Iter 2104/2250] R4[1956/2400] | LR: 0.007052 | E: -27.129015 | E_var:     0.2677 | E_err:   0.008085
[2025-10-02 16:20:26] [Iter 2105/2250] R4[1958/2400] | LR: 0.007034 | E: -27.122499 | E_var:     0.2039 | E_err:   0.007055
[2025-10-02 16:20:29] [Iter 2106/2250] R4[1960/2400] | LR: 0.007017 | E: -27.136238 | E_var:     0.2593 | E_err:   0.007956
[2025-10-02 16:20:31] [Iter 2107/2250] R4[1962/2400] | LR: 0.006999 | E: -27.144562 | E_var:     0.2512 | E_err:   0.007832
[2025-10-02 16:20:34] [Iter 2108/2250] R4[1964/2400] | LR: 0.006981 | E: -27.131020 | E_var:     0.3286 | E_err:   0.008957
[2025-10-02 16:20:36] [Iter 2109/2250] R4[1966/2400] | LR: 0.006963 | E: -27.118502 | E_var:     0.1718 | E_err:   0.006477
[2025-10-02 16:20:39] [Iter 2110/2250] R4[1968/2400] | LR: 0.006946 | E: -27.118573 | E_var:     0.1725 | E_err:   0.006490
[2025-10-02 16:20:41] [Iter 2111/2250] R4[1970/2400] | LR: 0.006928 | E: -27.125602 | E_var:     0.1534 | E_err:   0.006120
[2025-10-02 16:20:43] [Iter 2112/2250] R4[1972/2400] | LR: 0.006911 | E: -27.120638 | E_var:     0.1996 | E_err:   0.006981
[2025-10-02 16:20:46] [Iter 2113/2250] R4[1974/2400] | LR: 0.006894 | E: -27.127317 | E_var:     0.1682 | E_err:   0.006408
[2025-10-02 16:20:48] [Iter 2114/2250] R4[1976/2400] | LR: 0.006876 | E: -27.128692 | E_var:     0.1556 | E_err:   0.006163
[2025-10-02 16:20:51] [Iter 2115/2250] R4[1978/2400] | LR: 0.006859 | E: -27.123567 | E_var:     0.2357 | E_err:   0.007586
[2025-10-02 16:20:53] [Iter 2116/2250] R4[1980/2400] | LR: 0.006842 | E: -27.114289 | E_var:     0.1492 | E_err:   0.006035
[2025-10-02 16:20:55] [Iter 2117/2250] R4[1982/2400] | LR: 0.006825 | E: -27.123451 | E_var:     0.1739 | E_err:   0.006516
[2025-10-02 16:20:58] [Iter 2118/2250] R4[1984/2400] | LR: 0.006808 | E: -27.120043 | E_var:     0.1545 | E_err:   0.006141
[2025-10-02 16:21:00] [Iter 2119/2250] R4[1986/2400] | LR: 0.006791 | E: -27.128745 | E_var:     0.2035 | E_err:   0.007049
[2025-10-02 16:21:03] [Iter 2120/2250] R4[1988/2400] | LR: 0.006774 | E: -27.116986 | E_var:     0.1704 | E_err:   0.006449
[2025-10-02 16:21:05] [Iter 2121/2250] R4[1990/2400] | LR: 0.006757 | E: -27.127073 | E_var:     0.1515 | E_err:   0.006081
[2025-10-02 16:21:07] [Iter 2122/2250] R4[1992/2400] | LR: 0.006741 | E: -27.115869 | E_var:     0.1751 | E_err:   0.006538
[2025-10-02 16:21:10] [Iter 2123/2250] R4[1994/2400] | LR: 0.006724 | E: -27.126593 | E_var:     0.1649 | E_err:   0.006344
[2025-10-02 16:21:12] [Iter 2124/2250] R4[1996/2400] | LR: 0.006708 | E: -27.134914 | E_var:     0.1670 | E_err:   0.006386
[2025-10-02 16:21:15] [Iter 2125/2250] R4[1998/2400] | LR: 0.006691 | E: -27.119722 | E_var:     0.1348 | E_err:   0.005736
[2025-10-02 16:21:17] [Iter 2126/2250] R4[2000/2400] | LR: 0.006675 | E: -27.126924 | E_var:     0.1792 | E_err:   0.006615
[2025-10-02 16:21:19] [Iter 2127/2250] R4[2002/2400] | LR: 0.006658 | E: -27.132640 | E_var:     0.1921 | E_err:   0.006849
[2025-10-02 16:21:22] [Iter 2128/2250] R4[2004/2400] | LR: 0.006642 | E: -27.127731 | E_var:     0.2491 | E_err:   0.007798
[2025-10-02 16:21:24] [Iter 2129/2250] R4[2006/2400] | LR: 0.006626 | E: -27.136786 | E_var:     0.1594 | E_err:   0.006238
[2025-10-02 16:21:27] [Iter 2130/2250] R4[2008/2400] | LR: 0.006610 | E: -27.141505 | E_var:     0.1960 | E_err:   0.006917
[2025-10-02 16:21:29] [Iter 2131/2250] R4[2010/2400] | LR: 0.006594 | E: -27.126492 | E_var:     0.1980 | E_err:   0.006953
[2025-10-02 16:21:32] [Iter 2132/2250] R4[2012/2400] | LR: 0.006578 | E: -27.129338 | E_var:     0.2078 | E_err:   0.007122
[2025-10-02 16:21:34] [Iter 2133/2250] R4[2014/2400] | LR: 0.006562 | E: -27.117676 | E_var:     0.2557 | E_err:   0.007900
[2025-10-02 16:21:36] [Iter 2134/2250] R4[2016/2400] | LR: 0.006546 | E: -27.128943 | E_var:     0.1599 | E_err:   0.006248
[2025-10-02 16:21:39] [Iter 2135/2250] R4[2018/2400] | LR: 0.006530 | E: -27.127416 | E_var:     0.2053 | E_err:   0.007080
[2025-10-02 16:21:41] [Iter 2136/2250] R4[2020/2400] | LR: 0.006515 | E: -27.130851 | E_var:     0.3565 | E_err:   0.009329
[2025-10-02 16:21:44] [Iter 2137/2250] R4[2022/2400] | LR: 0.006499 | E: -27.139240 | E_var:     0.2818 | E_err:   0.008295
[2025-10-02 16:21:46] [Iter 2138/2250] R4[2024/2400] | LR: 0.006484 | E: -27.110473 | E_var:     0.2135 | E_err:   0.007219
[2025-10-02 16:21:48] [Iter 2139/2250] R4[2026/2400] | LR: 0.006468 | E: -27.119261 | E_var:     0.1874 | E_err:   0.006764
[2025-10-02 16:21:51] [Iter 2140/2250] R4[2028/2400] | LR: 0.006453 | E: -27.130773 | E_var:     0.1940 | E_err:   0.006882
[2025-10-02 16:21:53] [Iter 2141/2250] R4[2030/2400] | LR: 0.006438 | E: -27.132021 | E_var:     0.1655 | E_err:   0.006357
[2025-10-02 16:21:56] [Iter 2142/2250] R4[2032/2400] | LR: 0.006422 | E: -27.122369 | E_var:     0.1993 | E_err:   0.006975
[2025-10-02 16:21:58] [Iter 2143/2250] R4[2034/2400] | LR: 0.006407 | E: -27.135294 | E_var:     0.1510 | E_err:   0.006072
[2025-10-02 16:22:00] [Iter 2144/2250] R4[2036/2400] | LR: 0.006392 | E: -27.128976 | E_var:     0.1865 | E_err:   0.006748
[2025-10-02 16:22:03] [Iter 2145/2250] R4[2038/2400] | LR: 0.006377 | E: -27.128581 | E_var:     0.1647 | E_err:   0.006340
[2025-10-02 16:22:05] [Iter 2146/2250] R4[2040/2400] | LR: 0.006362 | E: -27.118138 | E_var:     0.2211 | E_err:   0.007347
[2025-10-02 16:22:08] [Iter 2147/2250] R4[2042/2400] | LR: 0.006348 | E: -27.114153 | E_var:     0.1793 | E_err:   0.006616
[2025-10-02 16:22:10] [Iter 2148/2250] R4[2044/2400] | LR: 0.006333 | E: -27.130592 | E_var:     0.1723 | E_err:   0.006486
[2025-10-02 16:22:12] [Iter 2149/2250] R4[2046/2400] | LR: 0.006318 | E: -27.121804 | E_var:     0.1416 | E_err:   0.005880
[2025-10-02 16:22:15] [Iter 2150/2250] R4[2048/2400] | LR: 0.006304 | E: -27.134543 | E_var:     0.1542 | E_err:   0.006137
[2025-10-02 16:22:17] [Iter 2151/2250] R4[2050/2400] | LR: 0.006289 | E: -27.123698 | E_var:     0.4142 | E_err:   0.010056
[2025-10-02 16:22:20] [Iter 2152/2250] R4[2052/2400] | LR: 0.006275 | E: -27.131418 | E_var:     0.1768 | E_err:   0.006570
[2025-10-02 16:22:22] [Iter 2153/2250] R4[2054/2400] | LR: 0.006260 | E: -27.125075 | E_var:     0.1597 | E_err:   0.006243
[2025-10-02 16:22:25] [Iter 2154/2250] R4[2056/2400] | LR: 0.006246 | E: -27.126407 | E_var:     0.1654 | E_err:   0.006355
[2025-10-02 16:22:27] [Iter 2155/2250] R4[2058/2400] | LR: 0.006232 | E: -27.117336 | E_var:     0.1623 | E_err:   0.006295
[2025-10-02 16:22:29] [Iter 2156/2250] R4[2060/2400] | LR: 0.006218 | E: -27.121636 | E_var:     0.1760 | E_err:   0.006556
[2025-10-02 16:22:32] [Iter 2157/2250] R4[2062/2400] | LR: 0.006204 | E: -27.132713 | E_var:     0.2129 | E_err:   0.007209
[2025-10-02 16:22:34] [Iter 2158/2250] R4[2064/2400] | LR: 0.006190 | E: -27.124147 | E_var:     0.1847 | E_err:   0.006715
[2025-10-02 16:22:37] [Iter 2159/2250] R4[2066/2400] | LR: 0.006176 | E: -27.123368 | E_var:     0.1659 | E_err:   0.006365
[2025-10-02 16:22:39] [Iter 2160/2250] R4[2068/2400] | LR: 0.006162 | E: -27.119028 | E_var:     0.1644 | E_err:   0.006336
[2025-10-02 16:22:41] [Iter 2161/2250] R4[2070/2400] | LR: 0.006148 | E: -27.121327 | E_var:     0.1630 | E_err:   0.006308
[2025-10-02 16:22:44] [Iter 2162/2250] R4[2072/2400] | LR: 0.006135 | E: -27.120262 | E_var:     0.1695 | E_err:   0.006433
[2025-10-02 16:22:46] [Iter 2163/2250] R4[2074/2400] | LR: 0.006121 | E: -27.134082 | E_var:     0.1500 | E_err:   0.006051
[2025-10-02 16:22:49] [Iter 2164/2250] R4[2076/2400] | LR: 0.006107 | E: -27.132517 | E_var:     0.2964 | E_err:   0.008507
[2025-10-02 16:22:51] [Iter 2165/2250] R4[2078/2400] | LR: 0.006094 | E: -27.131764 | E_var:     0.1374 | E_err:   0.005792
[2025-10-02 16:22:53] [Iter 2166/2250] R4[2080/2400] | LR: 0.006081 | E: -27.132606 | E_var:     0.1869 | E_err:   0.006756
[2025-10-02 16:22:56] [Iter 2167/2250] R4[2082/2400] | LR: 0.006067 | E: -27.131762 | E_var:     0.2468 | E_err:   0.007762
[2025-10-02 16:22:58] [Iter 2168/2250] R4[2084/2400] | LR: 0.006054 | E: -27.124771 | E_var:     0.1703 | E_err:   0.006448
[2025-10-02 16:23:01] [Iter 2169/2250] R4[2086/2400] | LR: 0.006041 | E: -27.118792 | E_var:     0.1619 | E_err:   0.006286
[2025-10-02 16:23:03] [Iter 2170/2250] R4[2088/2400] | LR: 0.006028 | E: -27.123929 | E_var:     0.1689 | E_err:   0.006421
[2025-10-02 16:23:05] [Iter 2171/2250] R4[2090/2400] | LR: 0.006015 | E: -27.145929 | E_var:     0.2084 | E_err:   0.007132
[2025-10-02 16:23:08] [Iter 2172/2250] R4[2092/2400] | LR: 0.006002 | E: -27.132254 | E_var:     0.2082 | E_err:   0.007129
[2025-10-02 16:23:10] [Iter 2173/2250] R4[2094/2400] | LR: 0.005989 | E: -27.117807 | E_var:     0.1600 | E_err:   0.006250
[2025-10-02 16:23:13] [Iter 2174/2250] R4[2096/2400] | LR: 0.005977 | E: -27.126853 | E_var:     0.1818 | E_err:   0.006663
[2025-10-02 16:23:15] [Iter 2175/2250] R4[2098/2400] | LR: 0.005964 | E: -27.128785 | E_var:     0.1447 | E_err:   0.005943
[2025-10-02 16:23:17] [Iter 2176/2250] R4[2100/2400] | LR: 0.005952 | E: -27.135323 | E_var:     0.1783 | E_err:   0.006597
[2025-10-02 16:23:20] [Iter 2177/2250] R4[2102/2400] | LR: 0.005939 | E: -27.131496 | E_var:     0.1645 | E_err:   0.006337
[2025-10-02 16:23:22] [Iter 2178/2250] R4[2104/2400] | LR: 0.005927 | E: -27.114312 | E_var:     0.1881 | E_err:   0.006777
[2025-10-02 16:23:25] [Iter 2179/2250] R4[2106/2400] | LR: 0.005914 | E: -27.126755 | E_var:     0.1793 | E_err:   0.006616
[2025-10-02 16:23:27] [Iter 2180/2250] R4[2108/2400] | LR: 0.005902 | E: -27.123490 | E_var:     0.2648 | E_err:   0.008041
[2025-10-02 16:23:29] [Iter 2181/2250] R4[2110/2400] | LR: 0.005890 | E: -27.136855 | E_var:     0.1567 | E_err:   0.006186
[2025-10-02 16:23:32] [Iter 2182/2250] R4[2112/2400] | LR: 0.005878 | E: -27.125327 | E_var:     0.2097 | E_err:   0.007155
[2025-10-02 16:23:34] [Iter 2183/2250] R4[2114/2400] | LR: 0.005866 | E: -27.114246 | E_var:     0.2422 | E_err:   0.007689
[2025-10-02 16:23:37] [Iter 2184/2250] R4[2116/2400] | LR: 0.005854 | E: -27.133275 | E_var:     0.1792 | E_err:   0.006615
[2025-10-02 16:23:39] [Iter 2185/2250] R4[2118/2400] | LR: 0.005842 | E: -27.133053 | E_var:     0.1674 | E_err:   0.006394
[2025-10-02 16:23:42] [Iter 2186/2250] R4[2120/2400] | LR: 0.005830 | E: -27.134044 | E_var:     0.1656 | E_err:   0.006358
[2025-10-02 16:23:44] [Iter 2187/2250] R4[2122/2400] | LR: 0.005819 | E: -27.123857 | E_var:     0.2212 | E_err:   0.007348
[2025-10-02 16:23:46] [Iter 2188/2250] R4[2124/2400] | LR: 0.005807 | E: -27.133290 | E_var:     0.2404 | E_err:   0.007661
[2025-10-02 16:23:49] [Iter 2189/2250] R4[2126/2400] | LR: 0.005795 | E: -27.135788 | E_var:     0.2131 | E_err:   0.007214
[2025-10-02 16:23:51] [Iter 2190/2250] R4[2128/2400] | LR: 0.005784 | E: -27.127237 | E_var:     0.1388 | E_err:   0.005820
[2025-10-02 16:23:54] [Iter 2191/2250] R4[2130/2400] | LR: 0.005773 | E: -27.120423 | E_var:     0.1673 | E_err:   0.006391
[2025-10-02 16:23:56] [Iter 2192/2250] R4[2132/2400] | LR: 0.005761 | E: -27.126074 | E_var:     0.1830 | E_err:   0.006684
[2025-10-02 16:23:58] [Iter 2193/2250] R4[2134/2400] | LR: 0.005750 | E: -27.120410 | E_var:     0.1401 | E_err:   0.005848
[2025-10-02 16:24:01] [Iter 2194/2250] R4[2136/2400] | LR: 0.005739 | E: -27.135940 | E_var:     0.1461 | E_err:   0.005973
[2025-10-02 16:24:03] [Iter 2195/2250] R4[2138/2400] | LR: 0.005728 | E: -27.127394 | E_var:     0.1460 | E_err:   0.005970
[2025-10-02 16:24:06] [Iter 2196/2250] R4[2140/2400] | LR: 0.005717 | E: -27.122402 | E_var:     0.1713 | E_err:   0.006468
[2025-10-02 16:24:08] [Iter 2197/2250] R4[2142/2400] | LR: 0.005706 | E: -27.132972 | E_var:     0.1530 | E_err:   0.006111
[2025-10-02 16:24:10] [Iter 2198/2250] R4[2144/2400] | LR: 0.005695 | E: -27.118000 | E_var:     0.1634 | E_err:   0.006316
[2025-10-02 16:24:13] [Iter 2199/2250] R4[2146/2400] | LR: 0.005685 | E: -27.141906 | E_var:     0.1790 | E_err:   0.006611
[2025-10-02 16:24:15] [Iter 2200/2250] R4[2148/2400] | LR: 0.005674 | E: -27.127407 | E_var:     0.1506 | E_err:   0.006063
[2025-10-02 16:24:15] ✓ Checkpoint saved: checkpoint_iter_002200.pkl
[2025-10-02 16:24:18] [Iter 2201/2250] R4[2150/2400] | LR: 0.005663 | E: -27.128127 | E_var:     0.1678 | E_err:   0.006400
[2025-10-02 16:24:20] [Iter 2202/2250] R4[2152/2400] | LR: 0.005653 | E: -27.135665 | E_var:     0.1621 | E_err:   0.006290
[2025-10-02 16:24:22] [Iter 2203/2250] R4[2154/2400] | LR: 0.005642 | E: -27.127167 | E_var:     0.1249 | E_err:   0.005522
[2025-10-02 16:24:25] [Iter 2204/2250] R4[2156/2400] | LR: 0.005632 | E: -27.121245 | E_var:     0.1441 | E_err:   0.005931
[2025-10-02 16:24:27] [Iter 2205/2250] R4[2158/2400] | LR: 0.005622 | E: -27.126065 | E_var:     0.1739 | E_err:   0.006516
[2025-10-02 16:24:30] [Iter 2206/2250] R4[2160/2400] | LR: 0.005612 | E: -27.131030 | E_var:     0.1708 | E_err:   0.006458
[2025-10-02 16:24:32] [Iter 2207/2250] R4[2162/2400] | LR: 0.005602 | E: -27.127328 | E_var:     0.1964 | E_err:   0.006925
[2025-10-02 16:24:35] [Iter 2208/2250] R4[2164/2400] | LR: 0.005592 | E: -27.125965 | E_var:     0.1504 | E_err:   0.006059
[2025-10-02 16:24:37] [Iter 2209/2250] R4[2166/2400] | LR: 0.005582 | E: -27.126474 | E_var:     0.1695 | E_err:   0.006433
[2025-10-02 16:24:39] [Iter 2210/2250] R4[2168/2400] | LR: 0.005572 | E: -27.128708 | E_var:     0.2187 | E_err:   0.007306
[2025-10-02 16:24:42] [Iter 2211/2250] R4[2170/2400] | LR: 0.005562 | E: -27.132768 | E_var:     0.2106 | E_err:   0.007171
[2025-10-02 16:24:44] [Iter 2212/2250] R4[2172/2400] | LR: 0.005553 | E: -27.130809 | E_var:     0.1547 | E_err:   0.006146
[2025-10-02 16:24:47] [Iter 2213/2250] R4[2174/2400] | LR: 0.005543 | E: -27.127217 | E_var:     0.5154 | E_err:   0.011217
[2025-10-02 16:24:49] [Iter 2214/2250] R4[2176/2400] | LR: 0.005534 | E: -27.126732 | E_var:     0.1827 | E_err:   0.006679
[2025-10-02 16:24:51] [Iter 2215/2250] R4[2178/2400] | LR: 0.005524 | E: -27.133606 | E_var:     0.1946 | E_err:   0.006892
[2025-10-02 16:24:54] [Iter 2216/2250] R4[2180/2400] | LR: 0.005515 | E: -27.132627 | E_var:     0.1811 | E_err:   0.006649
[2025-10-02 16:24:56] [Iter 2217/2250] R4[2182/2400] | LR: 0.005506 | E: -27.121488 | E_var:     0.1877 | E_err:   0.006769
[2025-10-02 16:24:59] [Iter 2218/2250] R4[2184/2400] | LR: 0.005496 | E: -27.124478 | E_var:     0.1462 | E_err:   0.005974
[2025-10-02 16:25:01] [Iter 2219/2250] R4[2186/2400] | LR: 0.005487 | E: -27.127301 | E_var:     0.2233 | E_err:   0.007383
[2025-10-02 16:25:03] [Iter 2220/2250] R4[2188/2400] | LR: 0.005478 | E: -27.129242 | E_var:     0.1540 | E_err:   0.006132
[2025-10-02 16:25:06] [Iter 2221/2250] R4[2190/2400] | LR: 0.005469 | E: -27.127077 | E_var:     0.2288 | E_err:   0.007473
[2025-10-02 16:25:08] [Iter 2222/2250] R4[2192/2400] | LR: 0.005460 | E: -27.129244 | E_var:     0.2394 | E_err:   0.007646
[2025-10-02 16:25:11] [Iter 2223/2250] R4[2194/2400] | LR: 0.005452 | E: -27.126178 | E_var:     0.1394 | E_err:   0.005833
[2025-10-02 16:25:13] [Iter 2224/2250] R4[2196/2400] | LR: 0.005443 | E: -27.114089 | E_var:     0.2063 | E_err:   0.007097
[2025-10-02 16:25:15] [Iter 2225/2250] R4[2198/2400] | LR: 0.005434 | E: -27.133257 | E_var:     0.1817 | E_err:   0.006661
[2025-10-02 16:25:18] [Iter 2226/2250] R4[2200/2400] | LR: 0.005426 | E: -27.126631 | E_var:     0.1599 | E_err:   0.006249
[2025-10-02 16:25:20] [Iter 2227/2250] R4[2202/2400] | LR: 0.005417 | E: -27.118785 | E_var:     0.1924 | E_err:   0.006854
[2025-10-02 16:25:23] [Iter 2228/2250] R4[2204/2400] | LR: 0.005409 | E: -27.125274 | E_var:     0.1480 | E_err:   0.006011
[2025-10-02 16:25:25] [Iter 2229/2250] R4[2206/2400] | LR: 0.005401 | E: -27.126638 | E_var:     0.2048 | E_err:   0.007071
[2025-10-02 16:25:27] [Iter 2230/2250] R4[2208/2400] | LR: 0.005393 | E: -27.135530 | E_var:     0.1643 | E_err:   0.006333
[2025-10-02 16:25:30] [Iter 2231/2250] R4[2210/2400] | LR: 0.005385 | E: -27.126517 | E_var:     0.2190 | E_err:   0.007313
[2025-10-02 16:25:32] [Iter 2232/2250] R4[2212/2400] | LR: 0.005377 | E: -27.128533 | E_var:     0.1967 | E_err:   0.006930
[2025-10-02 16:25:35] [Iter 2233/2250] R4[2214/2400] | LR: 0.005369 | E: -27.125714 | E_var:     0.1901 | E_err:   0.006812
[2025-10-02 16:25:37] [Iter 2234/2250] R4[2216/2400] | LR: 0.005361 | E: -27.129825 | E_var:     0.1763 | E_err:   0.006560
[2025-10-02 16:25:40] [Iter 2235/2250] R4[2218/2400] | LR: 0.005353 | E: -27.121652 | E_var:     0.1804 | E_err:   0.006636
[2025-10-02 16:25:42] [Iter 2236/2250] R4[2220/2400] | LR: 0.005345 | E: -27.129355 | E_var:     0.1976 | E_err:   0.006945
[2025-10-02 16:25:44] [Iter 2237/2250] R4[2222/2400] | LR: 0.005338 | E: -27.139090 | E_var:     0.1809 | E_err:   0.006645
[2025-10-02 16:25:47] [Iter 2238/2250] R4[2224/2400] | LR: 0.005330 | E: -27.131963 | E_var:     0.2510 | E_err:   0.007829
[2025-10-02 16:25:49] [Iter 2239/2250] R4[2226/2400] | LR: 0.005323 | E: -27.119277 | E_var:     0.1807 | E_err:   0.006642
[2025-10-02 16:25:52] [Iter 2240/2250] R4[2228/2400] | LR: 0.005315 | E: -27.122631 | E_var:     0.2063 | E_err:   0.007096
[2025-10-02 16:25:54] [Iter 2241/2250] R4[2230/2400] | LR: 0.005308 | E: -27.121285 | E_var:     0.1959 | E_err:   0.006916
[2025-10-02 16:25:56] [Iter 2242/2250] R4[2232/2400] | LR: 0.005301 | E: -27.126295 | E_var:     0.1876 | E_err:   0.006767
[2025-10-02 16:25:59] [Iter 2243/2250] R4[2234/2400] | LR: 0.005294 | E: -27.130734 | E_var:     0.2334 | E_err:   0.007548
[2025-10-02 16:26:01] [Iter 2244/2250] R4[2236/2400] | LR: 0.005287 | E: -27.136856 | E_var:     0.1637 | E_err:   0.006321
[2025-10-02 16:26:04] [Iter 2245/2250] R4[2238/2400] | LR: 0.005280 | E: -27.121622 | E_var:     0.1709 | E_err:   0.006459
[2025-10-02 16:26:06] [Iter 2246/2250] R4[2240/2400] | LR: 0.005273 | E: -27.123723 | E_var:     0.1819 | E_err:   0.006665
[2025-10-02 16:26:08] [Iter 2247/2250] R4[2242/2400] | LR: 0.005266 | E: -27.132000 | E_var:     0.2039 | E_err:   0.007056
[2025-10-02 16:26:11] [Iter 2248/2250] R4[2244/2400] | LR: 0.005260 | E: -27.119910 | E_var:     0.1989 | E_err:   0.006968
[2025-10-02 16:26:13] [Iter 2249/2250] R4[2246/2400] | LR: 0.005253 | E: -27.125992 | E_var:     0.2251 | E_err:   0.007414
[2025-10-02 16:26:16] [Iter 2250/2250] R4[2248/2400] | LR: 0.005247 | E: -27.122373 | E_var:     0.1571 | E_err:   0.006194
[2025-10-02 16:26:16] ================================================================================
[2025-10-02 16:26:16] ✅ Training completed successfully
[2025-10-02 16:26:16] Total restarts: 4
[2025-10-02 16:26:16] Final Energy: -27.12237299 ± 0.00619395
[2025-10-02 16:26:16] Final Variance: 0.157143
[2025-10-02 16:26:16] ================================================================================
[2025-10-02 16:26:16] ============================================================
[2025-10-02 16:26:16] Training completed | Runtime: 5449.1s
[2025-10-02 16:26:17] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-02 16:26:17] ============================================================
