[2025-10-06 00:59:30] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.77/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 00:59:30]   - 迭代次数: final
[2025-10-06 00:59:30]   - 能量: -27.531005+0.000478j ± 0.006471, Var: 0.171516
[2025-10-06 00:59:30]   - 时间戳: 2025-10-06T00:59:16.757472+08:00
[2025-10-06 00:59:45] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 00:59:45] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 00:59:45] ======================================================================================================
[2025-10-06 00:59:45] GCNN for Shastry-Sutherland Model
[2025-10-06 00:59:45] ======================================================================================================
[2025-10-06 00:59:45] System parameters:
[2025-10-06 00:59:45]   - System size: L=4, N=64
[2025-10-06 00:59:45]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-10-06 00:59:45] ------------------------------------------------------------------------------------------------------
[2025-10-06 00:59:45] Model parameters:
[2025-10-06 00:59:45]   - Number of layers = 4
[2025-10-06 00:59:45]   - Number of features = 4
[2025-10-06 00:59:45]   - Total parameters = 12572
[2025-10-06 00:59:45] ------------------------------------------------------------------------------------------------------
[2025-10-06 00:59:45] Training parameters:
[2025-10-06 00:59:45]   - Total iterations: 1050
[2025-10-06 00:59:45]   - Annealing cycles: 3
[2025-10-06 00:59:45]   - Initial period: 150
[2025-10-06 00:59:45]   - Period multiplier: 2.0
[2025-10-06 00:59:45]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 00:59:45]   - Samples: 4096
[2025-10-06 00:59:45]   - Discarded samples: 0
[2025-10-06 00:59:45]   - Chunk size: 4096
[2025-10-06 00:59:45]   - Diagonal shift: 0.15
[2025-10-06 00:59:45]   - Gradient clipping: 1.0
[2025-10-06 00:59:45]   - Checkpoint enabled: interval=100
[2025-10-06 00:59:45]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.78/model_L4F4/training/checkpoints
[2025-10-06 00:59:45] ------------------------------------------------------------------------------------------------------
[2025-10-06 00:59:45] Device status:
[2025-10-06 00:59:45]   - Devices model: NVIDIA H200 NVL
[2025-10-06 00:59:45]   - Number of devices: 1
[2025-10-06 00:59:45]   - Sharding: True
[2025-10-06 00:59:45] ======================================================================================================
[2025-10-06 01:00:19] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -27.943484 | E_var:     0.3574 | E_err:   0.009341
[2025-10-06 01:00:39] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -27.946692 | E_var:     0.2319 | E_err:   0.007525
[2025-10-06 01:00:41] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -27.939864 | E_var:     0.1810 | E_err:   0.006647
[2025-10-06 01:00:43] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -27.944286 | E_var:     0.1996 | E_err:   0.006981
[2025-10-06 01:00:46] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -27.946090 | E_var:     0.1707 | E_err:   0.006456
[2025-10-06 01:00:48] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -27.942115 | E_var:     0.1884 | E_err:   0.006782
[2025-10-06 01:00:51] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -27.947044 | E_var:     0.1546 | E_err:   0.006144
[2025-10-06 01:00:53] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -27.938935 | E_var:     0.1633 | E_err:   0.006315
[2025-10-06 01:00:56] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -27.948436 | E_var:     0.1379 | E_err:   0.005803
[2025-10-06 01:00:58] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -27.950003 | E_var:     0.1735 | E_err:   0.006509
[2025-10-06 01:01:00] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -27.960103 | E_var:     0.1708 | E_err:   0.006457
[2025-10-06 01:01:03] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -27.932566 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 01:01:05] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -27.941325 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 01:01:08] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -27.950615 | E_var:     0.1433 | E_err:   0.005915
[2025-10-06 01:01:10] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -27.942524 | E_var:     0.1656 | E_err:   0.006358
[2025-10-06 01:01:13] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -27.951477 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 01:01:15] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -27.945287 | E_var:     0.1288 | E_err:   0.005607
[2025-10-06 01:01:17] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -27.941569 | E_var:     0.1406 | E_err:   0.005860
[2025-10-06 01:01:20] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -27.951068 | E_var:     0.1458 | E_err:   0.005965
[2025-10-06 01:01:22] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -27.948239 | E_var:     0.2412 | E_err:   0.007674
[2025-10-06 01:01:25] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -27.937521 | E_var:     0.1388 | E_err:   0.005822
[2025-10-06 01:01:27] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -27.934495 | E_var:     0.1920 | E_err:   0.006846
[2025-10-06 01:01:30] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -27.953355 | E_var:     0.1479 | E_err:   0.006009
[2025-10-06 01:01:32] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -27.943004 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 01:01:34] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -27.946743 | E_var:     0.2721 | E_err:   0.008151
[2025-10-06 01:01:37] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -27.950572 | E_var:     0.1660 | E_err:   0.006367
[2025-10-06 01:01:39] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -27.936733 | E_var:     0.1637 | E_err:   0.006322
[2025-10-06 01:01:42] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -27.943427 | E_var:     0.1598 | E_err:   0.006247
[2025-10-06 01:01:44] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -27.952179 | E_var:     0.1869 | E_err:   0.006755
[2025-10-06 01:01:47] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -27.951599 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 01:01:49] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -27.950593 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 01:01:51] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -27.950277 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 01:01:54] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -27.947311 | E_var:     0.1265 | E_err:   0.005558
[2025-10-06 01:01:56] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -27.946639 | E_var:     0.1330 | E_err:   0.005699
[2025-10-06 01:01:59] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -27.942475 | E_var:     0.2923 | E_err:   0.008448
[2025-10-06 01:02:01] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -27.946625 | E_var:     0.1254 | E_err:   0.005533
[2025-10-06 01:02:04] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -27.972706 | E_var:     0.2306 | E_err:   0.007503
[2025-10-06 01:02:06] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -27.953453 | E_var:     0.1386 | E_err:   0.005816
[2025-10-06 01:02:08] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -27.943299 | E_var:     0.1460 | E_err:   0.005969
[2025-10-06 01:02:11] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -27.956555 | E_var:     0.2020 | E_err:   0.007023
[2025-10-06 01:02:13] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -27.952649 | E_var:     0.1275 | E_err:   0.005580
[2025-10-06 01:02:16] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -27.951419 | E_var:     0.1378 | E_err:   0.005801
[2025-10-06 01:02:18] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -27.946740 | E_var:     0.1087 | E_err:   0.005151
[2025-10-06 01:02:21] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -27.948970 | E_var:     0.1174 | E_err:   0.005353
[2025-10-06 01:02:23] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -27.955038 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 01:02:25] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -27.936694 | E_var:     0.1367 | E_err:   0.005776
[2025-10-06 01:02:28] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -27.949355 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 01:02:30] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -27.953091 | E_var:     0.1207 | E_err:   0.005429
[2025-10-06 01:02:33] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -27.942288 | E_var:     0.1533 | E_err:   0.006119
[2025-10-06 01:02:35] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -27.941640 | E_var:     0.1216 | E_err:   0.005449
[2025-10-06 01:02:38] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -27.961700 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 01:02:40] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -27.948689 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 01:02:42] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -27.944857 | E_var:     0.1515 | E_err:   0.006082
[2025-10-06 01:02:45] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -27.952799 | E_var:     0.1388 | E_err:   0.005821
[2025-10-06 01:02:47] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -27.958215 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 01:02:50] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -27.955060 | E_var:     0.1699 | E_err:   0.006441
[2025-10-06 01:02:52] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -27.947132 | E_var:     0.1253 | E_err:   0.005530
[2025-10-06 01:02:55] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -27.940548 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 01:02:57] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -27.939423 | E_var:     0.1205 | E_err:   0.005424
[2025-10-06 01:02:59] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -27.944019 | E_var:     0.1216 | E_err:   0.005449
[2025-10-06 01:03:02] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -27.957740 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 01:03:04] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -27.938060 | E_var:     0.2019 | E_err:   0.007022
[2025-10-06 01:03:07] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -27.952093 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 01:03:09] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -27.944353 | E_var:     0.1540 | E_err:   0.006131
[2025-10-06 01:03:12] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -27.955660 | E_var:     0.1398 | E_err:   0.005842
[2025-10-06 01:03:14] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -27.935616 | E_var:     0.2286 | E_err:   0.007470
[2025-10-06 01:03:16] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -27.934894 | E_var:     0.2971 | E_err:   0.008516
[2025-10-06 01:03:19] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -27.949391 | E_var:     0.1361 | E_err:   0.005764
[2025-10-06 01:03:21] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -27.942764 | E_var:     0.1362 | E_err:   0.005767
[2025-10-06 01:03:24] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -27.955355 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 01:03:26] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -27.952682 | E_var:     0.1777 | E_err:   0.006587
[2025-10-06 01:03:29] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -27.946105 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 01:03:31] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -27.955475 | E_var:     0.1702 | E_err:   0.006446
[2025-10-06 01:03:33] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -27.949722 | E_var:     0.1639 | E_err:   0.006327
[2025-10-06 01:03:36] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -27.939787 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 01:03:38] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -27.944294 | E_var:     0.1292 | E_err:   0.005617
[2025-10-06 01:03:41] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -27.949422 | E_var:     0.1380 | E_err:   0.005804
[2025-10-06 01:03:43] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -27.939199 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 01:03:46] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -27.948728 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 01:03:48] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -27.946485 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 01:03:50] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -27.952988 | E_var:     0.1634 | E_err:   0.006317
[2025-10-06 01:03:53] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -27.942562 | E_var:     0.1620 | E_err:   0.006289
[2025-10-06 01:03:55] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -27.946534 | E_var:     0.1463 | E_err:   0.005976
[2025-10-06 01:03:58] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -27.945900 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 01:04:00] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -27.949033 | E_var:     0.1173 | E_err:   0.005350
[2025-10-06 01:04:03] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -27.948071 | E_var:     0.1150 | E_err:   0.005298
[2025-10-06 01:04:05] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -27.948061 | E_var:     0.1395 | E_err:   0.005837
[2025-10-06 01:04:07] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -27.948099 | E_var:     0.1570 | E_err:   0.006191
[2025-10-06 01:04:10] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -27.954760 | E_var:     0.1225 | E_err:   0.005468
[2025-10-06 01:04:12] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -27.947057 | E_var:     0.1290 | E_err:   0.005612
[2025-10-06 01:04:15] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -27.941789 | E_var:     0.1389 | E_err:   0.005823
[2025-10-06 01:04:17] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -27.941847 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 01:04:20] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -27.942641 | E_var:     0.1592 | E_err:   0.006235
[2025-10-06 01:04:22] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -27.950121 | E_var:     0.1122 | E_err:   0.005233
[2025-10-06 01:04:24] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -27.948599 | E_var:     0.1309 | E_err:   0.005654
[2025-10-06 01:04:27] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -27.945393 | E_var:     0.1255 | E_err:   0.005536
[2025-10-06 01:04:29] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -27.959653 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 01:04:32] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -27.947160 | E_var:     0.2086 | E_err:   0.007137
[2025-10-06 01:04:34] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -27.960917 | E_var:     0.1266 | E_err:   0.005559
[2025-10-06 01:04:37] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -27.945968 | E_var:     0.1408 | E_err:   0.005864
[2025-10-06 01:04:37] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 01:04:39] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -27.946445 | E_var:     0.2298 | E_err:   0.007491
[2025-10-06 01:04:41] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -27.938575 | E_var:     0.1450 | E_err:   0.005950
[2025-10-06 01:04:44] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -27.950311 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 01:04:46] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -27.945905 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 01:04:49] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -27.952367 | E_var:     0.1373 | E_err:   0.005790
[2025-10-06 01:04:51] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -27.954224 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 01:04:54] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -27.945982 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 01:04:56] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -27.944795 | E_var:     0.1164 | E_err:   0.005332
[2025-10-06 01:04:58] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -27.953516 | E_var:     0.1243 | E_err:   0.005510
[2025-10-06 01:05:01] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -27.957412 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 01:05:03] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -27.945751 | E_var:     0.1311 | E_err:   0.005657
[2025-10-06 01:05:06] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -27.938074 | E_var:     0.1979 | E_err:   0.006951
[2025-10-06 01:05:08] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -27.955906 | E_var:     0.1399 | E_err:   0.005844
[2025-10-06 01:05:11] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -27.940172 | E_var:     0.1193 | E_err:   0.005397
[2025-10-06 01:05:13] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -27.945773 | E_var:     0.1709 | E_err:   0.006459
[2025-10-06 01:05:15] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -27.948443 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 01:05:18] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -27.943627 | E_var:     0.1211 | E_err:   0.005438
[2025-10-06 01:05:20] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -27.952787 | E_var:     0.1396 | E_err:   0.005838
[2025-10-06 01:05:23] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -27.946718 | E_var:     0.1299 | E_err:   0.005631
[2025-10-06 01:05:25] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -27.943356 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 01:05:28] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -27.946566 | E_var:     0.1349 | E_err:   0.005739
[2025-10-06 01:05:30] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -27.943550 | E_var:     0.1568 | E_err:   0.006187
[2025-10-06 01:05:32] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -27.946943 | E_var:     0.1282 | E_err:   0.005596
[2025-10-06 01:05:35] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -27.942974 | E_var:     0.1295 | E_err:   0.005624
[2025-10-06 01:05:37] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -27.941300 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 01:05:40] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -27.946680 | E_var:     0.1365 | E_err:   0.005773
[2025-10-06 01:05:42] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -27.944762 | E_var:     0.1273 | E_err:   0.005574
[2025-10-06 01:05:44] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -27.941253 | E_var:     0.1769 | E_err:   0.006572
[2025-10-06 01:05:47] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -27.951848 | E_var:     0.1350 | E_err:   0.005740
[2025-10-06 01:05:49] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -27.939426 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 01:05:52] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -27.939870 | E_var:     0.1296 | E_err:   0.005626
[2025-10-06 01:05:54] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -27.951819 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 01:05:57] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -27.945193 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 01:05:59] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -27.953005 | E_var:     0.1479 | E_err:   0.006009
[2025-10-06 01:06:01] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -27.947811 | E_var:     0.1164 | E_err:   0.005331
[2025-10-06 01:06:04] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -27.955131 | E_var:     0.1198 | E_err:   0.005409
[2025-10-06 01:06:06] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -27.940708 | E_var:     0.1142 | E_err:   0.005279
[2025-10-06 01:06:09] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -27.943363 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 01:06:11] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -27.942466 | E_var:     0.1269 | E_err:   0.005565
[2025-10-06 01:06:14] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -27.947834 | E_var:     0.1378 | E_err:   0.005801
[2025-10-06 01:06:16] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -27.955468 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 01:06:18] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -27.948545 | E_var:     0.1161 | E_err:   0.005323
[2025-10-06 01:06:21] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -27.950676 | E_var:     0.1508 | E_err:   0.006068
[2025-10-06 01:06:23] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -27.943492 | E_var:     0.1251 | E_err:   0.005526
[2025-10-06 01:06:26] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -27.950263 | E_var:     0.1836 | E_err:   0.006695
[2025-10-06 01:06:28] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -27.947457 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 01:06:31] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -27.954665 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 01:06:33] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -27.954489 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 01:06:35] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -27.954403 | E_var:     0.1437 | E_err:   0.005923
[2025-10-06 01:06:38] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -27.943100 | E_var:     0.1207 | E_err:   0.005429
[2025-10-06 01:06:38] 🔄 RESTART #1 | Period: 300
[2025-10-06 01:06:40] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -27.946086 | E_var:     0.1687 | E_err:   0.006418
[2025-10-06 01:06:43] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -27.946878 | E_var:     0.1591 | E_err:   0.006233
[2025-10-06 01:06:45] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -27.949698 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 01:06:48] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -27.955522 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 01:06:50] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -27.942808 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 01:06:52] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -27.951191 | E_var:     0.1352 | E_err:   0.005745
[2025-10-06 01:06:55] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -27.949910 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 01:06:57] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -27.959207 | E_var:     0.1355 | E_err:   0.005752
[2025-10-06 01:07:00] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -27.951051 | E_var:     0.1343 | E_err:   0.005726
[2025-10-06 01:07:02] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -27.947309 | E_var:     0.1028 | E_err:   0.005011
[2025-10-06 01:07:05] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -27.952354 | E_var:     0.1402 | E_err:   0.005851
[2025-10-06 01:07:07] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -27.949091 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 01:07:09] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -27.944939 | E_var:     0.1259 | E_err:   0.005545
[2025-10-06 01:07:12] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -27.941921 | E_var:     0.1142 | E_err:   0.005281
[2025-10-06 01:07:14] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -27.947332 | E_var:     0.1367 | E_err:   0.005778
[2025-10-06 01:07:17] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -27.950689 | E_var:     0.1262 | E_err:   0.005552
[2025-10-06 01:07:19] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -27.951082 | E_var:     0.2169 | E_err:   0.007276
[2025-10-06 01:07:22] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -27.946556 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 01:07:24] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -27.944994 | E_var:     0.1160 | E_err:   0.005322
[2025-10-06 01:07:26] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -27.942993 | E_var:     0.1274 | E_err:   0.005576
[2025-10-06 01:07:29] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -27.942970 | E_var:     0.1126 | E_err:   0.005242
[2025-10-06 01:07:31] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -27.958982 | E_var:     0.3503 | E_err:   0.009248
[2025-10-06 01:07:34] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -27.950498 | E_var:     0.1336 | E_err:   0.005710
[2025-10-06 01:07:36] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -27.952940 | E_var:     0.1232 | E_err:   0.005484
[2025-10-06 01:07:39] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -27.956456 | E_var:     0.1361 | E_err:   0.005765
[2025-10-06 01:07:41] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -27.953143 | E_var:     0.1567 | E_err:   0.006186
[2025-10-06 01:07:43] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -27.943572 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 01:07:46] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -27.941950 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 01:07:48] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -27.945857 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 01:07:51] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -27.940287 | E_var:     0.1284 | E_err:   0.005599
[2025-10-06 01:07:53] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -27.949531 | E_var:     0.1534 | E_err:   0.006120
[2025-10-06 01:07:56] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -27.946311 | E_var:     0.1501 | E_err:   0.006055
[2025-10-06 01:07:58] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -27.949302 | E_var:     0.1590 | E_err:   0.006231
[2025-10-06 01:08:00] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -27.951353 | E_var:     0.1408 | E_err:   0.005863
[2025-10-06 01:08:03] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -27.943126 | E_var:     0.1226 | E_err:   0.005471
[2025-10-06 01:08:05] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -27.953898 | E_var:     0.1197 | E_err:   0.005405
[2025-10-06 01:08:08] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -27.950686 | E_var:     0.1383 | E_err:   0.005810
[2025-10-06 01:08:10] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -27.950831 | E_var:     0.1185 | E_err:   0.005379
[2025-10-06 01:08:13] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -27.937558 | E_var:     0.1478 | E_err:   0.006008
[2025-10-06 01:08:15] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -27.949471 | E_var:     0.1693 | E_err:   0.006430
[2025-10-06 01:08:17] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -27.939378 | E_var:     0.2051 | E_err:   0.007077
[2025-10-06 01:08:20] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -27.946841 | E_var:     0.1352 | E_err:   0.005745
[2025-10-06 01:08:22] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -27.946606 | E_var:     0.3463 | E_err:   0.009195
[2025-10-06 01:08:25] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -27.938084 | E_var:     0.1483 | E_err:   0.006017
[2025-10-06 01:08:27] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -27.954653 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 01:08:30] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -27.946445 | E_var:     0.1454 | E_err:   0.005959
[2025-10-06 01:08:32] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -27.945497 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 01:08:34] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -27.943696 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 01:08:37] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -27.948105 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 01:08:39] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -27.944034 | E_var:     0.1228 | E_err:   0.005476
[2025-10-06 01:08:39] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 01:08:42] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -27.949686 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 01:08:44] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -27.952595 | E_var:     0.1295 | E_err:   0.005622
[2025-10-06 01:08:47] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -27.950833 | E_var:     0.1492 | E_err:   0.006035
[2025-10-06 01:08:49] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -27.947486 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 01:08:51] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -27.957443 | E_var:     0.1869 | E_err:   0.006754
[2025-10-06 01:08:54] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -27.943999 | E_var:     0.1537 | E_err:   0.006126
[2025-10-06 01:08:56] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -27.946770 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 01:08:59] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -27.948405 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 01:09:01] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -27.949872 | E_var:     0.1732 | E_err:   0.006503
[2025-10-06 01:09:04] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -27.943429 | E_var:     0.1619 | E_err:   0.006287
[2025-10-06 01:09:06] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -27.952207 | E_var:     0.2137 | E_err:   0.007223
[2025-10-06 01:09:08] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -27.955852 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 01:09:11] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -27.951055 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 01:09:13] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -27.941023 | E_var:     0.1420 | E_err:   0.005888
[2025-10-06 01:09:16] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -27.949478 | E_var:     0.1302 | E_err:   0.005638
[2025-10-06 01:09:18] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -27.941316 | E_var:     0.1695 | E_err:   0.006433
[2025-10-06 01:09:21] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -27.956750 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 01:09:23] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -27.953427 | E_var:     0.1485 | E_err:   0.006022
[2025-10-06 01:09:25] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -27.944118 | E_var:     0.1198 | E_err:   0.005409
[2025-10-06 01:09:28] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -27.944594 | E_var:     0.1737 | E_err:   0.006513
[2025-10-06 01:09:30] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -27.946019 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 01:09:33] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -27.946256 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 01:09:35] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -27.941452 | E_var:     0.1238 | E_err:   0.005498
[2025-10-06 01:09:38] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -27.949284 | E_var:     0.1150 | E_err:   0.005299
[2025-10-06 01:09:40] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -27.940027 | E_var:     0.1985 | E_err:   0.006961
[2025-10-06 01:09:42] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -27.944938 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 01:09:45] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -27.946034 | E_var:     0.1772 | E_err:   0.006577
[2025-10-06 01:09:47] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -27.946114 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 01:09:50] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -27.957952 | E_var:     0.1316 | E_err:   0.005668
[2025-10-06 01:09:52] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -27.955527 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 01:09:55] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -27.942484 | E_var:     0.1285 | E_err:   0.005601
[2025-10-06 01:09:57] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -27.941658 | E_var:     0.1431 | E_err:   0.005910
[2025-10-06 01:09:59] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -27.951819 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 01:10:02] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -27.951191 | E_var:     0.1271 | E_err:   0.005570
[2025-10-06 01:10:04] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -27.937769 | E_var:     0.1302 | E_err:   0.005638
[2025-10-06 01:10:07] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -27.952384 | E_var:     0.1416 | E_err:   0.005880
[2025-10-06 01:10:09] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -27.945055 | E_var:     0.1440 | E_err:   0.005930
[2025-10-06 01:10:12] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -27.944871 | E_var:     0.1247 | E_err:   0.005517
[2025-10-06 01:10:14] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -27.950163 | E_var:     0.1290 | E_err:   0.005612
[2025-10-06 01:10:16] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -27.944653 | E_var:     0.1355 | E_err:   0.005752
[2025-10-06 01:10:19] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -27.952692 | E_var:     0.1378 | E_err:   0.005800
[2025-10-06 01:10:21] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -27.953340 | E_var:     0.1191 | E_err:   0.005393
[2025-10-06 01:10:24] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -27.956074 | E_var:     0.1310 | E_err:   0.005656
[2025-10-06 01:10:26] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -27.949908 | E_var:     0.1403 | E_err:   0.005852
[2025-10-06 01:10:29] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -27.951792 | E_var:     0.1214 | E_err:   0.005444
[2025-10-06 01:10:31] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -27.952789 | E_var:     0.2119 | E_err:   0.007193
[2025-10-06 01:10:33] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -27.948024 | E_var:     0.1792 | E_err:   0.006613
[2025-10-06 01:10:36] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -27.944484 | E_var:     0.1269 | E_err:   0.005567
[2025-10-06 01:10:38] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -27.944561 | E_var:     0.1248 | E_err:   0.005519
[2025-10-06 01:10:41] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -27.952225 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 01:10:43] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -27.942770 | E_var:     0.1265 | E_err:   0.005557
[2025-10-06 01:10:46] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -27.951574 | E_var:     0.1564 | E_err:   0.006180
[2025-10-06 01:10:48] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -27.948320 | E_var:     0.1437 | E_err:   0.005923
[2025-10-06 01:10:51] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -27.939598 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 01:10:53] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -27.950943 | E_var:     0.1302 | E_err:   0.005639
[2025-10-06 01:10:55] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -27.949335 | E_var:     0.1270 | E_err:   0.005568
[2025-10-06 01:10:58] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -27.938344 | E_var:     0.1760 | E_err:   0.006554
[2025-10-06 01:11:00] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -27.943563 | E_var:     0.1620 | E_err:   0.006288
[2025-10-06 01:11:03] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -27.945454 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 01:11:05] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -27.953568 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 01:11:08] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -27.949863 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 01:11:10] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -27.947994 | E_var:     0.1377 | E_err:   0.005799
[2025-10-06 01:11:12] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -27.949372 | E_var:     0.1366 | E_err:   0.005776
[2025-10-06 01:11:15] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -27.949231 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 01:11:17] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -27.940985 | E_var:     0.1376 | E_err:   0.005795
[2025-10-06 01:11:20] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -27.952595 | E_var:     0.1225 | E_err:   0.005470
[2025-10-06 01:11:22] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -27.950609 | E_var:     0.1699 | E_err:   0.006441
[2025-10-06 01:11:24] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -27.937223 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 01:11:27] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -27.959562 | E_var:     0.1470 | E_err:   0.005991
[2025-10-06 01:11:29] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -27.948094 | E_var:     0.3347 | E_err:   0.009040
[2025-10-06 01:11:32] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -27.949648 | E_var:     0.1452 | E_err:   0.005953
[2025-10-06 01:11:34] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -27.945393 | E_var:     0.1499 | E_err:   0.006049
[2025-10-06 01:11:37] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -27.942061 | E_var:     0.2127 | E_err:   0.007206
[2025-10-06 01:11:39] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -27.947599 | E_var:     0.1055 | E_err:   0.005076
[2025-10-06 01:11:41] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -27.945579 | E_var:     0.1441 | E_err:   0.005931
[2025-10-06 01:11:44] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -27.948171 | E_var:     0.1309 | E_err:   0.005654
[2025-10-06 01:11:46] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -27.951133 | E_var:     0.1321 | E_err:   0.005678
[2025-10-06 01:11:49] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -27.958663 | E_var:     0.1272 | E_err:   0.005574
[2025-10-06 01:11:51] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -27.942022 | E_var:     0.2034 | E_err:   0.007047
[2025-10-06 01:11:54] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -27.938538 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 01:11:56] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -27.947387 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 01:11:58] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -27.946112 | E_var:     0.1357 | E_err:   0.005756
[2025-10-06 01:12:01] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -27.944923 | E_var:     0.1551 | E_err:   0.006154
[2025-10-06 01:12:03] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -27.953637 | E_var:     0.1309 | E_err:   0.005652
[2025-10-06 01:12:06] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -27.937778 | E_var:     0.1720 | E_err:   0.006479
[2025-10-06 01:12:08] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -27.948132 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 01:12:11] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -27.947020 | E_var:     0.1179 | E_err:   0.005366
[2025-10-06 01:12:13] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -27.945990 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 01:12:15] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -27.953214 | E_var:     0.1493 | E_err:   0.006037
[2025-10-06 01:12:18] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -27.952634 | E_var:     0.4583 | E_err:   0.010578
[2025-10-06 01:12:20] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -27.948981 | E_var:     0.1624 | E_err:   0.006296
[2025-10-06 01:12:23] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -27.940802 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 01:12:25] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -27.936289 | E_var:     0.1311 | E_err:   0.005658
[2025-10-06 01:12:28] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -27.942486 | E_var:     0.1274 | E_err:   0.005578
[2025-10-06 01:12:30] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -27.947650 | E_var:     0.1372 | E_err:   0.005788
[2025-10-06 01:12:32] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -27.950966 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 01:12:35] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -27.941309 | E_var:     0.1514 | E_err:   0.006081
[2025-10-06 01:12:37] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -27.942303 | E_var:     0.1502 | E_err:   0.006055
[2025-10-06 01:12:40] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -27.955017 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 01:12:42] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -27.943898 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 01:12:42] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 01:12:45] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -27.947862 | E_var:     0.1350 | E_err:   0.005741
[2025-10-06 01:12:47] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -27.949333 | E_var:     0.1107 | E_err:   0.005200
[2025-10-06 01:12:49] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -27.945086 | E_var:     0.1264 | E_err:   0.005556
[2025-10-06 01:12:52] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -27.945541 | E_var:     0.1672 | E_err:   0.006390
[2025-10-06 01:12:54] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -27.942525 | E_var:     0.1576 | E_err:   0.006204
[2025-10-06 01:12:57] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -27.956712 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 01:12:59] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -27.944643 | E_var:     0.1062 | E_err:   0.005092
[2025-10-06 01:13:02] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -27.947374 | E_var:     0.1515 | E_err:   0.006081
[2025-10-06 01:13:04] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -27.949457 | E_var:     0.1383 | E_err:   0.005810
[2025-10-06 01:13:06] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -27.942976 | E_var:     0.1328 | E_err:   0.005694
[2025-10-06 01:13:09] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -27.945083 | E_var:     0.1069 | E_err:   0.005110
[2025-10-06 01:13:11] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -27.951129 | E_var:     0.1225 | E_err:   0.005469
[2025-10-06 01:13:14] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -27.949041 | E_var:     0.1578 | E_err:   0.006207
[2025-10-06 01:13:16] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -27.944683 | E_var:     0.1371 | E_err:   0.005786
[2025-10-06 01:13:18] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -27.947041 | E_var:     0.1405 | E_err:   0.005857
[2025-10-06 01:13:21] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -27.954370 | E_var:     0.1340 | E_err:   0.005719
[2025-10-06 01:13:23] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -27.945955 | E_var:     0.1391 | E_err:   0.005828
[2025-10-06 01:13:26] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -27.948808 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 01:13:28] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -27.953888 | E_var:     0.1476 | E_err:   0.006003
[2025-10-06 01:13:31] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -27.944666 | E_var:     0.1472 | E_err:   0.005994
[2025-10-06 01:13:33] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -27.953460 | E_var:     0.1110 | E_err:   0.005206
[2025-10-06 01:13:35] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -27.937175 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 01:13:38] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -27.957769 | E_var:     0.1209 | E_err:   0.005434
[2025-10-06 01:13:40] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -27.948608 | E_var:     0.1463 | E_err:   0.005976
[2025-10-06 01:13:43] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -27.949774 | E_var:     0.1786 | E_err:   0.006603
[2025-10-06 01:13:45] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -27.938938 | E_var:     0.1448 | E_err:   0.005946
[2025-10-06 01:13:48] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -27.955713 | E_var:     0.1216 | E_err:   0.005448
[2025-10-06 01:13:50] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -27.952990 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 01:13:52] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -27.951746 | E_var:     0.1727 | E_err:   0.006493
[2025-10-06 01:13:55] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -27.947596 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 01:13:57] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -27.944740 | E_var:     0.1205 | E_err:   0.005425
[2025-10-06 01:14:00] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -27.959096 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 01:14:02] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -27.944729 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 01:14:05] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -27.948136 | E_var:     0.1123 | E_err:   0.005235
[2025-10-06 01:14:07] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -27.944653 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 01:14:09] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -27.944939 | E_var:     0.1250 | E_err:   0.005523
[2025-10-06 01:14:12] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -27.952649 | E_var:     0.2242 | E_err:   0.007399
[2025-10-06 01:14:14] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -27.943198 | E_var:     0.1279 | E_err:   0.005589
[2025-10-06 01:14:17] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -27.951453 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 01:14:19] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -27.951977 | E_var:     0.1294 | E_err:   0.005620
[2025-10-06 01:14:22] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -27.947666 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 01:14:24] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -27.943813 | E_var:     0.1476 | E_err:   0.006002
[2025-10-06 01:14:26] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -27.945160 | E_var:     0.1361 | E_err:   0.005764
[2025-10-06 01:14:29] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -27.945127 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 01:14:31] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -27.954392 | E_var:     0.1732 | E_err:   0.006503
[2025-10-06 01:14:34] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -27.958215 | E_var:     0.1273 | E_err:   0.005576
[2025-10-06 01:14:36] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -27.949756 | E_var:     0.1330 | E_err:   0.005699
[2025-10-06 01:14:39] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -27.942987 | E_var:     0.2793 | E_err:   0.008257
[2025-10-06 01:14:41] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -27.954359 | E_var:     0.2184 | E_err:   0.007302
[2025-10-06 01:14:43] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -27.948431 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 01:14:46] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -27.952111 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 01:14:48] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -27.943787 | E_var:     0.1169 | E_err:   0.005343
[2025-10-06 01:14:51] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -27.951552 | E_var:     0.1226 | E_err:   0.005471
[2025-10-06 01:14:53] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -27.950872 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 01:14:56] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -27.937414 | E_var:     0.1230 | E_err:   0.005479
[2025-10-06 01:14:58] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -27.958526 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 01:15:00] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -27.940409 | E_var:     0.1483 | E_err:   0.006018
[2025-10-06 01:15:03] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -27.952680 | E_var:     0.1301 | E_err:   0.005635
[2025-10-06 01:15:05] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -27.935279 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 01:15:08] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -27.952791 | E_var:     0.1072 | E_err:   0.005116
[2025-10-06 01:15:10] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -27.955115 | E_var:     0.1638 | E_err:   0.006324
[2025-10-06 01:15:13] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -27.952952 | E_var:     0.1112 | E_err:   0.005211
[2025-10-06 01:15:15] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -27.937979 | E_var:     0.1187 | E_err:   0.005384
[2025-10-06 01:15:17] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -27.943569 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 01:15:20] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -27.947547 | E_var:     0.1247 | E_err:   0.005517
[2025-10-06 01:15:22] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -27.959850 | E_var:     0.1827 | E_err:   0.006679
[2025-10-06 01:15:25] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -27.946351 | E_var:     0.1379 | E_err:   0.005803
[2025-10-06 01:15:27] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -27.946816 | E_var:     0.1364 | E_err:   0.005771
[2025-10-06 01:15:30] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -27.953656 | E_var:     0.1220 | E_err:   0.005457
[2025-10-06 01:15:32] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -27.948820 | E_var:     0.1299 | E_err:   0.005630
[2025-10-06 01:15:34] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -27.940747 | E_var:     0.2565 | E_err:   0.007914
[2025-10-06 01:15:37] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -27.950250 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 01:15:39] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -27.959992 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 01:15:42] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -27.948856 | E_var:     0.1169 | E_err:   0.005341
[2025-10-06 01:15:44] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -27.937490 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 01:15:46] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -27.948583 | E_var:     0.1618 | E_err:   0.006286
[2025-10-06 01:15:49] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -27.955459 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 01:15:51] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -27.943649 | E_var:     0.1355 | E_err:   0.005751
[2025-10-06 01:15:54] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -27.951809 | E_var:     0.1439 | E_err:   0.005926
[2025-10-06 01:15:56] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -27.943578 | E_var:     0.1835 | E_err:   0.006693
[2025-10-06 01:15:59] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -27.956016 | E_var:     0.1244 | E_err:   0.005512
[2025-10-06 01:16:01] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -27.945089 | E_var:     0.1337 | E_err:   0.005712
[2025-10-06 01:16:03] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -27.953632 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 01:16:06] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -27.958286 | E_var:     0.1237 | E_err:   0.005495
[2025-10-06 01:16:08] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -27.943786 | E_var:     0.2054 | E_err:   0.007081
[2025-10-06 01:16:11] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -27.949367 | E_var:     0.1346 | E_err:   0.005733
[2025-10-06 01:16:13] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -27.938922 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 01:16:16] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -27.944086 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 01:16:18] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -27.946561 | E_var:     0.1766 | E_err:   0.006566
[2025-10-06 01:16:20] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -27.956189 | E_var:     0.1670 | E_err:   0.006386
[2025-10-06 01:16:23] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -27.944483 | E_var:     0.1550 | E_err:   0.006151
[2025-10-06 01:16:25] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -27.947828 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 01:16:28] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -27.957426 | E_var:     0.1403 | E_err:   0.005852
[2025-10-06 01:16:30] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -27.955032 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 01:16:33] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -27.947611 | E_var:     0.1306 | E_err:   0.005647
[2025-10-06 01:16:35] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -27.956948 | E_var:     0.1463 | E_err:   0.005977
[2025-10-06 01:16:37] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -27.949583 | E_var:     0.1584 | E_err:   0.006218
[2025-10-06 01:16:40] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -27.947100 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 01:16:42] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -27.948973 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 01:16:45] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -27.953253 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 01:16:45] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 01:16:47] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -27.949288 | E_var:     0.1346 | E_err:   0.005733
[2025-10-06 01:16:50] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -27.950217 | E_var:     0.1359 | E_err:   0.005759
[2025-10-06 01:16:52] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -27.947284 | E_var:     0.1393 | E_err:   0.005832
[2025-10-06 01:16:54] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -27.950641 | E_var:     0.1523 | E_err:   0.006099
[2025-10-06 01:16:57] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -27.944091 | E_var:     0.1218 | E_err:   0.005454
[2025-10-06 01:16:59] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -27.940533 | E_var:     0.1301 | E_err:   0.005635
[2025-10-06 01:17:02] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -27.939969 | E_var:     0.1462 | E_err:   0.005974
[2025-10-06 01:17:04] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -27.944629 | E_var:     0.1413 | E_err:   0.005874
[2025-10-06 01:17:07] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -27.953528 | E_var:     0.1492 | E_err:   0.006035
[2025-10-06 01:17:09] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -27.949217 | E_var:     0.1329 | E_err:   0.005697
[2025-10-06 01:17:11] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -27.945649 | E_var:     0.1276 | E_err:   0.005581
[2025-10-06 01:17:14] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -27.963553 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 01:17:16] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -27.951982 | E_var:     0.2027 | E_err:   0.007035
[2025-10-06 01:17:19] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -27.948000 | E_var:     0.1201 | E_err:   0.005415
[2025-10-06 01:17:21] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -27.950321 | E_var:     0.2004 | E_err:   0.006995
[2025-10-06 01:17:24] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -27.948917 | E_var:     0.1286 | E_err:   0.005603
[2025-10-06 01:17:26] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -27.950163 | E_var:     0.1113 | E_err:   0.005214
[2025-10-06 01:17:28] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -27.939685 | E_var:     0.1353 | E_err:   0.005748
[2025-10-06 01:17:31] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -27.954018 | E_var:     0.1343 | E_err:   0.005727
[2025-10-06 01:17:33] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -27.940810 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 01:17:36] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -27.945463 | E_var:     0.1072 | E_err:   0.005117
[2025-10-06 01:17:38] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -27.943727 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 01:17:41] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -27.961150 | E_var:     0.1415 | E_err:   0.005878
[2025-10-06 01:17:43] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -27.940022 | E_var:     0.1455 | E_err:   0.005961
[2025-10-06 01:17:45] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -27.952643 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 01:17:48] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -27.946509 | E_var:     0.1981 | E_err:   0.006955
[2025-10-06 01:17:50] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -27.950740 | E_var:     0.1456 | E_err:   0.005962
[2025-10-06 01:17:53] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -27.948080 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 01:17:55] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -27.945381 | E_var:     0.1295 | E_err:   0.005624
[2025-10-06 01:17:58] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -27.946672 | E_var:     0.1126 | E_err:   0.005244
[2025-10-06 01:18:00] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -27.956952 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 01:18:02] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -27.943598 | E_var:     0.1325 | E_err:   0.005688
[2025-10-06 01:18:05] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -27.958311 | E_var:     0.1099 | E_err:   0.005179
[2025-10-06 01:18:07] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -27.943389 | E_var:     0.1334 | E_err:   0.005707
[2025-10-06 01:18:10] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -27.942746 | E_var:     0.1651 | E_err:   0.006348
[2025-10-06 01:18:12] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -27.950161 | E_var:     0.1218 | E_err:   0.005452
[2025-10-06 01:18:15] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -27.944801 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 01:18:17] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -27.952572 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 01:18:19] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -27.956815 | E_var:     0.3413 | E_err:   0.009129
[2025-10-06 01:18:22] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -27.944612 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 01:18:24] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -27.947189 | E_var:     0.1228 | E_err:   0.005475
[2025-10-06 01:18:27] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -27.950168 | E_var:     0.1374 | E_err:   0.005791
[2025-10-06 01:18:29] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -27.947886 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 01:18:32] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -27.948660 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 01:18:34] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -27.959712 | E_var:     0.1175 | E_err:   0.005357
[2025-10-06 01:18:36] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -27.941677 | E_var:     0.1278 | E_err:   0.005585
[2025-10-06 01:18:39] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -27.941513 | E_var:     0.1233 | E_err:   0.005487
[2025-10-06 01:18:41] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -27.943755 | E_var:     0.1147 | E_err:   0.005293
[2025-10-06 01:18:44] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -27.946594 | E_var:     0.1276 | E_err:   0.005582
[2025-10-06 01:18:46] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -27.951562 | E_var:     0.1266 | E_err:   0.005561
[2025-10-06 01:18:46] 🔄 RESTART #2 | Period: 600
[2025-10-06 01:18:49] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -27.954877 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 01:18:51] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -27.964007 | E_var:     0.1178 | E_err:   0.005362
[2025-10-06 01:18:53] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -27.947503 | E_var:     0.1193 | E_err:   0.005396
[2025-10-06 01:18:56] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -27.952535 | E_var:     0.1198 | E_err:   0.005407
[2025-10-06 01:18:58] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -27.942292 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 01:19:01] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -27.954139 | E_var:     0.1252 | E_err:   0.005528
[2025-10-06 01:19:03] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -27.948373 | E_var:     0.1059 | E_err:   0.005086
[2025-10-06 01:19:06] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -27.964849 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 01:19:08] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -27.944111 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 01:19:10] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -27.953057 | E_var:     0.1728 | E_err:   0.006495
[2025-10-06 01:19:13] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -27.943216 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 01:19:15] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -27.966037 | E_var:     0.1358 | E_err:   0.005757
[2025-10-06 01:19:18] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -27.951102 | E_var:     0.1342 | E_err:   0.005724
[2025-10-06 01:19:20] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -27.946711 | E_var:     0.1438 | E_err:   0.005926
[2025-10-06 01:19:23] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -27.949378 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 01:19:25] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -27.950874 | E_var:     0.1692 | E_err:   0.006428
[2025-10-06 01:19:27] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -27.952889 | E_var:     0.1314 | E_err:   0.005665
[2025-10-06 01:19:30] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -27.938073 | E_var:     0.1349 | E_err:   0.005739
[2025-10-06 01:19:32] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -27.953065 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 01:19:35] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -27.948505 | E_var:     0.1094 | E_err:   0.005167
[2025-10-06 01:19:37] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -27.950178 | E_var:     0.1557 | E_err:   0.006165
[2025-10-06 01:19:40] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -27.947555 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 01:19:42] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -27.953178 | E_var:     0.1175 | E_err:   0.005355
[2025-10-06 01:19:45] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -27.946613 | E_var:     0.1422 | E_err:   0.005892
[2025-10-06 01:19:47] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -27.949901 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 01:19:49] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -27.930909 | E_var:     0.9322 | E_err:   0.015086
[2025-10-06 01:19:52] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -27.945581 | E_var:     0.1926 | E_err:   0.006858
[2025-10-06 01:19:54] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -27.945044 | E_var:     0.1348 | E_err:   0.005737
[2025-10-06 01:19:57] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -27.949154 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 01:19:59] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -27.945251 | E_var:     0.1898 | E_err:   0.006807
[2025-10-06 01:20:02] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -27.948686 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 01:20:04] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -27.946905 | E_var:     0.1219 | E_err:   0.005456
[2025-10-06 01:20:06] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -27.955026 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 01:20:09] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -27.948631 | E_var:     0.1358 | E_err:   0.005759
[2025-10-06 01:20:11] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -27.937959 | E_var:     0.1356 | E_err:   0.005754
[2025-10-06 01:20:14] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -27.941915 | E_var:     0.1195 | E_err:   0.005400
[2025-10-06 01:20:16] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -27.950510 | E_var:     0.1309 | E_err:   0.005654
[2025-10-06 01:20:19] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -27.948082 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 01:20:21] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -27.948273 | E_var:     0.1475 | E_err:   0.006000
[2025-10-06 01:20:23] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -27.954440 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 01:20:26] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -27.949271 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 01:20:28] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -27.939644 | E_var:     0.1273 | E_err:   0.005575
[2025-10-06 01:20:31] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -27.939763 | E_var:     0.1467 | E_err:   0.005985
[2025-10-06 01:20:33] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -27.950625 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 01:20:36] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -27.946214 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 01:20:38] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -27.952421 | E_var:     0.0997 | E_err:   0.004933
[2025-10-06 01:20:40] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -27.943399 | E_var:     0.1080 | E_err:   0.005136
[2025-10-06 01:20:43] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -27.948838 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 01:20:45] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -27.949643 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 01:20:48] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -27.955036 | E_var:     0.1447 | E_err:   0.005944
[2025-10-06 01:20:48] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 01:20:50] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -27.947877 | E_var:     0.1298 | E_err:   0.005630
[2025-10-06 01:20:53] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -27.946149 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 01:20:55] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -27.953570 | E_var:     0.1212 | E_err:   0.005439
[2025-10-06 01:20:57] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -27.955662 | E_var:     0.1195 | E_err:   0.005401
[2025-10-06 01:21:00] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -27.952090 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 01:21:02] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -27.951783 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 01:21:05] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -27.946174 | E_var:     0.1330 | E_err:   0.005698
[2025-10-06 01:21:07] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -27.947040 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 01:21:10] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -27.943352 | E_var:     0.1429 | E_err:   0.005906
[2025-10-06 01:21:12] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -27.949263 | E_var:     0.1643 | E_err:   0.006334
[2025-10-06 01:21:14] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -27.957269 | E_var:     0.1474 | E_err:   0.005998
[2025-10-06 01:21:17] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -27.958575 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 01:21:19] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -27.949445 | E_var:     0.1411 | E_err:   0.005868
[2025-10-06 01:21:22] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -27.947259 | E_var:     0.1373 | E_err:   0.005789
[2025-10-06 01:21:24] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -27.942593 | E_var:     0.1372 | E_err:   0.005787
[2025-10-06 01:21:27] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -27.948734 | E_var:     0.1376 | E_err:   0.005797
[2025-10-06 01:21:29] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -27.953222 | E_var:     0.1318 | E_err:   0.005672
[2025-10-06 01:21:31] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -27.945825 | E_var:     0.1536 | E_err:   0.006123
[2025-10-06 01:21:34] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -27.948359 | E_var:     0.1392 | E_err:   0.005829
[2025-10-06 01:21:36] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -27.954955 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 01:21:39] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -27.945191 | E_var:     0.1985 | E_err:   0.006961
[2025-10-06 01:21:41] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -27.940124 | E_var:     0.3490 | E_err:   0.009230
[2025-10-06 01:21:44] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -27.954674 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 01:21:46] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -27.948310 | E_var:     0.1061 | E_err:   0.005091
[2025-10-06 01:21:48] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -27.954874 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 01:21:51] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -27.950892 | E_var:     0.0955 | E_err:   0.004828
[2025-10-06 01:21:53] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -27.953633 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 01:21:56] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -27.958098 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 01:21:58] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -27.944701 | E_var:     0.1572 | E_err:   0.006195
[2025-10-06 01:22:00] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -27.934219 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 01:22:03] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -27.934929 | E_var:     0.1091 | E_err:   0.005162
[2025-10-06 01:22:05] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -27.947651 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 01:22:08] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -27.951808 | E_var:     0.1115 | E_err:   0.005216
[2025-10-06 01:22:10] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -27.962612 | E_var:     0.1274 | E_err:   0.005578
[2025-10-06 01:22:13] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -27.946712 | E_var:     0.1311 | E_err:   0.005658
[2025-10-06 01:22:15] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -27.952162 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 01:22:17] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -27.942894 | E_var:     0.1121 | E_err:   0.005232
[2025-10-06 01:22:20] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -27.945749 | E_var:     0.1497 | E_err:   0.006046
[2025-10-06 01:22:22] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -27.952173 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 01:22:25] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -27.941842 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 01:22:27] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -27.959133 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 01:22:30] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -27.953430 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 01:22:32] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -27.950180 | E_var:     0.1266 | E_err:   0.005560
[2025-10-06 01:22:34] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -27.941332 | E_var:     0.1212 | E_err:   0.005439
[2025-10-06 01:22:37] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -27.947136 | E_var:     0.1336 | E_err:   0.005711
[2025-10-06 01:22:39] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -27.944649 | E_var:     0.1261 | E_err:   0.005549
[2025-10-06 01:22:42] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -27.943693 | E_var:     0.1267 | E_err:   0.005561
[2025-10-06 01:22:44] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -27.941215 | E_var:     0.1258 | E_err:   0.005541
[2025-10-06 01:22:47] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -27.954386 | E_var:     0.1190 | E_err:   0.005389
[2025-10-06 01:22:49] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -27.946598 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 01:22:51] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -27.938303 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 01:22:54] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -27.952657 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 01:22:56] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -27.956170 | E_var:     0.1126 | E_err:   0.005244
[2025-10-06 01:22:59] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -27.952037 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 01:23:01] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -27.943922 | E_var:     0.1314 | E_err:   0.005664
[2025-10-06 01:23:04] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -27.958971 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 01:23:06] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -27.941441 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 01:23:08] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -27.945273 | E_var:     0.1142 | E_err:   0.005279
[2025-10-06 01:23:11] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -27.955547 | E_var:     0.1218 | E_err:   0.005452
[2025-10-06 01:23:13] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -27.940355 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 01:23:16] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -27.945390 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 01:23:18] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -27.939247 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 01:23:21] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -27.939462 | E_var:     0.1887 | E_err:   0.006788
[2025-10-06 01:23:23] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -27.955195 | E_var:     0.1243 | E_err:   0.005509
[2025-10-06 01:23:25] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -27.946298 | E_var:     0.1167 | E_err:   0.005337
[2025-10-06 01:23:28] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -27.947800 | E_var:     0.1517 | E_err:   0.006085
[2025-10-06 01:23:30] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -27.940629 | E_var:     0.1518 | E_err:   0.006088
[2025-10-06 01:23:33] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -27.958578 | E_var:     0.1283 | E_err:   0.005598
[2025-10-06 01:23:35] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -27.951037 | E_var:     0.1405 | E_err:   0.005856
[2025-10-06 01:23:38] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -27.949114 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 01:23:40] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -27.948657 | E_var:     0.1846 | E_err:   0.006713
[2025-10-06 01:23:42] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -27.943125 | E_var:     0.1406 | E_err:   0.005858
[2025-10-06 01:23:45] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -27.955393 | E_var:     0.1198 | E_err:   0.005407
[2025-10-06 01:23:47] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -27.944411 | E_var:     0.1013 | E_err:   0.004973
[2025-10-06 01:23:50] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -27.944556 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 01:23:52] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -27.951926 | E_var:     0.5239 | E_err:   0.011309
[2025-10-06 01:23:55] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -27.950844 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 01:23:57] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -27.961060 | E_var:     0.2274 | E_err:   0.007452
[2025-10-06 01:23:59] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -27.942710 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 01:24:02] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -27.959393 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 01:24:04] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -27.949500 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 01:24:07] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -27.957158 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 01:24:09] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -27.951293 | E_var:     0.1473 | E_err:   0.005997
[2025-10-06 01:24:12] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -27.954577 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 01:24:14] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -27.959047 | E_var:     0.1226 | E_err:   0.005471
[2025-10-06 01:24:17] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -27.948463 | E_var:     0.1235 | E_err:   0.005492
[2025-10-06 01:24:19] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -27.934555 | E_var:     0.1880 | E_err:   0.006775
[2025-10-06 01:24:22] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -27.945053 | E_var:     0.1215 | E_err:   0.005445
[2025-10-06 01:24:24] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -27.936358 | E_var:     0.1423 | E_err:   0.005894
[2025-10-06 01:24:27] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -27.946093 | E_var:     0.1197 | E_err:   0.005407
[2025-10-06 01:24:29] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -27.949853 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 01:24:31] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -27.961599 | E_var:     0.1419 | E_err:   0.005887
[2025-10-06 01:24:34] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -27.945748 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 01:24:36] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -27.958672 | E_var:     0.1191 | E_err:   0.005392
[2025-10-06 01:24:39] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -27.949669 | E_var:     0.1081 | E_err:   0.005137
[2025-10-06 01:24:41] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -27.942450 | E_var:     0.1341 | E_err:   0.005722
[2025-10-06 01:24:44] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -27.949498 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 01:24:46] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -27.947058 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 01:24:48] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -27.939029 | E_var:     0.1176 | E_err:   0.005359
[2025-10-06 01:24:51] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -27.949208 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 01:24:51] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 01:24:53] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -27.949103 | E_var:     0.1388 | E_err:   0.005822
[2025-10-06 01:24:56] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -27.944351 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 01:24:58] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -27.941698 | E_var:     0.1480 | E_err:   0.006012
[2025-10-06 01:25:00] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -27.939895 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 01:25:03] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -27.959137 | E_var:     0.1481 | E_err:   0.006013
[2025-10-06 01:25:05] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -27.945168 | E_var:     0.2061 | E_err:   0.007094
[2025-10-06 01:25:08] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -27.947201 | E_var:     0.1211 | E_err:   0.005436
[2025-10-06 01:25:10] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -27.949817 | E_var:     0.1189 | E_err:   0.005387
[2025-10-06 01:25:13] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -27.948225 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 01:25:15] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -27.953283 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 01:25:17] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -27.951950 | E_var:     0.1685 | E_err:   0.006414
[2025-10-06 01:25:20] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -27.947170 | E_var:     0.1547 | E_err:   0.006145
[2025-10-06 01:25:22] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -27.953378 | E_var:     0.1129 | E_err:   0.005250
[2025-10-06 01:25:25] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -27.950598 | E_var:     0.1288 | E_err:   0.005607
[2025-10-06 01:25:27] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -27.943052 | E_var:     0.1585 | E_err:   0.006220
[2025-10-06 01:25:30] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -27.945093 | E_var:     0.1576 | E_err:   0.006203
[2025-10-06 01:25:32] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -27.945323 | E_var:     0.1535 | E_err:   0.006122
[2025-10-06 01:25:34] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -27.941643 | E_var:     0.1276 | E_err:   0.005582
[2025-10-06 01:25:37] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -27.946579 | E_var:     0.0933 | E_err:   0.004773
[2025-10-06 01:25:39] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -27.960527 | E_var:     0.1081 | E_err:   0.005137
[2025-10-06 01:25:42] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -27.937925 | E_var:     0.1422 | E_err:   0.005891
[2025-10-06 01:25:44] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -27.940155 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 01:25:47] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -27.948558 | E_var:     0.1122 | E_err:   0.005234
[2025-10-06 01:25:49] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -27.958463 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 01:25:52] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -27.943145 | E_var:     0.1147 | E_err:   0.005292
[2025-10-06 01:25:54] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -27.942860 | E_var:     0.1157 | E_err:   0.005314
[2025-10-06 01:25:56] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -27.954303 | E_var:     0.5169 | E_err:   0.011233
[2025-10-06 01:25:59] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -27.938204 | E_var:     0.1343 | E_err:   0.005727
[2025-10-06 01:26:01] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -27.952368 | E_var:     0.2050 | E_err:   0.007075
[2025-10-06 01:26:04] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -27.948876 | E_var:     0.1197 | E_err:   0.005406
[2025-10-06 01:26:06] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -27.950710 | E_var:     0.1163 | E_err:   0.005329
[2025-10-06 01:26:09] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -27.953498 | E_var:     0.1498 | E_err:   0.006048
[2025-10-06 01:26:11] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -27.949251 | E_var:     0.2117 | E_err:   0.007188
[2025-10-06 01:26:13] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -27.956770 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 01:26:16] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -27.953194 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 01:26:18] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -27.944604 | E_var:     0.1274 | E_err:   0.005578
[2025-10-06 01:26:21] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -27.940222 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 01:26:23] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -27.958915 | E_var:     0.1457 | E_err:   0.005965
[2025-10-06 01:26:26] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -27.943781 | E_var:     0.1256 | E_err:   0.005537
[2025-10-06 01:26:28] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -27.942257 | E_var:     0.1504 | E_err:   0.006059
[2025-10-06 01:26:30] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -27.948283 | E_var:     0.1104 | E_err:   0.005192
[2025-10-06 01:26:33] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -27.948748 | E_var:     0.1277 | E_err:   0.005583
[2025-10-06 01:26:35] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -27.957253 | E_var:     0.1547 | E_err:   0.006145
[2025-10-06 01:26:38] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -27.949711 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 01:26:40] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -27.929545 | E_var:     0.1709 | E_err:   0.006459
[2025-10-06 01:26:43] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -27.944841 | E_var:     0.1230 | E_err:   0.005481
[2025-10-06 01:26:45] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -27.948083 | E_var:     0.1033 | E_err:   0.005023
[2025-10-06 01:26:47] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -27.948945 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 01:26:50] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -27.948436 | E_var:     0.1444 | E_err:   0.005937
[2025-10-06 01:26:52] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -27.949041 | E_var:     0.1620 | E_err:   0.006288
[2025-10-06 01:26:55] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -27.949319 | E_var:     0.1289 | E_err:   0.005609
[2025-10-06 01:26:57] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -27.944505 | E_var:     0.1949 | E_err:   0.006899
[2025-10-06 01:26:59] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -27.955249 | E_var:     0.1188 | E_err:   0.005385
[2025-10-06 01:27:02] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -27.951475 | E_var:     0.2098 | E_err:   0.007157
[2025-10-06 01:27:04] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -27.948842 | E_var:     0.1114 | E_err:   0.005215
[2025-10-06 01:27:07] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -27.944957 | E_var:     0.1399 | E_err:   0.005844
[2025-10-06 01:27:09] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -27.957382 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 01:27:12] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -27.941724 | E_var:     0.1249 | E_err:   0.005521
[2025-10-06 01:27:14] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -27.960285 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 01:27:17] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -27.941155 | E_var:     0.1416 | E_err:   0.005879
[2025-10-06 01:27:19] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -27.943363 | E_var:     0.1380 | E_err:   0.005804
[2025-10-06 01:27:21] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -27.949334 | E_var:     0.1265 | E_err:   0.005556
[2025-10-06 01:27:24] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -27.947894 | E_var:     0.1088 | E_err:   0.005154
[2025-10-06 01:27:26] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -27.941789 | E_var:     0.1401 | E_err:   0.005849
[2025-10-06 01:27:29] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -27.954951 | E_var:     0.1416 | E_err:   0.005881
[2025-10-06 01:27:31] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -27.949462 | E_var:     0.1397 | E_err:   0.005841
[2025-10-06 01:27:33] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -27.950288 | E_var:     0.2031 | E_err:   0.007042
[2025-10-06 01:27:36] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -27.957857 | E_var:     0.1591 | E_err:   0.006233
[2025-10-06 01:27:38] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -27.948485 | E_var:     0.1349 | E_err:   0.005738
[2025-10-06 01:27:41] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -27.959556 | E_var:     0.1927 | E_err:   0.006858
[2025-10-06 01:27:43] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -27.945960 | E_var:     0.0975 | E_err:   0.004879
[2025-10-06 01:27:46] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -27.949051 | E_var:     0.2433 | E_err:   0.007708
[2025-10-06 01:27:48] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -27.949950 | E_var:     0.1424 | E_err:   0.005896
[2025-10-06 01:27:51] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -27.941791 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 01:27:53] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -27.950077 | E_var:     0.1538 | E_err:   0.006128
[2025-10-06 01:27:55] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -27.950000 | E_var:     0.1776 | E_err:   0.006584
[2025-10-06 01:27:58] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -27.947914 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 01:28:00] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -27.960869 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 01:28:03] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -27.945575 | E_var:     0.1342 | E_err:   0.005724
[2025-10-06 01:28:05] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -27.945120 | E_var:     0.1526 | E_err:   0.006105
[2025-10-06 01:28:07] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -27.947571 | E_var:     0.1455 | E_err:   0.005959
[2025-10-06 01:28:10] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -27.950652 | E_var:     0.1294 | E_err:   0.005621
[2025-10-06 01:28:12] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -27.947452 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 01:28:15] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -27.956789 | E_var:     0.1144 | E_err:   0.005285
[2025-10-06 01:28:17] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -27.944295 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 01:28:20] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -27.952080 | E_var:     0.1632 | E_err:   0.006313
[2025-10-06 01:28:22] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -27.956619 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 01:28:24] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -27.951531 | E_var:     0.1157 | E_err:   0.005314
[2025-10-06 01:28:27] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -27.950178 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 01:28:29] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -27.950315 | E_var:     0.1263 | E_err:   0.005552
[2025-10-06 01:28:32] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -27.945919 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 01:28:34] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -27.948852 | E_var:     0.1239 | E_err:   0.005500
[2025-10-06 01:28:37] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -27.958151 | E_var:     0.1195 | E_err:   0.005401
[2025-10-06 01:28:39] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -27.943313 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 01:28:41] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -27.941631 | E_var:     0.1950 | E_err:   0.006899
[2025-10-06 01:28:44] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -27.949318 | E_var:     0.1219 | E_err:   0.005455
[2025-10-06 01:28:46] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -27.951124 | E_var:     0.1410 | E_err:   0.005868
[2025-10-06 01:28:49] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -27.946766 | E_var:     0.1023 | E_err:   0.004997
[2025-10-06 01:28:51] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -27.949410 | E_var:     0.1324 | E_err:   0.005685
[2025-10-06 01:28:54] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -27.945050 | E_var:     0.1171 | E_err:   0.005346
[2025-10-06 01:28:54] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 01:28:56] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -27.938857 | E_var:     0.1544 | E_err:   0.006139
[2025-10-06 01:28:58] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -27.944125 | E_var:     0.1423 | E_err:   0.005895
[2025-10-06 01:29:01] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -27.949083 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 01:29:03] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -27.948057 | E_var:     0.1649 | E_err:   0.006345
[2025-10-06 01:29:06] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -27.955468 | E_var:     0.1153 | E_err:   0.005305
[2025-10-06 01:29:08] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -27.948773 | E_var:     0.2186 | E_err:   0.007305
[2025-10-06 01:29:11] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -27.953977 | E_var:     0.2898 | E_err:   0.008411
[2025-10-06 01:29:13] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -27.943636 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 01:29:15] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -27.959789 | E_var:     0.1147 | E_err:   0.005293
[2025-10-06 01:29:18] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -27.952401 | E_var:     0.1082 | E_err:   0.005141
[2025-10-06 01:29:20] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -27.952843 | E_var:     0.1122 | E_err:   0.005233
[2025-10-06 01:29:23] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -27.946469 | E_var:     0.1526 | E_err:   0.006103
[2025-10-06 01:29:25] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -27.947777 | E_var:     0.1488 | E_err:   0.006027
[2025-10-06 01:29:28] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -27.947793 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 01:29:30] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -27.952114 | E_var:     0.1397 | E_err:   0.005839
[2025-10-06 01:29:33] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -27.945202 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 01:29:35] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -27.955296 | E_var:     0.1686 | E_err:   0.006416
[2025-10-06 01:29:37] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -27.946295 | E_var:     0.1710 | E_err:   0.006460
[2025-10-06 01:29:40] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -27.950692 | E_var:     0.1090 | E_err:   0.005159
[2025-10-06 01:29:42] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -27.953633 | E_var:     0.1136 | E_err:   0.005267
[2025-10-06 01:29:45] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -27.944828 | E_var:     0.1255 | E_err:   0.005536
[2025-10-06 01:29:47] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -27.945470 | E_var:     0.1236 | E_err:   0.005494
[2025-10-06 01:29:50] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -27.945975 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 01:29:52] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -27.944658 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 01:29:54] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -27.953648 | E_var:     0.1670 | E_err:   0.006384
[2025-10-06 01:29:57] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -27.947743 | E_var:     0.1804 | E_err:   0.006637
[2025-10-06 01:29:59] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -27.946227 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 01:30:02] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -27.953243 | E_var:     0.1256 | E_err:   0.005538
[2025-10-06 01:30:04] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -27.944590 | E_var:     0.1176 | E_err:   0.005359
[2025-10-06 01:30:07] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -27.953995 | E_var:     0.1222 | E_err:   0.005462
[2025-10-06 01:30:09] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -27.952626 | E_var:     0.1282 | E_err:   0.005595
[2025-10-06 01:30:11] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -27.947019 | E_var:     0.1380 | E_err:   0.005804
[2025-10-06 01:30:14] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -27.946857 | E_var:     0.1113 | E_err:   0.005214
[2025-10-06 01:30:16] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -27.951335 | E_var:     0.1343 | E_err:   0.005725
[2025-10-06 01:30:19] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -27.950168 | E_var:     0.1217 | E_err:   0.005452
[2025-10-06 01:30:21] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -27.952271 | E_var:     0.1410 | E_err:   0.005867
[2025-10-06 01:30:24] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -27.948644 | E_var:     0.1333 | E_err:   0.005705
[2025-10-06 01:30:26] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -27.945408 | E_var:     0.1978 | E_err:   0.006950
[2025-10-06 01:30:28] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -27.941443 | E_var:     0.1270 | E_err:   0.005567
[2025-10-06 01:30:31] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -27.959811 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 01:30:33] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -27.949471 | E_var:     0.1218 | E_err:   0.005452
[2025-10-06 01:30:36] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -27.944917 | E_var:     0.1217 | E_err:   0.005450
[2025-10-06 01:30:38] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -27.947627 | E_var:     0.1569 | E_err:   0.006188
[2025-10-06 01:30:41] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -27.946977 | E_var:     0.1360 | E_err:   0.005762
[2025-10-06 01:30:43] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -27.948967 | E_var:     0.1239 | E_err:   0.005499
[2025-10-06 01:30:45] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -27.954857 | E_var:     0.1451 | E_err:   0.005951
[2025-10-06 01:30:48] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -27.950588 | E_var:     0.1060 | E_err:   0.005086
[2025-10-06 01:30:50] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -27.948083 | E_var:     0.1543 | E_err:   0.006138
[2025-10-06 01:30:53] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -27.957560 | E_var:     0.1389 | E_err:   0.005824
[2025-10-06 01:30:55] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -27.950725 | E_var:     0.1383 | E_err:   0.005810
[2025-10-06 01:30:58] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -27.938822 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 01:31:00] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -27.949106 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 01:31:02] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -27.942054 | E_var:     0.1293 | E_err:   0.005619
[2025-10-06 01:31:05] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -27.958223 | E_var:     0.1620 | E_err:   0.006289
[2025-10-06 01:31:07] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -27.947919 | E_var:     0.1215 | E_err:   0.005446
[2025-10-06 01:31:10] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -27.958079 | E_var:     0.1224 | E_err:   0.005466
[2025-10-06 01:31:12] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -27.945993 | E_var:     0.1089 | E_err:   0.005157
[2025-10-06 01:31:15] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -27.958709 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 01:31:17] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -27.951258 | E_var:     0.1280 | E_err:   0.005591
[2025-10-06 01:31:19] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -27.952059 | E_var:     0.1223 | E_err:   0.005463
[2025-10-06 01:31:22] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -27.944367 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 01:31:24] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -27.945064 | E_var:     0.1215 | E_err:   0.005447
[2025-10-06 01:31:27] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -27.952022 | E_var:     0.1498 | E_err:   0.006047
[2025-10-06 01:31:29] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -27.948946 | E_var:     0.1331 | E_err:   0.005701
[2025-10-06 01:31:32] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -27.960912 | E_var:     0.1112 | E_err:   0.005211
[2025-10-06 01:31:34] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -27.953266 | E_var:     0.1159 | E_err:   0.005320
[2025-10-06 01:31:36] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -27.939010 | E_var:     0.4895 | E_err:   0.010932
[2025-10-06 01:31:39] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -27.948649 | E_var:     0.1588 | E_err:   0.006227
[2025-10-06 01:31:41] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -27.948885 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 01:31:44] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -27.964283 | E_var:     0.1330 | E_err:   0.005698
[2025-10-06 01:31:46] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -27.940817 | E_var:     0.1044 | E_err:   0.005049
[2025-10-06 01:31:49] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -27.944362 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 01:31:51] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -27.946304 | E_var:     0.1486 | E_err:   0.006023
[2025-10-06 01:31:53] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -27.956236 | E_var:     0.2236 | E_err:   0.007389
[2025-10-06 01:31:56] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -27.937208 | E_var:     0.1192 | E_err:   0.005396
[2025-10-06 01:31:58] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -27.937817 | E_var:     0.1929 | E_err:   0.006863
[2025-10-06 01:32:01] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -27.949260 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 01:32:03] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -27.947934 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 01:32:06] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -27.946493 | E_var:     0.1467 | E_err:   0.005984
[2025-10-06 01:32:08] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -27.955495 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 01:32:10] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -27.942850 | E_var:     0.1161 | E_err:   0.005325
[2025-10-06 01:32:13] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -27.943781 | E_var:     0.1411 | E_err:   0.005870
[2025-10-06 01:32:15] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -27.940870 | E_var:     0.1530 | E_err:   0.006113
[2025-10-06 01:32:18] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -27.933791 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 01:32:20] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -27.951262 | E_var:     0.1301 | E_err:   0.005635
[2025-10-06 01:32:22] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -27.943329 | E_var:     0.1350 | E_err:   0.005741
[2025-10-06 01:32:25] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -27.954202 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 01:32:27] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -27.939632 | E_var:     0.1816 | E_err:   0.006658
[2025-10-06 01:32:30] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -27.947318 | E_var:     0.1295 | E_err:   0.005622
[2025-10-06 01:32:32] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -27.954279 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 01:32:35] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -27.956724 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 01:32:37] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -27.947785 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 01:32:40] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -27.940818 | E_var:     0.1375 | E_err:   0.005795
[2025-10-06 01:32:42] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -27.950313 | E_var:     0.1311 | E_err:   0.005657
[2025-10-06 01:32:44] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -27.948673 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 01:32:47] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -27.954283 | E_var:     0.1960 | E_err:   0.006918
[2025-10-06 01:32:49] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -27.954074 | E_var:     0.1464 | E_err:   0.005977
[2025-10-06 01:32:52] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -27.951712 | E_var:     0.2050 | E_err:   0.007074
[2025-10-06 01:32:54] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -27.955803 | E_var:     0.1289 | E_err:   0.005609
[2025-10-06 01:32:57] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -27.944783 | E_var:     0.1266 | E_err:   0.005560
[2025-10-06 01:32:57] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 01:32:59] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -27.945438 | E_var:     0.1748 | E_err:   0.006532
[2025-10-06 01:33:01] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -27.940174 | E_var:     0.1295 | E_err:   0.005622
[2025-10-06 01:33:04] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -27.953820 | E_var:     0.1954 | E_err:   0.006908
[2025-10-06 01:33:06] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -27.957392 | E_var:     0.1107 | E_err:   0.005199
[2025-10-06 01:33:09] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -27.935501 | E_var:     0.1253 | E_err:   0.005530
[2025-10-06 01:33:11] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -27.947481 | E_var:     0.1319 | E_err:   0.005675
[2025-10-06 01:33:14] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -27.955064 | E_var:     0.1591 | E_err:   0.006233
[2025-10-06 01:33:16] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -27.953485 | E_var:     0.1256 | E_err:   0.005538
[2025-10-06 01:33:18] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -27.949865 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 01:33:21] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -27.948269 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 01:33:23] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -27.947929 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 01:33:26] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -27.948391 | E_var:     0.1333 | E_err:   0.005705
[2025-10-06 01:33:28] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -27.956007 | E_var:     0.1279 | E_err:   0.005589
[2025-10-06 01:33:31] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -27.953965 | E_var:     0.1364 | E_err:   0.005770
[2025-10-06 01:33:33] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -27.959443 | E_var:     0.1140 | E_err:   0.005277
[2025-10-06 01:33:35] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -27.948061 | E_var:     0.1212 | E_err:   0.005441
[2025-10-06 01:33:38] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -27.942016 | E_var:     0.1339 | E_err:   0.005717
[2025-10-06 01:33:40] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -27.946589 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 01:33:43] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -27.958558 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 01:33:45] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -27.940124 | E_var:     0.1902 | E_err:   0.006814
[2025-10-06 01:33:48] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -27.960205 | E_var:     0.1339 | E_err:   0.005717
[2025-10-06 01:33:50] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -27.952801 | E_var:     0.1318 | E_err:   0.005672
[2025-10-06 01:33:52] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -27.959171 | E_var:     0.1160 | E_err:   0.005322
[2025-10-06 01:33:55] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -27.939363 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 01:33:57] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -27.952749 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 01:34:00] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -27.951495 | E_var:     0.1456 | E_err:   0.005963
[2025-10-06 01:34:02] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -27.949492 | E_var:     0.1275 | E_err:   0.005579
[2025-10-06 01:34:05] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -27.960416 | E_var:     0.1217 | E_err:   0.005450
[2025-10-06 01:34:07] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -27.961148 | E_var:     0.1471 | E_err:   0.005993
[2025-10-06 01:34:09] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -27.946836 | E_var:     0.1268 | E_err:   0.005565
[2025-10-06 01:34:12] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -27.946835 | E_var:     0.1222 | E_err:   0.005463
[2025-10-06 01:34:14] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -27.958427 | E_var:     0.1178 | E_err:   0.005362
[2025-10-06 01:34:17] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -27.946284 | E_var:     0.1382 | E_err:   0.005808
[2025-10-06 01:34:19] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -27.955070 | E_var:     0.1044 | E_err:   0.005048
[2025-10-06 01:34:22] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -27.946169 | E_var:     0.1493 | E_err:   0.006038
[2025-10-06 01:34:24] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -27.952824 | E_var:     0.1128 | E_err:   0.005247
[2025-10-06 01:34:26] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -27.951296 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 01:34:29] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -27.955613 | E_var:     0.1425 | E_err:   0.005899
[2025-10-06 01:34:31] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -27.949356 | E_var:     0.0913 | E_err:   0.004721
[2025-10-06 01:34:34] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -27.949965 | E_var:     0.1235 | E_err:   0.005491
[2025-10-06 01:34:36] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -27.952926 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 01:34:39] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -27.950772 | E_var:     0.1049 | E_err:   0.005060
[2025-10-06 01:34:41] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -27.956203 | E_var:     0.1674 | E_err:   0.006394
[2025-10-06 01:34:43] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -27.946615 | E_var:     0.1400 | E_err:   0.005846
[2025-10-06 01:34:46] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -27.947764 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 01:34:48] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -27.958152 | E_var:     0.1109 | E_err:   0.005204
[2025-10-06 01:34:51] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -27.946131 | E_var:     0.1195 | E_err:   0.005402
[2025-10-06 01:34:53] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -27.954746 | E_var:     0.1084 | E_err:   0.005144
[2025-10-06 01:34:56] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -27.938185 | E_var:     0.1815 | E_err:   0.006656
[2025-10-06 01:34:58] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -27.949478 | E_var:     0.1258 | E_err:   0.005543
[2025-10-06 01:35:00] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -27.951960 | E_var:     0.1588 | E_err:   0.006226
[2025-10-06 01:35:03] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -27.944694 | E_var:     0.1211 | E_err:   0.005438
[2025-10-06 01:35:05] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -27.945780 | E_var:     0.1528 | E_err:   0.006108
[2025-10-06 01:35:08] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -27.948991 | E_var:     0.1607 | E_err:   0.006263
[2025-10-06 01:35:10] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -27.950949 | E_var:     0.1303 | E_err:   0.005639
[2025-10-06 01:35:13] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -27.942464 | E_var:     0.1123 | E_err:   0.005235
[2025-10-06 01:35:15] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -27.950896 | E_var:     0.1094 | E_err:   0.005169
[2025-10-06 01:35:17] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -27.959592 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 01:35:20] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -27.961598 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 01:35:22] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -27.951363 | E_var:     0.1051 | E_err:   0.005066
[2025-10-06 01:35:25] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -27.945478 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 01:35:27] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -27.957079 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 01:35:30] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -27.953707 | E_var:     0.1446 | E_err:   0.005943
[2025-10-06 01:35:32] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -27.947745 | E_var:     0.1834 | E_err:   0.006692
[2025-10-06 01:35:34] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -27.944105 | E_var:     0.1283 | E_err:   0.005596
[2025-10-06 01:35:37] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -27.948876 | E_var:     0.1068 | E_err:   0.005106
[2025-10-06 01:35:39] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -27.950494 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 01:35:42] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -27.951022 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 01:35:44] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -27.952076 | E_var:     0.1120 | E_err:   0.005229
[2025-10-06 01:35:47] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -27.950380 | E_var:     0.1251 | E_err:   0.005526
[2025-10-06 01:35:49] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -27.946506 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 01:35:51] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -27.950728 | E_var:     0.1044 | E_err:   0.005050
[2025-10-06 01:35:54] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -27.944058 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 01:35:56] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -27.956663 | E_var:     0.1485 | E_err:   0.006020
[2025-10-06 01:35:59] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -27.953368 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 01:36:01] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -27.949672 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 01:36:04] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -27.953556 | E_var:     0.1207 | E_err:   0.005428
[2025-10-06 01:36:06] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -27.943610 | E_var:     0.1004 | E_err:   0.004951
[2025-10-06 01:36:08] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -27.955835 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 01:36:11] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -27.952044 | E_var:     0.1254 | E_err:   0.005532
[2025-10-06 01:36:13] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -27.944627 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 01:36:16] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -27.953702 | E_var:     0.1360 | E_err:   0.005762
[2025-10-06 01:36:18] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -27.957640 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 01:36:21] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -27.937766 | E_var:     0.1209 | E_err:   0.005433
[2025-10-06 01:36:23] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -27.946901 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 01:36:25] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -27.941251 | E_var:     0.1286 | E_err:   0.005603
[2025-10-06 01:36:28] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -27.949813 | E_var:     0.1268 | E_err:   0.005564
[2025-10-06 01:36:30] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -27.955212 | E_var:     0.1216 | E_err:   0.005448
[2025-10-06 01:36:33] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -27.952485 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 01:36:35] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -27.948857 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 01:36:38] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -27.954669 | E_var:     0.2165 | E_err:   0.007271
[2025-10-06 01:36:40] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -27.949404 | E_var:     0.1155 | E_err:   0.005310
[2025-10-06 01:36:42] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -27.947998 | E_var:     0.1168 | E_err:   0.005339
[2025-10-06 01:36:45] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -27.946553 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 01:36:47] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -27.950154 | E_var:     0.1292 | E_err:   0.005617
[2025-10-06 01:36:50] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -27.946120 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 01:36:52] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -27.950502 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 01:36:55] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -27.952217 | E_var:     0.1384 | E_err:   0.005812
[2025-10-06 01:36:57] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -27.951795 | E_var:     0.1515 | E_err:   0.006082
[2025-10-06 01:36:59] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -27.938066 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 01:36:59] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 01:37:02] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -27.956771 | E_var:     0.1103 | E_err:   0.005189
[2025-10-06 01:37:04] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -27.934826 | E_var:     0.1451 | E_err:   0.005953
[2025-10-06 01:37:07] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -27.950808 | E_var:     0.1116 | E_err:   0.005221
[2025-10-06 01:37:09] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -27.945303 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 01:37:12] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -27.960593 | E_var:     0.1520 | E_err:   0.006092
[2025-10-06 01:37:14] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -27.955457 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 01:37:16] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -27.942908 | E_var:     0.1567 | E_err:   0.006184
[2025-10-06 01:37:19] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -27.946351 | E_var:     0.1173 | E_err:   0.005352
[2025-10-06 01:37:21] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -27.947354 | E_var:     0.1068 | E_err:   0.005106
[2025-10-06 01:37:24] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -27.937082 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 01:37:26] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -27.949081 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 01:37:29] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -27.946234 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 01:37:31] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -27.950579 | E_var:     0.1311 | E_err:   0.005658
[2025-10-06 01:37:34] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -27.949665 | E_var:     0.1326 | E_err:   0.005691
[2025-10-06 01:37:36] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -27.948567 | E_var:     0.1423 | E_err:   0.005894
[2025-10-06 01:37:39] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -27.939360 | E_var:     0.1385 | E_err:   0.005815
[2025-10-06 01:37:41] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -27.949458 | E_var:     0.1426 | E_err:   0.005900
[2025-10-06 01:37:43] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -27.954217 | E_var:     0.1173 | E_err:   0.005351
[2025-10-06 01:37:46] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -27.941529 | E_var:     0.1934 | E_err:   0.006872
[2025-10-06 01:37:48] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -27.956341 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 01:37:51] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -27.950338 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 01:37:53] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -27.949935 | E_var:     0.3848 | E_err:   0.009692
[2025-10-06 01:37:56] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -27.950318 | E_var:     0.1176 | E_err:   0.005359
[2025-10-06 01:37:58] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -27.955802 | E_var:     0.1303 | E_err:   0.005641
[2025-10-06 01:38:00] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -27.943659 | E_var:     0.1269 | E_err:   0.005565
[2025-10-06 01:38:03] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -27.945345 | E_var:     0.1348 | E_err:   0.005736
[2025-10-06 01:38:05] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -27.953833 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 01:38:08] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -27.944899 | E_var:     0.2312 | E_err:   0.007513
[2025-10-06 01:38:10] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -27.945377 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 01:38:12] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -27.949888 | E_var:     0.1706 | E_err:   0.006453
[2025-10-06 01:38:15] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -27.949751 | E_var:     0.1325 | E_err:   0.005687
[2025-10-06 01:38:17] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -27.937097 | E_var:     0.1691 | E_err:   0.006426
[2025-10-06 01:38:20] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -27.958990 | E_var:     0.1202 | E_err:   0.005418
[2025-10-06 01:38:22] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -27.950920 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 01:38:25] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -27.955486 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 01:38:27] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -27.939496 | E_var:     0.1212 | E_err:   0.005440
[2025-10-06 01:38:29] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -27.950250 | E_var:     0.1326 | E_err:   0.005690
[2025-10-06 01:38:32] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -27.948420 | E_var:     0.1417 | E_err:   0.005882
[2025-10-06 01:38:34] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -27.958520 | E_var:     0.1739 | E_err:   0.006515
[2025-10-06 01:38:37] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -27.945212 | E_var:     0.1474 | E_err:   0.005998
[2025-10-06 01:38:39] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -27.940930 | E_var:     0.1797 | E_err:   0.006623
[2025-10-06 01:38:42] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -27.949778 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 01:38:44] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -27.948044 | E_var:     0.1227 | E_err:   0.005473
[2025-10-06 01:38:46] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -27.951585 | E_var:     0.1050 | E_err:   0.005064
[2025-10-06 01:38:49] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -27.950459 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 01:38:51] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -27.957619 | E_var:     0.1218 | E_err:   0.005453
[2025-10-06 01:38:54] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -27.955733 | E_var:     0.2135 | E_err:   0.007220
[2025-10-06 01:38:56] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -27.950477 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 01:38:59] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -27.947304 | E_var:     0.1059 | E_err:   0.005085
[2025-10-06 01:39:01] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -27.942076 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 01:39:03] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -27.945952 | E_var:     0.1279 | E_err:   0.005588
[2025-10-06 01:39:06] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -27.948037 | E_var:     0.1313 | E_err:   0.005661
[2025-10-06 01:39:08] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -27.951276 | E_var:     0.1239 | E_err:   0.005499
[2025-10-06 01:39:11] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -27.946061 | E_var:     0.1234 | E_err:   0.005490
[2025-10-06 01:39:13] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -27.949531 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 01:39:15] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -27.946727 | E_var:     0.1275 | E_err:   0.005578
[2025-10-06 01:39:18] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -27.952123 | E_var:     0.1510 | E_err:   0.006072
[2025-10-06 01:39:20] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -27.956711 | E_var:     0.1211 | E_err:   0.005438
[2025-10-06 01:39:23] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -27.965027 | E_var:     0.1632 | E_err:   0.006312
[2025-10-06 01:39:25] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -27.946396 | E_var:     0.1286 | E_err:   0.005604
[2025-10-06 01:39:28] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -27.953558 | E_var:     0.2120 | E_err:   0.007194
[2025-10-06 01:39:30] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -27.944793 | E_var:     0.1974 | E_err:   0.006942
[2025-10-06 01:39:32] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -27.947981 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 01:39:35] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -27.949170 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 01:39:37] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -27.943241 | E_var:     0.1022 | E_err:   0.004996
[2025-10-06 01:39:40] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -27.947327 | E_var:     0.1176 | E_err:   0.005359
[2025-10-06 01:39:42] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -27.945948 | E_var:     0.1107 | E_err:   0.005199
[2025-10-06 01:39:45] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -27.946554 | E_var:     0.1981 | E_err:   0.006955
[2025-10-06 01:39:47] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -27.944061 | E_var:     0.1316 | E_err:   0.005668
[2025-10-06 01:39:49] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -27.943166 | E_var:     0.1266 | E_err:   0.005559
[2025-10-06 01:39:52] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -27.949989 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 01:39:54] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -27.941380 | E_var:     0.1968 | E_err:   0.006932
[2025-10-06 01:39:57] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -27.948683 | E_var:     0.0956 | E_err:   0.004830
[2025-10-06 01:39:59] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -27.939736 | E_var:     0.1434 | E_err:   0.005917
[2025-10-06 01:40:02] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -27.954611 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 01:40:04] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -27.957397 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 01:40:06] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -27.944223 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 01:40:09] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -27.947065 | E_var:     0.1317 | E_err:   0.005671
[2025-10-06 01:40:11] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -27.943708 | E_var:     0.1198 | E_err:   0.005407
[2025-10-06 01:40:14] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -27.949118 | E_var:     0.1388 | E_err:   0.005821
[2025-10-06 01:40:16] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -27.951397 | E_var:     0.1029 | E_err:   0.005011
[2025-10-06 01:40:19] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -27.942914 | E_var:     0.1420 | E_err:   0.005887
[2025-10-06 01:40:21] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -27.959207 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 01:40:23] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -27.940143 | E_var:     0.1154 | E_err:   0.005307
[2025-10-06 01:40:26] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -27.950290 | E_var:     0.1209 | E_err:   0.005432
[2025-10-06 01:40:28] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -27.953229 | E_var:     0.1103 | E_err:   0.005188
[2025-10-06 01:40:31] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -27.948901 | E_var:     0.1204 | E_err:   0.005421
[2025-10-06 01:40:33] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -27.957128 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 01:40:36] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -27.950606 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 01:40:38] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -27.941757 | E_var:     0.1568 | E_err:   0.006187
[2025-10-06 01:40:40] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -27.953762 | E_var:     0.1280 | E_err:   0.005591
[2025-10-06 01:40:43] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -27.943822 | E_var:     0.1493 | E_err:   0.006038
[2025-10-06 01:40:45] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -27.951223 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 01:40:48] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -27.957050 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 01:40:50] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -27.958519 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 01:40:52] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -27.949570 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 01:40:55] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -27.953352 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 01:40:57] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -27.963956 | E_var:     0.1268 | E_err:   0.005565
[2025-10-06 01:41:00] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -27.956699 | E_var:     0.1394 | E_err:   0.005834
[2025-10-06 01:41:02] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -27.949075 | E_var:     0.1673 | E_err:   0.006391
[2025-10-06 01:41:02] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 01:41:05] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -27.950724 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 01:41:07] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -27.959571 | E_var:     0.1859 | E_err:   0.006737
[2025-10-06 01:41:09] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -27.949201 | E_var:     0.1235 | E_err:   0.005492
[2025-10-06 01:41:12] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -27.948053 | E_var:     0.1027 | E_err:   0.005006
[2025-10-06 01:41:14] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -27.948936 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 01:41:17] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -27.945603 | E_var:     0.2116 | E_err:   0.007188
[2025-10-06 01:41:19] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -27.946885 | E_var:     0.1482 | E_err:   0.006015
[2025-10-06 01:41:22] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -27.948534 | E_var:     0.1850 | E_err:   0.006720
[2025-10-06 01:41:24] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -27.941715 | E_var:     0.1209 | E_err:   0.005432
[2025-10-06 01:41:26] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -27.959999 | E_var:     0.1164 | E_err:   0.005330
[2025-10-06 01:41:29] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -27.942143 | E_var:     0.1276 | E_err:   0.005582
[2025-10-06 01:41:31] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -27.948540 | E_var:     0.1306 | E_err:   0.005646
[2025-10-06 01:41:34] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -27.945525 | E_var:     0.1279 | E_err:   0.005587
[2025-10-06 01:41:36] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -27.959632 | E_var:     0.1360 | E_err:   0.005761
[2025-10-06 01:41:39] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -27.953514 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 01:41:41] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -27.942879 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 01:41:43] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -27.948224 | E_var:     0.1130 | E_err:   0.005252
[2025-10-06 01:41:46] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -27.941992 | E_var:     0.1566 | E_err:   0.006183
[2025-10-06 01:41:48] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -27.946960 | E_var:     0.1334 | E_err:   0.005707
[2025-10-06 01:41:51] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -27.960693 | E_var:     0.1068 | E_err:   0.005105
[2025-10-06 01:41:53] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -27.949781 | E_var:     0.2320 | E_err:   0.007526
[2025-10-06 01:41:56] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -27.948684 | E_var:     0.1188 | E_err:   0.005385
[2025-10-06 01:41:58] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -27.941593 | E_var:     0.1407 | E_err:   0.005861
[2025-10-06 01:42:00] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -27.949204 | E_var:     0.1432 | E_err:   0.005913
[2025-10-06 01:42:03] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -27.957953 | E_var:     0.1363 | E_err:   0.005768
[2025-10-06 01:42:05] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -27.949847 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 01:42:08] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -27.941136 | E_var:     0.1145 | E_err:   0.005288
[2025-10-06 01:42:10] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -27.950504 | E_var:     0.1267 | E_err:   0.005561
[2025-10-06 01:42:13] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -27.946074 | E_var:     0.1341 | E_err:   0.005723
[2025-10-06 01:42:15] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -27.948970 | E_var:     0.1664 | E_err:   0.006374
[2025-10-06 01:42:17] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -27.951369 | E_var:     0.1187 | E_err:   0.005383
[2025-10-06 01:42:20] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -27.949847 | E_var:     0.1334 | E_err:   0.005708
[2025-10-06 01:42:22] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -27.943225 | E_var:     0.1587 | E_err:   0.006224
[2025-10-06 01:42:25] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -27.944393 | E_var:     0.1279 | E_err:   0.005588
[2025-10-06 01:42:27] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -27.962186 | E_var:     0.1890 | E_err:   0.006793
[2025-10-06 01:42:29] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -27.948857 | E_var:     0.1215 | E_err:   0.005446
[2025-10-06 01:42:32] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -27.953884 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 01:42:34] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -27.942498 | E_var:     0.1254 | E_err:   0.005533
[2025-10-06 01:42:37] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -27.947251 | E_var:     0.1268 | E_err:   0.005563
[2025-10-06 01:42:39] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -27.956231 | E_var:     0.1490 | E_err:   0.006032
[2025-10-06 01:42:42] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -27.947520 | E_var:     0.1965 | E_err:   0.006927
[2025-10-06 01:42:44] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -27.952811 | E_var:     0.1516 | E_err:   0.006084
[2025-10-06 01:42:46] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -27.950385 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 01:42:49] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -27.951644 | E_var:     0.1851 | E_err:   0.006723
[2025-10-06 01:42:51] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -27.947653 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 01:42:54] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -27.950966 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 01:42:56] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -27.942073 | E_var:     0.1311 | E_err:   0.005658
[2025-10-06 01:42:59] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -27.944908 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 01:43:01] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -27.957502 | E_var:     0.1088 | E_err:   0.005153
[2025-10-06 01:43:03] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -27.950664 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 01:43:03] ======================================================================================================
[2025-10-06 01:43:03] ✅ Training completed successfully
[2025-10-06 01:43:03] Total restarts: 2
[2025-10-06 01:43:04] Final Energy: -27.95066428 ± 0.00544462
[2025-10-06 01:43:04] Final Variance: 0.121421
[2025-10-06 01:43:04] ======================================================================================================
[2025-10-06 01:43:04] ======================================================================================================
[2025-10-06 01:43:04] Training completed | Runtime: 2599.5s
[2025-10-06 01:43:05] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 01:43:05] ======================================================================================================
