#!/usr/bin/env python3
"""
验证工作流设置脚本

此脚本检查所有配置文件、脚本和目录结构是否正确设置。
"""

import os
import sys
import yaml
from pathlib import Path
from collections import defaultdict

class SetupVerifier:
    """设置验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.workflows_dir = self.project_root / "workflows"
        self.errors = []
        self.warnings = []
        self.success = []
    
    def log_success(self, message):
        """记录成功信息"""
        self.success.append(f"✓ {message}")
        print(f"✓ {message}")
    
    def log_warning(self, message):
        """记录警告信息"""
        self.warnings.append(f"⚠ {message}")
        print(f"⚠ {message}")
    
    def log_error(self, message):
        """记录错误信息"""
        self.errors.append(f"✗ {message}")
        print(f"✗ {message}")
    
    def check_directory_structure(self):
        """检查目录结构"""
        print("\n" + "="*80)
        print("检查目录结构...")
        print("="*80)
        
        required_dirs = [
            "workflows",
            "workflows/configs",
            "workflows/hpc",
            "workflows/local",
            "scripts",
            "src",
            "src/analysis",
            "saved_models",
            "measurements",
            "measurements/structure_factor",
            "measurements/order_parameter",
            "measurements/energy",
            "measurements/infidelity",
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists():
                self.log_success(f"目录存在: {dir_path}")
            else:
                self.log_error(f"目录缺失: {dir_path}")
    
    def check_config_files(self):
        """检查配置文件"""
        print("\n" + "="*80)
        print("检查配置文件...")
        print("="*80)
        
        config_files = [
            "train.yaml",
            "structure_factor_measurement.yaml",
            "infidelity_measurement.yaml",
            "postprocess.yaml"
        ]

        for config_file in config_files:
            config_path = self.workflows_dir / "configs" / config_file

            if not config_path.exists():
                self.log_error(f"配置文件缺失: {config_file}")
                continue

            # 尝试解析YAML
            try:
                with open(config_path, 'r') as f:
                    config = yaml.safe_load(f)
                self.log_success(f"配置文件有效: {config_file}")

                # 检查关键字段
                if config_file == "train.yaml":
                    self._check_train_config(config)
                elif config_file == "structure_factor_measurement.yaml":
                    self._check_structure_factor_config(config)
                elif config_file == "infidelity_measurement.yaml":
                    self._check_infidelity_config(config)
                elif config_file == "postprocess.yaml":
                    self._check_postprocess_config(config)
                    
            except yaml.YAMLError as e:
                self.log_error(f"配置文件解析失败 {config_file}: {e}")
            except Exception as e:
                self.log_error(f"配置文件检查失败 {config_file}: {e}")
    
    def _check_train_config(self, config):
        """检查训练配置"""
        required_keys = ['system', 'model', 'training', 'checkpoint', 'hpc']
        for key in required_keys:
            if key not in config:
                self.log_warning(f"  train.yaml 缺少字段: {key}")
    
    def _check_structure_factor_config(self, config):
        """检查结构因子测量配置"""
        required_keys = ['analysis_params', 'model', 'sampling', 'checkpoint', 'hpc']
        for key in required_keys:
            if key not in config:
                self.log_warning(f"  structure_factor_measurement.yaml 缺少字段: {key}")
    
    def _check_infidelity_config(self, config):
        """检查Infidelity测量配置"""
        required_keys = ['scan_configs', 'model', 'sampling', 'checkpoint', 'hpc']
        for key in required_keys:
            if key not in config:
                self.log_warning(f"  infidelity_measurement.yaml 缺少字段: {key}")
    
    def _check_postprocess_config(self, config):
        """检查后处理配置"""
        required_keys = ['data_collection', 'order_analysis', 'energy_analysis']
        for key in required_keys:
            if key not in config:
                self.log_warning(f"  postprocess.yaml 缺少字段: {key}")
    
    def check_hpc_scripts(self):
        """检查HPC脚本"""
        print("\n" + "="*80)
        print("检查HPC脚本...")
        print("="*80)
        
        hpc_scripts = [
            "submit_train.pbs",
            "submit_structure_factor_measurement.pbs",
            "submit_infidelity_measurement.pbs"
        ]
        
        for script in hpc_scripts:
            script_path = self.workflows_dir / "hpc" / script
            
            if not script_path.exists():
                self.log_error(f"HPC脚本缺失: {script}")
                continue
            
            # 检查是否可执行
            if os.access(script_path, os.X_OK):
                self.log_success(f"HPC脚本存在且可执行: {script}")
            else:
                self.log_warning(f"HPC脚本存在但不可执行: {script}")
            
            # 检查PBS指令
            with open(script_path, 'r') as f:
                content = f.read()
                if '#PBS' in content:
                    self.log_success(f"  {script} 包含PBS指令")
                else:
                    self.log_warning(f"  {script} 缺少PBS指令")
    
    def check_local_scripts(self):
        """检查本地脚本"""
        print("\n" + "="*80)
        print("检查本地脚本...")
        print("="*80)
        
        local_scripts = [
            "run_train.sh",
            "run_structure_factor_measurement.sh",
            "run_postprocess.sh"
        ]
        
        for script in local_scripts:
            script_path = self.workflows_dir / "local" / script
            
            if not script_path.exists():
                self.log_error(f"本地脚本缺失: {script}")
                continue
            
            # 检查是否可执行
            if os.access(script_path, os.X_OK):
                self.log_success(f"本地脚本存在且可执行: {script}")
            else:
                self.log_warning(f"本地脚本存在但不可执行: {script}")
    
    def check_core_scripts(self):
        """检查核心执行脚本"""
        print("\n" + "="*80)
        print("检查核心执行脚本...")
        print("="*80)
        
        core_scripts = [
            "scripts/train.py",
            "scripts/structure_factor_measurement.py",
            "scripts/infidelity_measurement.py",
            "scripts/postprocess.py"
        ]
        
        for script in core_scripts:
            script_path = self.project_root / script
            
            if script_path.exists():
                self.log_success(f"核心脚本存在: {script}")
            else:
                self.log_error(f"核心脚本缺失: {script}")
    
    def check_pipeline_manager(self):
        """检查流水线管理器"""
        print("\n" + "="*80)
        print("检查流水线管理器...")
        print("="*80)
        
        pipeline_path = self.workflows_dir / "pipeline.py"
        
        if not pipeline_path.exists():
            self.log_error("流水线管理器缺失: pipeline.py")
            return
        
        self.log_success("流水线管理器存在: pipeline.py")
        
        # 检查是否可执行
        if os.access(pipeline_path, os.X_OK):
            self.log_success("流水线管理器可执行")
        else:
            self.log_warning("流水线管理器不可执行")
    
    def check_analysis_modules(self):
        """检查分析模块"""
        print("\n" + "="*80)
        print("检查分析模块...")
        print("="*80)

        analysis_files = {
            "order_calculations.py": "序参量计算工具",
            "order_analysis.py": "序参量分析模块",
            "energy_plot_L_J2.py": "能量分析模块"
        }

        for file_name, description in analysis_files.items():
            full_path = self.project_root / "src" / "analysis" / file_name
            if full_path.exists():
                self.log_success(f"{description}存在: {file_name}")
            else:
                self.log_error(f"{description}缺失: {file_name}")
    
    def print_summary(self):
        """打印总结"""
        print("\n" + "="*80)
        print("验证总结")
        print("="*80)
        
        print(f"\n✓ 成功: {len(self.success)}")
        print(f"⚠ 警告: {len(self.warnings)}")
        print(f"✗ 错误: {len(self.errors)}")
        
        if self.errors:
            print("\n错误列表:")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print("\n警告列表:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        print("\n" + "="*80)
        if not self.errors:
            print("✓ 所有关键组件验证通过！")
            print("="*80)
            return True
        else:
            print("✗ 发现错误，请修复后再使用")
            print("="*80)
            return False
    
    def run_all_checks(self):
        """运行所有检查"""
        print("="*80)
        print("开始验证工作流设置")
        print("="*80)

        self.check_directory_structure()
        self.check_config_files()
        self.check_hpc_scripts()
        self.check_local_scripts()
        self.check_core_scripts()
        self.check_pipeline_manager()
        self.check_analysis_modules()

        return self.print_summary()


def main():
    """主函数"""
    verifier = SetupVerifier()
    success = verifier.run_all_checks()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

