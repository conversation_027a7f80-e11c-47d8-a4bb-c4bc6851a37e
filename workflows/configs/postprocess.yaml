# ==================== 后处理任务配置文件 ====================
# 此文件包含本地后处理和可视化任务的配置

experiment:
  name: "ss_nqs_postprocess"
  description: "Shastry-Sutherland模型后处理和可视化"

# ==================== 数据收集配置 ====================
data_collection:
  # 要收集数据的L值
  L_values: [4, 5, 6]

  # 要收集数据的J2值
  J2_values: [0.00, 0.05, 1.00]

  # 结构因子测量结果目录
  structure_factor_dir: "measurements/structure_factor"

  # 输出目录
  output_dir: "measurements/order_parameter"

# ==================== 序参量分析配置 ====================
order_analysis:
  enabled: true

  # 要生成的图表类型
  plots:
    - "neel_ratio"
    - "dimer_ratio"
    - "diag_dimer_ratio"
    - "af_order_parameter"
    - "dimer_order_parameter"
    - "diag_dimer_order_parameter"

  # 图表保存路径
  save_dir: "measurements/order_parameter"

# ==================== 能量分析配置 ====================
energy_analysis:
  enabled: true

  # 扫描train.log文件的目录
  models_dir: "saved_models"

  # 输出前缀
  output_prefix: "energy_analysis"

  # 是否显示图表（本地分析时可以设为true）
  show_plot: false

  # Y轴范围（None表示自动调整）
  y_min_stable: null
  y_max_stable: null

  # 是否自动调整X轴
  auto_adjust_x_stable: true

  # 稳定迭代阈值
  stable_iter_threshold: "adaptive"

  # 图表保存路径
  save_dir: "measurements/energy"

# ==================== 层外推分析配置 ====================
layer_extrapolation:
  enabled: false  # 默认关闭，需要时手动启用
  
  # 分析的J2和J1值
  J2: 1.00
  J1_values: [0.76, 0.77, 0.78, 0.79, 0.80, 0.81, 0.82, 0.83, 0.84]
  
  # 图表保存路径
  save_dir: "results/figures/extrapolation"

# ==================== 报告生成配置 ====================
report:
  enabled: true
  
  # 报告格式
  format: "html"  # 可选：html, pdf, markdown
  
  # 报告模板
  template: "default"
  
  # 报告输出路径
  output_path: "results/reports/analysis_report.html"
  
  # 包含的章节
  sections:
    - "summary"
    - "order_parameters"
    - "energy_analysis"
    - "phase_diagram"

# ==================== 可视化配置 ====================
visualization:
  # 图表样式
  style: "seaborn"
  
  # 图表尺寸
  figure_size: [10, 7]
  
  # 字体大小
  font_size: 12
  
  # DPI设置
  dpi: 300
  
  # 颜色方案
  color_scheme: "default"

