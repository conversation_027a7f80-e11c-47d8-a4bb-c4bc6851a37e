# ==================== 结构因子测量任务配置文件 ====================
# 此文件包含结构因子测量任务的所有参数配置
# 测量结果将保存到 measurements/structure_factor/

experiment:
  name: "ss_structure_factor_measurement"
  description: "Shastry-Sutherland模型结构因子测量"

# ==================== 分析参数组合 ====================
# 格式：每个组合包含 [L, J2, J1]
analysis_params:
  - [6, 1.00, 0.76]
  - [6, 1.00, 0.77]
  - [6, 1.00, 0.78]
  - [6, 1.00, 0.79]
  - [6, 1.00, 0.80]
  - [6, 1.00, 0.81]
  - [6, 1.00, 0.82]
  - [6, 1.00, 0.83]
  - [6, 1.00, 0.84]

# ==================== 模型参数 ====================
model:
  # 模型参数组合（格式：[层数, 特征数]）
  configs:
    - [4, 4]

# ==================== 分析采样配置 ====================
sampling:
  n_samples: 21845  # 2**18，用于结构因子计算

# ==================== Checkpoint选择配置 ====================
checkpoint:
  # Checkpoint分析模式选择：
  # - "all": 分析所有checkpoint文件（按文件名排序）
  # - "final": 只分析最新的checkpoint文件
  # - "last_n": 分析倒数N个checkpoint文件
  mode: "last_n"
  
  # 当mode="last_n"时，指定分析倒数几个checkpoint
  # 注意：
  # - checkpoint按文件名排序（checkpoint_iter_XXXXXX.pkl）
  # - 如果总checkpoint数少于指定数量，将分析所有可用的checkpoint
  last_n: 4

# ==================== HPC配置 ====================
hpc:
  queue: "gpu_h200_pinaki"
  ngpus: 1
  walltime: "1440:00:00"
  project: "gs_spms_psengupta"
  job_name: "ss-gcnn-analyze"
  
  # 环境配置
  conda_env: "netket"
  anaconda_module: "anaconda2025/2025"
  unload_cuda_module: "cuda/12.2"
  
  # GPU设备
  cuda_visible_devices: "0"

