# ==================== 训练任务配置文件 ====================
# 此文件包含基础训练任务的所有参数配置

experiment:
  name: "ss_nqs_training"
  description: "Shastry-Sutherland模型NQS训练"

# ==================== 系统参数配置 ====================
system:
  L_values: [4]           # 晶格尺寸
  J2_values: [0.00]       # J2耦合强度
  J1_values: [0.00]       # J1耦合强度（训练任务）

# ==================== 模型参数 ====================
model:
  # 模型参数组合（格式：[层数, 特征数]）
  # 例如：[[2, 4], [4, 5], [6, 8]]
  configs:
    - [4, 4]
  
  # 其他模型参数
  diag_shift: 0.15
  grad_clip: 1.0

# ==================== 训练超参数 ====================
training:
  # 学习率调度（余弦退火+热重启）
  max_lr: 0.03              # 最大学习率（重启时的学习率）
  min_lr: 0.005             # 最小学习率（周期结束时的学习率）
  initial_period: 150       # 初始退火周期长度
  period_mult: 2.0          # 周期倍增因子（每次重启后周期长度翻倍）
  n_cycles: 4               # 重启周期数
  
  # 采样参数
  n_samples: 4096
  chunk_size: 4096

# ==================== Checkpoint配置 ====================
checkpoint:
  enable: true
  save_interval: 225        # 必须能整除总迭代数(2250)
  resume_from: ""           # 留空表示从头开始训练
  keep_history: true        # 保留所有checkpoint历史

# ==================== HPC配置 ====================
hpc:
  queue: "gpu_h200_pinaki"
  ngpus: 1
  walltime: "1440:00:00"
  project: "gs_spms_psengupta"
  job_name: "ss-gcnn-train"
  
  # 环境配置
  conda_env: "netket"
  anaconda_module: "anaconda2025/2025"
  unload_cuda_module: "cuda/12.2"
  
  # GPU设备
  cuda_visible_devices: "0"

