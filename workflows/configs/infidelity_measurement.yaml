# ==================== Infidelity 测量任务配置文件 ====================
# 此文件包含 Infidelity 测量任务的所有参数配置
# 测量结果将保存到 measurements/infidelity/
#
# Infidelity 测量用于计算相邻参数点之间波函数的 Infidelity，
# 以判断量子相变点的位置。
#
# 理论基础: "Fidelity approach to quantum phase transitions"

experiment:
  name: "ss_infidelity_measurement"
  description: "Shastry-Sutherland模型Infidelity测量（相变点检测）"

# ==================== Infidelity 扫描配置 ====================
# 每个扫描配置包含：L值、J2值、以及一系列J1值
# 格式：[L, J2, [J1_1, J1_2, J1_3, ...]]
# 
# 说明：
# - L: 晶格大小
# - J2: J2耦合强度（固定值）
# - J1列表: 一系列J1值，程序将计算相邻J1值之间的Infidelity
#
# 示例：
# [4, 1.00, [0.76, 0.77, 0.78, 0.79, 0.80]]
# 将计算以下Infidelity：
#   - (0.76, 0.77) 之间
#   - (0.77, 0.78) 之间
#   - (0.78, 0.79) 之间
#   - (0.79, 0.80) 之间

scan_configs:
  - L: 6
    J2: 1.00
    J1_values: [0.76, 0.77, 0.78, 0.79, 0.80, 0.81, 0.82, 0.83, 0.84]

# ==================== 模型参数 ====================
model:
  # 模型参数组合（格式：[层数, 特征数]）
  configs:
    - [4, 4]

# ==================== Checkpoint 选择 ====================
checkpoint:
  # 使用哪个checkpoint进行Infidelity分析
  # 留空表示自动为每个J1值选择最新的checkpoint（推荐）
  # 也可以手动指定checkpoint名称（不含.pkl），例如 "checkpoint_iter_002250"
  # 注意：如果手动指定，所有J1值将使用相同的checkpoint名称
  name: ""  # 留空表示自动选择

# ==================== 采样配置 ====================
sampling:
  # Infidelity 计算的采样数目
  # 注意：Infidelity_SR 需要计算量子几何张量（QGT），对于大采样数会消耗大量内存
  # QGT 的内存需求大约是 O(n_samples * n_params^2)
  # 对于 L=5 (n_params~19628)，2^20 采样会需要 TB 级内存
  # 建议使用较小的采样数，如 2^14 或 2^16
  n_samples: 65536  # 2**16=65536，平衡精度和内存使用

# ==================== HPC配置 ====================
hpc:
  queue: "gpu_h200_pinaki"
  ngpus: 1
  walltime: "1440:00:00"
  project: "gs_spms_psengupta"
  job_name: "ss-gcnn-infidelity"
  
  # 环境配置
  conda_env: "netket"
  anaconda_module: "anaconda2025/2025"
  unload_cuda_module: "cuda/12.2"
  
  # GPU设备
  cuda_visible_devices: "0"

