#!/bin/bash

# ==================== Shastry-Sutherland NQS 本地后处理脚本 ====================
# 此脚本用于在本地环境运行后处理和可视化任务
# 配置文件：workflows/configs/postprocess.yaml

set -e  # 遇到错误立即退出

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

cd "$PROJECT_ROOT" || exit 1

echo "==================== NQS 本地后处理任务 ===================="
echo "Job start at: $(date)"
echo "Working directory: $(pwd)"

# ==================== 环境检测和激活 ====================
echo "检测并激活conda环境..."

if command -v conda &> /dev/null; then
    # 初始化conda
    eval "$(conda shell.bash hook)"
    
    # 从配置文件读取环境名称（使用train.yaml的环境配置）
    CONDA_ENV=$(python3 -c "import yaml; print(yaml.safe_load(open('workflows/configs/train.yaml'))['hpc']['conda_env'])")
    
    # 激活环境
    conda activate "$CONDA_ENV"
    echo "✓ Conda环境已激活: $CONDA_ENV"
else
    echo "警告: 未找到conda，使用系统Python环境"
fi

echo "Python path: $(which python)"
echo "Python version: $(python --version)"

# ==================== 检查配置文件 ====================
CONFIG_FILE="workflows/configs/postprocess.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "使用配置文件: $CONFIG_FILE"

# ==================== 解析命令行参数 ====================
TASK="all"

# 简单的参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        --task)
            TASK="$2"
            shift 2
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --task TASK    指定要运行的任务类型"
            echo "                 可选值: all, order_analysis, energy_analysis, layer_extrapolation, report"
            echo "                 默认值: all"
            echo "  --help, -h     显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                              # 运行所有后处理任务"
            echo "  $0 --task order_analysis        # 只运行序参量分析"
            echo "  $0 --task energy_analysis       # 只运行能量分析"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# ==================== 执行后处理任务 ====================
echo "==================== 开始后处理任务 ===================="
echo "任务类型: $TASK"

python scripts/postprocess.py --config "$CONFIG_FILE" --task "$TASK"

EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo "==================== 所有后处理任务完成 ===================="
    echo "Job finished at: $(date)"
    
    # 显示生成的图表位置
    echo ""
    echo "生成的图表保存在:"
    echo "  - 序参量分析: results/figures/order_parameters/"
    echo "  - 能量分析: results/figures/energy/"
    echo ""
    echo "查看结果:"
    echo "  ls -lh results/figures/"
else
    echo ""
    echo "==================== 后处理任务失败 ===================="
    echo "Job failed at: $(date)"
    exit $EXIT_CODE
fi

