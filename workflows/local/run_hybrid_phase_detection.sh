#!/bin/bash

# ==================== Shastry-Sutherland NQS 表征学习相变检测脚本 ====================
# 此脚本用于在本地运行表征学习相变检测任务
# 基于VAE表征学习进行相变点检测

set -e  # 任何命令失败时退出

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

echo "==================== Shastry-Sutherland NQS 表征学习相变检测 ===================="
echo "Project root: $PROJECT_ROOT"
echo "Script directory: $SCRIPT_DIR"

# 进入项目根目录
cd "$PROJECT_ROOT"

# ==================== 环境配置 ====================
echo ""
echo "==================== 环境配置 ===================="

# ==================== 检测并激活conda环境 ====================
echo "检测并激活conda环境..."

if command -v conda &> /dev/null; then
    # 初始化conda
    eval "$(conda shell.bash hook)"
    
    # 从配置文件读取环境名称
    CONDA_ENV=$(python3 -c "import yaml; print(yaml.safe_load(open('workflows/configs/hybrid_phase_detection.yaml'))['hpc']['conda_env'])")
    
    # 激活环境
    conda activate "$CONDA_ENV"
    echo "✓ Conda环境已激活: $CONDA_ENV"
else
    echo "警告: 未找到conda，使用系统Python环境"
fi

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "✗ Python not found!"
    exit 1
fi

echo "Python path: $(which python)"
echo "Python version: $(python --version)"

# 检查必要的Python包
echo "Checking required packages..."
python -c "import jax; print(f'✓ JAX version: {jax.__version__}')" || {
    echo "✗ JAX not found. Please install it."
    exit 1
}

python -c "import netket; print(f'✓ NetKet version: {netket.__version__}')" || {
    echo "✗ NetKet not found. Please install it."
    exit 1
}

python -c "import sklearn; print(f'✓ scikit-learn version: {sklearn.__version__}')" || {
    echo "✗ scikit-learn not found. Please install it."
    exit 1
}

# ==================== 加载配置 ====================
CONFIG_FILE="workflows/configs/hybrid_phase_detection.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# ==================== 参数配置 ====================
echo ""
echo "==================== 参数配置 ===================="

# 使用Python解析YAML配置并获取默认值
CONFIG_OUTPUT=$(python3 -c "
import yaml
config = yaml.safe_load(open('$CONFIG_FILE'))

# 从第一个扫描配置中读取默认参数
scan = config['scan_configs'][0]
print(f\"L={scan['L']}\")
print(f\"J2={scan['J2']:.2f}\")
print(f\"J1_MIN={scan['J1_min']:.2f}\")
print(f\"J1_MAX={scan['J1_max']:.2f}\")
print(f\"J1_STEP={scan['J1_step']:.2f}\")

# 模型配置
model_config = config['model']['configs'][0]
print(f\"NUM_LAYERS={model_config[0]}\")
print(f\"NUM_FEATURES={model_config[1]}\")

# Checkpoint配置
checkpoint_name = config['checkpoint']['name']
print(f\"CHECKPOINT={checkpoint_name}\")

# 表征学习配置
rl_config = config['representation_learning']
print(f\"VAE_LATENT_DIM={rl_config['vae_latent_dim']}\")
print(f\"VAE_EPOCHS={rl_config['vae_epochs']}\")
print(f\"FUSION_WEIGHTS_PARAM={rl_config['fusion_weights'][0]:.1f}\")
print(f\"FUSION_WEIGHTS_VAE={rl_config['fusion_weights'][1]:.1f}\")
")
eval "$CONFIG_OUTPUT"

# 组合融合权重
FUSION_WEIGHTS="$FUSION_WEIGHTS_PARAM $FUSION_WEIGHTS_VAE"

# 允许命令行参数覆盖配置（如果提供的话）
L=${1:-$L}
J2=${2:-$J2}
J1_MIN=${3:-$J1_MIN}
J1_MAX=${4:-$J1_MAX}
J1_STEP=${5:-$J1_STEP}
NUM_LAYERS=${6:-$NUM_LAYERS}
NUM_FEATURES=${7:-$NUM_FEATURES}
CHECKPOINT=${8:-$CHECKPOINT}
VAE_LATENT_DIM=${9:-$VAE_LATENT_DIM}
VAE_EPOCHS=${10:-$VAE_EPOCHS}

# 解析融合权重（从字符串中提取）
if [ -z "$FUSION_WEIGHTS" ]; then
    FUSION_WEIGHTS="0.5 0.5"
fi

echo "System Configuration:"
echo "  L=$L, J2=$J2"
echo "  J1 range: $J1_MIN to $J1_MAX (step $J1_STEP)"
echo "  Model: $NUM_LAYERS layers, $NUM_FEATURES features"
echo "  Checkpoint: $CHECKPOINT"
echo "  VAE latent dim: $VAE_LATENT_DIM"
echo "  VAE epochs: $VAE_EPOCHS"

# 创建输出目录
OUTPUT_DIR="measurements/representation_learning/L=${L}/J2=${J2}"
mkdir -p "$OUTPUT_DIR"

echo ""
echo "Output directory: $OUTPUT_DIR"

# ==================== 表征学习相变检测 ===================="
echo ""
echo "==================== 表征学习相变检测 ===================="
echo "运行表征学习相变检测脚本..."

python scripts/run_hybrid_analysis.py \
    --L $L \
    --J2 $J2 \
    --J1_min $J1_MIN \
    --J1_max $J1_MAX \
    --J1_step $J1_STEP \
    --num_layers $NUM_LAYERS \
    --num_features $NUM_FEATURES \
    --checkpoint $CHECKPOINT \
    --vae_latent_dim $VAE_LATENT_DIM \
    --vae_epochs $VAE_EPOCHS \
    --fusion_weights $FUSION_WEIGHTS \
    --output_dir "$OUTPUT_DIR"

if [ $? -eq 0 ]; then
    echo "✓ 表征学习相变检测完成"
else
    echo "✗ 表征学习相变检测失败"
    exit 1
fi

# ==================== 结果统计 ===================="
echo ""
echo "==================== 结果统计 ===================="
echo "Output files in $OUTPUT_DIR:"
ls -lh "$OUTPUT_DIR"

echo ""
echo "==================== 所有任务完成 ===================="
echo "Results saved to: $OUTPUT_DIR"
echo ""
echo "Key output files:"
echo "  - hybrid_results.pkl: Complete results dictionary"
echo "  - hybrid_results.png: Visualization of all results"
echo "  - vae_analysis.png: VAE-specific analysis plots"
echo "  - hybrid_summary.txt: Summary report"

