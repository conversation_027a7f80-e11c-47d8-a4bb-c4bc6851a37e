#!/usr/bin/env python3
"""
统一流水线管理器 - Shastry-Sutherland NQS 工作流

此脚本提供统一的接口来管理整个NQS训练和测量流水线：
1. 训练 (TRAIN)
2. 结构因子测量 (STRUCTURE_FACTOR)
3. Infidelity测量 (INFIDELITY)
4. 后处理 (POSTPROCESS)

使用示例:
# 在本地运行训练
python workflows/pipeline.py --stage train --env local

# 提交训练到HPC
python workflows/pipeline.py --stage train --env hpc

# 提交结构因子测量到HPC
python workflows/pipeline.py --stage structure_factor --env hpc

# 运行完整流水线
python workflows/pipeline.py --run-all

# 查看状态
python workflows/pipeline.py --status
"""

import os
import sys
import argparse
import yaml
import subprocess
import time
from pathlib import Path
from datetime import datetime


class NQSPipeline:
    """NQS流水线管理器"""
    
    def __init__(self, project_root=None):
        """初始化流水线管理器"""
        if project_root is None:
            # 获取项目根目录
            script_dir = Path(__file__).parent
            self.project_root = script_dir.parent
        else:
            self.project_root = Path(project_root)
        
        self.workflows_dir = self.project_root / "workflows"
        self.configs_dir = self.workflows_dir / "configs"
        self.hpc_dir = self.workflows_dir / "hpc"
        self.local_dir = self.workflows_dir / "local"
        
        # 阶段定义
        self.stages = {
            'train': {
                'config': 'train.yaml',
                'hpc_script': 'submit_train.pbs',
                'local_script': 'run_train.sh',
                'description': 'NQS训练任务'
            },
            'structure_factor': {
                'config': 'structure_factor_measurement.yaml',
                'hpc_script': 'submit_structure_factor_measurement.pbs',
                'local_script': 'run_structure_factor_measurement.sh',
                'description': '结构因子测量任务'
            },
            'infidelity': {
                'config': 'infidelity_measurement.yaml',
                'hpc_script': 'submit_infidelity_measurement.pbs',
                'local_script': None,  # Infidelity通常在HPC上运行
                'description': 'Infidelity测量任务（相变点检测）'
            },
            'postprocess': {
                'config': 'postprocess.yaml',
                'hpc_script': None,  # 后处理通常在本地运行
                'local_script': 'run_postprocess.sh',
                'description': '后处理和可视化任务'
            }
        }
    
    def load_config(self, stage):
        """加载指定阶段的配置"""
        config_file = self.configs_dir / self.stages[stage]['config']
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        return config
    
    def run_local(self, stage, extra_args=None):
        """在本地环境运行指定阶段"""
        print(f"\n{'='*80}")
        print(f"在本地运行: {self.stages[stage]['description']}")
        print(f"{'='*80}\n")
        
        script_name = self.stages[stage]['local_script']
        
        if script_name is None:
            print(f"错误: {stage} 阶段没有本地执行脚本")
            print(f"建议在HPC上运行此阶段")
            return False
        
        script_path = self.local_dir / script_name
        
        if not script_path.exists():
            print(f"错误: 脚本不存在: {script_path}")
            return False
        
        # 构建命令
        cmd = [str(script_path)]
        if extra_args:
            cmd.extend(extra_args)
        
        # 执行脚本
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                check=True
            )
            print(f"\n✓ {stage} 阶段完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"\n✗ {stage} 阶段失败: {e}")
            return False
    
    def submit_hpc(self, stage):
        """提交任务到HPC"""
        print(f"\n{'='*80}")
        print(f"提交到HPC: {self.stages[stage]['description']}")
        print(f"{'='*80}\n")
        
        script_name = self.stages[stage]['hpc_script']
        
        if script_name is None:
            print(f"错误: {stage} 阶段没有HPC提交脚本")
            print(f"建议在本地运行此阶段")
            return False
        
        script_path = self.hpc_dir / script_name
        
        if not script_path.exists():
            print(f"错误: 脚本不存在: {script_path}")
            return False
        
        # 提交PBS作业
        try:
            result = subprocess.run(
                ['qsub', str(script_path)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            job_id = result.stdout.strip()
            print(f"✓ 作业已提交: {job_id}")
            print(f"  配置文件: {self.stages[stage]['config']}")
            print(f"  PBS脚本: {script_name}")
            print(f"\n查看作业状态: qstat {job_id}")
            print(f"查看作业输出: qstat -f {job_id}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ 提交失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def run_stage(self, stage, environment='local', extra_args=None):
        """运行指定阶段"""
        if stage not in self.stages:
            print(f"错误: 未知阶段 '{stage}'")
            print(f"可用阶段: {', '.join(self.stages.keys())}")
            return False
        
        if environment == 'local':
            return self.run_local(stage, extra_args)
        elif environment == 'hpc':
            return self.submit_hpc(stage)
        else:
            print(f"错误: 未知环境 '{environment}'")
            print("可用环境: local, hpc")
            return False
    
    def run_full_pipeline(self, train_env='hpc', analyze_env='hpc', postprocess_env='local'):
        """运行完整流水线"""
        print("\n" + "="*80)
        print("运行完整NQS流水线")
        print("="*80)
        print(f"训练环境: {train_env}")
        print(f"分析环境: {analyze_env}")
        print(f"后处理环境: {postprocess_env}")
        print("="*80 + "\n")
        
        # 1. 训练
        print("阶段 1/4: 训练")
        if not self.run_stage('train', train_env):
            print("✗ 训练阶段失败，流水线终止")
            return False
        
        if train_env == 'hpc':
            print("\n注意: 训练作业已提交到HPC")
            print("请等待训练完成后再运行分析阶段")
            print("使用以下命令继续:")
            print(f"  python {__file__} --stage analyze --env {analyze_env}")
            return True
        
        # 2. 分析
        print("\n阶段 2/4: 结构因子分析")
        if not self.run_stage('analyze', analyze_env):
            print("✗ 分析阶段失败")
            return False
        
        # 3. Infidelity分析
        print("\n阶段 3/4: Infidelity分析")
        if not self.run_stage('infidelity', analyze_env):
            print("✗ Infidelity分析阶段失败")
            return False
        
        if analyze_env == 'hpc':
            print("\n注意: 分析作业已提交到HPC")
            print("请等待分析完成后再运行后处理阶段")
            print("使用以下命令继续:")
            print(f"  python {__file__} --stage postprocess --env {postprocess_env}")
            return True
        
        # 4. 后处理
        print("\n阶段 4/4: 后处理和可视化")
        if not self.run_stage('postprocess', postprocess_env):
            print("✗ 后处理阶段失败")
            return False
        
        print("\n" + "="*80)
        print("✓ 完整流水线执行完成！")
        print("="*80)
        return True
    
    def show_status(self):
        """显示流水线状态"""
        print("\n" + "="*80)
        print("NQS流水线状态")
        print("="*80 + "\n")
        
        print("可用阶段:")
        for stage, info in self.stages.items():
            print(f"\n  {stage}:")
            print(f"    描述: {info['description']}")
            print(f"    配置: {info['config']}")
            print(f"    HPC脚本: {info['hpc_script'] or '不可用'}")
            print(f"    本地脚本: {info['local_script'] or '不可用'}")
        
        print("\n" + "="*80)
        print("配置文件位置: workflows/configs/")
        print("HPC脚本位置: workflows/hpc/")
        print("本地脚本位置: workflows/local/")
        print("="*80 + "\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='NQS流水线管理器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 在本地运行训练
  %(prog)s --stage train --env local
  
  # 提交训练到HPC
  %(prog)s --stage train --env hpc
  
  # 运行后处理
  %(prog)s --stage postprocess --env local
  
  # 运行完整流水线
  %(prog)s --run-all
  
  # 查看状态
  %(prog)s --status
        """
    )
    
    parser.add_argument('--stage', type=str,
                       choices=['train', 'analyze', 'infidelity', 'postprocess'],
                       help='要运行的阶段')
    parser.add_argument('--env', type=str,
                       choices=['local', 'hpc'],
                       default='local',
                       help='运行环境 (默认: local)')
    parser.add_argument('--run-all', action='store_true',
                       help='运行完整流水线')
    parser.add_argument('--status', action='store_true',
                       help='显示流水线状态')
    
    args = parser.parse_args()
    
    # 创建流水线管理器
    pipeline = NQSPipeline()
    
    # 执行操作
    if args.status:
        pipeline.show_status()
    elif args.run_all:
        success = pipeline.run_full_pipeline()
        sys.exit(0 if success else 1)
    elif args.stage:
        success = pipeline.run_stage(args.stage, args.env)
        sys.exit(0 if success else 1)
    else:
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()

