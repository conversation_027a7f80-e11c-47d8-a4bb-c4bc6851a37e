#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-gcnn-train
#PBS -j oe

# ==================== Shastry-Sutherland NQS 训练任务 ====================
# 此脚本用于在HPC上提交训练任务
# 配置文件：workflows/configs/train.yaml

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "==================== NQS 训练任务 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Working directory: $(pwd)"
echo "GPU Information:"
nvidia-smi

# ==================== 加载Python解析YAML配置 ====================
CONFIG_FILE="workflows/configs/train.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# 使用Python解析YAML配置
read -r -d '' PYTHON_PARSE_CONFIG << 'EOF'
import yaml
import sys

with open(sys.argv[1], 'r') as f:
    config = yaml.safe_load(f)

# 输出为shell变量格式
print(f"L_VALUES=\"{' '.join(map(str, config['system']['L_values']))}\"")
print(f"J2_VALUES=\"{' '.join(map(str, config['system']['J2_values']))}\"")
print(f"J1_VALUES=\"{' '.join(map(str, config['system']['J1_values']))}\"")

# 模型配置
model_configs = ' '.join([f"{layers},{features}" for layers, features in config['model']['configs']])
print(f"MODEL_CONFIGS=\"{model_configs}\"")
print(f"DIAG_SHIFT={config['model']['diag_shift']}")
print(f"GRAD_CLIP={config['model']['grad_clip']}")

# 训练参数
print(f"MAX_LR={config['training']['max_lr']}")
print(f"MIN_LR={config['training']['min_lr']}")
print(f"INITIAL_PERIOD={config['training']['initial_period']}")
print(f"PERIOD_MULT={config['training']['period_mult']}")
print(f"N_CYCLES={config['training']['n_cycles']}")
print(f"N_SAMPLES={config['training']['n_samples']}")
print(f"CHUNK_SIZE={config['training']['chunk_size']}")

# Checkpoint配置
print(f"ENABLE_CHECKPOINT={str(config['checkpoint']['enable']).lower()}")
print(f"CHECKPOINT_INTERVAL={config['checkpoint']['save_interval']}")
print(f"RESUME_FROM=\"{config['checkpoint']['resume_from']}\"")
print(f"KEEP_HISTORY={str(config['checkpoint']['keep_history']).lower()}")

# HPC配置
print(f"CONDA_ENV=\"{config['hpc']['conda_env']}\"")
print(f"CUDA_VISIBLE_DEVICES=\"{config['hpc']['cuda_visible_devices']}\"")
EOF

# 执行Python脚本并source结果
eval $(python3 -c "$PYTHON_PARSE_CONFIG" "$CONFIG_FILE")

# ==================== 环境配置 ====================
echo "Loading modules..."
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
export CUDA_VISIBLE_DEVICES

echo "Using GPU device: $CUDA_VISIBLE_DEVICES"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 构建checkpoint参数 ====================
build_checkpoint_args() {
    local enable=$1
    local interval=$2
    local keep_history=$3
    local resume_from=$4
    
    local args=""
    if [ "$enable" = "true" ]; then
        args="--enable_checkpoint --save_interval $interval"
        
        if [ "$keep_history" = "true" ]; then
            args="$args --keep_history"
        fi
        
        if [ -n "$resume_from" ]; then
            args="$args --resume_from $resume_from"
        fi
    fi
    
    echo "$args"
}

CHECKPOINT_ARGS=$(build_checkpoint_args "$ENABLE_CHECKPOINT" "$CHECKPOINT_INTERVAL" "$KEEP_HISTORY" "$RESUME_FROM")

# ==================== 执行训练任务 ====================
echo "==================== 开始训练任务 ===================="
echo "训练参数配置:"
echo "L values: $L_VALUES"
echo "J2 values: $J2_VALUES"
echo "J1 values: $J1_VALUES"
echo "Model configs: $MODEL_CONFIGS"
echo "Learning rate: [$MIN_LR, $MAX_LR]"
echo "Samples: $N_SAMPLES"
echo "Checkpoint args: $CHECKPOINT_ARGS"

# 将模型配置转换为数组
model_configs_array=($MODEL_CONFIGS)

for model_config in "${model_configs_array[@]}"; do
    IFS=',' read -r num_layers num_features <<< "$model_config"
    
    echo "==================== 模型: Layers=$num_layers, Features=$num_features ===================="
    
    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            for J1 in $J1_VALUES; do
                echo "Starting training L=$L, J2=$J2, J1=$J1, Layers=$num_layers, Features=$num_features at: $(date)"
                
                python scripts/train.py $L $J2 $J1 \
                    --n_samples $N_SAMPLES \
                    --chunk_size $CHUNK_SIZE \
                    --n_cycles $N_CYCLES \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --max_lr $MAX_LR \
                    --min_lr $MIN_LR \
                    --num_features $num_features \
                    --num_layers $num_layers \
                    --diag_shift $DIAG_SHIFT \
                    --grad_clip $GRAD_CLIP \
                    $CHECKPOINT_ARGS
                
                echo "Completed training L=$L, J2=$J2, J1=$J1 at: $(date)"
            done
        done
    done
done

echo "==================== 所有训练任务完成 ===================="
echo "Job finished at: $(date)"

