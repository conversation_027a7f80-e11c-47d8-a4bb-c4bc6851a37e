#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=24:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-gcnn-hybrid-phase
#PBS -j oe

# ==================== Shastry-Sutherland NQS 表征学习相变检测任务 ====================
# 此脚本用于在HPC上提交表征学习相变检测任务
# 基于VAE表征学习进行相变点检测

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "==================== NQS 表征学习相变检测任务 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Working directory: $(pwd)"
echo "GPU Information:"
nvidia-smi

# ==================== 环境配置 ====================
echo "Loading modules..."
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate netket

# 设置GPU设备
export CUDA_VISIBLE_DEVICES="0"
export XLA_FLAGS="--xla_gpu_cuda_data_dir=/usr/local/cuda"

echo "Using GPU device: $CUDA_VISIBLE_DEVICES"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 执行混合相变检测任务 ====================
echo "==================== 开始混合相变检测任务 ===================="

# 定义扫描参数
L=5
J2=1.00
J1_MIN=0.76
J1_MAX=0.84
J1_STEP=0.01
NUM_LAYERS=4
NUM_FEATURES=4
CHECKPOINT="checkpoint_iter_001050"
VAE_LATENT_DIM=16
VAE_EPOCHS=50

echo "System Configuration:"
echo "  L=$L, J2=$J2"
echo "  J1 range: $J1_MIN to $J1_MAX (step $J1_STEP)"
echo "  Model: $NUM_LAYERS layers, $NUM_FEATURES features"
echo "  Checkpoint: $CHECKPOINT"
echo "  VAE latent dim: $VAE_LATENT_DIM"

# 创建输出目录
OUTPUT_DIR="measurements/representation_learning/L=${L}/J2=${J2}"
mkdir -p "$OUTPUT_DIR"

echo ""
echo "==================== 表征学习相变检测 ===================="
echo "运行表征学习相变检测脚本..."
python scripts/run_hybrid_analysis.py \
    --L $L \
    --J2 $J2 \
    --J1_min $J1_MIN \
    --J1_max $J1_MAX \
    --J1_step $J1_STEP \
    --num_layers $NUM_LAYERS \
    --num_features $NUM_FEATURES \
    --checkpoint $CHECKPOINT \
    --vae_latent_dim $VAE_LATENT_DIM \
    --vae_epochs $VAE_EPOCHS \
    --fusion_weights 0.5 0.5 \
    --output_dir "$OUTPUT_DIR"

if [ $? -eq 0 ]; then
    echo "✓ 表征学习相变检测完成"
else
    echo "✗ 表征学习相变检测失败"
    exit 1
fi

# 记录磁盘使用情况
echo ""
echo "==================== 结果统计 ===================="
echo "Disk usage for measurements:"
du -sh measurements/

echo ""
echo "Output files in $OUTPUT_DIR:"
ls -lh "$OUTPUT_DIR"

echo ""
echo "==================== 所有任务完成 ===================="
echo "Job finished at: $(date)"
echo "Results saved to: $OUTPUT_DIR"

