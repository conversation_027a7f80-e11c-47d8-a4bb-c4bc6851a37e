#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-gcnn-analyze
#PBS -j oe

# ==================== Shastry-Sutherland NQS 结构因子测量任务 ====================
# 此脚本用于在HPC上提交结构因子测量任务
# 配置文件：workflows/configs/structure_factor_measurement.yaml

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "==================== NQS 分析任务 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Working directory: $(pwd)"
echo "GPU Information:"
nvidia-smi

# ==================== 加载Python解析YAML配置 ====================
CONFIG_FILE="workflows/configs/structure_factor_measurement.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# 使用Python解析YAML配置
read -r -d '' PYTHON_PARSE_CONFIG << 'EOF'
import yaml
import sys

with open(sys.argv[1], 'r') as f:
    config = yaml.safe_load(f)

# 输出分析参数组合
print("ANALYSIS_PARAMS=(")
for params in config['analysis_params']:
    L, J2, J1 = params
    print(f'  "{L} {J2:.2f} {J1:.2f}"')
print(")")

# 模型配置
model_configs = ' '.join([f"{layers},{features}" for layers, features in config['model']['configs']])
print(f"MODEL_CONFIGS=\"{model_configs}\"")

# 采样配置
print(f"N_SAMPLES={config['sampling']['n_samples']}")

# Checkpoint配置
print(f"CHECKPOINT_MODE=\"{config['checkpoint']['mode']}\"")
print(f"LAST_N_CHECKPOINTS={config['checkpoint']['last_n']}")

# HPC配置
print(f"CONDA_ENV=\"{config['hpc']['conda_env']}\"")
print(f"CUDA_VISIBLE_DEVICES=\"{config['hpc']['cuda_visible_devices']}\"")
EOF

# 执行Python脚本并source结果
eval $(python3 -c "$PYTHON_PARSE_CONFIG" "$CONFIG_FILE")

# ==================== 环境配置 ====================
echo "Loading modules..."
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
export CUDA_VISIBLE_DEVICES

echo "Using GPU device: $CUDA_VISIBLE_DEVICES"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 执行分析任务 ====================
echo "==================== 开始分析任务 ===================="
echo "分析参数组合:"
printf '%s\n' "${ANALYSIS_PARAMS[@]}"
echo "Model configs: $MODEL_CONFIGS"
echo "Checkpoint mode: $CHECKPOINT_MODE"
if [ "$CHECKPOINT_MODE" = "last_n" ]; then
    echo "Last N checkpoints: $LAST_N_CHECKPOINTS"
fi

# 将模型配置转换为数组
model_configs_array=($MODEL_CONFIGS)

for model_config in "${model_configs_array[@]}"; do
    IFS=',' read -r num_layers num_features <<< "$model_config"
    
    echo "==================== 模型: Layers=$num_layers, Features=$num_features ===================="
    model_dir="model_L${num_layers}F${num_features}"
    
    # 遍历所有参数组合
    for params in "${ANALYSIS_PARAMS[@]}"; do
        read -r L J2 J1 <<< "$params"

        echo "Starting analysis: L=$L, J2=$J2, J1=$J1, Layers=$num_layers, Features=$num_features at: $(date)"

        # 获取checkpoint目录
        checkpoint_dir="saved_models/L=$L/J2=$J2/J1=$J1/checkpoints"

        # 检查checkpoint目录是否存在
        if [ ! -d "$checkpoint_dir" ]; then
            echo "Warning: Checkpoint directory $checkpoint_dir does not exist, skipping..."
            continue
        fi
        
        # 根据配置决定分析哪些checkpoint
        case "$CHECKPOINT_MODE" in
            "all")
                checkpoint_files=($(find "$checkpoint_dir" -name "*.pkl" -printf '%T@ %p\n' | sort -n | cut -d' ' -f2-))
                ;;
            "final")
                final_checkpoint=$(find "$checkpoint_dir" -name "checkpoint_iter_*.pkl" 2>/dev/null | sort | tail -n 1)
                if [ -z "$final_checkpoint" ] || [ ! -f "$final_checkpoint" ]; then
                    echo "Warning: No checkpoint files found in $checkpoint_dir, skipping..."
                    continue
                fi
                checkpoint_files=("$final_checkpoint")
                ;;
            "last_n")
                all_checkpoints=($(find "$checkpoint_dir" -name "checkpoint_iter_*.pkl" 2>/dev/null | sort))
                total_checkpoints=${#all_checkpoints[@]}
                
                if [ $total_checkpoints -eq 0 ]; then
                    echo "Warning: No checkpoint files found in $checkpoint_dir, skipping..."
                    continue
                fi
                
                if [ $total_checkpoints -le $LAST_N_CHECKPOINTS ]; then
                    checkpoint_files=("${all_checkpoints[@]}")
                else
                    start_index=$((total_checkpoints - LAST_N_CHECKPOINTS))
                    checkpoint_files=("${all_checkpoints[@]:$start_index}")
                fi
                ;;
            *)
                echo "Error: Invalid CHECKPOINT_MODE '$CHECKPOINT_MODE'"
                exit 1
                ;;
        esac
        
        # 为每个checkpoint运行分析
        for checkpoint_file in "${checkpoint_files[@]}"; do
            checkpoint_name=$(basename "$checkpoint_file" .pkl)
            echo "  Processing checkpoint: $checkpoint_name"
            
            python scripts/structure_factor_measurement.py --L $L --J2 $J2 --J1 $J1 \
                --num_features $num_features --num_layers $num_layers \
                --checkpoint "$checkpoint_name" --n_samples $N_SAMPLES
            
            echo "  Completed checkpoint: $checkpoint_name"
        done
        
        echo "Completed analysis for L=$L, J2=$J2, J1=$J1 at: $(date)"
    done
done

# 记录磁盘使用情况
echo "Disk usage for results:"
du -sh results/

echo "==================== 所有分析任务完成 ===================="
echo "Job finished at: $(date)"

