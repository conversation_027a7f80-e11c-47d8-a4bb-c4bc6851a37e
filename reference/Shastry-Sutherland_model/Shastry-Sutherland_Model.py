import os
import logging

import sys
os.environ["CUDA_VISIBLE_DEVICES"]="1"

os.environ["JAX_PLATFORM_NAME"] = "gpu"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"]="false"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"]="platform"

import jax
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import eigsh
from matplotlib.colors import LogNorm
import time
import sys 
from matplotlib.colors import LogNorm
import json
import netket.nn as nknn
import flax
import flax.linen as nn
import jax.numpy as jnp
import math
from math import pi
from netket.nn import log_cosh, reim_selu
from netket.utils.group.planar import rotation, reflection_group, D, glide, glide_group
from netket.utils.group import PointGroup, Identity
from netket.operator.spin import sigmax, sigmay, sigmaz
from netket.utils.group import PermutationGroup,Permutation
from netket.utils.group import PointGroup, Identity
from netket.graph import Kagome
from netket.utils.group.planar import rotation, reflection_group, D
from netket.optimizer.qgt import (
QGTJacobianPyTree, QGTJacobianDense, QGTOnTheFly
)
from netket.operator import AbstractOperator
from netket.vqs import VariationalState

from scipy import sparse as _sparse
from netket.utils.types import DType as _DType 
from netket.hilbert import DiscreteHilbert as _DiscreteHilbert
from netket.operator import LocalOperator as _LocalOperator


def unit_m(
        hilbert:_DiscreteHilbert, site: int, dtype: _DType = None
        ) -> _LocalOperator:

    import numpy as np
    N=hilbert.size_at_index(site)
    S=(N-1) / 2

    D=np.array([2*m for m in np.arange(S,-(S+1), -1)])
    mat=np.diag(D,0)
    mat=_sparse.coo_matrix(mat)
    return _LocalOperator(hilbert,mat,[site],dtype=dtype)

  
#N_features and N_layers in GCNN 

N_features = 4
N_layers = 4

# J1,J2 and h in Hamiltonian

J1 = 0.8
J2 = 1.0
h = 0.0

# Shastry-Sutherland lattice with periodic boundary conditions (N=4*Lx*Ly lattice sites)

Lx = 3
Ly = 3


custom_edges=[(0,1,[1.0,0.0],0),
              (1,0,[1.0,0.0],0),
              (1,2,[0.0,1.0],0),
              (2,1,[0.0,1.0],0),
              (3,2,[1.0,0.0],0),
              (2,3,[1.0,0.0],0),
              (0,3,[0.0,1.0],0),
              (3,0,[0.0,1.0],0),
              (2,0,[1.0,-1.0],1),
              (3,1,[1.0,1.0],1),
    
    ]



lattice = nk.graph.Lattice(
basis_vectors = [[2.0,0.0], [0.0,2.0]], 
extent = (Lx,Ly), site_offsets=[[0.5,0.5],[0.5+1.0,0.5],[0.5+1.0,0.5+1.0],[0.5,0.5+1.0]], custom_edges=custom_edges, pbc=[True,True])


# draws the lattice graph
lattice.draw(edge_color='red',curvature=0.1)
plt.savefig("Shastry-Sutherland_Model.png")


#h=0
#Hilbert space, spin basis, total magnetization zero sector 
hi = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)

#finite h
#hi = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)


#spin-1/2 matrices  
sigmax = [[0, 0.5], [0.5, 0]]
sigmay = [[0, -0.5j], [0.5j, 0]] 
sigmaz = [[0.5, 0], [0, -0.5]]
unitm = [[1.0,0.0],[0.0,1.0]]


sxsx = np.kron(sigmax,sigmax)
sysy = np.kron(sigmay,sigmay)
szsz = np.kron(sigmaz,sigmaz)

umum = np.kron(unitm,unitm)

SiSj = sxsx + sysy + szsz 


#Metropolis sampler for Hilbert space

#h=0,total_sz=0
sampler = nk.sampler.MetropolisExchange(hilbert=hi, graph=lattice, n_chains=2**12,d_max=2)#d_max=1 for J2=0

#finite h 
#sampler = nk.sampler.MetropolisLocal(hilbert=hi, n_chains=2**12)

#functions for entropy gradient 

def T_logp2(params,inputs,temperature):
    variables={"params":params}
    preds=model.apply(variables,inputs)
    return 2.0*temperature*jnp.mean(jnp.real(preds)*jnp.real(preds))

def T_logp_2(params,inputs,temperature):
    variables={"params":params}
    preds=model.apply(variables,inputs)
    return 2.0*temperature*jnp.mean(jnp.real(preds))*jnp.mean(jnp.real(preds))


#symmetric local cluster for which elements of the embedding kernels K are nonzero for particular system size Lx,Ly
#for smaller system sizes all lattice sites can be included 

#3x3x4
local_cluster=jnp.arange(Lx*Ly*4)

local_cluster=local_cluster.tolist()

print(local_cluster)


mask=jnp.zeros(lattice.n_nodes)

for i in range(int(lattice.n_nodes)):
    mask=mask.at[i].set(False)
    
for i in local_cluster:
    mask=mask.at[i].set(True)    
   
# lattice symmetries, point group symmetries C4v +translation symmetry   

nc=4
cyclic_4 = PointGroup(
[Identity()] + [rotation((360 / nc) * i) for i in range(1, nc)],
ndim=2,
)

#C4v=cyclic_4

C4v=glide_group(trans=(1,1),origin=(0,0)) @ cyclic_4

symmetries=lattice.space_group(C4v)

sgb=lattice.space_group_builder(point_group=C4v)


momentum=[0.0,0.0]
chi=sgb.space_group_irreps(momentum)[0]

print(sgb.little_group(momentum).character_table_readable())
print(chi)

# group equivariant convolutional neural network - wavefunction NQS ansatz 

#h=0
model=nk.models.GCNN(symmetries = symmetries, layers=N_layers,param_dtype = np.complex128, features=N_features, equal_amplitudes=False,parity=1,input_mask=mask, characters=chi)

#finite h - no parity
#model=nk.models.GCNN(symmetries = symmetries, layers=N_layers,param_dtype = np.complex128, features=N_features, equal_amplitudes=False,input_mask=mask, characters=chi)

#Hamiltonian 

bond_operator = [
    (J1*SiSj).tolist(),
    (J2*SiSj).tolist(),
 ]   

bond_color = [0,1]

site_operator=[(-h*np.array(sigmaz)).tolist()]

H = nk.operator.GraphOperator(hi, graph=lattice, bond_ops=bond_operator, bond_ops_colors=bond_color,site_ops=site_operator)


start = time.time()

vqs = nk.vqs.MCState(
sampler=sampler,
model=model,
n_samples=2**12,
n_discard_per_chain=0,
chunk_size=2**10,
)

from tqdm import tqdm


n_ann = 2000
n_train=1

#with open("GCNN_0.mpack",'rb') as file:
#    vqs=flax.serialization.from_bytes(vqs,file.read())


if os.path.exists("E_real_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat"):
  os.remove("E_real_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat")
else:
  print("The file E_real does not exist") 
  
if os.path.exists("E_imag_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat"):
  os.remove("E_imag_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat")
else:
  print("The file E_imag does not exist")
  
if os.path.exists("Iters_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat"):
  os.remove("Iters_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat")
else:
  print("The file Iters does not exist")  
  
   
if os.path.exists("E_error_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat"):
  os.remove("E_error_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat")
else:
  print("The file E_error does not exist") 
  
if os.path.exists("E_variance_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat"):
  os.remove("E_variance_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat")
else:
  print("The file E_variance does not exist")  
  
#stochastic reconfiguration, imaginary time evolution, free energy loss function  

def custom_sr_free_energy(
      hamiltonian: AbstractOperator,    
      vstate: VariationalState,                                            
      lr: float,                                         
      temperature: float,
      n_ann: int,
      n_train:int,
):
      
      for i in range(n_ann):
          temperature_i=temperature*(jnp.exp(-i/50.0)/2.0)
          for j in tqdm(range(n_train)):
            with open("GCNN_Lx="+str(Lx)+"_Ly="+str(Ly)+"_J1="+str(J1)+"_h="+str(h)+".mpack", 'wb') as file:
                file.write(flax.serialization.to_bytes(vstate))
            
            energy, f = vstate.expect_and_grad(hamiltonian)
            variables=vstate.variables 
            inputs0=vstate.samples
            inputs1=jnp.reshape(inputs0,(1,sampler.n_chains,lattice.n_nodes))
            inputs=inputs1[0]
            
                
            with open("E_real_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat","a+") as file:
                file.write(str(energy.mean.real)+" ")
            with open("E_imag_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat","a+") as file:
                file.write(str(energy.mean.imag)+" ")
            with open("Iters_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat","a+") as file:
                file.write(str(i)+" ")
                
            with open("E_error_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat","a+") as file:
                file.write(str(energy.error_of_mean)+" ")
            with open("E_variance_Lx="+str(Lx)+"_Ly="+str(Ly)+"_GCNN_J1="+str(J1)+"_h="+str(h)+".dat","a+") as file:
                file.write(str(energy.variance)+" ")    
            
            
            print("iteration:",i)                           
            print("E:", energy.mean,"+-",energy.error_of_mean)  
            
            G = vstate.quantum_geometric_tensor(QGTJacobianDense(diag_shift=0.001,diag_scale=0.001))
           # G = vstate.quantum_geometric_tensor(QGTJacobianPyTree(diag_shift=0.001,diag_scale=0.001))
            
            mT_grad_S_1 = jax.grad(T_logp2,argnums=0)(variables["params"],inputs,temperature_i)
            
            mT_grad_S_2 = jax.grad(T_logp_2,argnums=0)(variables["params"],inputs,temperature_i)
            
            mT_grad_S = jax.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)            
            
            gamma_S=jax.tree_map(lambda x: -1.0 *jnp.conj(x), mT_grad_S)
            
            gamma_f = jax.tree_map(lambda x: -1.0 * x, f)
            
            gamma_tot= jax.tree_map(lambda x,y: x+y, gamma_f,gamma_S)
            
            dtheta, _ = G.solve(jax.scipy.sparse.linalg.cg, gamma_tot)
            
            vstate.parameters = jax.tree_map(
            lambda x, y: x + lr * y, vstate.parameters, dtheta
            
            )            
           

custom_sr_free_energy(hamiltonian=H,vstate=vqs,lr=0.05,n_ann=n_ann, n_train=n_train, temperature=1.0)

end = time.time()


print('The GCNN calculation took',end-start,'seconds')


