# Shastry-Sutherland NQS 项目结构

## 📁 项目组织

本项目采用清晰的三层结构组织：

```
Shastry-Sutherland_Extra/
├── workflows/              # 🔥 工作流管理层
├── saved_models/           # 💾 模型存储层
└── measurements/           # 📊 测量数据层
```

---

## 🔥 1. workflows/ - 工作流管理层

负责调用和运行所有计算任务。

```
workflows/
├── configs/                # 配置文件
│   ├── train.yaml         # 训练配置
│   ├── analyze.yaml       # 结构因子测量配置
│   ├── infidelity.yaml    # Infidelity测量配置
│   └── postprocess.yaml   # 后处理配置
├── hpc/                   # HPC提交脚本
│   ├── submit_train.pbs
│   ├── submit_analyze.pbs
│   └── submit_infidelity.pbs
├── local/                 # 本地执行脚本
│   ├── run_train.sh
│   ├── run_analyze.sh
│   └── run_postprocess.sh
├── pipeline.py            # 统一流水线管理器
├── verify_setup.py        # 设置验证脚本
├── README.md              # 详细文档
└── QUICKSTART.md          # 快速指南
```

### 使用方式

```bash
# 查看流水线状态
python workflows/pipeline.py --status

# 提交训练到HPC
python workflows/pipeline.py --stage train --env hpc

# 提交结构因子测量到HPC
python workflows/pipeline.py --stage analyze --env hpc

# 提交Infidelity测量到HPC
python workflows/pipeline.py --stage infidelity --env hpc

# 本地后处理
python workflows/pipeline.py --stage postprocess --env local
```

---

## 💾 2. saved_models/ - 模型存储层

存储训练好的NQS模型参数和训练日志。

```
saved_models/
└── L={L}/
    └── J2={J2}/
        └── J1={J1}/
            ├── train.log              # 训练日志（能量、梯度等）
            ├── checkpoints/           # 模型checkpoint
            │   ├── checkpoint_iter_000225.pkl
            │   ├── checkpoint_iter_000450.pkl
            │   └── ...
            └── final_model.pkl        # 最终模型（可选）
```

### 示例

```
saved_models/
└── L=6/
    └── J2=1.00/
        ├── J1=0.76/
        │   ├── train.log
        │   └── checkpoints/
        ├── J1=0.77/
        │   ├── train.log
        │   └── checkpoints/
        └── ...
```

### 说明

- **train.log**: 包含每个迭代的能量、梯度范数等训练信息
- **checkpoints/**: 定期保存的模型参数，用于恢复训练或进行测量
- 由 `scripts/train.py` 生成
- 由 `scripts/analyze.py` 和 `scripts/infidelity_analysis.py` 读取

---

## 📊 3. measurements/ - 测量数据层

存储所有物理量测量结果。

```
measurements/
├── structure_factor/      # 结构因子测量
├── order_parameter/       # 序参量分析
├── energy/               # 能量分析
└── infidelity/           # Infidelity测量
```

### 3.1 structure_factor/ - 结构因子测量

HPC上运行，从训练好的模型计算各种结构因子。

```
measurements/structure_factor/
└── L={L}/
    └── J2={J2}/
        └── J1={J1}/
            └── {checkpoint_name}/
                ├── structure_factor_*.log
                ├── spin/
                │   ├── spin_data.npy
                │   └── spin_structure_factor.png
                ├── dimer/
                │   ├── dimer_data.npy
                │   └── dimer_structure_factor.png
                ├── diag_dimer/
                │   └── ...
                └── plaquette/
                    └── ...
```

**生成方式**: 
```bash
python workflows/pipeline.py --stage analyze --env hpc
# 或
python scripts/analyze.py --L 6 --J2 1.00 --J1 0.76
```

### 3.2 order_parameter/ - 序参量分析

本地运行，从结构因子数据计算序参量。

```
measurements/order_parameter/
├── analysis_data.pkl      # 收集的所有数据
├── analysis_log.txt       # 分析日志
└── figures/               # 生成的图表
    ├── neel_ratio_vs_J1_J2=1.00.png
    ├── dimer_ratio_vs_J1_J2=1.00.png
    └── ...
```

**生成方式**:
```bash
python workflows/pipeline.py --stage postprocess --env local
# 或
python scripts/postprocess.py --task order_analysis
```

**说明**:
- 从 `measurements/structure_factor/` 读取结构因子数据
- 计算Neel、Dimer、对角Dimer等序参量
- 生成序参量随J1变化的图表

### 3.3 energy/ - 能量分析

本地运行，从训练日志分析能量收敛情况。

```
measurements/energy/
├── energy_analysis_*.png  # 能量收敛图
└── energy_data.pkl        # 能量数据
```

**生成方式**:
```bash
python workflows/pipeline.py --stage postprocess --env local
# 或
python scripts/postprocess.py --task energy_analysis
```

**说明**:
- 从 `saved_models/L={L}/J2={J2}/J1={J1}/train.log` 读取能量数据
- 生成能量随迭代次数变化的图表
- 分析能量收敛性

### 3.4 infidelity/ - Infidelity测量

HPC上运行，计算相邻参数点之间的Infidelity。

```
measurements/infidelity/
└── L={L}/
    └── J2={J2}/
        ├── infidelity_*.log
        ├── infidelity_data.pkl
        └── infidelity_vs_J1.png
```

**生成方式**:
```bash
python workflows/pipeline.py --stage infidelity --env hpc
# 或
python scripts/infidelity_analysis.py --L 6 --J2 1.00 --J1_values 0.76 0.77 0.78 0.79 0.80
```

**说明**:
- 从 `saved_models/` 加载不同J1值的模型参数
- 计算相邻J1值之间的Infidelity
- 用于识别量子相变点

---

## 🔧 核心脚本

### scripts/train.py
- **功能**: 训练NQS模型
- **输出**: `saved_models/L={L}/J2={J2}/J1={J1}/`
- **运行**: HPC GPU节点

### scripts/analyze.py
- **功能**: 计算结构因子
- **输入**: `saved_models/L={L}/J2={J2}/J1={J1}/checkpoints/`
- **输出**: `measurements/structure_factor/L={L}/J2={J2}/J1={J1}/{checkpoint}/`
- **运行**: HPC GPU节点

### scripts/infidelity_analysis.py
- **功能**: 计算Infidelity
- **输入**: `saved_models/L={L}/J2={J2}/J1={J1}/checkpoints/`
- **输出**: `measurements/infidelity/L={L}/J2={J2}/`
- **运行**: HPC GPU节点

### scripts/postprocess.py
- **功能**: 后处理分析（序参量、能量）
- **输入**: 
  - `measurements/structure_factor/` (序参量分析)
  - `saved_models/` (能量分析)
- **输出**: 
  - `measurements/order_parameter/`
  - `measurements/energy/`
- **运行**: 本地

---

## 📝 配置文件说明

### workflows/configs/train.yaml
- 训练参数配置
- 模型参数配置
- Checkpoint配置

### workflows/configs/analyze.yaml
- 结构因子测量参数配置
- 采样数配置
- Checkpoint选择配置

### workflows/configs/infidelity.yaml
- Infidelity测量参数配置
- J1扫描范围配置

### workflows/configs/postprocess.yaml
- 序参量分析配置
- 能量分析配置
- 图表生成配置

---

## 🚀 典型工作流程

### 1. 训练模型
```bash
# 修改 workflows/configs/train.yaml
# 提交训练任务
python workflows/pipeline.py --stage train --env hpc
```

### 2. 测量结构因子
```bash
# 修改 workflows/configs/analyze.yaml
# 提交测量任务
python workflows/pipeline.py --stage analyze --env hpc
```

### 3. 测量Infidelity
```bash
# 修改 workflows/configs/infidelity.yaml
# 提交测量任务
python workflows/pipeline.py --stage infidelity --env hpc
```

### 4. 本地后处理
```bash
# 修改 workflows/configs/postprocess.yaml
# 运行后处理
python workflows/pipeline.py --stage postprocess --env local
```

---

## ✅ 验证设置

```bash
python workflows/verify_setup.py
```

应该看到：
```
✓ 成功: 35
⚠ 警告: 0
✗ 错误: 0
✓ 所有关键组件验证通过！
```

---

## 📚 其他目录

### src/ - 源代码
```
src/
├── analysis/          # 分析模块
├── models/           # 模型定义
├── physics/          # 物理系统
├── utils/            # 工具函数
└── runner.py         # 训练运行器
```

### tests/ - 测试代码
```
tests/
├── unit/             # 单元测试
└── integration/      # 集成测试
```

### reference/ - 参考资料
```
reference/
├── papers/           # 相关论文
└── notes/            # 笔记
```

---

## 🎯 关键设计原则

1. **职责分离**: workflows管理流程，saved_models存储模型，measurements存储数据
2. **清晰命名**: 目录和文件名清晰表达其内容和用途
3. **标准化路径**: 所有脚本使用统一的路径规范
4. **无向前兼容**: 删除旧的results目录，全新的组织结构
5. **易于维护**: 结构清晰，便于理解和扩展

---

**最后更新**: 2025-10-24  
**状态**: ✅ 完成并验证

